#!/usr/bin/env python3
"""
Hyperparameter Optimization for Strategy Evolution Agent

This module contains hyperparameter optimization methods including Optuna,
grid search, and random search optimization.
"""

import copy
import itertools
import random
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from .data_types import (
    StrategyChromosome, OptimizationTarget, OptimizationMethod, EvolutionConfig
)
from .lazy_imports import lazy_import_optuna, log_critical


class HyperparameterOptimizer:
    """Handles hyperparameter optimization for strategies"""
    
    def __init__(self, evolution_config: EvolutionConfig, generation_counter: int = 0):
        self.evolution_config = evolution_config
        self.generation_counter = generation_counter
    
    def set_generation_counter(self, generation: int):
        """Update the generation counter"""
        self.generation_counter = generation
    
    async def optimize_strategy_hyperparameters(self, chromosome: StrategyChromosome,
                                              optimization_targets: List[OptimizationTarget] = None) -> StrategyChromosome:
        """Optimize strategy hyperparameters using Optuna or other methods"""
        try:
            if not self.evolution_config.enable_hyperparameter_optimization:
                return chromosome

            # Lazy import Optuna
            optuna, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>n<PERSON><PERSON><PERSON>, optuna_available = lazy_import_optuna()

            if not optuna_available and self.evolution_config.optimization_method in [
                OptimizationMethod.OPTUNA_TPE, OptimizationMethod.OPTUNA_RANDOM
            ]:
                return await self._random_search_optimization(chromosome)

            if self.evolution_config.optimization_method == OptimizationMethod.OPTUNA_TPE:
                return await self._optuna_optimization(chromosome, optimization_targets, optuna, TPESampler, MedianPruner)
            elif self.evolution_config.optimization_method == OptimizationMethod.GRID_SEARCH:
                return await self._grid_search_optimization(chromosome)
            elif self.evolution_config.optimization_method == OptimizationMethod.RANDOM_SEARCH:
                return await self._random_search_optimization(chromosome)
            else:
                return chromosome

        except Exception as e:
            return chromosome

    async def _optuna_optimization(self, chromosome: StrategyChromosome,
                                 optimization_targets: List[OptimizationTarget] = None,
                                 optuna=None, TPESampler=None, MedianPruner=None) -> StrategyChromosome:
        """Optimize using Optuna TPE sampler"""
        try:
            if not optuna:
                return chromosome

            # Create optimization study
            study_name = f"{chromosome.strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            study = optuna.create_study(
                direction="maximize",  # We'll handle multi-objective internally
                sampler=TPESampler(),
                pruner=MedianPruner(),
                study_name=study_name
            )
            
            # Define objective function
            def objective(trial):
                return self._optuna_objective(trial, chromosome, optimization_targets)

            # Run optimization
            study.optimize(
                objective,
                n_trials=self.evolution_config.optuna_trials,
                timeout=self.evolution_config.optuna_timeout
            )

            # Create optimized chromosome
            best_params = study.best_params
            optimized_chromosome = self._apply_optimized_parameters(chromosome, best_params)

            # Store optimization history
            optimized_chromosome.optimization_history.append({
                'method': 'optuna_tpe',
                'best_value': study.best_value,
                'best_params': best_params,
                'n_trials': len(study.trials),
                'timestamp': datetime.now().isoformat()
            })

            return optimized_chromosome

        except Exception as e:
            return chromosome

    def _optuna_objective(self, trial, chromosome: StrategyChromosome,
                         optimization_targets: List[OptimizationTarget] = None) -> float:
        """Optuna objective function"""
        try:
            # Suggest parameters for optimization
            suggested_params = {}

            for gene_name, gene in chromosome.genes.items():
                if gene.optimization_space:
                    space = gene.optimization_space

                    if space['type'] == 'float':
                        suggested_params[gene_name] = trial.suggest_float(
                            gene_name, space['low'], space['high'],
                            step=space.get('step'), log=space.get('log', False)
                        )
                    elif space['type'] == 'int':
                        suggested_params[gene_name] = trial.suggest_int(
                            gene_name, space['low'], space['high'],
                            step=space.get('step', 1)
                        )
                    elif space['type'] == 'categorical':
                        suggested_params[gene_name] = trial.suggest_categorical(
                            gene_name, space['choices']
                        )

            # Create temporary chromosome with suggested parameters
            temp_chromosome = copy.deepcopy(chromosome)
            for param_name, param_value in suggested_params.items():
                if param_name in temp_chromosome.genes:
                    temp_chromosome.genes[param_name].value = param_value

            # Evaluate performance (this would typically involve backtesting)
            performance_score = self._evaluate_chromosome_performance(temp_chromosome)

            return performance_score

        except Exception as e:
            return 0.0

    def _evaluate_chromosome_performance(self, chromosome: StrategyChromosome) -> float:
        """Evaluate chromosome performance for optimization"""
        try:
            # This is a simplified evaluation - in practice, this would run backtesting
            # For now, we'll use a heuristic based on gene values

            performance = 0.0

            # Risk-reward ratio scoring
            if 'stop_loss_pct' in chromosome.genes and 'take_profit_pct' in chromosome.genes:
                sl = chromosome.genes['stop_loss_pct'].value
                tp = chromosome.genes['take_profit_pct'].value
                rr_ratio = tp / sl if sl > 0 else 1.0
                performance += min(rr_ratio * 0.1, 0.3)  # Cap at 0.3

            # Parameter balance scoring
            if 'rsi_period' in chromosome.genes:
                rsi_period = chromosome.genes['rsi_period'].value
                if 10 <= rsi_period <= 20:
                    performance += 0.1

            if 'position_size_pct' in chromosome.genes:
                pos_size = chromosome.genes['position_size_pct'].value
                if 0.02 <= pos_size <= 0.08:
                    performance += 0.2

            # Add some randomness to simulate market uncertainty
            market_noise = random.gauss(0, 0.05)
            performance += market_noise

            return max(0.0, performance)

        except Exception as e:
            return 0.0

    def _apply_optimized_parameters(self, chromosome: StrategyChromosome,
                                  optimized_params: Dict[str, Any]) -> StrategyChromosome:
        """Apply optimized parameters to chromosome"""
        optimized_chromosome = copy.deepcopy(chromosome)
        optimized_chromosome.strategy_id = str(uuid.uuid4())
        optimized_chromosome.strategy_name = f"{chromosome.strategy_name}_OPT"
        optimized_chromosome.generation = self.generation_counter
        optimized_chromosome.parent_ids = [chromosome.strategy_id]
        optimized_chromosome.creation_timestamp = datetime.now()

        # Apply optimized parameters
        for param_name, param_value in optimized_params.items():
            if param_name in optimized_chromosome.genes:
                optimized_chromosome.genes[param_name].value = param_value

        return optimized_chromosome

    async def _grid_search_optimization(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Grid search optimization"""
        try:
            best_chromosome = chromosome
            best_score = 0.0

            # Define parameter grids
            parameter_grids = self._create_parameter_grids(chromosome)

            # Generate all combinations
            param_names = list(parameter_grids.keys())
            param_values = list(parameter_grids.values())

            for combination in itertools.product(*param_values):
                # Create test chromosome
                test_chromosome = copy.deepcopy(chromosome)

                for i, param_name in enumerate(param_names):
                    if param_name in test_chromosome.genes:
                        test_chromosome.genes[param_name].value = combination[i]

                # Evaluate performance
                score = self._evaluate_chromosome_performance(test_chromosome)

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome

            # Update chromosome metadata
            best_chromosome.strategy_id = str(uuid.uuid4())
            best_chromosome.strategy_name = f"{chromosome.strategy_name}_GRID"
            best_chromosome.optimization_history.append({
                'method': 'grid_search',
                'best_score': best_score,
                'timestamp': datetime.now().isoformat()
            })

            return best_chromosome

        except Exception as e:
            return chromosome

    def _create_parameter_grids(self, chromosome: StrategyChromosome) -> Dict[str, List[Any]]:
        """Create parameter grids for grid search"""
        grids = {}

        for gene_name, gene in chromosome.genes.items():
            if gene.gene_type == 'numeric' and gene.min_value is not None and gene.max_value is not None:
                if isinstance(gene.value, int):
                    # Integer parameter
                    step = max(1, int((gene.max_value - gene.min_value) / 10))
                    grids[gene_name] = list(range(int(gene.min_value), int(gene.max_value) + 1, step))
                else:
                    # Float parameter
                    step = (gene.max_value - gene.min_value) / 10
                    grids[gene_name] = [gene.min_value + i * step for i in range(11)]
            elif gene.gene_type == 'boolean':
                grids[gene_name] = [True, False]

        return grids

    async def _random_search_optimization(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Random search optimization"""
        try:
            from .genetic_operations import GeneticOperations
            
            best_chromosome = chromosome
            best_score = self._evaluate_chromosome_performance(chromosome)

            # Create genetic operations instance for mutation
            genetic_ops = GeneticOperations(self.evolution_config, self.generation_counter)

            # Random search iterations
            for _ in range(50):  # 50 random trials
                # Create random variant
                test_chromosome = genetic_ops.mutate_chromosome(chromosome, mutation_rate=0.3)

                # Evaluate performance
                score = self._evaluate_chromosome_performance(test_chromosome)

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome

            # Update chromosome metadata
            best_chromosome.strategy_id = str(uuid.uuid4())
            best_chromosome.strategy_name = f"{chromosome.strategy_name}_RAND"
            best_chromosome.optimization_history.append({
                'method': 'random_search',
                'best_score': best_score,
                'timestamp': datetime.now().isoformat()
            })

            return best_chromosome

        except Exception as e:
            return chromosome

    async def optimize_risk_reward_combinations(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Optimize risk-reward combinations for a strategy"""
        try:
            best_chromosome = chromosome
            best_score = 0.0

            # Test different risk-reward combinations
            for sl_ratio, tp_ratio in self.evolution_config.rr_combinations:
                # Create test chromosome with new RR ratio
                test_chromosome = copy.deepcopy(chromosome)

                # Apply risk-reward ratio
                if 'stop_loss_pct' in test_chromosome.genes:
                    base_sl = test_chromosome.genes['stop_loss_pct'].value
                    test_chromosome.genes['stop_loss_pct'].value = base_sl * sl_ratio

                if 'take_profit_pct' in test_chromosome.genes:
                    base_tp = test_chromosome.genes['take_profit_pct'].value
                    test_chromosome.genes['take_profit_pct'].value = base_tp * tp_ratio

                # Evaluate performance
                score = await self._evaluate_rr_combination(test_chromosome, sl_ratio, tp_ratio)

                # Track combination
                test_chromosome.rr_combinations.append((sl_ratio, tp_ratio))

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome
                    best_chromosome.best_rr_combo = (sl_ratio, tp_ratio)

            return best_chromosome

        except Exception as e:
            return chromosome

    async def _evaluate_rr_combination(self, chromosome: StrategyChromosome,
                                     sl_ratio: float, tp_ratio: float) -> float:
        """Evaluate a specific risk-reward combination"""
        try:
            # Calculate theoretical performance metrics
            rr_ratio = tp_ratio / sl_ratio if sl_ratio > 0 else 1.0

            # Base score from RR ratio
            score = min(rr_ratio * 0.1, 0.4)  # Cap at 0.4

            # Adjust for practical considerations
            if 1.5 <= rr_ratio <= 3.0:  # Sweet spot for most strategies
                score += 0.1

            # Penalize extreme ratios
            if rr_ratio > 5.0 or rr_ratio < 0.5:
                score -= 0.2

            # Add randomness to simulate market conditions
            market_factor = random.uniform(0.8, 1.2)
            score *= market_factor

            return max(0.0, score)

        except Exception as e:
            return 0.0
