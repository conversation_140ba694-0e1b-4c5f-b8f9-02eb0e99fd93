#!/usr/bin/env python3
"""
Genetic Algorithm Module for Strategy Evolution

This module handles genetic operations for strategy evolution including:
- DNA extraction and conversion
- Mutation operations
- Crossover operations
- Tournament selection
- Population management
"""

import uuid
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime

from agents.strategy_evolution.evolution_config import <PERSON><PERSON>ariant, StrategyStatus
from agents.strategy_evolution.evolution_logger import logger


class GeneticAlgorithm:
    """
    Handles genetic algorithm operations for strategy evolution
    """
    
    def __init__(self, evolution_config):
        self.evolution_config = evolution_config
        
        # Genetic Algorithm parameters
        self.population_size = 50
        self.mutation_rate = 0.15
        self.crossover_rate = 0.8
        self.elite_size = 5  # Top performers to keep unchanged
        self.tournament_size = 3  # For tournament selection
        
        logger.info("[GENETIC] Genetic Algorithm initialized")
    
    def create_strategy_dna(self, variant: StrategyVariant) -> Dict[str, float]:
        """Extract DNA (numerical parameters) from strategy variant for genetic operations"""
        try:
            dna = {}

            # Extract numerical parameters from risk management
            if variant.risk_management:
                risk_mgmt = variant.risk_management
                dna['stop_loss'] = float(risk_mgmt.get('stop_loss', 0.02))
                dna['take_profit'] = float(risk_mgmt.get('take_profit', 0.04))
                dna['max_position_size'] = float(risk_mgmt.get('max_position_size', 0.1))

            # Extract from entry conditions (assuming RSI-based for now)
            if variant.entry_conditions:
                entry = variant.entry_conditions
                dna['oversold_threshold'] = float(entry.get('oversold_threshold', 30))
                dna['overbought_threshold'] = float(entry.get('overbought_threshold', 70))
                dna['rsi_period'] = float(entry.get('rsi_period', 14))

            # Extract from position sizing
            if variant.position_sizing:
                pos_size = variant.position_sizing
                dna['risk_per_trade'] = float(pos_size.get('risk_per_trade', 0.02))
                dna['max_trades'] = float(pos_size.get('max_trades', 3))

            return dna
            
        except Exception as e:
            logger.error(f"Error creating strategy DNA: {e}")
            return {}

    def dna_to_variant(self, base_variant: StrategyVariant, dna: Dict[str, float]) -> StrategyVariant:
        """Convert DNA back to strategy variant"""
        try:
            new_variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_variant.base_strategy_name,
                stock_name=base_variant.stock_name,
                timeframe=base_variant.timeframe,
                ranking=0,  # Will be calculated
                entry_conditions={
                    'oversold_threshold': max(10, min(40, dna.get('oversold_threshold', 30))),
                    'overbought_threshold': max(60, min(90, dna.get('overbought_threshold', 70))),
                    'rsi_period': max(5, min(30, int(dna.get('rsi_period', 14))))
                },
                exit_conditions=base_variant.exit_conditions,
                intraday_rules=base_variant.intraday_rules,
                risk_reward_ratios=base_variant.risk_reward_ratios,
                risk_management={
                    'stop_loss': max(0.005, min(0.05, dna.get('stop_loss', 0.02))),
                    'take_profit': max(0.01, min(0.1, dna.get('take_profit', 0.04))),
                    'max_position_size': max(0.01, min(0.2, dna.get('max_position_size', 0.1)))
                },
                position_sizing={
                    'risk_per_trade': max(0.005, min(0.05, dna.get('risk_per_trade', 0.02))),
                    'max_trades': max(1, min(10, int(dna.get('max_trades', 3))))
                },
                performance_metrics={}, # Initialize as empty dictionary
                status=StrategyStatus.CANDIDATE, # Default status
                creation_date=datetime.now(),
                last_updated=datetime.now(),
                market_regime=None, # Default to None
                confidence_score=0.0
            )
            return new_variant
            
        except Exception as e:
            logger.error(f"Error converting DNA to variant: {e}")
            return None

    def mutate_dna(self, dna: Dict[str, float]) -> Dict[str, float]:
        """Apply mutation to DNA with GPU-compatible operations"""
        try:
            mutated_dna = dna.copy()

            for key, value in mutated_dna.items():
                if np.random.random() < self.mutation_rate:
                    # Apply Gaussian mutation with parameter-specific bounds
                    if key in ['oversold_threshold', 'overbought_threshold']:
                        mutation_strength = 5.0
                    elif key in ['stop_loss', 'take_profit', 'risk_per_trade']:
                        mutation_strength = 0.005
                    elif key in ['max_position_size']:
                        mutation_strength = 0.02
                    elif key in ['rsi_period', 'max_trades']:
                        mutation_strength = 2.0
                    else:
                        mutation_strength = 0.1

                    # Apply mutation
                    mutation = np.random.normal(0, mutation_strength)
                    mutated_dna[key] = value + mutation

            return mutated_dna
            
        except Exception as e:
            logger.error(f"Error mutating DNA: {e}")
            return dna

    def crossover_dna(self, parent1_dna: Dict[str, float], parent2_dna: Dict[str, float]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Perform crossover between two DNA sequences"""
        try:
            child1_dna = {}
            child2_dna = {}

            for key in parent1_dna.keys():
                if np.random.random() < self.crossover_rate:
                    # Uniform crossover
                    if np.random.random() < 0.5:
                        child1_dna[key] = parent1_dna[key]
                        child2_dna[key] = parent2_dna[key]
                    else:
                        child1_dna[key] = parent2_dna[key]
                        child2_dna[key] = parent1_dna[key]
                else:
                    # No crossover - keep parent genes
                    child1_dna[key] = parent1_dna[key]
                    child2_dna[key] = parent2_dna[key]

            return child1_dna, child2_dna
            
        except Exception as e:
            logger.error(f"Error in crossover: {e}")
            return parent1_dna, parent2_dna

    def tournament_selection(self, population: List[StrategyVariant]) -> StrategyVariant:
        """Tournament selection for genetic algorithm"""
        try:
            if not population:
                raise ValueError("Population is empty")
                
            tournament = np.random.choice(population, size=min(self.tournament_size, len(population)), replace=False)
            return max(tournament, key=lambda x: x.ranking)
            
        except Exception as e:
            logger.error(f"Error in tournament selection: {e}")
            return population[0] if population else None

    def evolve_population(self, population: List[StrategyVariant]) -> List[StrategyVariant]:
        """Evolve a population using genetic algorithm operations"""
        try:
            if not population:
                logger.warning("Empty population provided for evolution")
                return []

            # Sort population by fitness (ranking)
            population.sort(key=lambda x: x.ranking, reverse=True)
            
            # Keep elite individuals
            elite = population[:self.elite_size]
            new_population = elite.copy()
            
            # Generate offspring through crossover and mutation
            while len(new_population) < self.population_size:
                # Select parents
                parent1 = self.tournament_selection(population)
                parent2 = self.tournament_selection(population)
                
                if parent1 and parent2:
                    # Extract DNA
                    parent1_dna = self.create_strategy_dna(parent1)
                    parent2_dna = self.create_strategy_dna(parent2)
                    
                    # Crossover
                    child1_dna, child2_dna = self.crossover_dna(parent1_dna, parent2_dna)
                    
                    # Mutation
                    child1_dna = self.mutate_dna(child1_dna)
                    child2_dna = self.mutate_dna(child2_dna)
                    
                    # Convert back to variants
                    child1 = self.dna_to_variant(parent1, child1_dna)
                    child2 = self.dna_to_variant(parent2, child2_dna)
                    
                    if child1:
                        new_population.append(child1)
                    if child2 and len(new_population) < self.population_size:
                        new_population.append(child2)
            
            logger.info(f"[GENETIC] Evolved population: {len(elite)} elite + {len(new_population) - len(elite)} offspring")
            return new_population[:self.population_size]
            
        except Exception as e:
            logger.error(f"Error evolving population: {e}")
            return population
