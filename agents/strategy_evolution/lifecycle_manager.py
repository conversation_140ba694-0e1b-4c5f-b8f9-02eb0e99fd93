#!/usr/bin/env python3
"""
Lifecycle Manager Module

This module handles strategy lifecycle management including:
- Strategy status determination and updates
- Performance history tracking
- Promotion/demotion logic
- A/B testing coordination
- Strategy retirement and archival
"""

import json
import sqlite3
from typing import Dict, List, Any
from datetime import datetime, timedelta

from agents.strategy_evolution.evolution_config import StrategyVariant, StrategyStatus, MarketRegime
from agents.strategy_evolution.evolution_logger import logger


class LifecycleManager:
    """
    Manages the lifecycle of strategy variants including status transitions and performance tracking
    """
    
    def __init__(self, evolution_config, database_path: str):
        self.evolution_config = evolution_config
        self.database_path = database_path
        
        # Status transition thresholds
        self.status_thresholds = {
            StrategyStatus.CHAMPION: 0.8,
            StrategyStatus.CHALLENGER: 0.6,
            StrategyStatus.TESTING: 0.4,
            StrategyStatus.CANDIDATE: 0.2,
            StrategyStatus.FAILED: 0.0
        }
        
        logger.info("[LIFECYCLE] Lifecycle Manager initialized")
    
    async def manage_strategy_lifecycle(self, variants: List[StrategyVariant]) -> List[StrategyVariant]:
        """
        Manage strategy lifecycle with promotion/demotion logic
        
        This addresses Enhancement Point #9: Strategy Lifecycle Management
        """
        try:
            logger.info("🔄 Managing strategy lifecycle")
            updated_variants = []

            for variant in variants:
                # Evaluate current performance (this would typically come from the evaluator)
                current_metrics = variant.performance_metrics or {}

                # Update performance history
                await self.save_performance_history(variant.strategy_id, current_metrics)

                # Determine status based on performance
                new_status = self.determine_strategy_status(variant, current_metrics)

                if new_status != variant.status:
                    logger.info(f"📈 Status change for {variant.strategy_id}: {variant.status.value} -> {new_status.value}")
                    variant.status = new_status
                    variant.last_updated = datetime.now()

                updated_variants.append(variant)

            logger.info("✅ Strategy lifecycle management complete")
            return updated_variants

        except Exception as e:
            logger.error(f"Error managing strategy lifecycle: {e}")
            return variants
    
    def determine_strategy_status(self, variant: StrategyVariant, current_metrics: Dict[str, float]) -> StrategyStatus:
        """Determine strategy status based on performance"""
        try:
            composite_score = current_metrics.get('composite_score', 0.0)

            # Status promotion/demotion logic
            if composite_score >= self.status_thresholds[StrategyStatus.CHAMPION]:
                return StrategyStatus.CHAMPION
            elif composite_score >= self.status_thresholds[StrategyStatus.CHALLENGER]:
                return StrategyStatus.CHALLENGER
            elif composite_score >= self.status_thresholds[StrategyStatus.TESTING]:
                return StrategyStatus.TESTING
            elif composite_score >= self.status_thresholds[StrategyStatus.CANDIDATE]:
                return StrategyStatus.CANDIDATE
            else:
                return StrategyStatus.FAILED

        except Exception as e:
            logger.error(f"Error determining strategy status: {e}")
            return StrategyStatus.CANDIDATE
    
    async def save_performance_history(self, strategy_id: str, metrics: Dict[str, float], market_regime: str = None):
        """Save performance history to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO performance_history (strategy_id, timestamp, metrics, market_regime)
                VALUES (?, ?, ?, ?)
            ''', (
                strategy_id,
                datetime.now().isoformat(),
                json.dumps(metrics),
                market_regime
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving performance history: {e}")
    
    async def get_performance_history(self, strategy_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get performance history for a strategy"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor.execute('''
                SELECT timestamp, metrics, market_regime 
                FROM performance_history 
                WHERE strategy_id = ? AND timestamp >= ?
                ORDER BY timestamp DESC
            ''', (strategy_id, cutoff_date))
            
            rows = cursor.fetchall()
            conn.close()
            
            history = []
            for row in rows:
                try:
                    history.append({
                        'timestamp': row[0],
                        'metrics': json.loads(row[1]),
                        'market_regime': row[2]
                    })
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing performance history metrics: {e}")
                    
            return history
            
        except Exception as e:
            logger.error(f"Error getting performance history: {e}")
            return []
    
    def calculate_performance_trend(self, history: List[Dict[str, Any]], metric: str = 'composite_score') -> float:
        """Calculate performance trend for a specific metric"""
        try:
            if len(history) < 2:
                return 0.0
                
            values = []
            for entry in history:
                metrics = entry.get('metrics', {})
                if metric in metrics:
                    values.append(metrics[metric])
            
            if len(values) < 2:
                return 0.0
                
            # Simple linear trend calculation
            n = len(values)
            x_sum = sum(range(n))
            y_sum = sum(values)
            xy_sum = sum(i * values[i] for i in range(n))
            x2_sum = sum(i * i for i in range(n))
            
            # Calculate slope (trend)
            denominator = n * x2_sum - x_sum * x_sum
            if denominator == 0:
                return 0.0
                
            slope = (n * xy_sum - x_sum * y_sum) / denominator
            return slope
            
        except Exception as e:
            logger.error(f"Error calculating performance trend: {e}")
            return 0.0
    
    async def identify_underperforming_strategies(self, variants: List[StrategyVariant], 
                                                threshold_days: int = 7) -> List[StrategyVariant]:
        """Identify strategies that have been underperforming"""
        try:
            underperforming = []
            
            for variant in variants:
                history = await self.get_performance_history(variant.strategy_id, threshold_days)
                
                if history:
                    trend = self.calculate_performance_trend(history)
                    avg_score = sum(h['metrics'].get('composite_score', 0) for h in history) / len(history)
                    
                    # Mark as underperforming if declining trend and low average score
                    if trend < -0.01 and avg_score < 0.3:
                        underperforming.append(variant)
                        logger.info(f"Identified underperforming strategy: {variant.strategy_id} (trend: {trend:.3f}, avg: {avg_score:.3f})")
            
            return underperforming
            
        except Exception as e:
            logger.error(f"Error identifying underperforming strategies: {e}")
            return []
    
    async def promote_strategy(self, variant: StrategyVariant, new_status: StrategyStatus, reason: str = ""):
        """Promote a strategy to a higher status"""
        try:
            old_status = variant.status
            variant.status = new_status
            variant.last_updated = datetime.now()
            
            # Log the promotion
            logger.info(f"📈 Strategy promoted: {variant.strategy_id} from {old_status.value} to {new_status.value}")
            if reason:
                logger.info(f"Promotion reason: {reason}")
                
            # Save promotion event to performance history
            promotion_metrics = {
                'event_type': 'promotion',
                'old_status': old_status.value,
                'new_status': new_status.value,
                'reason': reason
            }
            await self.save_performance_history(variant.strategy_id, promotion_metrics)
            
        except Exception as e:
            logger.error(f"Error promoting strategy: {e}")
    
    async def demote_strategy(self, variant: StrategyVariant, new_status: StrategyStatus, reason: str = ""):
        """Demote a strategy to a lower status"""
        try:
            old_status = variant.status
            variant.status = new_status
            variant.last_updated = datetime.now()
            
            # Log the demotion
            logger.info(f"📉 Strategy demoted: {variant.strategy_id} from {old_status.value} to {new_status.value}")
            if reason:
                logger.info(f"Demotion reason: {reason}")
                
            # Save demotion event to performance history
            demotion_metrics = {
                'event_type': 'demotion',
                'old_status': old_status.value,
                'new_status': new_status.value,
                'reason': reason
            }
            await self.save_performance_history(variant.strategy_id, demotion_metrics)
            
        except Exception as e:
            logger.error(f"Error demoting strategy: {e}")
    
    async def retire_strategy(self, variant: StrategyVariant, reason: str = ""):
        """Retire a strategy (mark as deprecated)"""
        try:
            old_status = variant.status
            variant.status = StrategyStatus.DEPRECATED
            variant.last_updated = datetime.now()
            
            # Log the retirement
            logger.info(f"🏁 Strategy retired: {variant.strategy_id} from {old_status.value}")
            if reason:
                logger.info(f"Retirement reason: {reason}")
                
            # Save retirement event to performance history
            retirement_metrics = {
                'event_type': 'retirement',
                'old_status': old_status.value,
                'new_status': StrategyStatus.DEPRECATED.value,
                'reason': reason
            }
            await self.save_performance_history(variant.strategy_id, retirement_metrics)
            
        except Exception as e:
            logger.error(f"Error retiring strategy: {e}")
    
    def get_status_distribution(self, variants: List[StrategyVariant]) -> Dict[str, int]:
        """Get distribution of strategy statuses"""
        try:
            distribution = {}
            for status in StrategyStatus:
                distribution[status.value] = 0
                
            for variant in variants:
                distribution[variant.status.value] += 1
                
            return distribution
            
        except Exception as e:
            logger.error(f"Error getting status distribution: {e}")
            return {}
    
    async def run_lifecycle_maintenance(self, variants: List[StrategyVariant]) -> List[StrategyVariant]:
        """Run periodic lifecycle maintenance tasks"""
        try:
            logger.info("🔧 Running lifecycle maintenance")
            
            # Identify underperforming strategies
            underperforming = await self.identify_underperforming_strategies(variants)
            
            # Demote underperforming strategies
            for variant in underperforming:
                if variant.status != StrategyStatus.FAILED:
                    await self.demote_strategy(variant, StrategyStatus.FAILED, "Consistent underperformance")
            
            # Get status distribution
            distribution = self.get_status_distribution(variants)
            logger.info(f"Status distribution: {distribution}")
            
            # Clean up old performance history (keep last 90 days)
            await self.cleanup_old_performance_history(90)
            
            logger.info("✅ Lifecycle maintenance completed")
            return variants
            
        except Exception as e:
            logger.error(f"Error in lifecycle maintenance: {e}")
            return variants
    
    async def cleanup_old_performance_history(self, days: int = 90):
        """Clean up old performance history records"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor.execute('DELETE FROM performance_history WHERE timestamp < ?', (cutoff_date,))
            deleted_count = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} old performance history records")
                
        except Exception as e:
            logger.error(f"Error cleaning up performance history: {e}")
