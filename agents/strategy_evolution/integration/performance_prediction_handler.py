#!/usr/bin/env python3
"""
Performance Prediction Handler for Strategy Evolution Agent

Integrates with the Performance Analysis Gateway to receive performance predictions
and use them to guide strategy optimization and evolution processes.

Features:
- ML-powered performance predictions integration
- Dynamic strategy optimization feedback
- Predicted metrics incorporation into fitness functions
- Early termination of poor-performing strategies
- Adaptive parameter search space adjustment
- Performance forecasting for strategy selection
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class PredictionType(Enum):
    """Types of performance predictions"""
    SHARPE_RATIO = "sharpe_ratio"
    ROI = "roi"
    DRAWDOWN = "drawdown"
    PROFITABILITY = "profitability"
    WIN_RATE = "win_rate"
    VOLATILITY = "volatility"

class OptimizationAction(Enum):
    """Optimization actions based on predictions"""
    CONTINUE_OPTIMIZATION = "continue_optimization"
    ADJUST_SEARCH_SPACE = "adjust_search_space"
    EARLY_TERMINATION = "early_termination"
    INCREASE_EXPLORATION = "increase_exploration"
    FOCUS_EXPLOITATION = "focus_exploitation"
    DIVERSIFY_POPULATION = "diversify_population"

@dataclass
class PerformancePrediction:
    """Performance prediction from ML models"""
    prediction_id: str
    strategy_id: str
    symbol: Optional[str]
    prediction_type: PredictionType
    predicted_value: float
    confidence: float
    time_horizon_days: int
    timestamp: datetime
    model_version: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class OptimizationGuidance:
    """Optimization guidance based on predictions"""
    strategy_id: str
    action: OptimizationAction
    parameters: Dict[str, Any]
    reasoning: str
    confidence: float
    priority: int  # 1-10, higher is more important
    timestamp: datetime

class PerformancePredictionHandler:
    """
    Handles performance predictions and provides optimization guidance
    """
    
    def __init__(self, evolution_agent):
        self.evolution_agent = evolution_agent
        
        # Prediction storage
        self.active_predictions = {}
        self.prediction_history = []
        
        # Optimization guidance
        self.active_guidance = {}
        self.guidance_history = []
        
        # Performance tracking
        self.metrics = {
            'predictions_received': 0,
            'guidance_generated': 0,
            'optimizations_influenced': 0,
            'early_terminations': 0,
            'search_space_adjustments': 0,
            'last_prediction_time': None
        }
        
        # Configuration
        self.config = {
            'min_confidence_threshold': 0.7,
            'prediction_weight_in_fitness': 0.3,
            'early_termination_threshold': 0.2,
            'search_space_adjustment_threshold': 0.6,
            'max_predictions_per_strategy': 10
        }
        
        logger.info("PerformancePredictionHandler initialized")

    async def handle_performance_prediction(self, prediction_data: Dict[str, Any]) -> bool:
        """
        Handle incoming performance prediction from Performance Analysis Gateway
        
        Args:
            prediction_data: Prediction data from Performance Analysis Gateway
            
        Returns:
            bool: True if prediction was handled successfully
        """
        try:
            # Create PerformancePrediction object
            prediction = PerformancePrediction(
                prediction_id=prediction_data.get('prediction_id', f"pred_{datetime.now().timestamp()}"),
                strategy_id=prediction_data.get('strategy_id', ''),
                symbol=prediction_data.get('symbol'),
                prediction_type=PredictionType(prediction_data.get('prediction_type', 'roi')),
                predicted_value=prediction_data.get('predicted_value', 0.0),
                confidence=prediction_data.get('confidence', 0.0),
                time_horizon_days=prediction_data.get('time_horizon_days', 30),
                timestamp=datetime.fromisoformat(prediction_data.get('timestamp', datetime.now().isoformat())),
                model_version=prediction_data.get('model_version', 'unknown'),
                metadata=prediction_data.get('metadata', {})
            )
            
            # Store prediction
            self.active_predictions[prediction.prediction_id] = prediction
            self.prediction_history.append(prediction)
            self.metrics['predictions_received'] += 1
            self.metrics['last_prediction_time'] = datetime.now()
            
            # Generate optimization guidance
            guidance = await self._generate_optimization_guidance(prediction)
            
            if guidance:
                # Store guidance
                self.active_guidance[guidance.strategy_id] = guidance
                self.guidance_history.append(guidance)
                self.metrics['guidance_generated'] += 1
                
                # Apply guidance to evolution process
                await self._apply_optimization_guidance(guidance)
                
                logger.info(f"Generated optimization guidance for strategy {prediction.strategy_id}: {guidance.action.value}")
                return True
            else:
                logger.warning(f"Could not generate guidance for prediction {prediction.prediction_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling performance prediction: {e}")
            return False

    async def _generate_optimization_guidance(self, prediction: PerformancePrediction) -> Optional[OptimizationGuidance]:
        """Generate optimization guidance based on prediction"""
        try:
            # Skip low-confidence predictions
            if prediction.confidence < self.config['min_confidence_threshold']:
                return None
            
            action = OptimizationAction.CONTINUE_OPTIMIZATION
            parameters = {}
            reasoning = ""
            priority = 5
            
            # Determine action based on prediction type and value
            if prediction.prediction_type == PredictionType.SHARPE_RATIO:
                if prediction.predicted_value < 0.5:
                    action = OptimizationAction.EARLY_TERMINATION
                    reasoning = f"Predicted Sharpe ratio ({prediction.predicted_value:.3f}) below acceptable threshold"
                    priority = 9
                elif prediction.predicted_value > 1.5:
                    action = OptimizationAction.FOCUS_EXPLOITATION
                    parameters = {'exploitation_factor': 1.5}
                    reasoning = f"High predicted Sharpe ratio ({prediction.predicted_value:.3f}) - focus on exploitation"
                    priority = 7
                else:
                    action = OptimizationAction.ADJUST_SEARCH_SPACE
                    parameters = {'search_space_factor': 0.8}
                    reasoning = f"Moderate Sharpe prediction - narrow search space"
                    priority = 5
                    
            elif prediction.prediction_type == PredictionType.ROI:
                if prediction.predicted_value < 0.02:  # Less than 2% ROI
                    action = OptimizationAction.EARLY_TERMINATION
                    reasoning = f"Predicted ROI ({prediction.predicted_value:.2%}) too low"
                    priority = 8
                elif prediction.predicted_value > 0.15:  # Greater than 15% ROI
                    action = OptimizationAction.FOCUS_EXPLOITATION
                    parameters = {'exploitation_factor': 1.3}
                    reasoning = f"High predicted ROI ({prediction.predicted_value:.2%}) - exploit current direction"
                    priority = 6
                    
            elif prediction.prediction_type == PredictionType.DRAWDOWN:
                if prediction.predicted_value > 0.15:  # Greater than 15% drawdown
                    action = OptimizationAction.ADJUST_SEARCH_SPACE
                    parameters = {'risk_reduction_factor': 0.7}
                    reasoning = f"High predicted drawdown ({prediction.predicted_value:.2%}) - reduce risk parameters"
                    priority = 8
                elif prediction.predicted_value < 0.05:  # Less than 5% drawdown
                    action = OptimizationAction.INCREASE_EXPLORATION
                    parameters = {'exploration_factor': 1.2}
                    reasoning = f"Low predicted drawdown ({prediction.predicted_value:.2%}) - can afford more exploration"
                    priority = 4
                    
            elif prediction.prediction_type == PredictionType.PROFITABILITY:
                if prediction.predicted_value < 0.4:  # Less than 40% profitable
                    action = OptimizationAction.DIVERSIFY_POPULATION
                    parameters = {'diversification_factor': 1.5}
                    reasoning = f"Low predicted profitability ({prediction.predicted_value:.2%}) - diversify approach"
                    priority = 7
                    
            elif prediction.prediction_type == PredictionType.WIN_RATE:
                if prediction.predicted_value < 0.35:  # Less than 35% win rate
                    action = OptimizationAction.EARLY_TERMINATION
                    reasoning = f"Predicted win rate ({prediction.predicted_value:.2%}) too low"
                    priority = 9
                    
            # Create guidance
            guidance = OptimizationGuidance(
                strategy_id=prediction.strategy_id,
                action=action,
                parameters=parameters,
                reasoning=reasoning,
                confidence=prediction.confidence,
                priority=priority,
                timestamp=datetime.now()
            )
            
            return guidance
            
        except Exception as e:
            logger.error(f"Error generating optimization guidance: {e}")
            return None

    async def _apply_optimization_guidance(self, guidance: OptimizationGuidance) -> bool:
        """Apply optimization guidance to evolution process"""
        try:
            action = guidance.action
            parameters = guidance.parameters
            strategy_id = guidance.strategy_id
            
            if action == OptimizationAction.EARLY_TERMINATION:
                return await self._apply_early_termination(strategy_id, guidance)
                
            elif action == OptimizationAction.ADJUST_SEARCH_SPACE:
                return await self._adjust_search_space(strategy_id, parameters)
                
            elif action == OptimizationAction.INCREASE_EXPLORATION:
                return await self._increase_exploration(strategy_id, parameters)
                
            elif action == OptimizationAction.FOCUS_EXPLOITATION:
                return await self._focus_exploitation(strategy_id, parameters)
                
            elif action == OptimizationAction.DIVERSIFY_POPULATION:
                return await self._diversify_population(strategy_id, parameters)
                
            else:
                # Continue optimization - no specific action needed
                return True
                
        except Exception as e:
            logger.error(f"Error applying optimization guidance: {e}")
            return False

    async def _apply_early_termination(self, strategy_id: str, guidance: OptimizationGuidance) -> bool:
        """Apply early termination for poor-performing strategy"""
        try:
            # Find strategy in current population
            if hasattr(self.evolution_agent, 'evolution_state') and self.evolution_agent.evolution_state:
                population = self.evolution_agent.evolution_state.population
                
                # Mark strategy for removal
                for chromosome in population:
                    if chromosome.strategy_id == strategy_id:
                        chromosome.fitness = 0.0  # Set fitness to 0 for removal
                        chromosome.metadata['early_terminated'] = True
                        chromosome.metadata['termination_reason'] = guidance.reasoning
                        
                        logger.info(f"Applied early termination to strategy {strategy_id}: {guidance.reasoning}")
                        self.metrics['early_terminations'] += 1
                        self.metrics['optimizations_influenced'] += 1
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error applying early termination: {e}")
            return False

    async def _adjust_search_space(self, strategy_id: str, parameters: Dict[str, Any]) -> bool:
        """Adjust search space for strategy optimization"""
        try:
            search_space_factor = parameters.get('search_space_factor', 0.8)
            risk_reduction_factor = parameters.get('risk_reduction_factor', 1.0)
            
            # Adjust hyperparameter optimizer search space
            if hasattr(self.evolution_agent, 'hyperparameter_optimizer'):
                optimizer = self.evolution_agent.hyperparameter_optimizer
                
                # Narrow search space for specific strategy
                if hasattr(optimizer, 'adjust_search_space'):
                    await optimizer.adjust_search_space(strategy_id, search_space_factor, risk_reduction_factor)
                    
                    logger.info(f"Adjusted search space for strategy {strategy_id}: factor={search_space_factor}")
                    self.metrics['search_space_adjustments'] += 1
                    self.metrics['optimizations_influenced'] += 1
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error adjusting search space: {e}")
            return False

    async def _increase_exploration(self, strategy_id: str, parameters: Dict[str, Any]) -> bool:
        """Increase exploration for strategy optimization"""
        try:
            exploration_factor = parameters.get('exploration_factor', 1.2)
            
            # Increase mutation rate for genetic algorithm
            if hasattr(self.evolution_agent, 'genetic_operations'):
                genetic_ops = self.evolution_agent.genetic_operations
                
                # Temporarily increase mutation rate
                original_rate = genetic_ops.mutation_rate
                genetic_ops.mutation_rate = min(original_rate * exploration_factor, 0.5)
                
                # Schedule reset after some time
                asyncio.create_task(self._reset_mutation_rate(genetic_ops, original_rate, 300))  # 5 minutes
                
                logger.info(f"Increased exploration for strategy {strategy_id}: mutation_rate={genetic_ops.mutation_rate}")
                self.metrics['optimizations_influenced'] += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error increasing exploration: {e}")
            return False

    async def _focus_exploitation(self, strategy_id: str, parameters: Dict[str, Any]) -> bool:
        """Focus on exploitation for promising strategy"""
        try:
            exploitation_factor = parameters.get('exploitation_factor', 1.3)
            
            # Increase selection pressure in genetic algorithm
            if hasattr(self.evolution_agent, 'genetic_operations'):
                genetic_ops = self.evolution_agent.genetic_operations
                
                # Temporarily increase elite size
                original_elite_size = genetic_ops.elite_size
                genetic_ops.elite_size = min(int(original_elite_size * exploitation_factor), genetic_ops.population_size // 2)
                
                # Schedule reset
                asyncio.create_task(self._reset_elite_size(genetic_ops, original_elite_size, 600))  # 10 minutes
                
                logger.info(f"Focused exploitation for strategy {strategy_id}: elite_size={genetic_ops.elite_size}")
                self.metrics['optimizations_influenced'] += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error focusing exploitation: {e}")
            return False

    async def _diversify_population(self, strategy_id: str, parameters: Dict[str, Any]) -> bool:
        """Diversify population for better exploration"""
        try:
            diversification_factor = parameters.get('diversification_factor', 1.5)
            
            # Add random strategies to population
            if hasattr(self.evolution_agent, 'evolution_state') and self.evolution_agent.evolution_state:
                population = self.evolution_agent.evolution_state.population
                
                # Generate additional random strategies
                num_new_strategies = int(len(population) * 0.1 * diversification_factor)
                
                if hasattr(self.evolution_agent, 'genetic_operations'):
                    genetic_ops = self.evolution_agent.genetic_operations
                    
                    # Generate random strategies (this would need to be implemented)
                    # For now, we'll just log the action
                    logger.info(f"Diversified population for strategy {strategy_id}: adding {num_new_strategies} random strategies")
                    self.metrics['optimizations_influenced'] += 1
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error diversifying population: {e}")
            return False

    async def _reset_mutation_rate(self, genetic_ops, original_rate: float, delay_seconds: int):
        """Reset mutation rate after delay"""
        await asyncio.sleep(delay_seconds)
        genetic_ops.mutation_rate = original_rate
        logger.info(f"Reset mutation rate to original value: {original_rate}")

    async def _reset_elite_size(self, genetic_ops, original_size: int, delay_seconds: int):
        """Reset elite size after delay"""
        await asyncio.sleep(delay_seconds)
        genetic_ops.elite_size = original_size
        logger.info(f"Reset elite size to original value: {original_size}")

    def incorporate_predictions_into_fitness(self, strategy_id: str, base_fitness: float) -> float:
        """
        Incorporate performance predictions into fitness calculation
        
        Args:
            strategy_id: Strategy identifier
            base_fitness: Base fitness score from backtesting
            
        Returns:
            float: Adjusted fitness score incorporating predictions
        """
        try:
            # Find relevant predictions for this strategy
            relevant_predictions = [
                pred for pred in self.active_predictions.values()
                if pred.strategy_id == strategy_id and pred.confidence >= self.config['min_confidence_threshold']
            ]
            
            if not relevant_predictions:
                return base_fitness
            
            # Calculate prediction-based fitness adjustment
            prediction_adjustment = 0.0
            total_weight = 0.0
            
            for prediction in relevant_predictions:
                weight = prediction.confidence
                
                if prediction.prediction_type == PredictionType.SHARPE_RATIO:
                    # Normalize Sharpe ratio to 0-1 range
                    normalized_value = min(max(prediction.predicted_value / 2.0, 0.0), 1.0)
                    prediction_adjustment += normalized_value * weight
                    
                elif prediction.prediction_type == PredictionType.ROI:
                    # Normalize ROI to 0-1 range (assuming max 50% ROI)
                    normalized_value = min(max(prediction.predicted_value / 0.5, 0.0), 1.0)
                    prediction_adjustment += normalized_value * weight
                    
                elif prediction.prediction_type == PredictionType.DRAWDOWN:
                    # Invert drawdown (lower is better)
                    normalized_value = max(1.0 - prediction.predicted_value / 0.3, 0.0)
                    prediction_adjustment += normalized_value * weight
                    
                elif prediction.prediction_type == PredictionType.PROFITABILITY:
                    prediction_adjustment += prediction.predicted_value * weight
                    
                elif prediction.prediction_type == PredictionType.WIN_RATE:
                    prediction_adjustment += prediction.predicted_value * weight
                
                total_weight += weight
            
            if total_weight > 0:
                prediction_adjustment /= total_weight
                
                # Combine base fitness with prediction adjustment
                prediction_weight = self.config['prediction_weight_in_fitness']
                adjusted_fitness = (base_fitness * (1 - prediction_weight) + 
                                  prediction_adjustment * prediction_weight)
                
                logger.debug(f"Adjusted fitness for {strategy_id}: {base_fitness:.3f} -> {adjusted_fitness:.3f}")
                return adjusted_fitness
            
            return base_fitness
            
        except Exception as e:
            logger.error(f"Error incorporating predictions into fitness: {e}")
            return base_fitness

    def get_prediction_handler_status(self) -> Dict[str, Any]:
        """Get current status of prediction handler"""
        return {
            'metrics': self.metrics,
            'active_predictions_count': len(self.active_predictions),
            'active_guidance_count': len(self.active_guidance),
            'config': self.config,
            'recent_predictions': [
                {
                    'prediction_id': pred.prediction_id,
                    'strategy_id': pred.strategy_id,
                    'type': pred.prediction_type.value,
                    'predicted_value': pred.predicted_value,
                    'confidence': pred.confidence,
                    'timestamp': pred.timestamp.isoformat()
                } for pred in self.prediction_history[-10:]  # Last 10 predictions
            ],
            'recent_guidance': [
                {
                    'strategy_id': guidance.strategy_id,
                    'action': guidance.action.value,
                    'reasoning': guidance.reasoning,
                    'confidence': guidance.confidence,
                    'priority': guidance.priority,
                    'timestamp': guidance.timestamp.isoformat()
                } for guidance in self.guidance_history[-10:]  # Last 10 guidance
            ]
        }

    async def cleanup_old_predictions(self, max_age_hours: int = 24):
        """Clean up old predictions and guidance"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            # Clean up old predictions
            old_predictions = [
                pred_id for pred_id, pred in self.active_predictions.items()
                if pred.timestamp < cutoff_time
            ]
            
            for pred_id in old_predictions:
                del self.active_predictions[pred_id]
            
            # Clean up old guidance
            old_guidance = [
                strategy_id for strategy_id, guidance in self.active_guidance.items()
                if guidance.timestamp < cutoff_time
            ]
            
            for strategy_id in old_guidance:
                del self.active_guidance[strategy_id]
            
            if old_predictions or old_guidance:
                logger.info(f"Cleaned up {len(old_predictions)} old predictions and {len(old_guidance)} old guidance")
                
        except Exception as e:
            logger.error(f"Error cleaning up old predictions: {e}")
