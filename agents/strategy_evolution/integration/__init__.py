#!/usr/bin/env python3
"""
Strategy Evolution Integration Module

Handles integration with other agents and external systems:
- Performance Analysis Gateway integration
- ML-powered performance predictions
- Dynamic optimization feedback
- Risk Management Agent communication
- AI Training Agent model updates
"""

from .performance_prediction_handler import (
    PerformancePredictionHandler,
    PerformancePrediction,
    OptimizationGuidance,
    PredictionType,
    OptimizationAction
)

__all__ = [
    'PerformancePredictionHandler',
    'PerformancePrediction',
    'OptimizationGuidance',
    'PredictionType',
    'OptimizationAction'
]
