#!/usr/bin/env python3
"""
Performance Evaluation for Strategy Evolution Agent

This module contains performance evaluation, fitness calculation,
and performance tracking functionality.
"""

import asyncio
import random
from typing import Dict, List, Optional, Any, Tuple

from .data_types import (
    StrategyChromosome, PerformanceMetrics, StrategyGene, MarketRegime, EvolutionConfig
)
from .lazy_imports import log_critical


class PerformanceEvaluator:
    """Handles performance evaluation and fitness calculation for strategies"""
    
    def __init__(self, evolution_config: EvolutionConfig, config: Dict[str, Any]):
        self.evolution_config = evolution_config
        self.config = config
        self.strategy_performance_history: Dict[str, List[PerformanceMetrics]] = {}
        self.comm_interface = None
        self.performance_agent = None
    
    def set_communication_interface(self, comm_interface):
        """Set the communication interface"""
        self.comm_interface = comm_interface
    
    def set_performance_agent(self, performance_agent):
        """Set the performance agent"""
        self.performance_agent = performance_agent
    
    async def evaluate_population_fitness(self, population: List[StrategyChromosome]):
        """Evaluate fitness for all strategies in population"""
        try:
            log_critical(f"Evaluating fitness for {len(population)} strategies")

            # Batch evaluate strategies for efficiency
            evaluation_tasks = []

            for chromosome in population:
                task = self._evaluate_strategy_fitness(chromosome)
                evaluation_tasks.append(task)

            # Execute evaluations concurrently
            results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)

            # Update fitness scores
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    log_critical(f"Fitness evaluation failed for strategy {population[i].strategy_name}: {result}")
                    population[i].fitness_score = 0.0  # Assign poor fitness
                else:
                    population[i].fitness_score = result

            log_critical("Population fitness evaluation completed")

        except Exception as e:
            log_critical(f"Error evaluating population fitness: {e}")
            raise

    async def _evaluate_strategy_fitness(self, chromosome: StrategyChromosome) -> float:
        """Evaluate fitness for a single strategy"""
        try:
            # Get performance metrics from Performance Analysis Agent
            if self.performance_agent:
                performance_data = await self._get_strategy_performance_data(chromosome)

                if performance_data:
                    # Create PerformanceMetrics object
                    metrics = self._create_performance_metrics(chromosome.strategy_id, performance_data)

                    # Calculate fitness score
                    fitness_weights = self.config.get('performance', {}).get('fitness_weights', {})
                    fitness_score = metrics.calculate_fitness_score(fitness_weights)

                    # Store performance history
                    if chromosome.strategy_id not in self.strategy_performance_history:
                        self.strategy_performance_history[chromosome.strategy_id] = []

                    self.strategy_performance_history[chromosome.strategy_id].append(metrics)

                    return fitness_score

            # Fallback: simulate performance if no real data available
            return await self._simulate_strategy_performance(chromosome)

        except Exception as e:
            log_critical(f"Error evaluating strategy fitness for {chromosome.strategy_name}: {e}")
            return 0.0

    async def _get_strategy_performance_data(self, chromosome: StrategyChromosome) -> Optional[Dict[str, Any]]:
        """Get performance data for a strategy from Performance Analysis Agent"""
        try:
            if not self.performance_agent or not self.comm_interface:
                return None

            # Extract target stock and timeframe for focused testing
            target_stock = chromosome.genes.get('target_stock')
            target_timeframe = chromosome.genes.get('target_timeframe')

            # Only test on specific stock and timeframe if available
            test_parameters = {
                "strategy_name": chromosome.strategy_name,
                "strategy_id": chromosome.strategy_id,
                "period_days": self.evolution_config.performance_evaluation_period
            }

            # Add stock and timeframe constraints
            if target_stock and target_stock.value:
                test_parameters["target_stock"] = target_stock.value
                log_critical(f"Testing {chromosome.strategy_name} on specific stock: {target_stock.value}")

            if target_timeframe and target_timeframe.value:
                test_parameters["target_timeframe"] = target_timeframe.value
                log_critical(f"Testing {chromosome.strategy_name} on specific timeframe: {target_timeframe.value}")

            # Query performance data with constraints
            performance_data = await self.comm_interface.query_agent(
                agent_name="performance_analysis_agent",
                method="get_strategy_performance",
                params=test_parameters
            )

            return performance_data

        except Exception as e:
            log_critical(f"Error getting performance data: {e}")
            return None

    def _create_performance_metrics(self, strategy_id: str, performance_data: Dict[str, Any]) -> PerformanceMetrics:
        """Create PerformanceMetrics object from performance data"""
        return PerformanceMetrics(
            strategy_id=strategy_id,
            roi=performance_data.get('roi', 0.0),
            sharpe_ratio=performance_data.get('sharpe_ratio', 0.0),
            max_drawdown=performance_data.get('max_drawdown', 0.0),
            profit_factor=performance_data.get('profit_factor', 1.0),
            win_rate=performance_data.get('win_rate', 0.0),
            expectancy=performance_data.get('expectancy', 0.0),
            total_trades=performance_data.get('total_trades', 0),
            avg_holding_period=performance_data.get('avg_holding_period', 0.0),
            volatility=performance_data.get('volatility', 0.0),
            calmar_ratio=performance_data.get('calmar_ratio', 0.0),
            sortino_ratio=performance_data.get('sortino_ratio', 0.0),
            bull_market_performance=performance_data.get('bull_market_performance', 0.0),
            bear_market_performance=performance_data.get('bear_market_performance', 0.0),
            sideways_market_performance=performance_data.get('sideways_market_performance', 0.0),
            evaluation_period_days=self.evolution_config.performance_evaluation_period
        )

    async def _simulate_strategy_performance(self, chromosome: StrategyChromosome) -> float:
        """Simulate strategy performance for fitness evaluation"""
        try:
            # This is a simplified simulation for demonstration
            # In practice, this would run a backtest or use historical data

            # Extract key parameters for simulation
            genes = chromosome.genes

            # Base fitness calculation based on gene values
            fitness = 0.0

            # Risk-adjusted scoring
            stop_loss = genes.get('stop_loss_pct', StrategyGene('', 0.02)).value
            take_profit = genes.get('take_profit_pct', StrategyGene('', 0.04)).value
            position_size = genes.get('position_size_pct', StrategyGene('', 0.05)).value

            # Risk-reward ratio scoring
            rr_ratio = take_profit / stop_loss if stop_loss > 0 else 1.0
            fitness += min(rr_ratio * 0.1, 0.3)  # Cap at 0.3

            # Position sizing scoring (prefer moderate sizes)
            if 0.02 <= position_size <= 0.08:
                fitness += 0.2
            else:
                fitness += 0.1

            # Parameter balance scoring
            if 'rsi_period' in genes:
                rsi_period = genes['rsi_period'].value
                if 10 <= rsi_period <= 20:
                    fitness += 0.1

            if 'volume_multiplier' in genes:
                vol_mult = genes['volume_multiplier'].value
                if 1.2 <= vol_mult <= 2.0:
                    fitness += 0.1

            # Add some randomness to simulate market uncertainty
            market_noise = random.gauss(0, 0.1)
            fitness += market_noise

            # Ensure non-negative fitness
            fitness = max(0.0, fitness)

            return fitness

        except Exception as e:
            log_critical(f"Error simulating strategy performance: {e}")
            return 0.0

    async def track_strategy_performance_trends(self, active_strategies: Dict[str, StrategyChromosome]):
        """Track performance trends for all strategies"""
        try:
            improving_strategies = []
            degrading_strategies = []

            for strategy_id, performance_history in self.strategy_performance_history.items():
                if len(performance_history) >= 2:
                    # Calculate trend
                    recent_performance = performance_history[-3:]  # Last 3 evaluations

                    if len(recent_performance) >= 2:
                        trend = self._calculate_performance_trend(recent_performance)

                        if trend > 0.05:  # 5% improvement threshold
                            improving_strategies.append((strategy_id, trend))
                        elif trend < -0.05:  # 5% degradation threshold
                            degrading_strategies.append((strategy_id, trend))

            # Log trends
            if improving_strategies:
                log_critical(f"Improving strategies: {len(improving_strategies)}")
                for strategy_id, trend in improving_strategies[:5]:  # Top 5
                    strategy_name = active_strategies.get(strategy_id, {}).strategy_name
                    log_critical(f"  {strategy_name}: +{trend:.2%}")

            if degrading_strategies:
                log_critical(f"Degrading strategies: {len(degrading_strategies)}")
                for strategy_id, trend in degrading_strategies[:5]:  # Bottom 5
                    strategy_name = active_strategies.get(strategy_id, {}).strategy_name
                    log_critical(f"  {strategy_name}: {trend:.2%}")

            # Mark strategies for potential removal
            await self._mark_underperforming_strategies(degrading_strategies, active_strategies)

        except Exception as e:
            log_critical(f"Error tracking performance trends: {e}")

    def _calculate_performance_trend(self, performance_history: List[PerformanceMetrics]) -> float:
        """Calculate performance trend from history"""
        if len(performance_history) < 2:
            return 0.0

        # Use fitness scores for trend calculation
        fitness_scores = [metrics.calculate_fitness_score() for metrics in performance_history]

        # Simple linear trend calculation
        x = list(range(len(fitness_scores)))
        y = fitness_scores

        # Calculate slope
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))

        if n * sum_x2 - sum_x ** 2 == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)

        return slope

    async def _mark_underperforming_strategies(self, degrading_strategies: List[Tuple[str, float]], 
                                            active_strategies: Dict[str, StrategyChromosome]):
        """Mark underperforming strategies for removal or modification"""
        try:
            removal_threshold = -0.15  # 15% degradation

            for strategy_id, trend in degrading_strategies:
                if trend < removal_threshold:
                    # Mark for removal
                    if strategy_id in active_strategies:
                        strategy = active_strategies[strategy_id]
                        log_critical(f"Marking strategy for removal: {strategy.strategy_name} (trend: {trend:.2%})")

                        # Could implement strategy retirement logic here
                        # For now, just log the decision

        except Exception as e:
            log_critical(f"Error marking underperforming strategies: {e}")

    def get_strategy_performance_history(self, strategy_id: str) -> List[PerformanceMetrics]:
        """Get performance history for a strategy"""
        return self.strategy_performance_history.get(strategy_id, [])

    def clear_performance_history(self, strategy_id: str):
        """Clear performance history for a strategy"""
        if strategy_id in self.strategy_performance_history:
            del self.strategy_performance_history[strategy_id]
