# Strategy Evolution Modules

This directory contains the modularized components of the Enhanced Strategy Evolution Agent. The original monolithic file has been broken down into focused, reusable modules.

## Module Overview

### 1. `evolution_config.py`
**Purpose**: Configuration and data structures
- `EvolutionConfig`: Main configuration class
- `StrategyVariant`: Strategy variant data structure
- `StrategyStatus`: Lifecycle status enumeration
- `MarketRegime`: Market condition enumeration
- `OptimizationObjective`: Multi-objective optimization targets

### 2. `evolution_logger.py`
**Purpose**: Structured logging and monitoring
- Centralized logging configuration
- Progress tracking utilities
- Performance monitoring helpers

### 3. `strategy_database.py`
**Purpose**: Database operations and storage
- SQLite database management
- Strategy variant persistence
- Performance history storage
- Database schema management

### 4. `strategy_evaluator.py`
**Purpose**: Strategy fitness evaluation
- Backtesting integration
- Performance metric calculation
- GPU-accelerated evaluation
- Batch processing capabilities

### 5. `strategy_data_loader.py`
**Purpose**: Data loading and preprocessing
- Market data loading
- Feature extraction
- Stock universe discovery
- Data validation and cleaning

### 6. `strategy_optimizer.py`
**Purpose**: Strategy parameter optimization
- Multi-objective optimization
- Hyperparameter tuning
- Optuna integration
- GPU-accelerated optimization

### 7. `genetic_algorithm.py` *(NEW)*
**Purpose**: Genetic algorithm operations
- DNA extraction from strategy variants
- Mutation and crossover operations
- Tournament selection
- Population evolution management

### 8. `strategy_variant_manager.py` *(NEW)*
**Purpose**: Strategy variant creation and management
- Variant creation from different sources (GPU results, trials, etc.)
- Format conversions (backtesting format, YAML format)
- Parameter space definition
- Variant validation and processing

### 9. `yaml_manager.py` *(NEW)*
**Purpose**: YAML file operations
- Configuration loading and saving
- Strategies.yaml management
- Backup creation with rotation
- YAML structure validation

### 10. `lifecycle_manager.py` *(NEW)*
**Purpose**: Strategy lifecycle management
- Status determination and transitions
- Performance history tracking
- Promotion/demotion logic
- Strategy retirement and archival

### 11. `gpu_processing_manager.py` *(NEW)*
**Purpose**: GPU-accelerated processing
- GPU availability checking
- Batch processing coordination
- GPU memory management
- Parallel task execution
- CPU fallback processing

### 12. `performance_tracker.py` *(NEW)*
**Purpose**: Performance tracking and statistics
- Evolution statistics tracking
- Progress monitoring and reporting
- Performance summaries and trends
- Real-time metrics updates

## Key Benefits of Modularization

### 1. **Separation of Concerns**
Each module has a single, well-defined responsibility, making the code easier to understand and maintain.

### 2. **Reusability**
Modules can be used independently or combined in different ways for various use cases.

### 3. **Testability**
Individual modules can be unit tested in isolation, improving code quality and reliability.

### 4. **Maintainability**
Changes to one module don't affect others, reducing the risk of introducing bugs.

### 5. **Scalability**
New features can be added by creating new modules or extending existing ones without modifying the core logic.

### 6. **Real Data Focus**
All modules have been cleaned of demo, synthetic, and mock data. They now use real market data or throw errors when data is not found.

## Usage Example

```python
from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent

# Initialize the agent (all modules are automatically loaded)
agent = EnhancedStrategyEvolutionAgent()

# Access individual modules
genetic_ops = agent.genetic_algorithm
variant_mgr = agent.variant_manager
yaml_mgr = agent.yaml_manager
lifecycle_mgr = agent.lifecycle_manager
gpu_mgr = agent.gpu_manager
perf_tracker = agent.performance_tracker

# Run evolution
await agent.enhance_strategies_yaml()
```

## Module Dependencies

```
enhanced_strategy_evolution_agent.py
├── genetic_algorithm.py
├── strategy_variant_manager.py
├── yaml_manager.py
├── lifecycle_manager.py
├── gpu_processing_manager.py
├── performance_tracker.py
├── evolution_config.py (shared by all)
├── evolution_logger.py (shared by all)
├── strategy_database.py
├── strategy_evaluator.py
├── strategy_data_loader.py
└── strategy_optimizer.py
```

## Testing

Run the test script to verify all modules work correctly:

```bash
python test_modular_evolution.py
```

## Migration Notes

The main `enhanced_strategy_evolution_agent.py` file now acts as a coordinator that:
1. Initializes all modular components
2. Delegates operations to appropriate modules
3. Maintains backward compatibility with existing interfaces
4. Coordinates data flow between modules

All original functionality is preserved while gaining the benefits of modular architecture.
