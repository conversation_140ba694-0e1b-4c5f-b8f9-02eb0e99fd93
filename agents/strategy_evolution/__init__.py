#!/usr/bin/env python3
"""
Strategy Evolution Package

This package contains modularized components for the Strategy Evolution Agent.
"""

from .data_types import (
    EvolutionMethod, MarketRegime, StrategyStatus, OptimizationMethod,
    TimeWindow, MutationType, StrategyGene, HyperparameterSpace,
    OptimizationTarget, StrategyRule, StrategyTemplate, StrategyChromosome,
    PerformanceMetrics, EvolutionConfig, EvolutionState
)

from .lazy_imports import (
    Lazy<PERSON>oader, log_critical, lazy_import_optuna, lazy_import_polars_talib,
    lazy_import_mintalib, lazy_import_gplearn, lazy_import_agents
)

from .genetic_operations import GeneticOperations
from .hyperparameter_optimizer import HyperparameterOptimizer
from .performance_evaluator import PerformanceEvaluator
from .strategy_manager import StrategyManager
from .backtesting_integration import BacktestingIntegration

__all__ = [
    # Data types
    'EvolutionMethod', 'MarketRegime', 'StrategyStatus', 'OptimizationMethod',
    'TimeWindow', 'MutationType', 'StrategyGene', 'HyperparameterSpace',
    'OptimizationTarget', 'StrategyRule', 'StrategyTemplate', 'StrategyChromosome',
    'PerformanceMetrics', 'EvolutionConfig', 'EvolutionState',
    
    # Utilities
    'LazyLoader', 'log_critical', 'lazy_import_optuna', 'lazy_import_polars_talib',
    'lazy_import_mintalib', 'lazy_import_gplearn', 'lazy_import_agents',
    
    # Core components
    'GeneticOperations', 'HyperparameterOptimizer', 'PerformanceEvaluator',
    'StrategyManager', 'BacktestingIntegration'
]
