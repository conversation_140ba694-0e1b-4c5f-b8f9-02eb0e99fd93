#!/usr/bin/env python3
"""
Backtesting Integration for Strategy Evolution Agent

This module handles integration with backtesting systems and
time-window optimization for strategies.
"""

import copy
import random
import uuid
from typing import Dict, List, Optional, Any

from .data_types import (
    StrategyChromosome, TimeWindow, EvolutionConfig
)
from .lazy_imports import log_critical, lazy_import_agents


class BacktestingIntegration:
    """Handles backtesting integration and time-window optimization"""
    
    def __init__(self, evolution_config: EvolutionConfig):
        self.evolution_config = evolution_config
        self.run_backtesting_for_evolution = None
        self._initialize_backtesting()
    
    def _initialize_backtesting(self):
        """Initialize backtesting function"""
        try:
            _, _, _, run_backtesting_for_evolution = lazy_import_agents()
            self.run_backtesting_for_evolution = run_backtesting_for_evolution
        except Exception as e:
            log_critical(f"Failed to initialize backtesting: {e}")
    
    async def optimize_time_windows(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Optimize strategy for different time windows"""
        try:
            if not self.evolution_config.enable_time_aware_optimization:
                return chromosome

            best_chromosome = chromosome

            # Test strategy performance in different time windows
            for time_window in self.evolution_config.time_windows:
                # Create time-specific variant
                time_chromosome = await self._adapt_for_time_window(chromosome, time_window)

                # Evaluate performance for this time window
                performance = await self._evaluate_time_window_performance(time_chromosome, time_window)

                # Store time window performance
                time_chromosome.time_window_performance[time_window.value] = performance

                # Update best if this performs better
                if performance > best_chromosome.time_window_performance.get(time_window.value, 0):
                    best_chromosome = time_chromosome

            return best_chromosome

        except Exception as e:
            log_critical(f"Error optimizing time windows: {e}")
            return chromosome

    async def _adapt_for_time_window(self, chromosome: StrategyChromosome,
                                   time_window: TimeWindow) -> StrategyChromosome:
        """Adapt strategy for specific time window"""
        adapted_chromosome = copy.deepcopy(chromosome)
        adapted_chromosome.strategy_id = str(uuid.uuid4())
        adapted_chromosome.strategy_name = f"{chromosome.strategy_name}_{time_window.value.upper()}"

        # Time-specific adaptations
        if time_window == TimeWindow.MORNING:
            # Morning: Higher volatility, wider stops
            if 'stop_loss_pct' in adapted_chromosome.genes:
                current_sl = adapted_chromosome.genes['stop_loss_pct'].value
                adapted_chromosome.genes['stop_loss_pct'].value = current_sl * 1.2

            if 'volume_multiplier' in adapted_chromosome.genes:
                adapted_chromosome.genes['volume_multiplier'].value = 2.0  # Higher volume threshold

        elif time_window == TimeWindow.MIDDAY:
            # Midday: Lower volatility, tighter parameters
            if 'stop_loss_pct' in adapted_chromosome.genes:
                current_sl = adapted_chromosome.genes['stop_loss_pct'].value
                adapted_chromosome.genes['stop_loss_pct'].value = current_sl * 0.8

            if 'rsi_period' in adapted_chromosome.genes:
                adapted_chromosome.genes['rsi_period'].value = 21  # Longer period for stability

        elif time_window == TimeWindow.AFTERNOON:
            # Afternoon: Pre-close activity, momentum focus
            if 'ema_fast' in adapted_chromosome.genes:
                adapted_chromosome.genes['ema_fast'].value = 3  # Faster EMA for momentum

            if 'take_profit_pct' in adapted_chromosome.genes:
                current_tp = adapted_chromosome.genes['take_profit_pct'].value
                adapted_chromosome.genes['take_profit_pct'].value = current_tp * 1.1  # Slightly higher targets

        return adapted_chromosome

    async def _evaluate_time_window_performance(self, chromosome: StrategyChromosome,
                                              time_window: TimeWindow) -> float:
        """Evaluate strategy performance for specific time window"""
        try:
            # If backtesting is available, use it
            if self.run_backtesting_for_evolution:
                return await self._run_time_window_backtest(chromosome, time_window)
            
            # Otherwise, simulate time-window specific performance
            base_performance = self._simulate_chromosome_performance(chromosome)

            # Time window adjustments
            time_multipliers = {
                TimeWindow.MORNING: 1.1,    # Higher volatility = higher potential
                TimeWindow.MIDDAY: 0.9,     # Lower volatility = lower potential
                TimeWindow.AFTERNOON: 1.05, # Moderate activity
                TimeWindow.FULL_DAY: 1.0    # Baseline
            }

            multiplier = time_multipliers.get(time_window, 1.0)
            adjusted_performance = base_performance * multiplier

            # Add time-specific randomness
            time_noise = random.gauss(0, 0.02)  # 2% noise
            adjusted_performance += time_noise

            return max(0.0, adjusted_performance)

        except Exception as e:
            log_critical(f"Error evaluating time window performance: {e}")
            return 0.0

    async def _run_time_window_backtest(self, chromosome: StrategyChromosome, 
                                      time_window: TimeWindow) -> float:
        """Run backtesting for specific time window"""
        try:
            if not self.run_backtesting_for_evolution:
                return 0.0

            # Convert chromosome to strategy format for backtesting
            strategy_config = self._chromosome_to_backtest_config(chromosome, time_window)

            # Run backtesting
            results = await self.run_backtesting_for_evolution(
                strategy_config=strategy_config,
                time_window=time_window.value,
                evaluation_period=self.evolution_config.performance_evaluation_period
            )

            if results and 'fitness_score' in results:
                return results['fitness_score']
            
            return 0.0

        except Exception as e:
            log_critical(f"Error running time window backtest: {e}")
            return 0.0

    def _chromosome_to_backtest_config(self, chromosome: StrategyChromosome, 
                                     time_window: TimeWindow) -> Dict[str, Any]:
        """Convert chromosome to backtesting configuration"""
        try:
            config = {
                'strategy_id': chromosome.strategy_id,
                'strategy_name': chromosome.strategy_name,
                'time_window': time_window.value,
                'parameters': {}
            }

            # Extract parameters from genes
            for gene_name, gene in chromosome.genes.items():
                config['parameters'][gene_name] = gene.value

            # Add time window specific constraints
            if time_window == TimeWindow.MORNING:
                config['time_constraints'] = {
                    'start_time': '09:15',
                    'end_time': '11:00'
                }
            elif time_window == TimeWindow.MIDDAY:
                config['time_constraints'] = {
                    'start_time': '11:00',
                    'end_time': '13:30'
                }
            elif time_window == TimeWindow.AFTERNOON:
                config['time_constraints'] = {
                    'start_time': '13:30',
                    'end_time': '15:25'
                }

            return config

        except Exception as e:
            log_critical(f"Error converting chromosome to backtest config: {e}")
            return {}

    def _simulate_chromosome_performance(self, chromosome: StrategyChromosome) -> float:
        """Simulate chromosome performance (fallback when no backtesting available)"""
        try:
            # Extract key parameters for simulation
            genes = chromosome.genes

            # Base fitness calculation based on gene values
            fitness = 0.0

            # Risk-adjusted scoring
            stop_loss = genes.get('stop_loss_pct', type('obj', (object,), {'value': 0.02})).value
            take_profit = genes.get('take_profit_pct', type('obj', (object,), {'value': 0.04})).value
            position_size = genes.get('position_size_pct', type('obj', (object,), {'value': 0.05})).value

            # Risk-reward ratio scoring
            rr_ratio = take_profit / stop_loss if stop_loss > 0 else 1.0
            fitness += min(rr_ratio * 0.1, 0.3)  # Cap at 0.3

            # Position sizing scoring (prefer moderate sizes)
            if 0.02 <= position_size <= 0.08:
                fitness += 0.2
            else:
                fitness += 0.1

            # Parameter balance scoring
            if 'rsi_period' in genes:
                rsi_period = genes['rsi_period'].value
                if 10 <= rsi_period <= 20:
                    fitness += 0.1

            if 'volume_multiplier' in genes:
                vol_mult = genes['volume_multiplier'].value
                if 1.2 <= vol_mult <= 2.0:
                    fitness += 0.1

            # Add some randomness to simulate market uncertainty
            market_noise = random.gauss(0, 0.1)
            fitness += market_noise

            # Ensure non-negative fitness
            fitness = max(0.0, fitness)

            return fitness

        except Exception as e:
            log_critical(f"Error simulating chromosome performance: {e}")
            return 0.0

    async def run_strategy_backtest(self, chromosome: StrategyChromosome, 
                                  symbols: List[str] = None,
                                  timeframe: str = None) -> Dict[str, Any]:
        """Run full backtesting for a strategy"""
        try:
            if not self.run_backtesting_for_evolution:
                return {}

            # Convert chromosome to strategy format
            strategy_config = {
                'strategy_id': chromosome.strategy_id,
                'strategy_name': chromosome.strategy_name,
                'parameters': {gene_name: gene.value for gene_name, gene in chromosome.genes.items()}
            }

            # Add symbol and timeframe constraints if provided
            if symbols:
                strategy_config['symbols'] = symbols
            if timeframe:
                strategy_config['timeframe'] = timeframe

            # Run backtesting
            results = await self.run_backtesting_for_evolution(
                strategy_config=strategy_config,
                evaluation_period=self.evolution_config.performance_evaluation_period
            )

            return results or {}

        except Exception as e:
            log_critical(f"Error running strategy backtest: {e}")
            return {}

    def is_backtesting_available(self) -> bool:
        """Check if backtesting is available"""
        return self.run_backtesting_for_evolution is not None
