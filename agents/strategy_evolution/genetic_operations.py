#!/usr/bin/env python3
"""
Genetic Algorithm Operations for Strategy Evolution Agent

This module contains all genetic algorithm operations including mutation,
crossover, selection, and related evolutionary operations.
"""

import copy
import random
import re
import uuid
from datetime import datetime
from typing import List, Tuple, Dict, Any

from .data_types import (
    StrategyChromosome, StrategyRule, MutationType, TimeWindow,
    EvolutionConfig, EvolutionState
)
from .lazy_imports import log_critical


class GeneticOperations:
    """Handles all genetic algorithm operations"""
    
    def __init__(self, evolution_config: EvolutionConfig, generation_counter: int = 0):
        self.evolution_config = evolution_config
        self.generation_counter = generation_counter
    
    def set_generation_counter(self, generation: int):
        """Update the generation counter"""
        self.generation_counter = generation
    
    def mutate_chromosome(self, chromosome: StrategyChromosome, mutation_rate: float = None) -> StrategyChromosome:
        """Apply mutation to a chromosome"""
        if mutation_rate is None:
            mutation_rate = self.evolution_config.mutation_rate

        # Create a copy for mutation
        mutated_chromosome = copy.deepcopy(chromosome)
        mutated_chromosome.strategy_id = str(uuid.uuid4())
        mutated_chromosome.strategy_name = f"{chromosome.strategy_name}_M{self.generation_counter}"
        mutated_chromosome.generation = self.generation_counter
        mutated_chromosome.parent_ids = [chromosome.strategy_id]
        mutated_chromosome.creation_timestamp = datetime.now()

        # Apply different types of mutations
        mutation_applied = False

        # 1. Parameter tweaking (traditional gene mutation)
        if random.random() < mutation_rate:
            mutation_applied = True
            self._apply_parameter_mutation(mutated_chromosome, mutation_rate)

        # 2. Rule-based mutations
        if random.random() < self.evolution_config.rule_mutation_rate:
            mutation_applied = True
            self._apply_rule_mutations(mutated_chromosome)

        # 3. Condition simplification
        if random.random() < self.evolution_config.condition_simplification_rate:
            mutation_applied = True
            self._apply_condition_simplification(mutated_chromosome)

        # 4. New feature injection
        if random.random() < 0.1:  # 10% chance
            mutation_applied = True
            self._apply_feature_injection(mutated_chromosome)

        # Ensure at least some mutation occurred
        if not mutation_applied:
            self._apply_parameter_mutation(mutated_chromosome, mutation_rate * 0.5)

        return mutated_chromosome

    def _apply_parameter_mutation(self, chromosome: StrategyChromosome, mutation_rate: float):
        """Apply traditional parameter mutations"""
        for gene_name, gene in chromosome.genes.items():
            if random.random() < mutation_rate:
                if gene.gene_type == 'numeric':
                    # Gaussian mutation for numeric values
                    if gene.min_value is not None and gene.max_value is not None:
                        # Bounded mutation
                        range_size = gene.max_value - gene.min_value
                        mutation_strength = range_size * 0.1  # 10% of range

                        new_value = gene.value + random.gauss(0, mutation_strength)
                        new_value = max(gene.min_value, min(gene.max_value, new_value))

                        if isinstance(gene.value, int):
                            new_value = int(round(new_value))

                        gene.value = new_value
                    else:
                        # Unbounded mutation
                        mutation_strength = abs(gene.value) * 0.1 if gene.value != 0 else 0.1
                        gene.value += random.gauss(0, mutation_strength)

                elif gene.gene_type == 'boolean':
                    # Flip boolean values
                    gene.value = not gene.value

                elif gene.gene_type == 'categorical':
                    # Random selection from predefined categories
                    if hasattr(gene, 'choices') and gene.choices:
                        gene.value = random.choice(gene.choices)

    def _apply_rule_mutations(self, chromosome: StrategyChromosome):
        """Apply rule-based mutations to strategy conditions"""
        try:
            if not chromosome.rules:
                return

            # Select random rule to mutate
            rule_to_mutate = random.choice(chromosome.rules)

            # Choose mutation type
            mutation_type = random.choice(self.evolution_config.mutation_types)

            if mutation_type == MutationType.CONDITION_MODIFY:
                self._mutate_rule_condition(rule_to_mutate, chromosome)
            elif mutation_type == MutationType.INDICATOR_REPLACE:
                self._mutate_rule_indicator(rule_to_mutate, chromosome)
            elif mutation_type == MutationType.LOGIC_SIMPLIFY:
                self._simplify_rule_logic(rule_to_mutate)
            elif mutation_type == MutationType.NEW_FEATURE_INJECT:
                self._inject_new_feature(rule_to_mutate, chromosome)

        except Exception as e:
            pass

    def _mutate_rule_condition(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Mutate a rule's condition"""
        try:
            # Parse the condition and modify operators or thresholds
            condition = rule.condition

            # Replace comparison operators
            operator_replacements = {
                '<': ['<=', '>', '>='],
                '<=': ['<', '>', '>='],
                '>': ['>=', '<', '<='],
                '>=': ['>', '<', '<='],
                '==': ['!='],
                '!=': ['==']
            }

            for old_op, new_ops in operator_replacements.items():
                if old_op in condition:
                    new_op = random.choice(new_ops)
                    condition = condition.replace(old_op, new_op, 1)  # Replace first occurrence
                    break

            # Modify numeric thresholds
            numbers = re.findall(r'\d+\.?\d*', condition)
            if numbers:
                old_number = random.choice(numbers)
                try:
                    old_value = float(old_number)
                    # Apply small random change
                    change_factor = random.uniform(0.8, 1.2)
                    new_value = old_value * change_factor

                    # Format appropriately
                    if '.' in old_number:
                        new_number = f"{new_value:.2f}"
                    else:
                        new_number = str(int(new_value))

                    condition = condition.replace(old_number, new_number, 1)
                except ValueError:
                    pass

            rule.condition = condition

        except Exception as e:
            pass

    def _mutate_rule_indicator(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Replace indicators in a rule"""
        try:
            # Define indicator replacements
            indicator_replacements = {
                'RSI_14': ['RSI_21', 'RSI_9', 'STOCH_K', 'STOCH_D'],
                'EMA_5': ['EMA_8', 'EMA_13', 'SMA_5', 'SMA_10'],
                'EMA_20': ['EMA_21', 'EMA_30', 'SMA_20', 'SMA_30'],
                'MACD': ['MACD_signal', 'PPO', 'TRIX'],
                'Volume': ['Volume_SMA', 'Volume_EMA', 'OBV'],
                'VWAP': ['TWAP', 'Close', 'Typical_Price']
            }

            condition = rule.condition

            for old_indicator, replacements in indicator_replacements.items():
                if old_indicator in condition:
                    new_indicator = random.choice(replacements)
                    condition = condition.replace(old_indicator, new_indicator)

                    # Update rule indicators list
                    if old_indicator in rule.indicators:
                        rule.indicators.remove(old_indicator)
                    if new_indicator not in rule.indicators:
                        rule.indicators.append(new_indicator)

                    break

            rule.condition = condition

        except Exception as e:
            pass

    def _simplify_rule_logic(self, rule: StrategyRule):
        """Simplify rule logic by removing redundant conditions"""
        try:
            condition = rule.condition

            # Remove redundant parentheses
            while '((' in condition and '))' in condition:
                condition = condition.replace('((', '(').replace('))', ')')

            # Simplify double negations
            condition = condition.replace('not not ', '')

            # Remove redundant AND/OR operations
            condition = re.sub(r'\s+and\s+and\s+', ' and ', condition)
            condition = re.sub(r'\s+or\s+or\s+', ' or ', condition)

            rule.condition = condition

        except Exception as e:
            pass

    def _inject_new_feature(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Inject new technical indicators into a rule"""
        try:
            # List of new indicators to potentially inject
            new_indicators = [
                'ADX', 'MFI', 'CCI', 'Williams_R', 'ROC', 'TSI',
                'BB_upper', 'BB_lower', 'BB_width', 'ATR',
                'Donchian_high', 'Donchian_low', 'Pivot_Point',
                'Support_1', 'Resistance_1', 'SuperTrend'
            ]

            # Select random new indicator
            new_indicator = random.choice(new_indicators)

            # Create new condition component
            operators = ['>', '<', '>=', '<=']
            operator = random.choice(operators)
            threshold = random.uniform(0.1, 100)  # Adjust based on indicator

            new_condition_part = f"{new_indicator} {operator} {threshold:.2f}"

            # Add to existing condition with AND/OR
            connector = random.choice([' and ', ' or '])
            rule.condition = f"({rule.condition}){connector}({new_condition_part})"

            # Update indicators list
            if new_indicator not in rule.indicators:
                rule.indicators.append(new_indicator)

        except Exception as e:
            pass

    def _apply_condition_simplification(self, chromosome: StrategyChromosome):
        """Apply condition simplification across all rules"""
        try:
            for rule in chromosome.rules:
                self._simplify_rule_logic(rule)

        except Exception as e:
            pass

    def _apply_feature_injection(self, chromosome: StrategyChromosome):
        """Apply feature injection to random rules"""
        try:
            if chromosome.rules:
                # Select random subset of rules for feature injection
                num_rules_to_modify = max(1, len(chromosome.rules) // 3)
                rules_to_modify = random.sample(chromosome.rules, num_rules_to_modify)

                for rule in rules_to_modify:
                    self._inject_new_feature(rule, chromosome)

        except Exception as e:
            pass

    def crossover_chromosomes(self, parent1: StrategyChromosome, parent2: StrategyChromosome) -> Tuple[StrategyChromosome, StrategyChromosome]:
        """Perform crossover between two chromosomes"""

        # Create offspring
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)

        # Generate new IDs and metadata
        offspring1.strategy_id = str(uuid.uuid4())
        offspring2.strategy_id = str(uuid.uuid4())

        offspring1.strategy_name = f"{parent1.strategy_name}_X{parent2.strategy_name}_{self.generation_counter}"
        offspring2.strategy_name = f"{parent2.strategy_name}_X{parent1.strategy_name}_{self.generation_counter}"

        offspring1.generation = self.generation_counter
        offspring2.generation = self.generation_counter

        offspring1.parent_ids = [parent1.strategy_id, parent2.strategy_id]
        offspring2.parent_ids = [parent1.strategy_id, parent2.strategy_id]

        offspring1.creation_timestamp = datetime.now()
        offspring2.creation_timestamp = datetime.now()

        # Perform uniform crossover for genes
        common_genes = set(parent1.genes.keys()) & set(parent2.genes.keys())

        for gene_name in common_genes:
            if random.random() < 0.5:  # 50% chance to swap
                # Swap genes between offspring
                offspring1.genes[gene_name] = copy.deepcopy(parent2.genes[gene_name])
                offspring2.genes[gene_name] = copy.deepcopy(parent1.genes[gene_name])

        # Perform rule crossover
        self._crossover_rules(offspring1, offspring2, parent1, parent2)

        return offspring1, offspring2

    def _crossover_rules(self, offspring1: StrategyChromosome, offspring2: StrategyChromosome,
                        parent1: StrategyChromosome, parent2: StrategyChromosome):
        """Perform crossover for strategy rules"""
        try:
            # Mix rules from both parents
            all_rules = parent1.rules + parent2.rules

            if all_rules:
                # Randomly distribute rules between offspring
                random.shuffle(all_rules)
                mid_point = len(all_rules) // 2

                offspring1.rules = all_rules[:mid_point]
                offspring2.rules = all_rules[mid_point:]

                # Ensure each offspring has at least one rule
                if not offspring1.rules and parent1.rules:
                    offspring1.rules = [random.choice(parent1.rules)]
                if not offspring2.rules and parent2.rules:
                    offspring2.rules = [random.choice(parent2.rules)]

        except Exception as e:
            pass

    def tournament_selection(self, population: List[StrategyChromosome], tournament_size: int = None) -> StrategyChromosome:
        """Select a chromosome using tournament selection"""
        if tournament_size is None:
            tournament_size = self.evolution_config.tournament_size
        
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda x: x.fitness_score)

    def select_elite(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Select elite chromosomes from population"""
        sorted_population = sorted(population, key=lambda x: x.fitness_score, reverse=True)
        elite_size = min(self.evolution_config.elite_size, len(sorted_population))
        return sorted_population[:elite_size]
