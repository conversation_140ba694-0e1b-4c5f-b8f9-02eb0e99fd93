import uuid
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Any, Optional

class EvolutionMode(Enum):
    """Evolution modes for strategy optimization"""
    GENETIC_ALGORITHM = "genetic_algorithm"
    MULTI_OBJECTIVE = "multi_objective"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    HYBRID = "hybrid"

class StrategyStatus(Enum):
    """Strategy lifecycle status"""
    CANDIDATE = "candidate"
    TESTING = "testing"
    CHALLENGER = "challenger"
    CHAMPION = "champion"
    DEPRECATED = "deprecated"
    FAILED = "failed"

class MarketRegime(Enum):
    """Market regime types"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"

@dataclass
class StrategyVariant:
    """Represents a stock-specific strategy variant"""
    strategy_id: str
    base_strategy_name: str
    stock_name: str
    timeframe: str
    ranking: int  # 0-100 ranking system
    entry_conditions: Dict[str, str]
    exit_conditions: Dict[str, str]
    intraday_rules: Dict[str, Any]
    risk_reward_ratios: List[List[float]]
    risk_management: Dict[str, Any]
    position_sizing: Dict[str, Any]
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    status: StrategyStatus = StrategyStatus.CANDIDATE
    creation_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    market_regime: Optional[MarketRegime] = None
    confidence_score: float = 0.0

@dataclass
class OptimizationObjective:
    """Multi-objective optimization target"""
    name: str
    weight: float
    direction: str  # "maximize" or "minimize"
    target_value: Optional[float] = None

@dataclass
class EvolutionConfig:
    """Enhanced evolution configuration"""
    # Population parameters
    population_size: int = 50
    elite_size: int = 10
    max_generations: int = 100
    
    # Multi-objective optimization
    objectives: List[OptimizationObjective] = field(default_factory=lambda: [
        OptimizationObjective("sharpe_ratio", 0.4, "maximize"),
        OptimizationObjective("max_drawdown", 0.3, "minimize"),
        OptimizationObjective("win_rate", 0.3, "maximize")
    ])
    
    # Strategy enhancement parameters (DYNAMIC QUALITY-BASED)
    max_variants_per_strategy: int = 12
    min_variants_per_strategy: int = 2
    min_ranking_threshold: int = 50  # Higher threshold for quality
    quality_scaling: bool = True

    # Ranking system (as requested in memories)
    ranking_system: Dict[str, Any] = field(default_factory=lambda: {
        "initial_ranking": 100,
        "ranking_decay_rate": 0.95,
        "ranking_boost_rate": 1.05,
        "min_ranking": 10,
        "max_ranking": 100
    })

    stock_selection_criteria: Dict[str, Any] = field(default_factory=lambda: {
        "min_volume": 1000000,
        "min_price": 10.0,
        "max_price": 5000.0,
        "sectors": ["all"]  # or specific sectors
    })
    
    # Backtesting parameters
    backtesting_config: Dict[str, Any] = field(default_factory=lambda: {
        "max_symbols": 10,
        "max_files": 50,
        "ranking_threshold": 70
    })
    
    # DYNAMIC STRATEGY SELECTION (NEW)
    strategy_selection: Dict[str, Any] = field(default_factory=lambda: {
        "mode": "diverse_selection",
        "min_ranking": 0
    })

    # GPU CONFIGURATION (NEW) - Enhanced with multi-layer parallel processing
    gpu_config: Dict[str, Any] = field(default_factory=lambda: {
        "strategy_batch_size": 150,
        "stocks_per_worker": 3,
        "max_stocks_per_strategy": None,
        "min_stocks_per_strategy": 8,
        "variants_per_stock": 3,
        "variants_per_result": 3,
        "batch_timeout_seconds": 120,
        "timeout_per_combination": 3,
        # Multi-layer parallel processing settings
        "max_batch_size": 24,
        "max_concurrent_batches": 4,
        "gpu_recovery_delay": 0.1,
        "enable_gpu_parallel": True,
        "gpu_memory_threshold": 0.8,
        "enable_adaptive_batching": True,
        "stream_isolation": True,
        "concurrent_batch_processing": True,
        "batch_optimization_enabled": True,
        # Strategy-level parallel processing
        "max_concurrent_strategies": 2,
        "enable_strategy_parallelism": True,
        "gpu_worker_allocation": "balanced"
    })

    # Storage configuration
    storage_config: Dict[str, Any] = field(default_factory=lambda: {
        "database_path": "data/evolved_strategies.db",
        "backup_interval_hours": 24,
        "max_backup_files": 10
    })
