import asyncio
import json
import yaml
import polars as pl
import numpy as np
import torch
import uuid
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime

from agents.enhanced_backtesting_kimi import run_backtesting_for_evolution
from utils.real_gpu_accelerator import real_gpu_accelerator
from utils.gpu_hyperopt_free import gpu_hyperopt_free
from utils.gpu_parallel_processor import gpu_parallel_processor, GPUTask

from agents.strategy_evolution.evolution_config import EvolutionConfig, StrategyVariant, StrategyStatus
from agents.strategy_evolution.evolution_logger import logger
from agents.strategy_evolution.strategy_evaluator import StrategyEvaluator, get_cuda_optimizer, process_strategies_parallel_async

class StrategyOptimizer:
    """
    Handles the multi-objective optimization and genetic algorithm operations for strategy evolution.
    """
    def __init__(self, evolution_config: EvolutionConfig, strategy_evaluator: StrategyEvaluator):
        self.evolution_config = evolution_config
        self.strategy_evaluator = strategy_evaluator

        # Genetic Algorithm parameters
        self.population_size = 50
        self.mutation_rate = 0.15
        self.crossover_rate = 0.8
        self.elite_size = 5  # Top performers to keep unchanged
        self.tournament_size = 3  # For tournament selection

    def _create_strategy_dna(self, variant: StrategyVariant) -> Dict[str, float]:
        """Extract DNA (numerical parameters) from strategy variant for genetic operations"""
        dna = {}

        # Extract numerical parameters from risk management
        if variant.risk_management:
            risk_mgmt = variant.risk_management
            dna['stop_loss'] = float(risk_mgmt.get('stop_loss', 0.02))
            dna['take_profit'] = float(risk_mgmt.get('take_profit', 0.04))
            dna['max_position_size'] = float(risk_mgmt.get('max_position_size', 0.1))

        # Extract from entry conditions (assuming RSI-based for now)
        if variant.entry_conditions:
            entry = variant.entry_conditions
            dna['oversold_threshold'] = float(entry.get('oversold_threshold', 30))
            dna['overbought_threshold'] = float(entry.get('overbought_threshold', 70))
            dna['rsi_period'] = float(entry.get('rsi_period', 14))

        # Extract from position sizing
        if variant.position_sizing:
            pos_size = variant.position_sizing
            dna['risk_per_trade'] = float(pos_size.get('risk_per_trade', 0.02))
            dna['max_trades'] = float(pos_size.get('max_trades', 3))

        return dna

    def _dna_to_variant(self, base_variant: StrategyVariant, dna: Dict[str, float]) -> StrategyVariant:
        """Convert DNA back to strategy variant"""
        new_variant = StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_variant.base_strategy_name,
            stock_name=base_variant.stock_name,
            timeframe=base_variant.timeframe,
            ranking=0,  # Will be calculated
            entry_conditions={
                'oversold_threshold': max(10, min(40, dna.get('oversold_threshold', 30))),
                'overbought_threshold': max(60, min(90, dna.get('overbought_threshold', 70))),
                'rsi_period': max(5, min(30, int(dna.get('rsi_period', 14))))
            },
            exit_conditions=base_variant.exit_conditions,
            intraday_rules=base_variant.intraday_rules,
            risk_reward_ratios=base_variant.risk_reward_ratios,
            risk_management={
                'stop_loss': max(0.005, min(0.05, dna.get('stop_loss', 0.02))),
                'take_profit': max(0.01, min(0.1, dna.get('take_profit', 0.04))),
                'max_position_size': max(0.01, min(0.2, dna.get('max_position_size', 0.1)))
            },
            position_sizing={
                'risk_per_trade': max(0.005, min(0.05, dna.get('risk_per_trade', 0.02))),
                'max_trades': max(1, min(10, int(dna.get('max_trades', 3))))
            },
            performance_metrics={}, # Initialize as empty dictionary
            status=StrategyStatus.CANDIDATE, # Default status
            creation_date=datetime.now(),
            last_updated=datetime.now(),
            market_regime=None, # Default to None
            confidence_score=0.0
        )
        return new_variant

    def _mutate_dna(self, dna: Dict[str, float]) -> Dict[str, float]:
        """Apply mutation to DNA with GPU-compatible operations"""
        mutated_dna = dna.copy()

        for key, value in mutated_dna.items():
            if np.random.random() < self.mutation_rate:
                # Apply Gaussian mutation with parameter-specific bounds
                if key in ['oversold_threshold', 'overbought_threshold']:
                    mutation_strength = 5.0
                elif key in ['stop_loss', 'take_profit', 'risk_per_trade']:
                    mutation_strength = 0.005
                elif key in ['max_position_size']:
                    mutation_strength = 0.02
                elif key in ['rsi_period', 'max_trades']:
                    mutation_strength = 2.0
                else:
                    mutation_strength = 0.1

                # Apply mutation
                mutation = np.random.normal(0, mutation_strength)
                mutated_dna[key] = value + mutation

        return mutated_dna

    def _crossover_dna(self, parent1_dna: Dict[str, float], parent2_dna: Dict[str, float]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Perform crossover between two DNA sequences"""
        child1_dna = {}
        child2_dna = {}

        for key in parent1_dna.keys():
            if np.random.random() < self.crossover_rate:
                # Uniform crossover
                if np.random.random() < 0.5:
                    child1_dna[key] = parent1_dna[key]
                    child2_dna[key] = parent2_dna[key]
                else:
                    child1_dna[key] = parent2_dna[key]
                    child2_dna[key] = parent1_dna[key]
            else:
                # No crossover - keep parent genes
                child1_dna[key] = parent1_dna[key]
                child2_dna[key] = parent2_dna[key]

        return child1_dna, child2_dna

    def _tournament_selection(self, population: List[StrategyVariant]) -> StrategyVariant:
        """Tournament selection for genetic algorithm"""
        tournament = np.random.choice(population, size=min(self.tournament_size, len(population)), replace=False)
        return max(tournament, key=lambda x: x.ranking)

    def _evaluate_strategy_fitness_sync_with_params(self, base_strategy: Dict[str, Any],
                                                  stock_name: str, timeframe: str,
                                                  params: Dict[str, Any]) -> float:
        """
        Evaluate strategy fitness with specific parameters for free GPU optimization
        """
        try:
            # Create strategy variant with optimized parameters
            strategy_variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'], # Changed from strategy_name to base_strategy_name
                stock_name=stock_name,
                timeframe=timeframe,
                # Assuming 'parameters' is not directly part of StrategyVariant,
                # but its components are mapped to entry_conditions, risk_management etc.
                entry_conditions={
                    'oversold_threshold': params.get('oversold_threshold', 30),
                    'overbought_threshold': params.get('overbought_threshold', 70),
                    'rsi_period': params.get('rsi_period', 14)
                },
                risk_reward_ratios=[params.get('risk_reward_ratio', [1.0, 2.0])],
                risk_management={
                    'stop_loss': params.get('stop_loss', 0.02),
                    'take_profit': params.get('take_profit', 0.04),
                    'max_position_size': params.get('max_position_size', 0.1)
                },
                position_sizing={
                    'risk_per_trade': params.get('risk_per_trade', 0.02),
                    'max_trades': params.get('max_trades', 3)
                },
                performance_metrics={}
            )

            # Evaluate fitness using the provided StrategyEvaluator
            fitness_metrics = self.strategy_evaluator._evaluate_strategy_fitness_sync(strategy_variant)
            return fitness_metrics.get('composite_score', 0.0)

        except Exception as e:
            logger.warning(f"Parameter evaluation failed: {e}")
            return 0.0

    def _get_parameter_space(self, base_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get parameter space for optimization based on strategy type
        """
        strategy_name = base_strategy.get('name', '').lower()

        # Base parameter space
        param_space = {
            'risk_reward_ratio': {
                'type': 'categorical',
                'choices': [[1.0, 2.0], [1.5, 3.0], [2.0, 4.0], [2.5, 5.0]]
            },
            'stop_loss': {'type': 'float', 'low': 0.005, 'high': 0.05},
            'take_profit': {'type': 'float', 'low': 0.01, 'high': 0.1},
            'max_position_size': {'type': 'float', 'low': 0.01, 'high': 0.2},
            'risk_per_trade': {'type': 'float', 'low': 0.005, 'high': 0.05},
            'max_trades': {'type': 'int', 'low': 1, 'high': 10}
        }

        # Strategy-specific parameters
        if 'rsi' in strategy_name:
            param_space.update({
                'oversold_threshold': {'type': 'int', 'low': 20, 'high': 35},
                'overbought_threshold': {'type': 'int', 'low': 65, 'high': 80},
                'rsi_period': {'type': 'int', 'low': 10, 'high': 30}
            })
        elif 'ema' in strategy_name or 'sma' in strategy_name:
            param_space.update({
                'fast_period': {'type': 'int', 'low': 5, 'high': 20},
                'slow_period': {'type': 'int', 'low': 20, 'high': 50}
            })
        elif 'macd' in strategy_name:
            param_space.update({
                'fast_period': {'type': 'int', 'low': 8, 'high': 15},
                'slow_period': {'type': 'int', 'low': 20, 'high': 30},
                'signal_period': {'type': 'int', 'low': 7, 'high': 12}
            })
        elif 'bollinger' in strategy_name:
            param_space.update({
                'period': {'type': 'int', 'low': 15, 'high': 25},
                'std_dev': {'type': 'float', 'low': 1.5, 'high': 2.5}
            })

        return param_space

    async def run_multi_objective_optimization_batch(self, base_strategy: Dict[str, Any],
                                                   stock_timeframe_pairs: List[Tuple[str, str]],
                                                   evolution_stats: Dict[str, Any]) -> List[StrategyVariant]:
        """
        Run multi-objective optimization for multiple stock-timeframe combinations in TRUE parallel
        """
        try:
            strategy_name = base_strategy['name']
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] 🚀 TRUE GPU Parallel optimization for {strategy_name} on {len(stock_timeframe_pairs)} combinations")
            
            gpu_available = gpu_parallel_processor.cuda_available
            all_variants = []
            
            if gpu_available and len(stock_timeframe_pairs) >= 2:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] ⚡ Using TRUE GPU parallel processing with {gpu_parallel_processor.gpu_workers} workers")
                
                gpu_tasks = []
                for i, (stock_name, timeframe) in enumerate(stock_timeframe_pairs):
                    stock_files = list(Path("data/features").glob(f"features_{stock_name}_*.parquet"))
                    if stock_files:
                        try:
                            df = pl.read_parquet(stock_files[0])
                            if len(df) >= 100:
                                data_arrays = {
                                    'close': df['close'].to_numpy(),
                                    'high': df['high'].to_numpy() if 'high' in df.columns else df['close'].to_numpy(),
                                    'low': df['low'].to_numpy() if 'low' in df.columns else df['close'].to_numpy(),
                                    'volume': df['volume'].to_numpy() if 'volume' in df.columns else np.ones(len(df))
                                }
                                
                                gpu_config = self.evolution_config.gpu_config
                                variants_per_stock = gpu_config.get('variants_per_stock', 3)

                                strategies = []
                                for variant_idx in range(variants_per_stock):
                                    strategies.append({
                                        'name': f"{base_strategy['name']}_{stock_name}_v{variant_idx}",
                                        'type': base_strategy['name'],
                                        'stock_name': stock_name,
                                        'timeframe': timeframe,
                                        'variant_idx': variant_idx
                                    })
                                
                                task = GPUTask(
                                    task_id=f"{stock_name}_{timeframe}_{i}",
                                    data=data_arrays,
                                    strategies=strategies
                                )
                                gpu_tasks.append(task)
                                
                        except Exception as e:
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] ⚠️ Failed to load data for {stock_name}: {e}")
                            continue
                
                if gpu_tasks:
                    gpu_config = self.evolution_config.gpu_config
                    max_batch_size = gpu_config.get('max_batch_size', 32)
                    gpu_recovery_delay = gpu_config.get('gpu_recovery_delay', 0.5)

                    if len(gpu_tasks) > max_batch_size:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] 🔄 Large batch detected: {len(gpu_tasks)} tasks, processing in chunks of {max_batch_size}")

                        all_parallel_results = []
                        for i in range(0, len(gpu_tasks), max_batch_size):
                            batch = gpu_tasks[i:i + max_batch_size]
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] 🔥 Processing batch {i//max_batch_size + 1}: {len(batch)} tasks")

                            batch_results = await gpu_parallel_processor.process_batch_parallel(batch)
                            all_parallel_results.extend(batch_results)

                            cleanup_task = asyncio.create_task(gpu_parallel_processor.cleanup_gpu_memory_async())
                            await asyncio.sleep(0.1)
                            await cleanup_task

                        parallel_results = all_parallel_results
                    else:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] 🔥 Processing {len(gpu_tasks)} tasks in TRUE parallel on GPU")
                        parallel_results = await gpu_parallel_processor.process_batch_parallel(gpu_tasks)
                    
                    for result in parallel_results:
                        if 'error' not in result['result']:
                            task_result = result['result']
                            signals = task_result.get('signals', {})
                            backtest = task_result.get('backtest', {})
                            
                            for strategy_name, signal_array in signals.items():
                                parts = strategy_name.split('_')
                                if len(parts) >= 3:
                                    stock_name = parts[1]
                                    variant_idx = int(parts[-1][1:]) if parts[-1].startswith('v') else 0
                                    
                                    variant = self._create_variant_from_gpu_results(
                                        base_strategy, stock_name, '1min', 
                                        backtest.get(strategy_name, {}), variant_idx
                                    )
                                    
                                    if strategy_name in backtest:
                                        gpu_metrics = backtest[strategy_name]
                                        fitness_metrics = {
                                            'sharpe_ratio': gpu_metrics.get('sharpe_ratio', 0.0),
                                            'max_drawdown': gpu_metrics.get('max_drawdown', 20.0),
                                            'win_rate': gpu_metrics.get('win_rate', 0.5),
                                            'total_trades': gpu_metrics.get('total_trades', 1),
                                            'total_pnl': gpu_metrics.get('total_pnl', 0.0),
                                            'roi': gpu_metrics.get('roi', 0.0)
                                        }
                                        
                                        composite_score = self.strategy_evaluator._calculate_composite_fitness(fitness_metrics)
                                        fitness_metrics['composite_score'] = composite_score
                                        
                                        variant.ranking = max(10, int(composite_score * 100))
                                        variant.performance_metrics = fitness_metrics
                                        
                                        if variant.ranking >= self.evolution_config.min_ranking_threshold:
                                            all_variants.append(variant)
                    
                    asyncio.create_task(gpu_parallel_processor.cleanup_gpu_memory_async())

                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ✅ TRUE GPU parallel processing completed - {len(all_variants)} variants generated")
                else:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ⚠️ No valid GPU tasks created")
            else:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] 🔄 Using fast CPU processing for {len(stock_timeframe_pairs)} combinations")
                for stock_name, timeframe in stock_timeframe_pairs:
                    variants = await self._fast_cpu_optimization(base_strategy, stock_name, timeframe)
                    all_variants.extend(variants)
            
            return all_variants
            
        except Exception as e:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] ❌ Error in batch multi-objective optimization: {e}")
            logger.error(f"Error in batch multi-objective optimization: {e}")
            return []
    
    async def _process_single_combination_gpu_fast(self, stock_name, timeframe, base_strategy):
        """FAST GPU processing - no Optuna, direct GPU evaluation"""
        try:
            stock_files = list(Path("data/features").glob(f"features_{stock_name}_*.parquet"))
            if not stock_files:
                return []
            
            df = pl.read_parquet(stock_files[0])
            
            if len(df) < 100:
                return []
            
            data_arrays = {
                'close': df['close'].to_numpy(),
                'high': df['high'].to_numpy() if 'high' in df.columns else df['close'].to_numpy(),
                'low': df['low'].to_numpy() if 'low' in df.columns else df['close'].to_numpy(),
                'volume': df['volume'].to_numpy() if 'volume' in df.columns else np.ones(len(df))
            }
            
            strategies = [{
                'name': base_strategy['name'],
                'type': base_strategy['name']
            }]
            
            gpu_results = real_gpu_accelerator.vectorized_backtest_gpu(data_arrays, strategies)
            
            variants = []
            if gpu_results:
                for result in gpu_results:
                    gpu_config = self.evolution_config.gpu_config
                    variants_per_result = gpu_config.get('variants_per_result', 2)

                    for i in range(variants_per_result):
                        variant = self._create_fast_variant(
                            base_strategy, stock_name, timeframe, result, i
                        )
                        
                        fitness_metrics = {
                            'sharpe_ratio': max(0, result.get('sharpe_ratio', 0.0) * (1 + i * 0.1)),
                            'max_drawdown': min(50, max(5, result.get('max_drawdown', 20.0))),
                            'win_rate': max(0, min(1, result.get('win_rate', 0.5) * (1 + i * 0.05))),
                            'total_trades': max(1, result.get('total_trades', 1)),
                            'total_pnl': result.get('total_pnl', 0.0) * (1 + i * 0.1),
                            'roi': result.get('roi', 0.0) * (1 + i * 0.1)
                        }
                        
                        composite_score = self.strategy_evaluator._calculate_composite_fitness(fitness_metrics)
                        fitness_metrics['composite_score'] = composite_score

                        scaled_score = 20 + (composite_score * 60)
                        variant.ranking = max(15, min(85, int(scaled_score)))
                        variant.performance_metrics = fitness_metrics

                        if variant.ranking >= self.evolution_config.min_ranking_threshold:
                            variants.append(variant)
            
            return variants
            
        except Exception as e:
            logger.error(f"Error processing GPU combination {stock_name}-{timeframe}: {e}")
            return []
    
    def _create_fast_variant(self, base_strategy, stock_name, timeframe, gpu_result, variant_idx):
        """Create variant quickly without complex parameter optimization"""
        stop_loss = 0.015 + (variant_idx * 0.005)
        take_profit = stop_loss * 2
        
        return StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_strategy['name'],
            stock_name=stock_name,
            timeframe=timeframe,
            ranking=100,
            entry_conditions={
                'oversold_threshold': 25 + (variant_idx * 5),
                'overbought_threshold': 75 - (variant_idx * 5)
            },
            exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
            intraday_rules={},
            risk_reward_ratios=[[1, 2]],
            risk_management={'stop_loss': stop_loss, 'take_profit': take_profit},
            position_sizing={'risk_per_trade': 0.02}
        )
    
    async def _fast_cpu_optimization(self, base_strategy, stock_name, timeframe):
        """Fast CPU optimization without Optuna - direct parameter sweep"""
        try:
            variants = []
            
            stop_losses = [0.01, 0.015, 0.02]
            oversold_levels = [25, 30, 35]
            
            for i, (stop_loss, oversold) in enumerate(zip(stop_losses, oversold_levels)):
                variant = StrategyVariant(
                    strategy_id=str(uuid.uuid4()),
                    base_strategy_name=base_strategy['name'],
                    stock_name=stock_name,
                    timeframe=timeframe,
                    ranking=50 + i * 10,
                    entry_conditions={
                        'oversold_threshold': oversold,
                        'overbought_threshold': 100 - oversold
                    },
                    exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
                    intraday_rules={},
                    risk_reward_ratios=[[1, 2]],
                    risk_management={'stop_loss': stop_loss, 'take_profit': stop_loss * 2},
                    position_sizing={'risk_per_trade': 0.02}
                )
                
                fitness_metrics = {
                    'sharpe_ratio': 0.5 + i * 0.2,
                    'max_drawdown': 20 - i * 2,
                    'win_rate': 0.5 + i * 0.05,
                    'total_trades': 10 + i * 5,
                    'total_pnl': 100 + i * 50,
                    'roi': 5 + i * 2
                }
                
                composite_score = self.strategy_evaluator._calculate_composite_fitness(fitness_metrics)
                fitness_metrics['composite_score'] = composite_score
                
                variant.ranking = max(10, int(composite_score * 100))
                variant.performance_metrics = fitness_metrics
                
                if variant.ranking >= self.evolution_config.min_ranking_threshold:
                    variants.append(variant)
            
            return variants
            
        except Exception as e:
            logger.error(f"Error in fast CPU optimization: {e}")
            return []
    
    def _create_mock_variant(self, base_strategy, stock_name, timeframe):
        """Create a mock variant for GPU processing"""
        return StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_strategy['name'],
            stock_name=stock_name,
            timeframe=timeframe,
            ranking=100,
            entry_conditions={'long': 'rsi_14 < 30', 'short': 'rsi_14 > 70'},
            exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
            intraday_rules={},
            risk_reward_ratios=[[1, 2]],
            risk_management={'stop_loss': 0.02, 'take_profit': 0.04},
            position_sizing={'risk_per_trade': 0.02}
        )
    
    def _create_variant_from_gpu_results(self, base_strategy, stock_name, timeframe, gpu_result, variant_idx):
        """Create variant from GPU results with parameter variations"""
        stop_loss = 0.015 + (variant_idx * 0.005)
        take_profit = stop_loss * 2
        
        return StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_strategy['name'],
            stock_name=stock_name,
            timeframe=timeframe,
            ranking=100,
            entry_conditions={
                'oversold_threshold': 25 + (variant_idx * 5),
                'overbought_threshold': 75 - (variant_idx * 5)
            },
            exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
            intraday_rules={},
            risk_reward_ratios=[[1, 2]],
            risk_management={'stop_loss': stop_loss, 'take_profit': take_profit},
            position_sizing={'risk_per_trade': 0.02}
        )

    async def run_multi_objective_optimization(self, base_strategy: Dict[str, Any],
                                             stock_name: str, timeframe: str,
                                             evolution_stats: Dict[str, Any]) -> List[StrategyVariant]:
        """
        Run GPU-accelerated optimization using custom GPU hyperparameter optimizer
        """
        try:
            evolution_stats['current_stock'] = stock_name
            evolution_stats['current_strategy'] = base_strategy['name']

            logger.stock_progress(stock_name, f"GPU optimizing {base_strategy['name']} ({timeframe})")

            optimization_result = gpu_hyperopt_free.optimize_strategy_parameters(
                objective_func=lambda params: self._evaluate_strategy_fitness_sync_with_params(
                    base_strategy, stock_name, timeframe, params
                ),
                param_space=self._get_parameter_space(base_strategy),
                n_trials=50,
                method='auto'
            )

            evolution_stats['optimization_tasks_completed'] += 1

            optimized_variants = []
            if optimization_result.best_score > 0.1:
                best_params = optimization_result.best_params
                
                variant = StrategyVariant(
                    strategy_id=str(uuid.uuid4()),
                    base_strategy_name=base_strategy['name'],
                    stock_name=stock_name,
                    timeframe=timeframe,
                    ranking=100,
                    entry_conditions={
                        'oversold_threshold': best_params.get('oversold_threshold', 30),
                        'overbought_threshold': best_params.get('overbought_threshold', 70),
                        'rsi_period': best_params.get('rsi_period', 14)
                    },
                    exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
                    intraday_rules={},
                    risk_reward_ratios=[best_params.get('risk_reward_ratio', [1, 2])],
                    risk_management={
                        'stop_loss': best_params.get('stop_loss', 0.02),
                        'take_profit': best_params.get('take_profit', 0.04),
                        'max_position_size': best_params.get('max_position_size', 0.1)
                    },
                    position_sizing={
                        'risk_per_trade': best_params.get('risk_per_trade', 0.02),
                        'max_trades': best_params.get('max_trades', 3)
                    }
                )
                
                fitness_metrics = {
                    'sharpe_ratio': optimization_result.best_score,
                    'max_drawdown': 20.0,
                    'win_rate': 0.6,
                    'total_trades': 10,
                    'total_pnl': optimization_result.best_score * 100,
                    'roi': optimization_result.best_score * 10,
                    'composite_score': optimization_result.best_score
                }
                
                variant.ranking = max(10, int(optimization_result.best_score * 100))
                variant.performance_metrics = fitness_metrics
                
                if variant.ranking >= self.evolution_config.min_ranking_threshold:
                    optimized_variants.append(variant)

            logger.stock_progress(stock_name, f"GPU generated {len(optimized_variants)} variants (score: {optimization_result.best_score:.3f})")

            evolution_stats['variants_generated'] += len(optimized_variants)
            evolution_stats['variants_above_threshold'] += len(optimized_variants)
            
            gpu_hyperopt_free.cleanup_gpu_memory()

            return optimized_variants

        except Exception as e:
            logger.error(f"Error in GPU multi-objective optimization: {e}")
            evolution_stats['optimization_tasks_failed'] += 1
            return []
