#!/usr/bin/env python3
"""
Performance Analysis Agent Runner

This script provides a robust runner for the Performance Analysis Agent with:
- Configuration validation
- Error handling and recovery
- Performance monitoring
- Graceful shutdown
- Health checks
- Integration with other agents
"""

import os
import sys
import asyncio
import logging
import signal
import yaml
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
import argparse

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.performance_analysis.performance_analysis_agent import PerformanceAnalysisAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceAnalysisRunner:
    """
    Runner for Performance Analysis Agent with enhanced error handling and monitoring
    """
    
    def __init__(self, config_path: str = "config/performance_analysis_config.yaml"):
        """Initialize the runner"""
        self.config_path = config_path
        self.agent: Optional[PerformanceAnalysisAgent] = None
        self.running = False
        self.shutdown_event = asyncio.Event()
        self.start_time: Optional[datetime] = None
        self.health_check_interval = 300  # 5 minutes
        self.restart_count = 0
        self.max_restarts = 5
        
    async def initialize(self) -> bool:
        """Initialize the Performance Analysis Agent"""
        try:
            logger.info("[INIT] Initializing Performance Analysis Agent Runner...")
            
            # Validate configuration
            if not self._validate_config():
                return False
            
            # Initialize performance analysis agent
            self.agent = PerformanceAnalysisAgent(self.config_path)
            await self.agent.setup()
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.start_time = datetime.now()
            logger.info("[SUCCESS] Performance Analysis Agent Runner initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing runner: {e}")
            return False
    
    def _validate_config(self) -> bool:
        """Validate configuration file"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"[ERROR] Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
            
            # Check required sections
            required_sections = ['analysis', 'storage', 'logging']
            for section in required_sections:
                if section not in config:
                    logger.error(f"[ERROR] Missing required config section: {section}")
                    return False
            
            # Validate storage paths
            storage_config = config.get('storage', {})
            required_paths = [
                'trade_data_path',
                'strategy_performance_path',
                'execution_quality_path',
                'reports_path'
            ]
            
            for path_key in required_paths:
                if path_key not in storage_config:
                    logger.error(f"[ERROR] Missing storage path: {path_key}")
                    return False
            
            logger.info("[SUCCESS] Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Configuration validation failed: {e}")
            return False
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self):
        """Main execution loop"""
        try:
            self.running = True
            logger.info("[RUNNING] Performance Analysis Agent Runner started")
            
            # Start the performance analysis agent
            agent_task = asyncio.create_task(self.agent.start())
            
            # Start background monitoring tasks
            health_check_task = asyncio.create_task(self._health_check_loop())
            performance_monitor_task = asyncio.create_task(self._performance_monitor_loop())
            
            # Wait for shutdown signal or agent completion
            done, pending = await asyncio.wait(
                [agent_task, asyncio.create_task(self.shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Cancel background tasks
            health_check_task.cancel()
            performance_monitor_task.cancel()
            
            try:
                await health_check_task
            except asyncio.CancelledError:
                pass
            
            try:
                await performance_monitor_task
            except asyncio.CancelledError:
                pass
            
            logger.info("[SUCCESS] Performance Analysis Agent Runner stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in main execution loop: {e}")
            
            # Attempt restart if within limits
            if self.restart_count < self.max_restarts:
                logger.info(f"[WORKFLOW] Attempting restart ({self.restart_count + 1}/{self.max_restarts})")
                self.restart_count += 1
                await asyncio.sleep(30)  # Wait before restart
                await self.run()
            else:
                logger.error("[ERROR] Maximum restart attempts reached, shutting down")
        finally:
            self.running = False
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("[STOP] Initiating graceful shutdown...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.agent:
            try:
                await self.agent.stop()
                logger.info("[SUCCESS] Performance Analysis Agent stopped successfully")
            except Exception as e:
                logger.error(f"[ERROR] Error stopping agent: {e}")
    
    async def _health_check_loop(self):
        """Health check monitoring loop"""
        while self.running:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Health check error: {e}")
                await asyncio.sleep(60)
    
    async def _perform_health_check(self):
        """Perform health check on the agent"""
        try:
            if not self.agent:
                logger.warning("[WARN]  Agent not initialized")
                return
            
            # Check if agent is running
            if not self.agent.is_running:
                logger.warning("[WARN]  Agent is not running")
                return
            
            # Check memory usage
            memory_usage = self.agent.performance_metrics.get('memory_usage_mb', 0)
            if memory_usage > 2048:  # 2GB threshold
                logger.warning(f"[WARN]  High memory usage: {memory_usage:.2f} MB")
            
            # Check processing time
            processing_time = self.agent.performance_metrics.get('processing_time_ms', 0)
            if processing_time > 30000:  # 30 second threshold
                logger.warning(f"[WARN]  Slow processing time: {processing_time:.2f} ms")
            
            # Check last analysis time
            last_analysis = self.agent.performance_metrics.get('last_analysis_time')
            if last_analysis:
                time_since_analysis = (datetime.now() - last_analysis).total_seconds()
                if time_since_analysis > 3600:  # 1 hour threshold
                    logger.warning(f"[WARN]  No analysis for {time_since_analysis:.0f} seconds")
            
            logger.debug("[SUCCESS] Health check passed")
            
        except Exception as e:
            logger.error(f"[ERROR] Health check failed: {e}")
    
    async def _performance_monitor_loop(self):
        """Performance monitoring loop"""
        while self.running:
            try:
                await self._log_performance_metrics()
                await asyncio.sleep(300)  # 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Performance monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _log_performance_metrics(self):
        """Log performance metrics"""
        try:
            if not self.agent:
                return
            
            metrics = self.agent.performance_metrics
            uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
            
            logger.info(
                f"[STATUS] Performance Metrics - "
                f"Uptime: {uptime:.0f}s, "
                f"Trades: {metrics.get('trades_analyzed', 0)}, "
                f"Strategies: {metrics.get('strategies_tracked', 0)}, "
                f"Memory: {metrics.get('memory_usage_mb', 0):.1f}MB, "
                f"Processing: {metrics.get('processing_time_ms', 0):.1f}ms"
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error logging performance metrics: {e}")

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Performance Analysis Agent Runner")
    parser.add_argument(
        "--config",
        default="config/performance_analysis_config.yaml",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create and run the runner
    runner = PerformanceAnalysisRunner(args.config)
    
    try:
        # Initialize
        if not await runner.initialize():
            logger.error("[ERROR] Failed to initialize Performance Analysis Agent Runner")
            return 1
        
        # Run
        await runner.run()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("[STOP] Received keyboard interrupt")
        await runner.shutdown()
        return 0
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
