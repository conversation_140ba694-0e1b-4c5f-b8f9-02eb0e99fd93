#!/usr/bin/env python3
"""
Advanced Order Matching Engine

Handles complex order matching scenarios:
- Partial fills across multiple executions
- Order modifications and cancellations
- Multi-leg orders (brackets, covers)
- Order state transitions
- Fill aggregation and reporting

Features:
- Real-time order state tracking
- Partial fill detection and aggregation
- Order lifecycle management
- Advanced matching algorithms
- Performance analytics
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import uuid

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """Order status types"""
    PENDING = "pending"
    OPEN = "open"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    MODIFIED = "modified"

class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    STOP_LOSS_MARKET = "stop_loss_market"
    BRACKET = "bracket"
    COVER = "cover"

class FillType(Enum):
    """Fill types"""
    COMPLETE = "complete"
    PARTIAL = "partial"
    OVERFILL = "overfill"  # Rare but possible

@dataclass
class OrderRecord:
    """Represents an order in the system"""
    order_id: str
    parent_order_id: Optional[str]
    symbol: str
    side: str  # BUY/SELL
    order_type: OrderType
    quantity: int
    price: Optional[float]
    stop_price: Optional[float]
    status: OrderStatus
    timestamp: datetime
    strategy: Optional[str] = None
    signal_id: Optional[str] = None
    source: str = "unknown"
    
    # Execution tracking
    filled_quantity: int = 0
    remaining_quantity: int = 0
    avg_fill_price: float = 0.0
    total_commission: float = 0.0
    
    # Metadata
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        self.remaining_quantity = self.quantity - self.filled_quantity

@dataclass
class FillRecord:
    """Represents a fill/execution"""
    fill_id: str
    order_id: str
    symbol: str
    side: str
    quantity: int
    price: float
    timestamp: datetime
    commission: float = 0.0
    fill_type: FillType = FillType.COMPLETE
    source: str = "unknown"
    raw_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MatchResult:
    """Result of order-fill matching"""
    order: OrderRecord
    fills: List[FillRecord]
    total_filled_quantity: int
    total_commission: float
    avg_fill_price: float
    is_complete: bool
    match_confidence: float
    issues: List[str] = field(default_factory=list)

class OrderMatcher:
    """
    Advanced order matching engine with partial fill handling
    """
    
    def __init__(self):
        # Order and fill storage
        self.orders: Dict[str, OrderRecord] = {}
        self.fills: Dict[str, FillRecord] = {}
        self.orphaned_fills: List[FillRecord] = []
        
        # Matching results
        self.match_results: Dict[str, MatchResult] = {}
        
        # Performance tracking
        self.metrics = {
            'total_orders': 0,
            'total_fills': 0,
            'matched_orders': 0,
            'partial_fills_detected': 0,
            'orphaned_fills': 0,
            'matching_accuracy': 0.0,
            'last_match_time': None
        }
        
        # Configuration
        self.config = {
            'time_tolerance_seconds': 300,  # 5 minutes
            'price_tolerance_percent': 0.1,
            'enable_fuzzy_matching': True,
            'max_partial_fill_gap_minutes': 30
        }
        
        logger.info("OrderMatcher initialized")

    def add_order(self, order: OrderRecord) -> bool:
        """Add an order to the matching engine"""
        try:
            if order.order_id in self.orders:
                # Update existing order
                existing_order = self.orders[order.order_id]
                if order.timestamp > existing_order.timestamp:
                    self.orders[order.order_id] = order
                    logger.debug(f"Updated order: {order.order_id}")
                else:
                    logger.debug(f"Ignoring older order update: {order.order_id}")
            else:
                # New order
                self.orders[order.order_id] = order
                self.metrics['total_orders'] += 1
                logger.debug(f"Added new order: {order.order_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding order {order.order_id}: {e}")
            return False

    def add_fill(self, fill: FillRecord) -> bool:
        """Add a fill to the matching engine"""
        try:
            if fill.fill_id in self.fills:
                logger.debug(f"Duplicate fill ignored: {fill.fill_id}")
                return False
            
            self.fills[fill.fill_id] = fill
            self.metrics['total_fills'] += 1
            logger.debug(f"Added fill: {fill.fill_id} for order: {fill.order_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding fill {fill.fill_id}: {e}")
            return False

    def match_orders_and_fills(self) -> Dict[str, MatchResult]:
        """
        Perform comprehensive order-fill matching
        """
        try:
            logger.info("Starting order-fill matching...")
            start_time = datetime.now()
            
            # Clear previous results
            self.match_results.clear()
            self.orphaned_fills.clear()
            
            # Group fills by order_id for direct matching
            fills_by_order = defaultdict(list)
            unmatched_fills = []
            
            for fill in self.fills.values():
                if fill.order_id and fill.order_id in self.orders:
                    fills_by_order[fill.order_id].append(fill)
                else:
                    unmatched_fills.append(fill)
            
            # Match orders with their direct fills
            for order_id, order in self.orders.items():
                order_fills = fills_by_order.get(order_id, [])
                
                if order_fills:
                    match_result = self._create_match_result(order, order_fills)
                    self.match_results[order_id] = match_result
                    self.metrics['matched_orders'] += 1
                else:
                    # Try fuzzy matching for unmatched orders
                    if self.config['enable_fuzzy_matching']:
                        fuzzy_fills = self._find_fuzzy_matches(order, unmatched_fills)
                        if fuzzy_fills:
                            match_result = self._create_match_result(order, fuzzy_fills)
                            match_result.issues.append("Matched using fuzzy matching")
                            self.match_results[order_id] = match_result
                            self.metrics['matched_orders'] += 1
                            
                            # Remove matched fills from unmatched list
                            for fill in fuzzy_fills:
                                if fill in unmatched_fills:
                                    unmatched_fills.remove(fill)
            
            # Store orphaned fills
            self.orphaned_fills = unmatched_fills
            self.metrics['orphaned_fills'] = len(self.orphaned_fills)
            
            # Update metrics
            self._update_matching_metrics()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Order-fill matching completed in {processing_time:.2f}s")
            logger.info(f"Matched {self.metrics['matched_orders']} orders, "
                       f"{self.metrics['orphaned_fills']} orphaned fills")
            
            return self.match_results
            
        except Exception as e:
            logger.error(f"Error in order-fill matching: {e}")
            return {}

    def _create_match_result(self, order: OrderRecord, fills: List[FillRecord]) -> MatchResult:
        """Create a match result from order and fills"""
        try:
            # Sort fills by timestamp
            sorted_fills = sorted(fills, key=lambda f: f.timestamp)
            
            # Calculate aggregated metrics
            total_filled_qty = sum(fill.quantity for fill in sorted_fills)
            total_commission = sum(fill.commission for fill in sorted_fills)
            
            # Calculate average fill price (weighted by quantity)
            if total_filled_qty > 0:
                weighted_price_sum = sum(fill.price * fill.quantity for fill in sorted_fills)
                avg_fill_price = weighted_price_sum / total_filled_qty
            else:
                avg_fill_price = 0.0
            
            # Determine if order is complete
            is_complete = total_filled_qty >= order.quantity
            
            # Calculate match confidence
            confidence = self._calculate_match_confidence(order, sorted_fills)
            
            # Detect issues
            issues = []
            
            # Check for overfill
            if total_filled_qty > order.quantity:
                issues.append(f"Overfill detected: {total_filled_qty} > {order.quantity}")
            
            # Check for significant price deviation
            if order.price and avg_fill_price > 0:
                price_deviation = abs(order.price - avg_fill_price) / order.price
                if price_deviation > self.config['price_tolerance_percent'] / 100:
                    issues.append(f"Significant price deviation: {price_deviation:.2%}")
            
            # Check for time gaps in partial fills
            if len(sorted_fills) > 1:
                max_gap = self._find_max_time_gap(sorted_fills)
                if max_gap > self.config['max_partial_fill_gap_minutes']:
                    issues.append(f"Large time gap between fills: {max_gap:.1f} minutes")
            
            # Update order with fill information
            order.filled_quantity = total_filled_qty
            order.remaining_quantity = max(0, order.quantity - total_filled_qty)
            order.avg_fill_price = avg_fill_price
            order.total_commission = total_commission
            
            # Update order status
            if is_complete:
                order.status = OrderStatus.FILLED
            elif total_filled_qty > 0:
                order.status = OrderStatus.PARTIALLY_FILLED
                self.metrics['partial_fills_detected'] += 1
            
            return MatchResult(
                order=order,
                fills=sorted_fills,
                total_filled_quantity=total_filled_qty,
                total_commission=total_commission,
                avg_fill_price=avg_fill_price,
                is_complete=is_complete,
                match_confidence=confidence,
                issues=issues
            )
            
        except Exception as e:
            logger.error(f"Error creating match result for order {order.order_id}: {e}")
            return MatchResult(
                order=order,
                fills=[],
                total_filled_quantity=0,
                total_commission=0.0,
                avg_fill_price=0.0,
                is_complete=False,
                match_confidence=0.0,
                issues=[f"Error creating match result: {e}"]
            )

    def _find_fuzzy_matches(self, order: OrderRecord, unmatched_fills: List[FillRecord]) -> List[FillRecord]:
        """Find potential matches using fuzzy matching"""
        potential_matches = []
        
        for fill in unmatched_fills:
            match_score = self._calculate_fuzzy_match_score(order, fill)
            if match_score > 0.7:  # Threshold for fuzzy matching
                potential_matches.append((fill, match_score))
        
        # Sort by match score and return top matches
        potential_matches.sort(key=lambda x: x[1], reverse=True)
        
        # Return fills that could reasonably belong to this order
        matched_fills = []
        remaining_quantity = order.quantity
        
        for fill, score in potential_matches:
            if remaining_quantity <= 0:
                break
            
            if fill.quantity <= remaining_quantity:
                matched_fills.append(fill)
                remaining_quantity -= fill.quantity
            elif len(matched_fills) == 0:  # Accept partial match if it's the only option
                matched_fills.append(fill)
                break
        
        return matched_fills

    def _calculate_fuzzy_match_score(self, order: OrderRecord, fill: FillRecord) -> float:
        """Calculate fuzzy matching score between order and fill"""
        score = 0.0
        
        # Symbol match (required)
        if order.symbol != fill.symbol:
            return 0.0
        score += 30
        
        # Side match (required)
        if order.side != fill.side:
            return 0.0
        score += 30
        
        # Time proximity (within tolerance)
        time_diff = abs((order.timestamp - fill.timestamp).total_seconds())
        if time_diff <= self.config['time_tolerance_seconds']:
            time_score = (1 - time_diff / self.config['time_tolerance_seconds']) * 20
            score += time_score
        
        # Price proximity (if order has price)
        if order.price and fill.price:
            price_diff = abs(order.price - fill.price) / max(order.price, fill.price)
            if price_diff <= self.config['price_tolerance_percent'] / 100:
                price_score = (1 - price_diff / (self.config['price_tolerance_percent'] / 100)) * 15
                score += price_score
        
        # Quantity reasonableness
        if fill.quantity <= order.quantity:
            qty_score = 5
            score += qty_score
        
        return score / 100  # Normalize to 0-1

    def _calculate_match_confidence(self, order: OrderRecord, fills: List[FillRecord]) -> float:
        """Calculate confidence score for order-fill match"""
        confidence = 1.0
        
        # Penalize for missing order_id in fills
        direct_matches = sum(1 for fill in fills if fill.order_id == order.order_id)
        if direct_matches < len(fills):
            confidence *= 0.8
        
        # Penalize for time gaps
        if len(fills) > 1:
            max_gap = self._find_max_time_gap(fills)
            if max_gap > self.config['max_partial_fill_gap_minutes']:
                confidence *= 0.7
        
        # Penalize for price deviations
        if order.price and fills:
            avg_fill_price = sum(f.price * f.quantity for f in fills) / sum(f.quantity for f in fills)
            price_deviation = abs(order.price - avg_fill_price) / order.price
            if price_deviation > self.config['price_tolerance_percent'] / 100:
                confidence *= 0.9
        
        return max(0.0, min(1.0, confidence))

    def _find_max_time_gap(self, fills: List[FillRecord]) -> float:
        """Find maximum time gap between consecutive fills in minutes"""
        if len(fills) < 2:
            return 0.0
        
        sorted_fills = sorted(fills, key=lambda f: f.timestamp)
        max_gap = 0.0
        
        for i in range(1, len(sorted_fills)):
            gap = (sorted_fills[i].timestamp - sorted_fills[i-1].timestamp).total_seconds() / 60
            max_gap = max(max_gap, gap)
        
        return max_gap

    def _update_matching_metrics(self):
        """Update matching performance metrics"""
        total_orders = len(self.orders)
        matched_orders = len(self.match_results)
        
        if total_orders > 0:
            self.metrics['matching_accuracy'] = matched_orders / total_orders
        
        self.metrics['last_match_time'] = datetime.now()

    def get_partial_fills_summary(self) -> Dict[str, Any]:
        """Get summary of partial fills"""
        partial_fills = []
        
        for match_result in self.match_results.values():
            if not match_result.is_complete and match_result.total_filled_quantity > 0:
                partial_fills.append({
                    'order_id': match_result.order.order_id,
                    'symbol': match_result.order.symbol,
                    'total_quantity': match_result.order.quantity,
                    'filled_quantity': match_result.total_filled_quantity,
                    'remaining_quantity': match_result.order.remaining_quantity,
                    'fill_percentage': (match_result.total_filled_quantity / match_result.order.quantity) * 100,
                    'fills_count': len(match_result.fills),
                    'avg_fill_price': match_result.avg_fill_price
                })
        
        return {
            'partial_fills_count': len(partial_fills),
            'partial_fills': partial_fills,
            'total_partial_quantity': sum(pf['filled_quantity'] for pf in partial_fills),
            'avg_fill_percentage': np.mean([pf['fill_percentage'] for pf in partial_fills]) if partial_fills else 0
        }

    def get_orphaned_fills_summary(self) -> Dict[str, Any]:
        """Get summary of orphaned fills"""
        if not self.orphaned_fills:
            return {'orphaned_fills_count': 0, 'orphaned_fills': []}
        
        orphaned_summary = []
        for fill in self.orphaned_fills:
            orphaned_summary.append({
                'fill_id': fill.fill_id,
                'order_id': fill.order_id,
                'symbol': fill.symbol,
                'side': fill.side,
                'quantity': fill.quantity,
                'price': fill.price,
                'timestamp': fill.timestamp,
                'source': fill.source
            })
        
        return {
            'orphaned_fills_count': len(self.orphaned_fills),
            'orphaned_fills': orphaned_summary,
            'total_orphaned_quantity': sum(fill.quantity for fill in self.orphaned_fills),
            'total_orphaned_value': sum(fill.quantity * fill.price for fill in self.orphaned_fills)
        }

    def export_matching_report(self) -> pl.DataFrame:
        """Export matching results as DataFrame"""
        try:
            records = []
            
            for match_result in self.match_results.values():
                order = match_result.order
                
                base_record = {
                    'order_id': order.order_id,
                    'symbol': order.symbol,
                    'side': order.side,
                    'order_type': order.order_type.value,
                    'order_quantity': order.quantity,
                    'order_price': order.price,
                    'order_timestamp': order.timestamp,
                    'filled_quantity': match_result.total_filled_quantity,
                    'remaining_quantity': order.remaining_quantity,
                    'avg_fill_price': match_result.avg_fill_price,
                    'total_commission': match_result.total_commission,
                    'is_complete': match_result.is_complete,
                    'fills_count': len(match_result.fills),
                    'match_confidence': match_result.match_confidence,
                    'issues_count': len(match_result.issues),
                    'issues': '; '.join(match_result.issues),
                    'strategy': order.strategy,
                    'source': order.source
                }
                
                records.append(base_record)
            
            return pl.DataFrame(records)
            
        except Exception as e:
            logger.error(f"Error exporting matching report: {e}")
            return pl.DataFrame()

    def get_matching_metrics(self) -> Dict[str, Any]:
        """Get current matching metrics"""
        return self.metrics.copy()

    def clear_old_data(self, days_to_keep: int = 7):
        """Clear old order and fill data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # Clear old orders
            old_orders = [oid for oid, order in self.orders.items() if order.timestamp < cutoff_date]
            for oid in old_orders:
                del self.orders[oid]
            
            # Clear old fills
            old_fills = [fid for fid, fill in self.fills.items() if fill.timestamp < cutoff_date]
            for fid in old_fills:
                del self.fills[fid]
            
            # Clear old match results
            old_matches = [oid for oid, match in self.match_results.items() 
                          if match.order.timestamp < cutoff_date]
            for oid in old_matches:
                del self.match_results[oid]
            
            if old_orders or old_fills:
                logger.info(f"Cleared {len(old_orders)} old orders and {len(old_fills)} old fills")
                
        except Exception as e:
            logger.error(f"Error clearing old data: {e}")
