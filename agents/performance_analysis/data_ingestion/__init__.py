#!/usr/bin/env python3
"""
Data Ingestion Module for Performance Analysis Agent

This module handles all data sources with real-time streaming and reconciliation capabilities.
Provides robust trade reconciliation, historical data backfilling, and data validation.
"""

from .stream_processor import StreamProcessor
from .trade_reconciler import TradeReconciler
from .historical_backfiller import HistoricalBackfiller
from .data_validator import DataValidator
from .order_matcher import OrderMatcher

__all__ = [
    'StreamProcessor',
    'TradeReconciler', 
    'HistoricalBackfiller',
    'DataValidator',
    'OrderMatcher'
]
