#!/usr/bin/env python3
"""
Data Validation Module

Validates and cleans incoming data from multiple sources:
- Schema validation
- Data type checking
- Range validation
- Business rule validation
- Data quality scoring
- Anomaly detection

Features:
- Configurable validation rules
- Real-time validation
- Data quality metrics
- Automatic data cleaning
- Validation reporting
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import re
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)

class ValidationSeverity(Enum):
    """Validation issue severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ValidationResult(Enum):
    """Validation result status"""
    PASSED = "passed"
    FAILED = "failed"
    FIXED = "fixed"
    SKIPPED = "skipped"

@dataclass
class ValidationIssue:
    """Represents a validation issue"""
    field_name: str
    issue_type: str
    severity: ValidationSeverity
    message: str
    original_value: Any
    suggested_value: Any = None
    rule_name: str = ""

@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    record_id: str
    source: str
    timestamp: datetime
    overall_result: ValidationResult
    quality_score: float  # 0-100
    issues: List[ValidationIssue] = field(default_factory=list)
    fixes_applied: int = 0
    validation_time_ms: float = 0.0

@dataclass
class ValidationRule:
    """Defines a validation rule"""
    name: str
    field_name: str
    rule_type: str  # 'required', 'type', 'range', 'pattern', 'custom'
    parameters: Dict[str, Any]
    severity: ValidationSeverity
    auto_fix: bool = False
    fix_function: Optional[Callable] = None

class DataValidator:
    """
    Comprehensive data validation engine with configurable rules
    """
    
    def __init__(self):
        self.validation_rules: Dict[str, List[ValidationRule]] = {}
        self.custom_validators: Dict[str, Callable] = {}
        
        # Validation statistics
        self.stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'fixed_validations': 0,
            'avg_quality_score': 0.0,
            'last_validation_time': None
        }
        
        # Initialize default rules
        self._initialize_default_rules()
        
        logger.info("DataValidator initialized")

    def _initialize_default_rules(self):
        """Initialize default validation rules for trading data"""
        
        # Trade data validation rules
        trade_rules = [
            ValidationRule(
                name="trade_id_required",
                field_name="trade_id",
                rule_type="required",
                parameters={},
                severity=ValidationSeverity.CRITICAL
            ),
            ValidationRule(
                name="symbol_format",
                field_name="symbol",
                rule_type="pattern",
                parameters={"pattern": r"^[A-Z0-9\-&]+$"},
                severity=ValidationSeverity.ERROR
            ),
            ValidationRule(
                name="side_values",
                field_name="side",
                rule_type="enum",
                parameters={"allowed_values": ["BUY", "SELL"]},
                severity=ValidationSeverity.CRITICAL
            ),
            ValidationRule(
                name="quantity_positive",
                field_name="quantity",
                rule_type="range",
                parameters={"min_value": 1, "max_value": 1000000},
                severity=ValidationSeverity.ERROR
            ),
            ValidationRule(
                name="price_positive",
                field_name="price",
                rule_type="range",
                parameters={"min_value": 0.01, "max_value": 100000},
                severity=ValidationSeverity.ERROR
            ),
            ValidationRule(
                name="timestamp_reasonable",
                field_name="timestamp",
                rule_type="custom",
                parameters={"validator": "validate_timestamp"},
                severity=ValidationSeverity.WARNING
            )
        ]
        
        self.validation_rules["trade"] = trade_rules
        
        # Signal data validation rules
        signal_rules = [
            ValidationRule(
                name="signal_id_required",
                field_name="signal_id",
                rule_type="required",
                parameters={},
                severity=ValidationSeverity.CRITICAL
            ),
            ValidationRule(
                name="strategy_name_format",
                field_name="strategy",
                rule_type="pattern",
                parameters={"pattern": r"^[a-zA-Z0-9_\-]+$"},
                severity=ValidationSeverity.WARNING
            ),
            ValidationRule(
                name="confidence_range",
                field_name="confidence",
                rule_type="range",
                parameters={"min_value": 0.0, "max_value": 1.0},
                severity=ValidationSeverity.WARNING,
                auto_fix=True,
                fix_function=lambda x: max(0.0, min(1.0, float(x)))
            )
        ]
        
        self.validation_rules["signal"] = signal_rules
        
        # Register custom validators
        self.custom_validators["validate_timestamp"] = self._validate_timestamp
        self.custom_validators["validate_market_hours"] = self._validate_market_hours

    def add_validation_rule(self, data_type: str, rule: ValidationRule):
        """Add a custom validation rule"""
        if data_type not in self.validation_rules:
            self.validation_rules[data_type] = []
        
        self.validation_rules[data_type].append(rule)
        logger.info(f"Added validation rule '{rule.name}' for data type '{data_type}'")

    def validate_record(self, data: Dict[str, Any], data_type: str, record_id: str = None) -> ValidationReport:
        """
        Validate a single data record
        
        Args:
            data: Dictionary containing the data to validate
            data_type: Type of data (e.g., 'trade', 'signal')
            record_id: Optional record identifier
            
        Returns:
            ValidationReport with validation results
        """
        start_time = datetime.now()
        
        if record_id is None:
            record_id = data.get('id', f"record_{int(datetime.now().timestamp())}")
        
        report = ValidationReport(
            record_id=record_id,
            source=data.get('source', 'unknown'),
            timestamp=start_time,
            overall_result=ValidationResult.PASSED,
            quality_score=100.0
        )
        
        try:
            # Get validation rules for this data type
            rules = self.validation_rules.get(data_type, [])
            
            if not rules:
                logger.warning(f"No validation rules found for data type: {data_type}")
                report.overall_result = ValidationResult.SKIPPED
                return report
            
            total_rules = len(rules)
            failed_rules = 0
            critical_failures = 0
            
            # Apply each validation rule
            for rule in rules:
                try:
                    issue = self._apply_validation_rule(data, rule)
                    
                    if issue:
                        report.issues.append(issue)
                        
                        # Count failures by severity
                        if issue.severity == ValidationSeverity.CRITICAL:
                            critical_failures += 1
                            failed_rules += 1
                        elif issue.severity == ValidationSeverity.ERROR:
                            failed_rules += 1
                        
                        # Apply auto-fix if available
                        if rule.auto_fix and rule.fix_function and issue.suggested_value is not None:
                            try:
                                data[rule.field_name] = issue.suggested_value
                                report.fixes_applied += 1
                                logger.debug(f"Auto-fixed {rule.field_name}: {issue.original_value} -> {issue.suggested_value}")
                            except Exception as e:
                                logger.error(f"Auto-fix failed for {rule.field_name}: {e}")
                
                except Exception as e:
                    logger.error(f"Error applying validation rule '{rule.name}': {e}")
                    report.issues.append(ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="validation_error",
                        severity=ValidationSeverity.ERROR,
                        message=f"Validation rule error: {e}",
                        original_value=data.get(rule.field_name),
                        rule_name=rule.name
                    ))
                    failed_rules += 1
            
            # Calculate quality score
            if total_rules > 0:
                base_score = ((total_rules - failed_rules) / total_rules) * 100
                # Penalize critical failures more heavily
                critical_penalty = critical_failures * 20
                report.quality_score = max(0.0, base_score - critical_penalty)
            
            # Determine overall result
            if critical_failures > 0:
                report.overall_result = ValidationResult.FAILED
            elif report.fixes_applied > 0:
                report.overall_result = ValidationResult.FIXED
            elif failed_rules > 0:
                report.overall_result = ValidationResult.FAILED
            else:
                report.overall_result = ValidationResult.PASSED
            
            # Update statistics
            self._update_validation_stats(report)
            
        except Exception as e:
            logger.error(f"Error validating record {record_id}: {e}")
            report.overall_result = ValidationResult.FAILED
            report.quality_score = 0.0
            report.issues.append(ValidationIssue(
                field_name="general",
                issue_type="validation_exception",
                severity=ValidationSeverity.CRITICAL,
                message=f"Validation exception: {e}",
                original_value=None
            ))
        
        finally:
            # Calculate validation time
            end_time = datetime.now()
            report.validation_time_ms = (end_time - start_time).total_seconds() * 1000
        
        return report

    def _apply_validation_rule(self, data: Dict[str, Any], rule: ValidationRule) -> Optional[ValidationIssue]:
        """Apply a single validation rule"""
        field_value = data.get(rule.field_name)
        
        # Required field validation
        if rule.rule_type == "required":
            if field_value is None or field_value == "":
                return ValidationIssue(
                    field_name=rule.field_name,
                    issue_type="missing_required",
                    severity=rule.severity,
                    message=f"Required field '{rule.field_name}' is missing or empty",
                    original_value=field_value,
                    rule_name=rule.name
                )
        
        # Skip other validations if field is None (unless required)
        if field_value is None:
            return None
        
        # Type validation
        if rule.rule_type == "type":
            expected_type = rule.parameters.get("expected_type")
            if expected_type and not isinstance(field_value, expected_type):
                try:
                    # Try to convert
                    converted_value = expected_type(field_value)
                    return ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="type_mismatch",
                        severity=rule.severity,
                        message=f"Field '{rule.field_name}' should be {expected_type.__name__}",
                        original_value=field_value,
                        suggested_value=converted_value,
                        rule_name=rule.name
                    )
                except (ValueError, TypeError):
                    return ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="type_conversion_failed",
                        severity=rule.severity,
                        message=f"Cannot convert '{rule.field_name}' to {expected_type.__name__}",
                        original_value=field_value,
                        rule_name=rule.name
                    )
        
        # Range validation
        elif rule.rule_type == "range":
            try:
                numeric_value = float(field_value)
                min_val = rule.parameters.get("min_value")
                max_val = rule.parameters.get("max_value")
                
                if min_val is not None and numeric_value < min_val:
                    suggested_value = min_val if rule.auto_fix else None
                    return ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="below_minimum",
                        severity=rule.severity,
                        message=f"Field '{rule.field_name}' value {numeric_value} is below minimum {min_val}",
                        original_value=field_value,
                        suggested_value=suggested_value,
                        rule_name=rule.name
                    )
                
                if max_val is not None and numeric_value > max_val:
                    suggested_value = max_val if rule.auto_fix else None
                    return ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="above_maximum",
                        severity=rule.severity,
                        message=f"Field '{rule.field_name}' value {numeric_value} is above maximum {max_val}",
                        original_value=field_value,
                        suggested_value=suggested_value,
                        rule_name=rule.name
                    )
            except (ValueError, TypeError):
                return ValidationIssue(
                    field_name=rule.field_name,
                    issue_type="non_numeric",
                    severity=rule.severity,
                    message=f"Field '{rule.field_name}' should be numeric for range validation",
                    original_value=field_value,
                    rule_name=rule.name
                )
        
        # Pattern validation
        elif rule.rule_type == "pattern":
            pattern = rule.parameters.get("pattern")
            if pattern and not re.match(pattern, str(field_value)):
                return ValidationIssue(
                    field_name=rule.field_name,
                    issue_type="pattern_mismatch",
                    severity=rule.severity,
                    message=f"Field '{rule.field_name}' does not match required pattern: {pattern}",
                    original_value=field_value,
                    rule_name=rule.name
                )
        
        # Enum validation
        elif rule.rule_type == "enum":
            allowed_values = rule.parameters.get("allowed_values", [])
            if field_value not in allowed_values:
                return ValidationIssue(
                    field_name=rule.field_name,
                    issue_type="invalid_enum_value",
                    severity=rule.severity,
                    message=f"Field '{rule.field_name}' value '{field_value}' not in allowed values: {allowed_values}",
                    original_value=field_value,
                    rule_name=rule.name
                )
        
        # Custom validation
        elif rule.rule_type == "custom":
            validator_name = rule.parameters.get("validator")
            if validator_name in self.custom_validators:
                try:
                    validator_func = self.custom_validators[validator_name]
                    result = validator_func(field_value, rule.parameters)
                    if result is not True:
                        return ValidationIssue(
                            field_name=rule.field_name,
                            issue_type="custom_validation_failed",
                            severity=rule.severity,
                            message=result if isinstance(result, str) else f"Custom validation failed for '{rule.field_name}'",
                            original_value=field_value,
                            rule_name=rule.name
                        )
                except Exception as e:
                    return ValidationIssue(
                        field_name=rule.field_name,
                        issue_type="custom_validator_error",
                        severity=rule.severity,
                        message=f"Custom validator error: {e}",
                        original_value=field_value,
                        rule_name=rule.name
                    )
        
        return None

    def _validate_timestamp(self, value: Any, parameters: Dict[str, Any]) -> Union[bool, str]:
        """Custom validator for timestamp fields"""
        try:
            if isinstance(value, datetime):
                timestamp = value
            elif isinstance(value, str):
                timestamp = datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                return f"Timestamp must be datetime object or ISO string, got {type(value)}"
            
            now = datetime.now()
            
            # Check if timestamp is too far in the future
            if timestamp > now + timedelta(hours=1):
                return f"Timestamp is too far in the future: {timestamp}"
            
            # Check if timestamp is too old
            if timestamp < now - timedelta(days=30):
                return f"Timestamp is too old: {timestamp}"
            
            return True
            
        except Exception as e:
            return f"Invalid timestamp format: {e}"

    def _validate_market_hours(self, value: Any, parameters: Dict[str, Any]) -> Union[bool, str]:
        """Custom validator for market hours"""
        try:
            if isinstance(value, datetime):
                timestamp = value
            else:
                timestamp = datetime.fromisoformat(str(value))
            
            # Check if it's a weekday
            if timestamp.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return "Trade timestamp is on weekend"
            
            # Check market hours (9:15 AM to 3:30 PM IST)
            time_only = timestamp.time()
            market_open = datetime.strptime("09:15", "%H:%M").time()
            market_close = datetime.strptime("15:30", "%H:%M").time()
            
            if not (market_open <= time_only <= market_close):
                return f"Trade timestamp {time_only} is outside market hours (09:15-15:30)"
            
            return True
            
        except Exception as e:
            return f"Error validating market hours: {e}"

    def _update_validation_stats(self, report: ValidationReport):
        """Update validation statistics"""
        self.stats['total_validations'] += 1
        
        if report.overall_result == ValidationResult.PASSED:
            self.stats['passed_validations'] += 1
        elif report.overall_result == ValidationResult.FAILED:
            self.stats['failed_validations'] += 1
        elif report.overall_result == ValidationResult.FIXED:
            self.stats['fixed_validations'] += 1
        
        # Update average quality score
        total_score = self.stats['avg_quality_score'] * (self.stats['total_validations'] - 1)
        self.stats['avg_quality_score'] = (total_score + report.quality_score) / self.stats['total_validations']
        
        self.stats['last_validation_time'] = datetime.now()

    def get_validation_stats(self) -> Dict[str, Any]:
        """Get current validation statistics"""
        return self.stats.copy()

    def export_validation_report(self, reports: List[ValidationReport]) -> pl.DataFrame:
        """Export validation reports as DataFrame"""
        try:
            records = []
            
            for report in reports:
                base_record = {
                    'record_id': report.record_id,
                    'source': report.source,
                    'timestamp': report.timestamp,
                    'overall_result': report.overall_result.value,
                    'quality_score': report.quality_score,
                    'issues_count': len(report.issues),
                    'fixes_applied': report.fixes_applied,
                    'validation_time_ms': report.validation_time_ms
                }
                
                # Add issue details
                if report.issues:
                    for i, issue in enumerate(report.issues):
                        issue_record = base_record.copy()
                        issue_record.update({
                            'issue_number': i + 1,
                            'field_name': issue.field_name,
                            'issue_type': issue.issue_type,
                            'severity': issue.severity.value,
                            'message': issue.message,
                            'original_value': str(issue.original_value),
                            'suggested_value': str(issue.suggested_value) if issue.suggested_value is not None else None,
                            'rule_name': issue.rule_name
                        })
                        records.append(issue_record)
                else:
                    records.append(base_record)
            
            return pl.DataFrame(records)
            
        except Exception as e:
            logger.error(f"Error exporting validation report: {e}")
            return pl.DataFrame()
