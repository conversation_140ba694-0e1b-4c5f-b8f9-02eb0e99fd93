#!/usr/bin/env python3
"""
Core Performance Metrics Calculator

Calculates fundamental performance metrics including:
- Return on Investment (ROI)
- Sharpe Ratio
- Win Rate and Accuracy
- Expectancy and Profit Factor
- Average Win/Loss
- Total Return and PnL
- Trade Statistics
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import math

logger = logging.getLogger(__name__)

@dataclass
class CoreMetrics:
    """Core performance metrics data structure"""
    # Return metrics
    total_return: float = 0.0
    roi_percent: float = 0.0
    annualized_return: float = 0.0
    
    # Trade statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    
    # Win/Loss metrics
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Performance ratios
    profit_factor: float = 0.0
    expectancy: float = 0.0
    sharpe_ratio: float = 0.0
    
    # PnL metrics
    total_pnl: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    net_profit: float = 0.0
    
    # Time metrics
    avg_holding_period_hours: float = 0.0
    total_trading_days: int = 0
    trades_per_day: float = 0.0
    
    # Risk metrics
    volatility: float = 0.0
    downside_deviation: float = 0.0

class CoreMetricsCalculator:
    """
    Calculator for core performance metrics
    """
    
    def __init__(self, risk_free_rate: float = 0.06):
        """
        Initialize core metrics calculator
        
        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate
        logger.info("CoreMetricsCalculator initialized")

    def calculate_metrics(self, trades_df: pl.DataFrame, initial_capital: float = 100000) -> CoreMetrics:
        """
        Calculate comprehensive core metrics from trades data
        
        Args:
            trades_df: DataFrame with trade data
            initial_capital: Initial capital for ROI calculation
            
        Returns:
            CoreMetrics object with calculated metrics
        """
        try:
            if trades_df.is_empty():
                logger.warning("Empty trades DataFrame provided")
                return CoreMetrics()
            
            # Ensure required columns exist
            required_columns = ['pnl', 'entry_time', 'exit_time']
            missing_columns = [col for col in required_columns if col not in trades_df.columns]
            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return CoreMetrics()
            
            # Filter completed trades only
            completed_trades = trades_df.filter(
                (pl.col('exit_time').is_not_null()) & 
                (pl.col('pnl').is_not_null())
            )
            
            if completed_trades.is_empty():
                logger.warning("No completed trades found")
                return CoreMetrics()
            
            metrics = CoreMetrics()
            
            # Basic trade statistics
            metrics.total_trades = len(completed_trades)
            
            # PnL calculations
            pnl_values = completed_trades.select(pl.col('pnl')).to_series().to_list()
            metrics.total_pnl = sum(pnl_values)
            metrics.net_profit = metrics.total_pnl
            
            # Win/Loss analysis
            winning_trades = [pnl for pnl in pnl_values if pnl > 0]
            losing_trades = [pnl for pnl in pnl_values if pnl < 0]
            
            metrics.winning_trades = len(winning_trades)
            metrics.losing_trades = len(losing_trades)
            metrics.win_rate = (metrics.winning_trades / metrics.total_trades) * 100 if metrics.total_trades > 0 else 0
            
            # Win/Loss metrics
            if winning_trades:
                metrics.average_win = np.mean(winning_trades)
                metrics.largest_win = max(winning_trades)
                metrics.gross_profit = sum(winning_trades)
            
            if losing_trades:
                metrics.average_loss = abs(np.mean(losing_trades))
                metrics.largest_loss = abs(min(losing_trades))
                metrics.gross_loss = abs(sum(losing_trades))
            
            # Performance ratios
            metrics.profit_factor = (metrics.gross_profit / metrics.gross_loss) if metrics.gross_loss > 0 else float('inf')
            metrics.expectancy = (metrics.win_rate / 100 * metrics.average_win) - ((100 - metrics.win_rate) / 100 * metrics.average_loss)
            
            # Return calculations
            metrics.total_return = metrics.total_pnl
            metrics.roi_percent = (metrics.total_pnl / initial_capital) * 100 if initial_capital > 0 else 0
            
            # Time-based calculations
            self._calculate_time_metrics(completed_trades, metrics)
            
            # Risk calculations
            self._calculate_risk_metrics(completed_trades, metrics)
            
            logger.debug(f"Calculated core metrics for {metrics.total_trades} trades")
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating core metrics: {e}")
            return CoreMetrics()

    def _calculate_time_metrics(self, trades_df: pl.DataFrame, metrics: CoreMetrics):
        """Calculate time-based metrics"""
        try:
            # Calculate holding periods
            holding_periods = []
            
            for row in trades_df.iter_rows(named=True):
                if row['entry_time'] and row['exit_time']:
                    entry_time = row['entry_time']
                    exit_time = row['exit_time']
                    
                    if isinstance(entry_time, str):
                        entry_time = datetime.fromisoformat(entry_time)
                    if isinstance(exit_time, str):
                        exit_time = datetime.fromisoformat(exit_time)
                    
                    holding_period = (exit_time - entry_time).total_seconds() / 3600  # Hours
                    holding_periods.append(holding_period)
            
            if holding_periods:
                metrics.avg_holding_period_hours = np.mean(holding_periods)
            
            # Calculate trading period
            entry_times = trades_df.select(pl.col('entry_time')).to_series().to_list()
            entry_times = [t for t in entry_times if t is not None]
            
            if entry_times:
                if isinstance(entry_times[0], str):
                    entry_times = [datetime.fromisoformat(t) for t in entry_times]
                
                start_date = min(entry_times).date()
                end_date = max(entry_times).date()
                metrics.total_trading_days = (end_date - start_date).days + 1
                
                if metrics.total_trading_days > 0:
                    metrics.trades_per_day = metrics.total_trades / metrics.total_trading_days
                
                # Calculate annualized return
                if metrics.total_trading_days > 0:
                    years = metrics.total_trading_days / 365.25
                    if years > 0:
                        metrics.annualized_return = (metrics.roi_percent / 100 / years) * 100
            
        except Exception as e:
            logger.error(f"Error calculating time metrics: {e}")

    def _calculate_risk_metrics(self, trades_df: pl.DataFrame, metrics: CoreMetrics):
        """Calculate basic risk metrics"""
        try:
            # Calculate returns for volatility
            pnl_values = trades_df.select(pl.col('pnl')).to_series().to_list()
            
            if len(pnl_values) > 1:
                # Calculate volatility (standard deviation of returns)
                metrics.volatility = np.std(pnl_values)
                
                # Calculate downside deviation (volatility of negative returns only)
                negative_returns = [pnl for pnl in pnl_values if pnl < 0]
                if negative_returns:
                    metrics.downside_deviation = np.std(negative_returns)
                
                # Calculate Sharpe ratio
                if metrics.volatility > 0:
                    # Convert to daily returns for Sharpe calculation
                    mean_daily_return = np.mean(pnl_values)
                    daily_risk_free_rate = self.risk_free_rate / 365.25
                    
                    excess_return = mean_daily_return - daily_risk_free_rate
                    metrics.sharpe_ratio = excess_return / metrics.volatility
                    
                    # Annualize Sharpe ratio
                    metrics.sharpe_ratio *= np.sqrt(252)  # 252 trading days per year
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")

    def calculate_rolling_metrics(self, trades_df: pl.DataFrame, window_size: int = 30) -> pl.DataFrame:
        """
        Calculate rolling metrics over time
        
        Args:
            trades_df: DataFrame with trade data
            window_size: Rolling window size in number of trades
            
        Returns:
            DataFrame with rolling metrics
        """
        try:
            if trades_df.is_empty() or len(trades_df) < window_size:
                logger.warning("Insufficient data for rolling metrics calculation")
                return pl.DataFrame()
            
            # Sort by entry time
            sorted_trades = trades_df.sort('entry_time')
            
            rolling_metrics = []
            
            for i in range(window_size - 1, len(sorted_trades)):
                # Get window of trades
                window_trades = sorted_trades[i - window_size + 1:i + 1]
                
                # Calculate metrics for this window
                window_metrics = self.calculate_metrics(window_trades)
                
                # Add timestamp and window info
                rolling_record = {
                    'timestamp': sorted_trades[i]['entry_time'],
                    'window_end_trade': i + 1,
                    'total_trades': window_metrics.total_trades,
                    'win_rate': window_metrics.win_rate,
                    'total_pnl': window_metrics.total_pnl,
                    'roi_percent': window_metrics.roi_percent,
                    'sharpe_ratio': window_metrics.sharpe_ratio,
                    'profit_factor': window_metrics.profit_factor,
                    'expectancy': window_metrics.expectancy,
                    'volatility': window_metrics.volatility
                }
                
                rolling_metrics.append(rolling_record)
            
            return pl.DataFrame(rolling_metrics)
            
        except Exception as e:
            logger.error(f"Error calculating rolling metrics: {e}")
            return pl.DataFrame()

    def calculate_strategy_comparison(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate metrics by strategy for comparison
        
        Args:
            trades_df: DataFrame with trade data including strategy column
            
        Returns:
            DataFrame with metrics by strategy
        """
        try:
            if 'strategy' not in trades_df.columns:
                logger.warning("Strategy column not found in trades data")
                return pl.DataFrame()
            
            strategies = trades_df.select(pl.col('strategy')).unique().to_series().to_list()
            strategy_metrics = []
            
            for strategy in strategies:
                if strategy is None:
                    continue
                
                strategy_trades = trades_df.filter(pl.col('strategy') == strategy)
                metrics = self.calculate_metrics(strategy_trades)
                
                strategy_record = {
                    'strategy': strategy,
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'roi_percent': metrics.roi_percent,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'profit_factor': metrics.profit_factor,
                    'expectancy': metrics.expectancy,
                    'average_win': metrics.average_win,
                    'average_loss': metrics.average_loss,
                    'largest_win': metrics.largest_win,
                    'largest_loss': metrics.largest_loss,
                    'volatility': metrics.volatility,
                    'avg_holding_period_hours': metrics.avg_holding_period_hours
                }
                
                strategy_metrics.append(strategy_record)
            
            # Sort by total PnL descending
            strategy_df = pl.DataFrame(strategy_metrics)
            if not strategy_df.is_empty():
                strategy_df = strategy_df.sort('total_pnl', descending=True)
            
            return strategy_df
            
        except Exception as e:
            logger.error(f"Error calculating strategy comparison: {e}")
            return pl.DataFrame()

    def calculate_symbol_performance(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate metrics by symbol
        
        Args:
            trades_df: DataFrame with trade data including symbol column
            
        Returns:
            DataFrame with metrics by symbol
        """
        try:
            if 'symbol' not in trades_df.columns:
                logger.warning("Symbol column not found in trades data")
                return pl.DataFrame()
            
            symbols = trades_df.select(pl.col('symbol')).unique().to_series().to_list()
            symbol_metrics = []
            
            for symbol in symbols:
                if symbol is None:
                    continue
                
                symbol_trades = trades_df.filter(pl.col('symbol') == symbol)
                metrics = self.calculate_metrics(symbol_trades)
                
                symbol_record = {
                    'symbol': symbol,
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'roi_percent': metrics.roi_percent,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'profit_factor': metrics.profit_factor,
                    'expectancy': metrics.expectancy,
                    'volatility': metrics.volatility
                }
                
                symbol_metrics.append(symbol_record)
            
            # Sort by total PnL descending
            symbol_df = pl.DataFrame(symbol_metrics)
            if not symbol_df.is_empty():
                symbol_df = symbol_df.sort('total_pnl', descending=True)
            
            return symbol_df
            
        except Exception as e:
            logger.error(f"Error calculating symbol performance: {e}")
            return pl.DataFrame()

    def calculate_monthly_performance(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate monthly performance breakdown
        
        Args:
            trades_df: DataFrame with trade data
            
        Returns:
            DataFrame with monthly metrics
        """
        try:
            if trades_df.is_empty():
                return pl.DataFrame()
            
            # Add month-year column
            trades_with_month = trades_df.with_columns([
                pl.col('entry_time').dt.strftime('%Y-%m').alias('month_year')
            ])
            
            months = trades_with_month.select(pl.col('month_year')).unique().to_series().to_list()
            monthly_metrics = []
            
            for month in months:
                if month is None:
                    continue
                
                month_trades = trades_with_month.filter(pl.col('month_year') == month)
                metrics = self.calculate_metrics(month_trades)
                
                monthly_record = {
                    'month_year': month,
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'roi_percent': metrics.roi_percent,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'profit_factor': metrics.profit_factor,
                    'expectancy': metrics.expectancy
                }
                
                monthly_metrics.append(monthly_record)
            
            # Sort by month
            monthly_df = pl.DataFrame(monthly_metrics)
            if not monthly_df.is_empty():
                monthly_df = monthly_df.sort('month_year')
            
            return monthly_df
            
        except Exception as e:
            logger.error(f"Error calculating monthly performance: {e}")
            return pl.DataFrame()

    def get_performance_summary(self, metrics: CoreMetrics) -> Dict[str, Any]:
        """
        Get a formatted performance summary
        
        Args:
            metrics: CoreMetrics object
            
        Returns:
            Dictionary with formatted summary
        """
        return {
            'overview': {
                'total_trades': metrics.total_trades,
                'win_rate': f"{metrics.win_rate:.2f}%",
                'total_pnl': f"₹{metrics.total_pnl:,.2f}",
                'roi': f"{metrics.roi_percent:.2f}%"
            },
            'performance': {
                'sharpe_ratio': f"{metrics.sharpe_ratio:.3f}",
                'profit_factor': f"{metrics.profit_factor:.2f}",
                'expectancy': f"₹{metrics.expectancy:.2f}",
                'annualized_return': f"{metrics.annualized_return:.2f}%"
            },
            'trade_analysis': {
                'average_win': f"₹{metrics.average_win:,.2f}",
                'average_loss': f"₹{metrics.average_loss:,.2f}",
                'largest_win': f"₹{metrics.largest_win:,.2f}",
                'largest_loss': f"₹{metrics.largest_loss:,.2f}"
            },
            'risk_metrics': {
                'volatility': f"{metrics.volatility:.4f}",
                'downside_deviation': f"{metrics.downside_deviation:.4f}"
            },
            'time_analysis': {
                'avg_holding_period': f"{metrics.avg_holding_period_hours:.2f} hours",
                'trading_days': metrics.total_trading_days,
                'trades_per_day': f"{metrics.trades_per_day:.2f}"
            }
        }
