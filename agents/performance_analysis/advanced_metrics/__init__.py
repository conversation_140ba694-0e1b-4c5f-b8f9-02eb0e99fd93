#!/usr/bin/env python3
"""
Advanced Metrics Module for Performance Analysis Agent

This module provides comprehensive performance and risk metrics calculations including:
- Core performance metrics (ROI, Sharpe, win rate, etc.)
- Advanced risk metrics (Sortino, Calmar, VaR, CVaR)
- Drawdown analysis with duration
- Time-series analysis and rolling metrics
- Attribution analysis by factors
- Transaction cost analysis
- Market regime detection
"""

from .core_metrics import CoreMetricsCalculator
from .risk_metrics import RiskMetricsCalculator
from .drawdown_analyzer import DrawdownAnalyzer
from .time_series_analyzer import TimeSeriesAnalyzer
from .attribution_analyzer import AttributionAnalyzer
from .transaction_cost_analyzer import TransactionCostAnalyzer
from .regime_detector import RegimeDetector

__all__ = [
    'CoreMetricsCalculator',
    'RiskMetricsCalculator',
    'DrawdownAnalyzer',
    'TimeSeriesAnalyzer',
    'AttributionAnalyzer',
    'TransactionCostAnalyzer',
    'RegimeDetector'
]
