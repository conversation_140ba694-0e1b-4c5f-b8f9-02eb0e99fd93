#!/usr/bin/env python3
"""
Comprehensive Drawdown Analyzer

Provides detailed drawdown analysis including:
- Maximum drawdown calculation with proper duration
- Drawdown periods identification
- Recovery time analysis
- Underwater curve generation
- Drawdown distribution analysis
- Rolling drawdown metrics
- Drawdown clustering and patterns
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import math

logger = logging.getLogger(__name__)

@dataclass
class DrawdownPeriod:
    """Represents a single drawdown period"""
    start_date: datetime
    end_date: Optional[datetime]
    recovery_date: Optional[datetime]
    peak_value: float
    trough_value: float
    max_drawdown_percent: float
    duration_days: int
    recovery_days: Optional[int] = None
    is_recovered: bool = False

@dataclass
class DrawdownMetrics:
    """Comprehensive drawdown metrics"""
    # Maximum drawdown metrics
    max_drawdown_percent: float = 0.0
    max_drawdown_value: float = 0.0
    max_drawdown_duration_days: int = 0
    max_recovery_days: int = 0
    
    # Average drawdown metrics
    avg_drawdown_percent: float = 0.0
    avg_drawdown_duration_days: float = 0.0
    avg_recovery_days: float = 0.0
    
    # Drawdown frequency
    total_drawdown_periods: int = 0
    drawdown_frequency_per_year: float = 0.0
    time_underwater_percent: float = 0.0
    
    # Distribution metrics
    drawdown_std: float = 0.0
    drawdown_skewness: float = 0.0
    drawdown_kurtosis: float = 0.0
    
    # Recovery metrics
    recovery_factor: float = 0.0  # Average gain needed to recover from drawdowns
    pain_index: float = 0.0  # Average drawdown depth over time
    
    # Current status
    current_drawdown_percent: float = 0.0
    days_since_peak: int = 0
    is_in_drawdown: bool = False

class DrawdownAnalyzer:
    """
    Comprehensive drawdown analysis engine
    """
    
    def __init__(self):
        logger.info("DrawdownAnalyzer initialized")

    def analyze_drawdowns(self, equity_curve: pl.DataFrame) -> Tuple[DrawdownMetrics, List[DrawdownPeriod]]:
        """
        Perform comprehensive drawdown analysis
        
        Args:
            equity_curve: DataFrame with 'timestamp' and 'equity' columns
            
        Returns:
            Tuple of (DrawdownMetrics, List of DrawdownPeriods)
        """
        try:
            if equity_curve.is_empty():
                logger.warning("Empty equity curve provided")
                return DrawdownMetrics(), []
            
            # Ensure required columns exist
            if 'equity' not in equity_curve.columns or 'timestamp' not in equity_curve.columns:
                logger.error("Equity curve missing required columns")
                return DrawdownMetrics(), []
            
            # Sort by timestamp
            sorted_curve = equity_curve.sort('timestamp')
            
            # Extract data
            timestamps = sorted_curve.select(pl.col('timestamp')).to_series().to_list()
            equity_values = sorted_curve.select(pl.col('equity')).to_series().to_list()
            
            if len(equity_values) < 2:
                logger.warning("Insufficient data for drawdown analysis")
                return DrawdownMetrics(), []
            
            # Calculate running maximum (peaks)
            running_max = self._calculate_running_maximum(equity_values)
            
            # Calculate drawdown percentages
            drawdown_percentages = self._calculate_drawdown_percentages(equity_values, running_max)
            
            # Identify drawdown periods
            drawdown_periods = self._identify_drawdown_periods(timestamps, equity_values, running_max, drawdown_percentages)
            
            # Calculate comprehensive metrics
            metrics = self._calculate_drawdown_metrics(timestamps, equity_values, drawdown_percentages, drawdown_periods)
            
            logger.debug(f"Analyzed {len(drawdown_periods)} drawdown periods")
            return metrics, drawdown_periods
            
        except Exception as e:
            logger.error(f"Error analyzing drawdowns: {e}")
            return DrawdownMetrics(), []

    def _calculate_running_maximum(self, equity_values: List[float]) -> List[float]:
        """Calculate running maximum (peak values)"""
        running_max = []
        current_max = equity_values[0]
        
        for equity in equity_values:
            current_max = max(current_max, equity)
            running_max.append(current_max)
        
        return running_max

    def _calculate_drawdown_percentages(self, equity_values: List[float], running_max: List[float]) -> List[float]:
        """Calculate drawdown percentages"""
        drawdown_percentages = []
        
        for i, equity in enumerate(equity_values):
            if running_max[i] > 0:
                drawdown_pct = ((equity - running_max[i]) / running_max[i]) * 100
                drawdown_percentages.append(drawdown_pct)
            else:
                drawdown_percentages.append(0.0)
        
        return drawdown_percentages

    def _identify_drawdown_periods(self, timestamps: List[datetime], equity_values: List[float], 
                                 running_max: List[float], drawdown_percentages: List[float]) -> List[DrawdownPeriod]:
        """Identify individual drawdown periods"""
        periods = []
        current_period = None
        
        for i, (timestamp, equity, peak, dd_pct) in enumerate(zip(timestamps, equity_values, running_max, drawdown_percentages)):
            if dd_pct < -0.01:  # In drawdown (threshold of 0.01% to avoid noise)
                if current_period is None:
                    # Start of new drawdown period
                    current_period = DrawdownPeriod(
                        start_date=timestamp,
                        end_date=None,
                        recovery_date=None,
                        peak_value=peak,
                        trough_value=equity,
                        max_drawdown_percent=dd_pct,
                        duration_days=0
                    )
                else:
                    # Continue existing drawdown
                    current_period.trough_value = min(current_period.trough_value, equity)
                    current_period.max_drawdown_percent = min(current_period.max_drawdown_percent, dd_pct)
                    current_period.duration_days = (timestamp - current_period.start_date).days
            
            elif current_period is not None:
                # End of drawdown period (back to peak or new high)
                current_period.end_date = timestamp
                current_period.duration_days = (timestamp - current_period.start_date).days
                
                # Check if recovered to peak
                if equity >= current_period.peak_value:
                    current_period.recovery_date = timestamp
                    current_period.recovery_days = (timestamp - current_period.start_date).days
                    current_period.is_recovered = True
                
                periods.append(current_period)
                current_period = None
        
        # Handle ongoing drawdown
        if current_period is not None:
            current_period.end_date = timestamps[-1]
            current_period.duration_days = (timestamps[-1] - current_period.start_date).days
            periods.append(current_period)
        
        return periods

    def _calculate_drawdown_metrics(self, timestamps: List[datetime], equity_values: List[float],
                                  drawdown_percentages: List[float], periods: List[DrawdownPeriod]) -> DrawdownMetrics:
        """Calculate comprehensive drawdown metrics"""
        metrics = DrawdownMetrics()
        
        try:
            if not periods:
                return metrics
            
            # Maximum drawdown metrics
            max_dd_period = min(periods, key=lambda p: p.max_drawdown_percent)
            metrics.max_drawdown_percent = abs(max_dd_period.max_drawdown_percent)
            metrics.max_drawdown_value = max_dd_period.peak_value - max_dd_period.trough_value
            metrics.max_drawdown_duration_days = max_dd_period.duration_days
            
            # Maximum recovery time
            recovered_periods = [p for p in periods if p.is_recovered and p.recovery_days is not None]
            if recovered_periods:
                metrics.max_recovery_days = max(p.recovery_days for p in recovered_periods)
            
            # Average metrics
            metrics.total_drawdown_periods = len(periods)
            metrics.avg_drawdown_percent = abs(np.mean([p.max_drawdown_percent for p in periods]))
            metrics.avg_drawdown_duration_days = np.mean([p.duration_days for p in periods])
            
            if recovered_periods:
                metrics.avg_recovery_days = np.mean([p.recovery_days for p in recovered_periods])
            
            # Frequency metrics
            total_days = (timestamps[-1] - timestamps[0]).days
            if total_days > 0:
                metrics.drawdown_frequency_per_year = (len(periods) / total_days) * 365.25
                
                # Time underwater (percentage of time in drawdown)
                underwater_days = sum(p.duration_days for p in periods)
                metrics.time_underwater_percent = (underwater_days / total_days) * 100
            
            # Distribution metrics
            dd_values = [abs(p.max_drawdown_percent) for p in periods]
            if len(dd_values) > 1:
                metrics.drawdown_std = np.std(dd_values)
                
                # Skewness and kurtosis
                from scipy import stats
                metrics.drawdown_skewness = stats.skew(dd_values)
                metrics.drawdown_kurtosis = stats.kurtosis(dd_values)
            
            # Recovery factor (average percentage gain needed to recover)
            recovery_factors = []
            for period in periods:
                if period.trough_value > 0:
                    recovery_factor = ((period.peak_value - period.trough_value) / period.trough_value) * 100
                    recovery_factors.append(recovery_factor)
            
            if recovery_factors:
                metrics.recovery_factor = np.mean(recovery_factors)
            
            # Pain index (average drawdown over time)
            negative_drawdowns = [dd for dd in drawdown_percentages if dd < 0]
            if negative_drawdowns:
                metrics.pain_index = abs(np.mean(negative_drawdowns))
            
            # Current status
            current_dd = drawdown_percentages[-1]
            metrics.current_drawdown_percent = abs(current_dd) if current_dd < 0 else 0.0
            metrics.is_in_drawdown = current_dd < -0.01
            
            if metrics.is_in_drawdown:
                # Find days since last peak
                last_peak_idx = len(equity_values) - 1
                for i in range(len(equity_values) - 1, -1, -1):
                    if drawdown_percentages[i] >= -0.01:  # At or near peak
                        last_peak_idx = i
                        break
                
                if last_peak_idx < len(timestamps) - 1:
                    metrics.days_since_peak = (timestamps[-1] - timestamps[last_peak_idx]).days
            
        except Exception as e:
            logger.error(f"Error calculating drawdown metrics: {e}")
        
        return metrics

    def create_underwater_curve(self, equity_curve: pl.DataFrame) -> pl.DataFrame:
        """
        Create underwater curve (drawdown over time)
        
        Args:
            equity_curve: DataFrame with timestamp and equity columns
            
        Returns:
            DataFrame with timestamp and drawdown_percent columns
        """
        try:
            if equity_curve.is_empty():
                return pl.DataFrame()
            
            # Sort by timestamp
            sorted_curve = equity_curve.sort('timestamp')
            
            # Calculate running maximum
            equity_values = sorted_curve.select(pl.col('equity')).to_series().to_list()
            running_max = self._calculate_running_maximum(equity_values)
            
            # Calculate drawdown percentages
            drawdown_percentages = self._calculate_drawdown_percentages(equity_values, running_max)
            
            # Create underwater curve DataFrame
            underwater_data = {
                'timestamp': sorted_curve.select(pl.col('timestamp')).to_series().to_list(),
                'drawdown_percent': drawdown_percentages,
                'equity': equity_values,
                'peak': running_max
            }
            
            return pl.DataFrame(underwater_data)
            
        except Exception as e:
            logger.error(f"Error creating underwater curve: {e}")
            return pl.DataFrame()

    def calculate_rolling_drawdown_metrics(self, equity_curve: pl.DataFrame, window_days: int = 30) -> pl.DataFrame:
        """
        Calculate rolling drawdown metrics
        
        Args:
            equity_curve: DataFrame with timestamp and equity columns
            window_days: Rolling window size in days
            
        Returns:
            DataFrame with rolling drawdown metrics
        """
        try:
            if equity_curve.is_empty():
                return pl.DataFrame()
            
            sorted_curve = equity_curve.sort('timestamp')
            timestamps = sorted_curve.select(pl.col('timestamp')).to_series().to_list()
            equity_values = sorted_curve.select(pl.col('equity')).to_series().to_list()
            
            rolling_metrics = []
            
            for i in range(len(timestamps)):
                # Define window
                end_date = timestamps[i]
                start_date = end_date - timedelta(days=window_days)
                
                # Get data within window
                window_data = []
                window_timestamps = []
                
                for j, ts in enumerate(timestamps):
                    if start_date <= ts <= end_date:
                        window_data.append(equity_values[j])
                        window_timestamps.append(ts)
                
                if len(window_data) < 2:
                    continue
                
                # Calculate metrics for window
                running_max = self._calculate_running_maximum(window_data)
                drawdown_pcts = self._calculate_drawdown_percentages(window_data, running_max)
                
                max_dd = abs(min(drawdown_pcts)) if drawdown_pcts else 0.0
                avg_dd = abs(np.mean([dd for dd in drawdown_pcts if dd < 0])) if any(dd < 0 for dd in drawdown_pcts) else 0.0
                
                rolling_record = {
                    'timestamp': end_date,
                    'window_days': window_days,
                    'max_drawdown_percent': max_dd,
                    'avg_drawdown_percent': avg_dd,
                    'current_drawdown_percent': abs(drawdown_pcts[-1]) if drawdown_pcts[-1] < 0 else 0.0,
                    'is_in_drawdown': drawdown_pcts[-1] < -0.01 if drawdown_pcts else False
                }
                
                rolling_metrics.append(rolling_record)
            
            return pl.DataFrame(rolling_metrics)
            
        except Exception as e:
            logger.error(f"Error calculating rolling drawdown metrics: {e}")
            return pl.DataFrame()

    def analyze_drawdown_clusters(self, periods: List[DrawdownPeriod]) -> Dict[str, Any]:
        """
        Analyze drawdown clustering and patterns
        
        Args:
            periods: List of DrawdownPeriod objects
            
        Returns:
            Dictionary with clustering analysis
        """
        try:
            if not periods:
                return {}
            
            # Severity clusters
            severity_ranges = {
                'minor': (0, 5),      # 0-5%
                'moderate': (5, 15),   # 5-15%
                'major': (15, 30),     # 15-30%
                'severe': (30, float('inf'))  # >30%
            }
            
            severity_counts = {category: 0 for category in severity_ranges}
            
            for period in periods:
                dd_pct = abs(period.max_drawdown_percent)
                for category, (min_val, max_val) in severity_ranges.items():
                    if min_val <= dd_pct < max_val:
                        severity_counts[category] += 1
                        break
            
            # Duration clusters
            duration_ranges = {
                'short': (0, 7),       # 0-7 days
                'medium': (7, 30),     # 7-30 days
                'long': (30, 90),      # 30-90 days
                'extended': (90, float('inf'))  # >90 days
            }
            
            duration_counts = {category: 0 for category in duration_ranges}
            
            for period in periods:
                duration = period.duration_days
                for category, (min_val, max_val) in duration_ranges.items():
                    if min_val <= duration < max_val:
                        duration_counts[category] += 1
                        break
            
            # Recovery analysis
            recovery_stats = {
                'recovered_count': len([p for p in periods if p.is_recovered]),
                'unrecovered_count': len([p for p in periods if not p.is_recovered]),
                'avg_recovery_days': np.mean([p.recovery_days for p in periods if p.recovery_days is not None]) if any(p.recovery_days for p in periods) else 0
            }
            
            # Seasonal analysis (if enough data)
            seasonal_analysis = {}
            if len(periods) >= 12:  # Need at least a year of data
                monthly_counts = {i: 0 for i in range(1, 13)}
                for period in periods:
                    month = period.start_date.month
                    monthly_counts[month] += 1
                
                seasonal_analysis = {
                    'monthly_distribution': monthly_counts,
                    'peak_drawdown_month': max(monthly_counts, key=monthly_counts.get),
                    'lowest_drawdown_month': min(monthly_counts, key=monthly_counts.get)
                }
            
            return {
                'severity_distribution': severity_counts,
                'duration_distribution': duration_counts,
                'recovery_analysis': recovery_stats,
                'seasonal_analysis': seasonal_analysis,
                'total_periods': len(periods),
                'avg_severity': np.mean([abs(p.max_drawdown_percent) for p in periods]),
                'avg_duration': np.mean([p.duration_days for p in periods])
            }
            
        except Exception as e:
            logger.error(f"Error analyzing drawdown clusters: {e}")
            return {}

    def get_drawdown_summary(self, metrics: DrawdownMetrics, periods: List[DrawdownPeriod]) -> Dict[str, Any]:
        """
        Get formatted drawdown analysis summary
        
        Args:
            metrics: DrawdownMetrics object
            periods: List of DrawdownPeriod objects
            
        Returns:
            Dictionary with formatted summary
        """
        return {
            'maximum_drawdown': {
                'max_drawdown': f"{metrics.max_drawdown_percent:.2f}%",
                'max_dd_value': f"₹{metrics.max_drawdown_value:,.2f}",
                'max_dd_duration': f"{metrics.max_drawdown_duration_days} days",
                'max_recovery_time': f"{metrics.max_recovery_days} days"
            },
            'average_metrics': {
                'avg_drawdown': f"{metrics.avg_drawdown_percent:.2f}%",
                'avg_duration': f"{metrics.avg_drawdown_duration_days:.1f} days",
                'avg_recovery': f"{metrics.avg_recovery_days:.1f} days"
            },
            'frequency_analysis': {
                'total_periods': metrics.total_drawdown_periods,
                'frequency_per_year': f"{metrics.drawdown_frequency_per_year:.2f}",
                'time_underwater': f"{metrics.time_underwater_percent:.1f}%"
            },
            'current_status': {
                'current_drawdown': f"{metrics.current_drawdown_percent:.2f}%",
                'days_since_peak': metrics.days_since_peak,
                'in_drawdown': metrics.is_in_drawdown
            },
            'risk_measures': {
                'pain_index': f"{metrics.pain_index:.2f}%",
                'recovery_factor': f"{metrics.recovery_factor:.2f}%",
                'drawdown_volatility': f"{metrics.drawdown_std:.2f}%"
            }
        }
