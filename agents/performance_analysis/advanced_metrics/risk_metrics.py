#!/usr/bin/env python3
"""
Advanced Risk Metrics Calculator

Calculates sophisticated risk metrics including:
- <PERSON><PERSON><PERSON> (downside deviation focus)
- Calmar Ratio (return to max drawdown)
- Value at Risk (VaR) at multiple confidence levels
- Conditional Value at Risk (CVaR/Expected Shortfall)
- Maximum Adverse Excursion (MAE)
- Maximum Favorable Excursion (MFE)
- Risk-adjusted returns
- Tail risk measures
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import math
from scipy import stats
from scipy.stats import norm

logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Advanced risk metrics data structure"""
    # Ratio metrics
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    sterling_ratio: float = 0.0
    burke_ratio: float = 0.0
    
    # Value at Risk metrics
    var_95: float = 0.0  # 95% confidence VaR
    var_99: float = 0.0  # 99% confidence VaR
    var_99_9: float = 0.0  # 99.9% confidence VaR
    
    # Conditional VaR (Expected Shortfall)
    cvar_95: float = 0.0
    cvar_99: float = 0.0
    cvar_99_9: float = 0.0
    
    # Drawdown metrics
    max_drawdown: float = 0.0
    max_drawdown_duration_days: int = 0
    avg_drawdown: float = 0.0
    drawdown_frequency: float = 0.0
    
    # Excursion metrics
    max_adverse_excursion: float = 0.0
    max_favorable_excursion: float = 0.0
    mae_percentage: float = 0.0
    mfe_percentage: float = 0.0
    
    # Tail risk metrics
    skewness: float = 0.0
    kurtosis: float = 0.0
    tail_ratio: float = 0.0
    
    # Volatility metrics
    upside_volatility: float = 0.0
    downside_volatility: float = 0.0
    volatility_ratio: float = 0.0
    
    # Risk-adjusted metrics
    information_ratio: float = 0.0
    treynor_ratio: float = 0.0
    jensen_alpha: float = 0.0

class RiskMetricsCalculator:
    """
    Calculator for advanced risk metrics
    """
    
    def __init__(self, risk_free_rate: float = 0.06, benchmark_return: float = 0.12):
        """
        Initialize risk metrics calculator
        
        Args:
            risk_free_rate: Annual risk-free rate
            benchmark_return: Annual benchmark return for comparison
        """
        self.risk_free_rate = risk_free_rate
        self.benchmark_return = benchmark_return
        logger.info("RiskMetricsCalculator initialized")

    def calculate_risk_metrics(self, trades_df: pl.DataFrame, equity_curve: Optional[pl.DataFrame] = None) -> RiskMetrics:
        """
        Calculate comprehensive risk metrics
        
        Args:
            trades_df: DataFrame with trade data
            equity_curve: Optional equity curve data for drawdown analysis
            
        Returns:
            RiskMetrics object with calculated metrics
        """
        try:
            if trades_df.is_empty():
                logger.warning("Empty trades DataFrame provided")
                return RiskMetrics()
            
            # Filter completed trades
            completed_trades = trades_df.filter(
                (pl.col('exit_time').is_not_null()) & 
                (pl.col('pnl').is_not_null())
            )
            
            if completed_trades.is_empty():
                logger.warning("No completed trades found")
                return RiskMetrics()
            
            metrics = RiskMetrics()
            
            # Get returns data
            returns = completed_trades.select(pl.col('pnl')).to_series().to_list()
            
            # Calculate ratio metrics
            self._calculate_ratio_metrics(returns, metrics)
            
            # Calculate VaR and CVaR
            self._calculate_var_metrics(returns, metrics)
            
            # Calculate drawdown metrics
            if equity_curve is not None:
                self._calculate_drawdown_metrics(equity_curve, metrics)
            else:
                self._calculate_drawdown_from_trades(completed_trades, metrics)
            
            # Calculate excursion metrics
            self._calculate_excursion_metrics(completed_trades, metrics)
            
            # Calculate tail risk metrics
            self._calculate_tail_risk_metrics(returns, metrics)
            
            # Calculate volatility metrics
            self._calculate_volatility_metrics(returns, metrics)
            
            # Calculate risk-adjusted metrics
            self._calculate_risk_adjusted_metrics(returns, metrics)
            
            logger.debug(f"Calculated risk metrics for {len(returns)} trades")
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return RiskMetrics()

    def _calculate_ratio_metrics(self, returns: List[float], metrics: RiskMetrics):
        """Calculate risk-adjusted ratio metrics"""
        try:
            if len(returns) < 2:
                return
            
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            
            # Sortino Ratio (using downside deviation)
            negative_returns = returns_array[returns_array < 0]
            if len(negative_returns) > 0:
                downside_deviation = np.std(negative_returns)
                if downside_deviation > 0:
                    daily_risk_free = self.risk_free_rate / 365.25
                    excess_return = mean_return - daily_risk_free
                    metrics.sortino_ratio = (excess_return / downside_deviation) * np.sqrt(252)
            
            # Calmar Ratio (return to max drawdown ratio)
            if metrics.max_drawdown > 0:
                annualized_return = mean_return * 252
                metrics.calmar_ratio = annualized_return / metrics.max_drawdown
            
            # Sterling Ratio (similar to Calmar but uses average drawdown)
            if metrics.avg_drawdown > 0:
                annualized_return = mean_return * 252
                metrics.sterling_ratio = annualized_return / metrics.avg_drawdown
            
            # Burke Ratio (return to square root of sum of squared drawdowns)
            # This will be calculated after drawdown analysis
            
        except Exception as e:
            logger.error(f"Error calculating ratio metrics: {e}")

    def _calculate_var_metrics(self, returns: List[float], metrics: RiskMetrics):
        """Calculate Value at Risk and Conditional VaR"""
        try:
            if len(returns) < 10:  # Need sufficient data
                return
            
            returns_array = np.array(returns)
            
            # Calculate VaR at different confidence levels
            metrics.var_95 = np.percentile(returns_array, 5)  # 5th percentile for 95% VaR
            metrics.var_99 = np.percentile(returns_array, 1)  # 1st percentile for 99% VaR
            metrics.var_99_9 = np.percentile(returns_array, 0.1)  # 0.1st percentile for 99.9% VaR
            
            # Calculate Conditional VaR (Expected Shortfall)
            # CVaR is the expected value of losses beyond the VaR threshold
            var_95_losses = returns_array[returns_array <= metrics.var_95]
            if len(var_95_losses) > 0:
                metrics.cvar_95 = np.mean(var_95_losses)
            
            var_99_losses = returns_array[returns_array <= metrics.var_99]
            if len(var_99_losses) > 0:
                metrics.cvar_99 = np.mean(var_99_losses)
            
            var_99_9_losses = returns_array[returns_array <= metrics.var_99_9]
            if len(var_99_9_losses) > 0:
                metrics.cvar_99_9 = np.mean(var_99_9_losses)
            
            # Parametric VaR using normal distribution (alternative method)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            if std_return > 0:
                # Z-scores for different confidence levels
                z_95 = norm.ppf(0.05)  # -1.645
                z_99 = norm.ppf(0.01)  # -2.326
                z_99_9 = norm.ppf(0.001)  # -3.090
                
                parametric_var_95 = mean_return + z_95 * std_return
                parametric_var_99 = mean_return + z_99 * std_return
                parametric_var_99_9 = mean_return + z_99_9 * std_return
                
                # Use the more conservative (worse) VaR estimate
                metrics.var_95 = min(metrics.var_95, parametric_var_95)
                metrics.var_99 = min(metrics.var_99, parametric_var_99)
                metrics.var_99_9 = min(metrics.var_99_9, parametric_var_99_9)
            
        except Exception as e:
            logger.error(f"Error calculating VaR metrics: {e}")

    def _calculate_drawdown_metrics(self, equity_curve: pl.DataFrame, metrics: RiskMetrics):
        """Calculate drawdown metrics from equity curve"""
        try:
            if equity_curve.is_empty():
                return
            
            # Ensure we have the required columns
            if 'equity' not in equity_curve.columns or 'timestamp' not in equity_curve.columns:
                logger.warning("Equity curve missing required columns")
                return
            
            equity_values = equity_curve.select(pl.col('equity')).to_series().to_list()
            timestamps = equity_curve.select(pl.col('timestamp')).to_series().to_list()
            
            if len(equity_values) < 2:
                return
            
            # Calculate running maximum (peak)
            running_max = []
            current_max = equity_values[0]
            
            for equity in equity_values:
                current_max = max(current_max, equity)
                running_max.append(current_max)
            
            # Calculate drawdowns
            drawdowns = []
            for i, equity in enumerate(equity_values):
                if running_max[i] > 0:
                    drawdown = (equity - running_max[i]) / running_max[i]
                    drawdowns.append(drawdown)
                else:
                    drawdowns.append(0.0)
            
            # Maximum drawdown
            metrics.max_drawdown = abs(min(drawdowns)) if drawdowns else 0.0
            
            # Average drawdown
            negative_drawdowns = [dd for dd in drawdowns if dd < 0]
            metrics.avg_drawdown = abs(np.mean(negative_drawdowns)) if negative_drawdowns else 0.0
            
            # Drawdown frequency (percentage of time in drawdown)
            drawdown_periods = len(negative_drawdowns)
            total_periods = len(drawdowns)
            metrics.drawdown_frequency = (drawdown_periods / total_periods) * 100 if total_periods > 0 else 0.0
            
            # Maximum drawdown duration
            metrics.max_drawdown_duration_days = self._calculate_max_drawdown_duration(drawdowns, timestamps)
            
            # Burke Ratio calculation (now that we have drawdown data)
            if len(negative_drawdowns) > 0:
                sum_squared_drawdowns = sum(dd ** 2 for dd in negative_drawdowns)
                sqrt_sum_squared = math.sqrt(sum_squared_drawdowns)
                if sqrt_sum_squared > 0:
                    mean_return = np.mean([equity_values[i] - equity_values[i-1] for i in range(1, len(equity_values))])
                    annualized_return = mean_return * 252
                    metrics.burke_ratio = annualized_return / sqrt_sum_squared
            
        except Exception as e:
            logger.error(f"Error calculating drawdown metrics: {e}")

    def _calculate_drawdown_from_trades(self, trades_df: pl.DataFrame, metrics: RiskMetrics):
        """Calculate drawdown metrics from trade PnL when equity curve is not available"""
        try:
            # Sort trades by entry time
            sorted_trades = trades_df.sort('entry_time')
            pnl_values = sorted_trades.select(pl.col('pnl')).to_series().to_list()
            
            # Calculate cumulative PnL
            cumulative_pnl = np.cumsum(pnl_values)
            
            # Calculate running maximum
            running_max = np.maximum.accumulate(cumulative_pnl)
            
            # Calculate drawdowns
            drawdowns = (cumulative_pnl - running_max) / np.maximum(running_max, 1)  # Avoid division by zero
            
            # Maximum drawdown
            metrics.max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0.0
            
            # Average drawdown
            negative_drawdowns = drawdowns[drawdowns < 0]
            metrics.avg_drawdown = abs(np.mean(negative_drawdowns)) if len(negative_drawdowns) > 0 else 0.0
            
            # Drawdown frequency
            metrics.drawdown_frequency = (len(negative_drawdowns) / len(drawdowns)) * 100 if len(drawdowns) > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating drawdown from trades: {e}")

    def _calculate_max_drawdown_duration(self, drawdowns: List[float], timestamps: List[datetime]) -> int:
        """Calculate maximum drawdown duration in days"""
        try:
            if len(drawdowns) != len(timestamps):
                return 0
            
            max_duration = 0
            current_duration = 0
            in_drawdown = False
            drawdown_start = None
            
            for i, dd in enumerate(drawdowns):
                if dd < 0 and not in_drawdown:
                    # Start of drawdown
                    in_drawdown = True
                    drawdown_start = timestamps[i]
                    current_duration = 0
                elif dd >= 0 and in_drawdown:
                    # End of drawdown
                    in_drawdown = False
                    if drawdown_start:
                        duration_days = (timestamps[i] - drawdown_start).days
                        max_duration = max(max_duration, duration_days)
                elif in_drawdown:
                    # Continue drawdown
                    if drawdown_start:
                        current_duration = (timestamps[i] - drawdown_start).days
            
            # Handle case where drawdown continues to the end
            if in_drawdown and drawdown_start:
                duration_days = (timestamps[-1] - drawdown_start).days
                max_duration = max(max_duration, duration_days)
            
            return max_duration
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown duration: {e}")
            return 0

    def _calculate_excursion_metrics(self, trades_df: pl.DataFrame, metrics: RiskMetrics):
        """Calculate Maximum Adverse Excursion (MAE) and Maximum Favorable Excursion (MFE)"""
        try:
            # These metrics require intra-trade price data
            # For now, we'll use entry/exit prices as approximation
            
            mae_values = []
            mfe_values = []
            
            for row in trades_df.iter_rows(named=True):
                entry_price = row.get('entry_price', 0)
                exit_price = row.get('exit_price', 0)
                quantity = row.get('quantity', 0)
                side = row.get('side', '')
                
                if entry_price > 0 and exit_price > 0 and quantity > 0:
                    if side.upper() == 'BUY':
                        # For long positions
                        # MAE: worst price during trade (assume it's entry price for now)
                        mae = (entry_price - min(entry_price, exit_price)) * quantity
                        # MFE: best price during trade (assume it's exit price for now)
                        mfe = (max(entry_price, exit_price) - entry_price) * quantity
                    else:
                        # For short positions
                        mae = (max(entry_price, exit_price) - entry_price) * quantity
                        mfe = (entry_price - min(entry_price, exit_price)) * quantity
                    
                    mae_values.append(mae)
                    mfe_values.append(mfe)
            
            if mae_values:
                metrics.max_adverse_excursion = max(mae_values)
                metrics.mae_percentage = (metrics.max_adverse_excursion / abs(sum(mae_values))) * 100 if sum(mae_values) != 0 else 0
            
            if mfe_values:
                metrics.max_favorable_excursion = max(mfe_values)
                metrics.mfe_percentage = (metrics.max_favorable_excursion / sum(mfe_values)) * 100 if sum(mfe_values) > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating excursion metrics: {e}")

    def _calculate_tail_risk_metrics(self, returns: List[float], metrics: RiskMetrics):
        """Calculate tail risk metrics (skewness, kurtosis, tail ratio)"""
        try:
            if len(returns) < 4:  # Need minimum data for kurtosis
                return
            
            returns_array = np.array(returns)
            
            # Skewness (asymmetry of distribution)
            metrics.skewness = stats.skew(returns_array)
            
            # Kurtosis (tail heaviness)
            metrics.kurtosis = stats.kurtosis(returns_array)
            
            # Tail ratio (ratio of 95th percentile to 5th percentile)
            p95 = np.percentile(returns_array, 95)
            p5 = np.percentile(returns_array, 5)
            if p5 != 0:
                metrics.tail_ratio = abs(p95 / p5)
            
        except Exception as e:
            logger.error(f"Error calculating tail risk metrics: {e}")

    def _calculate_volatility_metrics(self, returns: List[float], metrics: RiskMetrics):
        """Calculate upside/downside volatility metrics"""
        try:
            if len(returns) < 2:
                return
            
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            
            # Upside volatility (volatility of positive returns)
            upside_returns = returns_array[returns_array > mean_return]
            if len(upside_returns) > 1:
                metrics.upside_volatility = np.std(upside_returns)
            
            # Downside volatility (volatility of negative returns)
            downside_returns = returns_array[returns_array < mean_return]
            if len(downside_returns) > 1:
                metrics.downside_volatility = np.std(downside_returns)
            
            # Volatility ratio
            if metrics.downside_volatility > 0:
                metrics.volatility_ratio = metrics.upside_volatility / metrics.downside_volatility
            
        except Exception as e:
            logger.error(f"Error calculating volatility metrics: {e}")

    def _calculate_risk_adjusted_metrics(self, returns: List[float], metrics: RiskMetrics):
        """Calculate additional risk-adjusted metrics"""
        try:
            if len(returns) < 2:
                return
            
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Information Ratio (excess return over benchmark divided by tracking error)
            daily_benchmark_return = self.benchmark_return / 365.25
            excess_returns = returns_array - daily_benchmark_return
            tracking_error = np.std(excess_returns)
            
            if tracking_error > 0:
                metrics.information_ratio = np.mean(excess_returns) / tracking_error
            
            # Treynor Ratio (excess return per unit of systematic risk)
            # Note: This requires beta calculation which needs market data
            # For now, we'll use a simplified version
            daily_risk_free = self.risk_free_rate / 365.25
            excess_return = mean_return - daily_risk_free
            
            # Assuming beta = 1 for simplification (would need market data for actual beta)
            beta = 1.0
            if beta > 0:
                metrics.treynor_ratio = excess_return / beta
            
            # Jensen's Alpha (excess return over CAPM expected return)
            # Alpha = Return - (Risk_free + Beta * (Market_return - Risk_free))
            expected_return = daily_risk_free + beta * (daily_benchmark_return - daily_risk_free)
            metrics.jensen_alpha = mean_return - expected_return
            
        except Exception as e:
            logger.error(f"Error calculating risk-adjusted metrics: {e}")

    def calculate_risk_metrics_by_period(self, trades_df: pl.DataFrame, period: str = 'monthly') -> pl.DataFrame:
        """
        Calculate risk metrics by time period
        
        Args:
            trades_df: DataFrame with trade data
            period: 'daily', 'weekly', 'monthly', or 'quarterly'
            
        Returns:
            DataFrame with risk metrics by period
        """
        try:
            if trades_df.is_empty():
                return pl.DataFrame()
            
            # Add period column based on entry_time
            if period == 'daily':
                period_col = pl.col('entry_time').dt.strftime('%Y-%m-%d')
            elif period == 'weekly':
                period_col = pl.col('entry_time').dt.strftime('%Y-W%U')
            elif period == 'monthly':
                period_col = pl.col('entry_time').dt.strftime('%Y-%m')
            elif period == 'quarterly':
                period_col = pl.col('entry_time').dt.quarter().cast(pl.Utf8)
            else:
                logger.error(f"Unsupported period: {period}")
                return pl.DataFrame()
            
            trades_with_period = trades_df.with_columns([period_col.alias('period')])
            
            periods = trades_with_period.select(pl.col('period')).unique().to_series().to_list()
            period_metrics = []
            
            for p in periods:
                if p is None:
                    continue
                
                period_trades = trades_with_period.filter(pl.col('period') == p)
                risk_metrics = self.calculate_risk_metrics(period_trades)
                
                period_record = {
                    'period': p,
                    'sortino_ratio': risk_metrics.sortino_ratio,
                    'calmar_ratio': risk_metrics.calmar_ratio,
                    'var_95': risk_metrics.var_95,
                    'var_99': risk_metrics.var_99,
                    'cvar_95': risk_metrics.cvar_95,
                    'cvar_99': risk_metrics.cvar_99,
                    'max_drawdown': risk_metrics.max_drawdown,
                    'skewness': risk_metrics.skewness,
                    'kurtosis': risk_metrics.kurtosis,
                    'upside_volatility': risk_metrics.upside_volatility,
                    'downside_volatility': risk_metrics.downside_volatility
                }
                
                period_metrics.append(period_record)
            
            # Sort by period
            period_df = pl.DataFrame(period_metrics)
            if not period_df.is_empty():
                period_df = period_df.sort('period')
            
            return period_df
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics by period: {e}")
            return pl.DataFrame()

    def get_risk_summary(self, metrics: RiskMetrics) -> Dict[str, Any]:
        """
        Get a formatted risk metrics summary
        
        Args:
            metrics: RiskMetrics object
            
        Returns:
            Dictionary with formatted summary
        """
        return {
            'risk_ratios': {
                'sortino_ratio': f"{metrics.sortino_ratio:.3f}",
                'calmar_ratio': f"{metrics.calmar_ratio:.3f}",
                'sterling_ratio': f"{metrics.sterling_ratio:.3f}",
                'burke_ratio': f"{metrics.burke_ratio:.3f}"
            },
            'value_at_risk': {
                'var_95': f"₹{metrics.var_95:,.2f}",
                'var_99': f"₹{metrics.var_99:,.2f}",
                'var_99_9': f"₹{metrics.var_99_9:,.2f}",
                'cvar_95': f"₹{metrics.cvar_95:,.2f}",
                'cvar_99': f"₹{metrics.cvar_99:,.2f}"
            },
            'drawdown_analysis': {
                'max_drawdown': f"{metrics.max_drawdown:.2%}",
                'max_dd_duration': f"{metrics.max_drawdown_duration_days} days",
                'avg_drawdown': f"{metrics.avg_drawdown:.2%}",
                'drawdown_frequency': f"{metrics.drawdown_frequency:.1f}%"
            },
            'tail_risk': {
                'skewness': f"{metrics.skewness:.3f}",
                'kurtosis': f"{metrics.kurtosis:.3f}",
                'tail_ratio': f"{metrics.tail_ratio:.2f}"
            },
            'volatility_analysis': {
                'upside_volatility': f"{metrics.upside_volatility:.4f}",
                'downside_volatility': f"{metrics.downside_volatility:.4f}",
                'volatility_ratio': f"{metrics.volatility_ratio:.2f}"
            }
        }
