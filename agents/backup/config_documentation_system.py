#!/usr/bin/env python3
"""
Configuration and Documentation System for LLM Interface Agent

This module provides comprehensive configuration management and automatic
documentation generation for the trading system components.

Features:
⚙️ 1. Configuration Management
- Dynamic configuration loading and validation
- Environment-specific configurations
- Configuration hot-reloading
- Schema validation and type checking

📚 2. Auto-Documentation Generation
- Strategy documentation from code analysis
- API documentation generation
- Configuration documentation
- Usage examples and tutorials

[CONFIG] 3. Configuration Editing
- Natural language configuration updates
- YAML/JSON configuration parsing
- Configuration diff and merge
- Backup and rollback capabilities

[STATUS] 4. Knowledge Graph
- Component relationship mapping
- Dependency tracking
- Configuration impact analysis
- System architecture documentation

Author: AI Assistant
Date: 2025-01-16
"""

import os
import yaml
import json
import ast
import inspect
import logging
from typing import Dict, List, Optional, Any, Union, Type
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
from pathlib import Path
import re
import shutil
from collections import defaultdict
import importlib

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] CONFIGURATION MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class ConfigType(Enum):
    """Configuration type enumeration"""
    STRATEGY = "strategy"
    AGENT = "agent"
    SYSTEM = "system"
    MODEL = "model"
    API = "api"
    DATABASE = "database"

@dataclass
class ConfigSchema:
    """Configuration schema definition"""
    name: str
    type: ConfigType
    required_fields: List[str]
    optional_fields: List[str]
    field_types: Dict[str, str]
    validation_rules: Dict[str, Any]
    description: str
    examples: Dict[str, Any]

@dataclass
class DocumentationEntry:
    """Documentation entry structure"""
    title: str
    content: str
    category: str
    tags: List[str]
    last_updated: datetime
    auto_generated: bool
    source_file: Optional[str] = None

@dataclass
class ConfigChange:
    """Configuration change record"""
    timestamp: datetime
    config_path: str
    change_type: str  # create, update, delete
    old_value: Optional[Any]
    new_value: Optional[Any]
    description: str
    user: str

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ CONFIGURATION AND DOCUMENTATION SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

class ConfigDocumentationSystem:
    """
    Comprehensive configuration management and documentation system
    
    Provides intelligent configuration handling, validation, and automatic
    documentation generation for all trading system components.
    """
    
    def __init__(self, workspace_path: str = "."):
        """Initialize configuration and documentation system"""
        self.workspace_path = Path(workspace_path)
        self.logger = logging.getLogger(__name__)
        
        # Configuration paths
        self.config_dir = self.workspace_path / "config"
        self.docs_dir = self.workspace_path / "docs"
        self.backup_dir = self.workspace_path / "config_backups"
        
        # Create directories
        for dir_path in [self.config_dir, self.docs_dir, self.backup_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # Configuration schemas
        self.schemas = self._load_schemas()
        
        # Documentation entries
        self.documentation = {}
        
        # Configuration cache
        self.config_cache = {}
        self.config_watchers = {}
        
        # Change tracking
        self.change_log = []
        
        # Knowledge graph
        self.knowledge_graph = defaultdict(list)
        
        self.logger.info("⚙️ Configuration and Documentation System initialized")
    
    def _load_schemas(self) -> Dict[str, ConfigSchema]:
        """Load configuration schemas"""
        return {
            "strategy": ConfigSchema(
                name="Strategy Configuration",
                type=ConfigType.STRATEGY,
                required_fields=["name", "type", "parameters"],
                optional_fields=["description", "enabled", "risk_limits"],
                field_types={
                    "name": "str",
                    "type": "str", 
                    "parameters": "dict",
                    "description": "str",
                    "enabled": "bool",
                    "risk_limits": "dict"
                },
                validation_rules={
                    "name": {"min_length": 3, "max_length": 50},
                    "type": {"allowed_values": ["momentum", "mean_reversion", "breakout", "scalping"]},
                    "parameters": {"required_keys": ["period", "threshold"]}
                },
                description="Configuration for trading strategies",
                examples={
                    "basic": {
                        "name": "RSI Strategy",
                        "type": "momentum",
                        "parameters": {"period": 14, "threshold": 30}
                    }
                }
            ),
            
            "agent": ConfigSchema(
                name="Agent Configuration",
                type=ConfigType.AGENT,
                required_fields=["name", "type", "enabled"],
                optional_fields=["config_path", "dependencies", "resources"],
                field_types={
                    "name": "str",
                    "type": "str",
                    "enabled": "bool",
                    "config_path": "str",
                    "dependencies": "list",
                    "resources": "dict"
                },
                validation_rules={
                    "name": {"min_length": 3},
                    "type": {"allowed_values": ["trading", "analysis", "monitoring", "execution"]}
                },
                description="Configuration for trading agents",
                examples={
                    "basic": {
                        "name": "Performance Analysis Agent",
                        "type": "analysis",
                        "enabled": True
                    }
                }
            ),
            
            "system": ConfigSchema(
                name="System Configuration",
                type=ConfigType.SYSTEM,
                required_fields=["environment", "logging"],
                optional_fields=["database", "api", "security"],
                field_types={
                    "environment": "str",
                    "logging": "dict",
                    "database": "dict",
                    "api": "dict",
                    "security": "dict"
                },
                validation_rules={
                    "environment": {"allowed_values": ["development", "testing", "production"]},
                    "logging": {"required_keys": ["level", "format"]}
                },
                description="System-wide configuration settings",
                examples={
                    "basic": {
                        "environment": "development",
                        "logging": {"level": "INFO", "format": "%(asctime)s - %(message)s"}
                    }
                }
            )
        }
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # ⚙️ CONFIGURATION MANAGEMENT METHODS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def load_config(self, config_path: str, validate: bool = True) -> Optional[Dict[str, Any]]:
        """Load configuration from file"""
        try:
            full_path = self.config_dir / config_path
            
            if not full_path.exists():
                self.logger.error(f"[ERROR] Configuration file not found: {config_path}")
                return None
            
            # Load based on file extension
            if full_path.suffix.lower() == '.yaml' or full_path.suffix.lower() == '.yml':
                with open(full_path, 'r') as f:
                    config = yaml.safe_load(f)
            elif full_path.suffix.lower() == '.json':
                with open(full_path, 'r') as f:
                    config = json.load(f)
            else:
                self.logger.error(f"[ERROR] Unsupported config format: {full_path.suffix}")
                return None
            
            # Validate if requested
            if validate:
                validation_result = await self.validate_config(config, config_path)
                if not validation_result['valid']:
                    self.logger.warning(f"[WARN] Configuration validation failed: {validation_result['errors']}")
            
            # Cache the configuration
            self.config_cache[config_path] = config
            
            self.logger.info(f"[SUCCESS] Loaded configuration: {config_path}")
            return config
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error loading config {config_path}: {e}")
            return None
    
    async def save_config(self, config_path: str, config: Dict[str, Any], 
                         backup: bool = True) -> bool:
        """Save configuration to file"""
        try:
            full_path = self.config_dir / config_path
            
            # Create backup if requested
            if backup and full_path.exists():
                await self._create_config_backup(config_path)
            
            # Validate configuration
            validation_result = await self.validate_config(config, config_path)
            if not validation_result['valid']:
                self.logger.error(f"[ERROR] Cannot save invalid configuration: {validation_result['errors']}")
                return False
            
            # Save based on file extension
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                with open(full_path, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
            elif config_path.endswith('.json'):
                with open(full_path, 'w') as f:
                    json.dump(config, f, indent=2)
            else:
                self.logger.error(f"[ERROR] Unsupported config format for saving: {config_path}")
                return False
            
            # Update cache
            self.config_cache[config_path] = config
            
            # Log change
            await self._log_config_change(config_path, "update", None, config, "Configuration saved")
            
            self.logger.info(f"[SUCCESS] Saved configuration: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error saving config {config_path}: {e}")
            return False
    
    async def validate_config(self, config: Dict[str, Any], config_path: str) -> Dict[str, Any]:
        """Validate configuration against schema"""
        try:
            # Determine config type from path or content
            config_type = self._determine_config_type(config_path, config)
            
            if config_type not in self.schemas:
                return {"valid": True, "warnings": [f"No schema found for {config_type}"]}
            
            schema = self.schemas[config_type]
            errors = []
            warnings = []
            
            # Check required fields
            for field in schema.required_fields:
                if field not in config:
                    errors.append(f"Missing required field: {field}")
            
            # Check field types
            for field, expected_type in schema.field_types.items():
                if field in config:
                    if not self._validate_field_type(config[field], expected_type):
                        errors.append(f"Invalid type for {field}: expected {expected_type}")
            
            # Check validation rules
            for field, rules in schema.validation_rules.items():
                if field in config:
                    field_errors = self._validate_field_rules(config[field], rules, field)
                    errors.extend(field_errors)
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "schema_used": config_type
            }
            
        except Exception as e:
            return {"valid": False, "errors": [f"Validation error: {str(e)}"]}
    
    async def update_config_field(self, config_path: str, field_path: str, 
                                 new_value: Any, description: str = "") -> bool:
        """Update a specific field in configuration"""
        try:
            config = await self.load_config(config_path)
            if not config:
                return False
            
            # Store old value for change tracking
            old_value = self._get_nested_value(config, field_path)
            
            # Update the field
            self._set_nested_value(config, field_path, new_value)
            
            # Save updated configuration
            success = await self.save_config(config_path, config)
            
            if success:
                # Log the change
                await self._log_config_change(
                    config_path, "field_update", old_value, new_value,
                    description or f"Updated {field_path}"
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error updating config field: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📚 DOCUMENTATION GENERATION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def generate_strategy_documentation(self, strategy_file: str) -> str:
        """Generate documentation for a strategy file"""
        try:
            strategy_path = self.workspace_path / strategy_file

            if not strategy_path.exists():
                return f"[ERROR] Strategy file not found: {strategy_file}"

            # Parse the Python file
            with open(strategy_path, 'r') as f:
                content = f.read()

            tree = ast.parse(content)

            # Extract information
            doc_info = self._extract_strategy_info(tree, content)

            # Generate markdown documentation
            documentation = self._generate_strategy_markdown(doc_info, strategy_file)

            # Save documentation
            doc_path = self.docs_dir / f"{Path(strategy_file).stem}_strategy.md"
            with open(doc_path, 'w') as f:
                f.write(documentation)

            # Add to documentation registry
            self.documentation[strategy_file] = DocumentationEntry(
                title=f"{doc_info['name']} Strategy Documentation",
                content=documentation,
                category="strategy",
                tags=["strategy", "trading", "auto-generated"],
                last_updated=datetime.now(),
                auto_generated=True,
                source_file=strategy_file
            )

            self.logger.info(f"📚 Generated strategy documentation: {doc_path}")
            return documentation

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating strategy documentation: {e}")
            return f"[ERROR] Error generating documentation: {str(e)}"

    async def generate_agent_documentation(self, agent_file: str) -> str:
        """Generate documentation for an agent file"""
        try:
            agent_path = self.workspace_path / agent_file

            if not agent_path.exists():
                return f"[ERROR] Agent file not found: {agent_file}"

            # Parse the Python file
            with open(agent_path, 'r') as f:
                content = f.read()

            tree = ast.parse(content)

            # Extract agent information
            doc_info = self._extract_agent_info(tree, content)

            # Generate markdown documentation
            documentation = self._generate_agent_markdown(doc_info, agent_file)

            # Save documentation
            doc_path = self.docs_dir / f"{Path(agent_file).stem}_agent.md"
            with open(doc_path, 'w') as f:
                f.write(documentation)

            # Add to documentation registry
            self.documentation[agent_file] = DocumentationEntry(
                title=f"{doc_info['name']} Agent Documentation",
                content=documentation,
                category="agent",
                tags=["agent", "system", "auto-generated"],
                last_updated=datetime.now(),
                auto_generated=True,
                source_file=agent_file
            )

            self.logger.info(f"📚 Generated agent documentation: {doc_path}")
            return documentation

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating agent documentation: {e}")
            return f"[ERROR] Error generating documentation: {str(e)}"

    async def generate_config_documentation(self, config_path: str) -> str:
        """Generate documentation for a configuration file"""
        try:
            config = await self.load_config(config_path)
            if not config:
                return f"[ERROR] Could not load configuration: {config_path}"

            # Determine config type and get schema
            config_type = self._determine_config_type(config_path, config)
            schema = self.schemas.get(config_type)

            # Generate documentation
            documentation = self._generate_config_markdown(config, schema, config_path)

            # Save documentation
            doc_path = self.docs_dir / f"{Path(config_path).stem}_config.md"
            with open(doc_path, 'w') as f:
                f.write(documentation)

            # Add to documentation registry
            self.documentation[config_path] = DocumentationEntry(
                title=f"{config_type.title()} Configuration Documentation",
                content=documentation,
                category="configuration",
                tags=["config", config_type, "auto-generated"],
                last_updated=datetime.now(),
                auto_generated=True,
                source_file=config_path
            )

            self.logger.info(f"📚 Generated config documentation: {doc_path}")
            return documentation

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating config documentation: {e}")
            return f"[ERROR] Error generating documentation: {str(e)}"

    async def generate_system_overview(self) -> str:
        """Generate system overview documentation"""
        try:
            # Collect system information
            agents = self._discover_agents()
            strategies = self._discover_strategies()
            configs = self._discover_configs()

            # Generate overview
            overview = f"""# Trading System Overview

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## System Architecture

### [AGENT] Agents ({len(agents)})
"""

            for agent in agents:
                overview += f"- **{agent['name']}**: {agent['description']}\n"

            overview += f"\n### [TARGET] Strategies ({len(strategies)})\n"

            for strategy in strategies:
                overview += f"- **{strategy['name']}**: {strategy['description']}\n"

            overview += f"\n### ⚙️ Configurations ({len(configs)})\n"

            for config in configs:
                overview += f"- **{config['name']}**: {config['description']}\n"

            # Add knowledge graph
            overview += "\n## Component Dependencies\n\n"
            overview += self._generate_dependency_graph()

            # Save overview
            doc_path = self.docs_dir / "SYSTEM_OVERVIEW.md"
            with open(doc_path, 'w') as f:
                f.write(overview)

            self.logger.info(f"📚 Generated system overview: {doc_path}")
            return overview

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating system overview: {e}")
            return f"[ERROR] Error generating overview: {str(e)}"

    async def process_natural_language_config_request(self, request: str) -> str:
        """Process natural language configuration requests"""
        try:
            request_lower = request.lower()

            # Configuration viewing requests
            if any(word in request_lower for word in ['show', 'display', 'view', 'get']):
                return await self._handle_config_view_request(request)

            # Configuration update requests
            elif any(word in request_lower for word in ['update', 'change', 'set', 'modify']):
                return await self._handle_config_update_request(request)

            # Documentation requests
            elif any(word in request_lower for word in ['document', 'docs', 'explain', 'describe']):
                return await self._handle_documentation_request(request)

            # Schema requests
            elif any(word in request_lower for word in ['schema', 'structure', 'format']):
                return await self._handle_schema_request(request)

            else:
                return """❓ I can help you with:
• **View configs**: "Show me the strategy configuration"
• **Update configs**: "Set RSI threshold to 30 in momentum strategy"
• **Generate docs**: "Document the performance analysis agent"
• **Schema info**: "Show me the strategy configuration schema"

What would you like to do?"""

        except Exception as e:
            self.logger.error(f"[ERROR] Error processing config request: {e}")
            return f"[ERROR] Error processing your request: {str(e)}"

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _determine_config_type(self, config_path: str, config: Dict[str, Any]) -> str:
        """Determine configuration type from path or content"""
        path_lower = config_path.lower()

        if 'strategy' in path_lower or 'strategies' in config:
            return 'strategy'
        elif 'agent' in path_lower or 'agents' in config:
            return 'agent'
        elif 'system' in path_lower or 'environment' in config:
            return 'system'
        elif 'model' in path_lower or 'models' in config:
            return 'model'
        elif 'api' in path_lower or 'endpoints' in config:
            return 'api'
        else:
            return 'system'  # Default

    def _validate_field_type(self, value: Any, expected_type: str) -> bool:
        """Validate field type"""
        type_map = {
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'list': list,
            'dict': dict
        }

        if expected_type in type_map:
            return isinstance(value, type_map[expected_type])

        return True  # Unknown type, assume valid

    def _validate_field_rules(self, value: Any, rules: Dict[str, Any], field_name: str) -> List[str]:
        """Validate field against rules"""
        errors = []

        # String validation
        if isinstance(value, str):
            if 'min_length' in rules and len(value) < rules['min_length']:
                errors.append(f"{field_name} too short (min: {rules['min_length']})")
            if 'max_length' in rules and len(value) > rules['max_length']:
                errors.append(f"{field_name} too long (max: {rules['max_length']})")
            if 'allowed_values' in rules and value not in rules['allowed_values']:
                errors.append(f"{field_name} must be one of: {rules['allowed_values']}")

        # Dictionary validation
        if isinstance(value, dict):
            if 'required_keys' in rules:
                for key in rules['required_keys']:
                    if key not in value:
                        errors.append(f"{field_name} missing required key: {key}")

        # Numeric validation
        if isinstance(value, (int, float)):
            if 'min_value' in rules and value < rules['min_value']:
                errors.append(f"{field_name} below minimum: {rules['min_value']}")
            if 'max_value' in rules and value > rules['max_value']:
                errors.append(f"{field_name} above maximum: {rules['max_value']}")

        return errors
