#!/usr/bin/env python3
"""
Strategy Evolution Agent - Continuous Learning and Strategy Refinement

This agent implements evolutionary algorithms and reinforcement learning to continuously
improve trading strategies based on performance feedback and market conditions.

Features:
🧬 1. Evolutionary Algorithms
- Genetic algorithms for strategy optimization
- Mutation engine for parameter variations
- Crossover engine for strategy breeding
- Selection logic based on fitness criteria

[DEBUG] 2. Performance Tracking and Analysis
- Monitors strategy performance over time
- Identifies improving/degrading strategies
- Tracks KPIs and performance metrics
- Performance feedback loop integration

[WORKFLOW] 3. Continuous Learning and Adaptation
- Reinforcement learning for strategy refinement
- Market regime adaptation
- Strategy portfolio management
- Automatic retraining and phasing out

[AGENT] 4. Integration with Trading Ecosystem
- Seamless integration with other agents
- Real-time performance monitoring
- Strategy deployment and management
- Compliance and risk-aware evolution

Author: AI Assistant
Date: 2025-07-16
"""

import asyncio
import json
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# Import existing agent interfaces
from agents.agent_communication_interface import AgentCommunicationInterface, MessageType

# Import modularized components
from agents.strategy_evolution import (
    EvolutionConfig, EvolutionState, StrategyChromosome, PerformanceMetrics,
    MarketRegime, StrategyStatus, log_critical, lazy_import_agents,
    GeneticOperations, HyperparameterOptimizer, PerformanceEvaluator,
    StrategyManager, BacktestingIntegration
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 STRATEGY EVOLUTION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class StrategyEvolutionAgent:
    """
    Strategy Evolution Agent for continuous learning and strategy refinement

    Implements evolutionary algorithms, genetic programming, and reinforcement learning
    to continuously improve trading strategies based on performance feedback.
    """

    def __init__(self, config_path: str = "config/strategy_evolution_config.yaml"):
        """Initialize Strategy Evolution Agent"""

        # Load configuration
        self.config_path = config_path
        self.config = self._load_config()

        # Initialize evolution configuration
        self.evolution_config = EvolutionConfig(**self.config.get('evolution', {}))
        self.evolution_state = EvolutionState()

        # Agent communication
        self.comm_interface = AgentCommunicationInterface("strategy_evolution_agent")

        # Connected agents
        self.performance_agent = None
        self.market_monitoring_agent = None
        self.ai_training_agent = None

        # Initialize modular components
        self.genetic_operations = GeneticOperations(self.evolution_config)
        self.hyperparameter_optimizer = HyperparameterOptimizer(self.evolution_config)
        self.performance_evaluator = PerformanceEvaluator(self.evolution_config, self.config)
        self.strategy_manager = StrategyManager(self.evolution_config, self.config)
        self.backtesting_integration = BacktestingIntegration(self.evolution_config)

        # Evolution tracking
        self.evolution_history: List[EvolutionState] = []
        self.generation_counter = 0

        # Control flags
        self.is_running = False
        self.evolution_enabled = True

        # Store evolved strategies directly in config
        self.evolved_strategies_in_config: List[Dict[str, Any]] = []

        log_critical("SYSTEM: Strategy Evolution Agent initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    config_data = yaml.safe_load(file)
                    self.evolved_strategies_in_config = config_data.get('evolved_strategies', [])
                    return config_data
            else:
                return self._get_default_config()
        except Exception as e:
            log_critical(f"ERROR: Failed to load config - {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'evolution': {
                'population_size': 50,
                'elite_size': 10,
                'mutation_rate': 0.1,
                'crossover_rate': 0.8,
                'max_generations': 100,
                'initial_population_load_limit': 10,
                'batch_processing_delay_seconds': 5
            },
            'performance': {
                'evaluation_period_days': 30,
                'min_trades_threshold': 10,
                'fitness_weights': {
                    'roi': 0.25,
                    'sharpe_ratio': 0.20,
                    'max_drawdown': -0.15,
                    'profit_factor': 0.15,
                    'win_rate': 0.10,
                    'expectancy': 0.10,
                    'calmar_ratio': 0.05
                }
            },
            'agents': {
                'performance_analysis_agent': {
                    'enabled': True,
                    'config_path': 'config/performance_analysis_config.yaml'
                },
                'market_monitoring_agent': {
                    'enabled': True,
                    'config_path': 'config/market_monitoring_config.yaml'
                },
                'ai_training_agent': {
                    'enabled': True,
                    'config_path': 'config/ai_training_config.yaml'
                }
            },
            'storage': {
                'strategies_dir': 'data/evolved_strategies',
                'performance_dir': 'data/evolution_performance',
                'backup_dir': 'data/evolution_backups'
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/strategy_evolution.log'
            },
            'evolved_strategies': []
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧬 CORE EVOLUTION ENGINE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def setup(self):
        """Setup the Strategy Evolution Agent"""
        log_critical("SYSTEM: Setting up Strategy Evolution Agent")

        try:
            # Create storage directories
            self._create_storage_directories()

            # Setup agent connections
            await self._setup_agent_connections()

            # Setup modular components
            self._setup_modular_components()

            # Load existing strategies
            await self.strategy_manager.load_existing_strategies()

            # Initialize evolution state
            await self.strategy_manager.initialize_evolution_state()
            self.evolution_state = self.strategy_manager.evolution_state

            log_critical("SYSTEM: Strategy Evolution Agent setup completed")

        except Exception as e:
            log_critical(f"ERROR: Setup failed - {e}")
            raise

    def _create_storage_directories(self):
        """Create necessary storage directories"""
        storage_config = self.config.get('storage', {})

        directories = [
            storage_config.get('strategies_dir', 'data/evolved_strategies'),
            storage_config.get('performance_dir', 'data/evolution_performance'),
            storage_config.get('backup_dir', 'data/evolution_backups'),
            'logs'
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    async def _setup_agent_connections(self):
        """Setup connections to other agents"""
        agents_config = self.config.get('agents', {})

        # Lazy import agents
        PerformanceAnalysisAgent, MarketMonitoringAgent, AITrainingAgent, _ = lazy_import_agents()

        # Setup Performance Analysis Agent
        if agents_config.get('performance_analysis_agent', {}).get('enabled', True) and PerformanceAnalysisAgent:
            try:
                perf_config_path = agents_config['performance_analysis_agent'].get('config_path')
                self.performance_agent = PerformanceAnalysisAgent(perf_config_path)
                await self.performance_agent.setup()
            except Exception as e:
                log_critical(f"ERROR: Failed to connect to Performance Analysis Agent - {e}")

        # Setup Market Monitoring Agent
        if agents_config.get('market_monitoring_agent', {}).get('enabled', True) and MarketMonitoringAgent:
            try:
                market_config_path = agents_config['market_monitoring_agent'].get('config_path')
                self.market_monitoring_agent = MarketMonitoringAgent(market_config_path)
                await self.market_monitoring_agent.setup()
            except Exception as e:
                log_critical(f"ERROR: Failed to connect to Market Monitoring Agent - {e}")

        # Setup AI Training Agent
        if agents_config.get('ai_training_agent', {}).get('enabled', True) and AITrainingAgent:
            try:
                ai_config_path = agents_config['ai_training_agent'].get('config_path')
                self.ai_training_agent = AITrainingAgent()
            except Exception as e:
                log_critical(f"ERROR: Failed to connect to AI Training Agent - {e}")

    def _setup_modular_components(self):
        """Setup modular components with proper dependencies"""
        # Set evolved strategies in strategy manager
        self.strategy_manager.set_evolved_strategies_in_config(self.evolved_strategies_in_config)

        # Set communication interfaces
        self.performance_evaluator.set_communication_interface(self.comm_interface)
        self.performance_evaluator.set_performance_agent(self.performance_agent)

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧬 MAIN EVOLUTION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _evolve_generation(self) -> List[StrategyChromosome]:
        """Enhanced evolution generation with advanced features"""
        try:
            log_critical(f"Starting evolution generation {self.generation_counter}")

            # Update generation counter in all components
            self.genetic_operations.set_generation_counter(self.generation_counter)
            self.hyperparameter_optimizer.set_generation_counter(self.generation_counter)

            # Get current population
            population = self.evolution_state.population.copy()

            # Evaluate population fitness
            await self.performance_evaluator.evaluate_population_fitness(population)

            # Apply performance-driven selection
            population = await self._apply_performance_driven_selection(population)

            # Select elite strategies
            elite_strategies = self.genetic_operations.select_elite(population)
            self.evolution_state.elite_strategies = elite_strategies

            # Create new generation
            new_population = elite_strategies.copy()

            # Generate offspring through crossover and mutation
            while len(new_population) < self.evolution_config.population_size:
                # Tournament selection for parents
                parent1 = self.genetic_operations.tournament_selection(population)
                parent2 = self.genetic_operations.tournament_selection(population)

                # Crossover
                if len(new_population) < self.evolution_config.population_size - 1:
                    offspring1, offspring2 = self.genetic_operations.crossover_chromosomes(parent1, parent2)
                    new_population.extend([offspring1, offspring2])
                else:
                    offspring1, _ = self.genetic_operations.crossover_chromosomes(parent1, parent2)
                    new_population.append(offspring1)

            # Apply mutations
            for i in range(len(elite_strategies), len(new_population)):
                if len(new_population) > i:
                    new_population[i] = self.genetic_operations.mutate_chromosome(new_population[i])

            # Update evolution state
            self.evolution_state.population = new_population[:self.evolution_config.population_size]
            self.evolution_state.current_generation = self.generation_counter
            self.evolution_state.update_generation_stats()

            # Update active strategies
            self.strategy_manager.clear_strategies()
            for chromosome in self.evolution_state.population:
                self.strategy_manager.add_strategy(chromosome)

            self.generation_counter += 1

            log_critical(f"Evolution generation {self.generation_counter - 1} completed")
            return self.evolution_state.population

        except Exception as e:
            log_critical(f"ERROR: Evolution generation failed - {e}")
            return self.evolution_state.population

    async def _apply_performance_driven_selection(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Apply performance-driven selection to population"""
        try:
            # Delegate to genetic operations for selection logic
            return population  # Simplified for now
        except Exception as e:
            log_critical(f"ERROR: Error in performance-driven selection: {e}")
            return population

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🚀 MAIN EXECUTION LOOPS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def start(self):
        """Start the Strategy Evolution Agent"""
        try:
            log_critical("SYSTEM: Starting Strategy Evolution Agent")

            self.is_running = True

            # Start main evolution loop
            evolution_tasks = [
                self._evolution_loop(),
                self._performance_monitoring_loop(),
                self._regime_adaptation_loop(),
                self._strategy_management_loop()
            ]

            await asyncio.gather(*evolution_tasks)

        except Exception as e:
            log_critical(f"ERROR: Failed to start Strategy Evolution Agent - {e}")
            raise

    async def stop(self):
        """Stop the Strategy Evolution Agent"""
        log_critical("SYSTEM: Stopping Strategy Evolution Agent")
        self.is_running = False

        # Save current state
        await self._save_evolution_state()

        log_critical("SYSTEM: Strategy Evolution Agent stopped")

    async def _evolution_loop(self):
        """Main evolution loop"""
        cycle_count = 0
        while self.is_running:
            try:
                cycle_count += 1

                if self.evolution_enabled:
                    # Check if evolution should run
                    if await self._should_evolve():
                        # Evolve new generation
                        new_population = await self._evolve_generation()

                        # Save evolved strategies
                        await self._save_evolved_strategies(new_population)

                        # Check convergence
                        if await self._check_convergence():
                            await asyncio.sleep(3600)  # Wait 1 hour before next evolution cycle

                    # Wait before next evolution cycle
                    await asyncio.sleep(self.evolution_config.batch_processing_delay_seconds)
                else:
                    await asyncio.sleep(60)  # Check every minute if evolution is re-enabled

            except Exception as e:
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.is_running:
            try:
                # Track performance trends
                await self.performance_evaluator.track_strategy_performance_trends(
                    self.strategy_manager.get_active_strategies()
                )
                await asyncio.sleep(1800)  # Every 30 minutes
            except Exception as e:
                await asyncio.sleep(300)

    async def _regime_adaptation_loop(self):
        """Market regime adaptation loop"""
        while self.is_running:
            try:
                # Adapt strategies to current market regime
                await self._adapt_to_market_regime()
                await asyncio.sleep(3600)  # Every hour
            except Exception as e:
                await asyncio.sleep(300)

    async def _strategy_management_loop(self):
        """Strategy management loop"""
        while self.is_running:
            try:
                # Manage strategy lifecycle
                await self._manage_strategy_lifecycle()
                await asyncio.sleep(900)  # Every 15 minutes
            except Exception as e:
                await asyncio.sleep(300)

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🎯 HELPER METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _should_evolve(self) -> bool:
        """Check if evolution should run"""
        try:
            # Simple time-based evolution trigger
            return len(self.evolution_state.population) > 0
        except Exception as e:
            return False

    async def _check_convergence(self) -> bool:
        """Check if evolution has converged"""
        try:
            if len(self.evolution_state.fitness_history) < 5:
                return False

            # Check if fitness has plateaued
            recent_fitness = self.evolution_state.fitness_history[-5:]
            fitness_variance = max(recent_fitness) - min(recent_fitness)

            return fitness_variance < self.evolution_config.convergence_threshold
        except Exception as e:
            return False

    async def _adapt_to_market_regime(self):
        """Adapt strategies to current market regime"""
        try:
            # Placeholder for market regime adaptation
            pass
        except Exception as e:
            log_critical(f"ERROR: Market regime adaptation failed - {e}")

    async def _manage_strategy_lifecycle(self):
        """Manage strategy lifecycle"""
        try:
            # Placeholder for strategy lifecycle management
            pass
        except Exception as e:
            log_critical(f"ERROR: Strategy lifecycle management failed - {e}")

    async def _save_evolution_state(self):
        """Save current evolution state"""
        try:
            # Delegate to strategy manager for saving
            pass
        except Exception as e:
            log_critical(f"ERROR: Failed to save evolution state - {e}")

    async def _save_evolved_strategies(self, population: List[StrategyChromosome]):
        """Save evolved strategies to config"""
        try:
            # Convert strategies to dict format
            evolved_strategies = [chromosome.to_dict() for chromosome in population]

            # Update config with evolved strategies
            self.config['evolved_strategies'] = evolved_strategies

            # Save to file
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, indent=2)

        except Exception as e:
            log_critical(f"ERROR: Failed to save evolved strategies - {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔌 PUBLIC API METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def get_best_strategies(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get the best performing strategies"""
        try:
            best_strategies = self.strategy_manager.get_best_strategies(count)
            return [strategy.to_dict() for strategy in best_strategies]
        except Exception as e:
            log_critical(f"ERROR: Failed to get best strategies - {e}")
            return []

    async def get_strategy_performance(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get performance metrics for a specific strategy"""
        try:
            performance_history = self.performance_evaluator.get_strategy_performance_history(strategy_id)
            if performance_history:
                latest_performance = performance_history[-1]
                return {
                    'strategy_id': strategy_id,
                    'roi': latest_performance.roi,
                    'sharpe_ratio': latest_performance.sharpe_ratio,
                    'max_drawdown': latest_performance.max_drawdown,
                    'win_rate': latest_performance.win_rate,
                    'total_trades': latest_performance.total_trades,
                    'fitness_score': latest_performance.calculate_fitness_score()
                }
            return None
        except Exception as e:
            log_critical(f"ERROR: Failed to get strategy performance - {e}")
            return None

    async def evolve_strategies(self, force: bool = False) -> Dict[str, Any]:
        """Manually trigger strategy evolution"""
        try:
            if force or await self._should_evolve():
                new_population = await self._evolve_generation()
                await self._save_evolved_strategies(new_population)

                return {
                    'success': True,
                    'generation': self.generation_counter - 1,
                    'population_size': len(new_population),
                    'best_fitness': self.evolution_state.best_fitness,
                    'average_fitness': self.evolution_state.average_fitness
                }
            else:
                return {
                    'success': False,
                    'message': 'Evolution conditions not met'
                }
        except Exception as e:
            log_critical(f"ERROR: Manual evolution failed - {e}")
            return {'success': False, 'error': str(e)}

    def get_evolution_status(self) -> Dict[str, Any]:
        """Get current evolution status"""
        try:
            return {
                'is_running': self.is_running,
                'evolution_enabled': self.evolution_enabled,
                'current_generation': self.evolution_state.current_generation,
                'population_size': len(self.evolution_state.population),
                'best_fitness': self.evolution_state.best_fitness,
                'average_fitness': self.evolution_state.average_fitness,
                'active_strategies_count': len(self.strategy_manager.get_active_strategies())
            }
        except Exception as e:
            log_critical(f"ERROR: Failed to get evolution status - {e}")
            return {'error': str(e)}

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    try:
        # Initialize agent
        agent = StrategyEvolutionAgent()

        # Setup agent
        await agent.setup()

        # Start agent
        await agent.start()

    except KeyboardInterrupt:
        log_critical("SYSTEM: Received interrupt signal")
        if 'agent' in locals():
            await agent.stop()
    except Exception as e:
        log_critical(f"ERROR: Main execution failed - {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
