#!/usr/bin/env python3
"""
Duplicate Data Prevention Module for AI Training Agent
Provides mechanisms to prevent training on duplicate data during incremental updates
"""

import hashlib
import json
import logging
from pathlib import Path
from typing import Set, Dict, Any, List, Tuple
import polars as pl
import numpy as np
from datetime import datetime

logger = logging.getLogger(__name__)

class DuplicateDataPrevention:
    """Handles duplicate data detection and prevention for incremental training"""
    
    def __init__(self, models_dir: str, processed_keys_file: str = "processed_data_keys.json"):
        self.models_dir = Path(models_dir)
        self.processed_keys_file = processed_keys_file
        self.processed_keys_path = self.models_dir / self.processed_keys_file
        self.processed_data_keys: Set[str] = set()
        self.data_fingerprints: Dict[str, str] = {}
        
        # Load existing processed keys
        self._load_processed_keys()
    
    def _load_processed_keys(self):
        """Load previously processed data keys from file"""
        if self.processed_keys_path.exists():
            try:
                with open(self.processed_keys_path, 'r') as f:
                    data = json.load(f)
                    self.processed_data_keys = set(data.get('keys', []))
                    self.data_fingerprints = data.get('fingerprints', {})
                logger.info(f"Loaded {len(self.processed_data_keys)} processed data keys")
            except Exception as e:
                logger.warning(f"Failed to load processed keys: {e}")
    
    def _save_processed_keys(self):
        """Save processed data keys to file"""
        try:
            self.models_dir.mkdir(parents=True, exist_ok=True)
            data = {
                'keys': list(self.processed_data_keys),
                'fingerprints': self.data_fingerprints,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.processed_keys_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved {len(self.processed_data_keys)} processed data keys")
        except Exception as e:
            logger.error(f"Failed to save processed keys: {e}")
    
    def generate_row_key(self, row_data: Dict[str, Any]) -> str:
        """Generate unique key for a data row"""
        # Create deterministic key from row data
        key_components = []
        
        # Use specific columns that make a row unique
        unique_cols = ['date', 'strategy_name', 'symbol', 'entry_time', 'exit_time']
        
        for col in unique_cols:
            if col in row_data:
                key_components.append(f"{col}:{row_data[col]}")
        
        # If no unique columns found, use all data
        if not key_components:
            key_components = [f"{k}:{v}" for k, v in sorted(row_data.items())]
        
        key_string = "|".join(key_components)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def generate_chunk_fingerprint(self, df: pl.DataFrame) -> str:
        """Generate fingerprint for data chunk"""
        # Use sample of data for fingerprint
        sample_size = min(100, len(df))
        sample_df = df.sample(sample_size, seed=42) if len(df) > sample_size else df
        
        # Create fingerprint from sample data
        # Convert Polars DataFrame to NumPy array and then to bytes
        data_bytes = sample_df.to_numpy().tobytes()
        return hashlib.sha256(data_bytes).hexdigest()
    
    def filter_new_data(self, df: pl.DataFrame) -> Tuple[pl.DataFrame, Dict[str, Any]]:
        """Filter out already processed data rows"""
        logger.info(f"Filtering {len(df)} rows for duplicates...")
        
        # Generate keys for all rows
        row_keys = []
        for row in df.iter_rows(named=True):
            key = self.generate_row_key(row)
            row_keys.append(key)
        
        # Add keys as column for filtering
        df_with_keys = df.with_columns(pl.Series("_row_key", row_keys))
        
        # Filter out processed rows
        new_data_mask = ~pl.col("_row_key").is_in(list(self.processed_data_keys))
        new_df = df_with_keys.filter(new_data_mask).drop("_row_key")
        
        # Statistics
        original_count = len(df)
        new_count = len(new_df)
        duplicate_count = original_count - new_count
        
        stats = {
            'original_rows': original_count,
            'new_rows': new_count,
            'duplicate_rows': duplicate_count,
            'duplicate_ratio': duplicate_count / original_count if original_count > 0 else 0
        }
        
        logger.info(f"Filtered data: {new_count} new rows, {duplicate_count} duplicates")
        return new_df, stats
    
    def mark_data_processed(self, df: pl.DataFrame):
        """Mark data rows as processed"""
        new_keys = []
        for row in df.iter_rows(named=True):
            key = self.generate_row_key(row)
            new_keys.append(key)
        
        # Add new keys to processed set
        self.processed_data_keys.update(new_keys)
        
        # Generate and store chunk fingerprint
        chunk_fingerprint = self.generate_chunk_fingerprint(df)
        timestamp = datetime.now().isoformat()
        self.data_fingerprints[timestamp] = chunk_fingerprint
        
        # Save to file
        self._save_processed_keys()
        
        logger.info(f"Marked {len(new_keys)} rows as processed")
    
    def detect_data_drift(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Detect if new data has significant drift from previous data"""
        current_fingerprint = self.generate_chunk_fingerprint(df)
        
        drift_info = {
            'current_fingerprint': current_fingerprint,
            'drift_detected': False,
            'similarity_scores': []
        }
        
        # Compare with recent fingerprints
        recent_fingerprints = list(self.data_fingerprints.values())[-10:]  # Last 10 chunks
        
        for prev_fingerprint in recent_fingerprints:
            # Simple similarity based on fingerprint difference
            similarity = self._calculate_fingerprint_similarity(current_fingerprint, prev_fingerprint)
            drift_info['similarity_scores'].append(similarity)
        
        if drift_info['similarity_scores']:
            avg_similarity = np.mean(drift_info['similarity_scores'])
            drift_info['average_similarity'] = avg_similarity
            drift_info['drift_detected'] = avg_similarity < 0.7  # Threshold for drift
        
        return drift_info
    
    def _calculate_fingerprint_similarity(self, fp1: str, fp2: str) -> float:
        """Calculate similarity between two fingerprints"""
        # Simple Hamming distance-based similarity
        if len(fp1) != len(fp2):
            return 0.0
        
        matches = sum(c1 == c2 for c1, c2 in zip(fp1, fp2))
        return matches / len(fp1)
    
    def cleanup_old_keys(self, max_keys: int = 1000000):
        """Cleanup old processed keys to prevent memory issues"""
        if len(self.processed_data_keys) > max_keys:
            # Keep most recent keys (this is approximate since sets are unordered)
            keys_list = list(self.processed_data_keys)
            self.processed_data_keys = set(keys_list[-max_keys:])
            
            # Cleanup old fingerprints
            fingerprint_items = list(self.data_fingerprints.items())
            if len(fingerprint_items) > 100:
                recent_fingerprints = dict(fingerprint_items[-100:])
                self.data_fingerprints = recent_fingerprints
            
            self._save_processed_keys()
            logger.info(f"Cleaned up old keys, kept {len(self.processed_data_keys)} keys")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about processed data"""
        return {
            'total_processed_keys': len(self.processed_data_keys),
            'total_fingerprints': len(self.data_fingerprints),
            'memory_usage_mb': len(str(self.processed_data_keys)) / (1024 * 1024),
            'last_updated': datetime.now().isoformat()
        }
