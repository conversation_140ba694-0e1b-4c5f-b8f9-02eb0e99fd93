#!/usr/bin/env python3
"""
AI Training Agent Monitor
Model performance monitoring, drift detection, and automated alerts
"""

import os
import json
import logging
import numpy as np
import polars as pl
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Statistical libraries
from scipy import stats
from sklearn.metrics import mean_squared_error, r2_score

# Import our modules
from ai_training_agent import AITrainingAgent
from ai_training_utils import load_config, save_training_metrics

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] MONITORING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class MonitoringConfig:
    """Configuration for model monitoring"""
    
    # Drift detection thresholds
    drift_threshold_psi: float = 0.1  # Population Stability Index threshold
    drift_threshold_ks: float = 0.05  # Kolmogorov-Smirnov test threshold
    drift_threshold_chi2: float = 0.05  # Chi-square test threshold
    
    # Performance degradation thresholds
    performance_threshold_r2: float = 0.05  # R² degradation threshold
    performance_threshold_rmse: float = 0.1  # RMSE increase threshold
    
    # Monitoring windows
    monitoring_window_days: int = 7  # Days to look back for monitoring
    baseline_window_days: int = 30  # Days for baseline calculation
    
    # Alert settings
    enable_alerts: bool = True
    alert_email: Optional[str] = None
    alert_webhook: Optional[str] = None
    
    # Storage settings
    metrics_storage_path: str = "data/models/monitoring_metrics.json"
    drift_storage_path: str = "data/models/drift_reports.json"

class AITrainingMonitor:
    """
    Monitor AI Training Agent performance and detect model drift
    
    Features:
    - Data drift detection using PSI, KS test, Chi-square test
    - Model performance monitoring
    - Automated alerts for degradation
    - Retraining recommendations
    - Performance trend analysis
    """
    
    def __init__(self, config: Optional[MonitoringConfig] = None):
        """Initialize monitoring system"""
        self.config = config or MonitoringConfig()
        self.baseline_metrics = {}
        self.monitoring_history = []
        
        # Create storage directories
        os.makedirs(os.path.dirname(self.config.metrics_storage_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.config.drift_storage_path), exist_ok=True)
        
        logger.info("[DEBUG] AI Training Monitor initialized")
    
    def calculate_psi(self, baseline: np.ndarray, current: np.ndarray, 
                     bins: int = 10) -> float:
        """
        Calculate Population Stability Index (PSI) for drift detection
        
        Args:
            baseline: Baseline data distribution
            current: Current data distribution
            bins: Number of bins for discretization
            
        Returns:
            PSI value (higher = more drift)
        """
        # Create bins based on baseline data
        bin_edges = np.histogram_bin_edges(baseline, bins=bins)
        
        # Calculate distributions
        baseline_dist, _ = np.histogram(baseline, bins=bin_edges, density=True)
        current_dist, _ = np.histogram(current, bins=bin_edges, density=True)
        
        # Add small epsilon to avoid division by zero
        epsilon = 1e-8
        baseline_dist = baseline_dist + epsilon
        current_dist = current_dist + epsilon
        
        # Normalize to probabilities
        baseline_dist = baseline_dist / np.sum(baseline_dist)
        current_dist = current_dist / np.sum(current_dist)
        
        # Calculate PSI
        psi = np.sum((current_dist - baseline_dist) * np.log(current_dist / baseline_dist))
        
        return psi
    
    def detect_feature_drift(self, baseline_data: pl.DataFrame, 
                           current_data: pl.DataFrame,
                           feature_columns: List[str]) -> Dict[str, Dict[str, float]]:
        """
        Detect drift in input features
        
        Args:
            baseline_data: Historical baseline data
            current_data: Current data to compare
            feature_columns: List of feature columns to monitor
            
        Returns:
            Dictionary of drift metrics per feature
        """
        drift_results = {}
        
        for feature in feature_columns:
            if feature not in baseline_data.columns or feature not in current_data.columns:
                continue
            
            baseline_values = baseline_data[feature].to_numpy()
            current_values = current_data[feature].to_numpy()
            
            # Remove null values
            baseline_values = baseline_values[~np.isnan(baseline_values)]
            current_values = current_values[~np.isnan(current_values)]
            
            if len(baseline_values) == 0 or len(current_values) == 0:
                continue
            
            # Calculate drift metrics
            psi = self.calculate_psi(baseline_values, current_values)
            ks_stat, ks_pvalue = stats.ks_2samp(baseline_values, current_values)
            
            # Chi-square test for categorical features (if applicable)
            chi2_pvalue = None
            if len(np.unique(baseline_values)) < 20:  # Treat as categorical
                try:
                    # Create contingency table
                    unique_vals = np.union1d(np.unique(baseline_values), np.unique(current_values))
                    baseline_counts = np.array([np.sum(baseline_values == val) for val in unique_vals])
                    current_counts = np.array([np.sum(current_values == val) for val in unique_vals])
                    
                    # Chi-square test
                    chi2_stat, chi2_pvalue = stats.chisquare(current_counts, baseline_counts)
                except:
                    chi2_pvalue = None
            
            drift_results[feature] = {
                'psi': psi,
                'ks_statistic': ks_stat,
                'ks_pvalue': ks_pvalue,
                'chi2_pvalue': chi2_pvalue,
                'drift_detected': (
                    psi > self.config.drift_threshold_psi or 
                    ks_pvalue < self.config.drift_threshold_ks or
                    (chi2_pvalue is not None and chi2_pvalue < self.config.drift_threshold_chi2)
                )
            }
        
        return drift_results
    
    def monitor_model_performance(self, agent: AITrainingAgent,
                                test_data: pl.DataFrame) -> Dict[str, Any]:
        """
        Monitor model performance on recent data
        
        Args:
            agent: Trained AI Training Agent
            test_data: Recent test data
            
        Returns:
            Performance monitoring results
        """
        if not agent.is_trained:
            raise ValueError("Agent must be trained before monitoring")
        
        # Preprocess test data
        features, targets, _ = agent.preprocess_data(test_data)
        
        # Make predictions
        predictions, confidence = agent.predict(features)
        
        # Calculate performance metrics
        performance_metrics = {}
        
        for i, target_name in enumerate(agent.config.target_columns):
            y_true = targets[:, i]
            y_pred = predictions[:, i]
            
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            r2 = r2_score(y_true, y_pred)
            mae = np.mean(np.abs(y_true - y_pred))
            
            performance_metrics[target_name] = {
                'rmse': rmse,
                'r2': r2,
                'mae': mae,
                'mean_confidence': np.mean(confidence)
            }
        
        # Overall metrics
        overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
        overall_r2 = r2_score(targets, predictions)
        
        performance_metrics['overall'] = {
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_confidence': np.mean(confidence),
            'prediction_count': len(predictions)
        }
        
        return performance_metrics
    
    def detect_performance_degradation(self, current_metrics: Dict[str, Any],
                                     baseline_metrics: Dict[str, Any]) -> Dict[str, bool]:
        """
        Detect if model performance has degraded
        
        Args:
            current_metrics: Current performance metrics
            baseline_metrics: Baseline performance metrics
            
        Returns:
            Dictionary indicating degradation per metric
        """
        degradation_detected = {}
        
        for target in current_metrics:
            if target not in baseline_metrics:
                continue
            
            current = current_metrics[target]
            baseline = baseline_metrics[target]
            
            # Check R² degradation (lower is worse)
            r2_degraded = (baseline['r2'] - current['r2']) > self.config.performance_threshold_r2
            
            # Check RMSE increase (higher is worse)
            rmse_increased = (current['rmse'] - baseline['rmse']) > self.config.performance_threshold_rmse
            
            degradation_detected[target] = r2_degraded or rmse_increased
        
        return degradation_detected
    
    def generate_monitoring_report(self, agent: AITrainingAgent,
                                 current_data: pl.DataFrame,
                                 baseline_data: Optional[pl.DataFrame] = None) -> Dict[str, Any]:
        """
        Generate comprehensive monitoring report
        
        Args:
            agent: Trained AI Training Agent
            current_data: Current data for monitoring
            baseline_data: Optional baseline data for comparison
            
        Returns:
            Complete monitoring report
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'data_summary': {
                'current_data_rows': len(current_data),
                'current_data_columns': len(current_data.columns)
            },
            'performance_metrics': {},
            'drift_detection': {},
            'alerts': [],
            'recommendations': []
        }
        
        # Monitor performance
        try:
            current_performance = self.monitor_model_performance(agent, current_data)
            report['performance_metrics'] = current_performance
            
            # Check for performance degradation if baseline exists
            if self.baseline_metrics:
                degradation = self.detect_performance_degradation(
                    current_performance, self.baseline_metrics
                )
                
                for target, is_degraded in degradation.items():
                    if is_degraded:
                        report['alerts'].append(f"Performance degradation detected for {target}")
                        report['recommendations'].append(f"Consider retraining model for {target}")
        
        except Exception as e:
            report['alerts'].append(f"Performance monitoring failed: {str(e)}")
        
        # Detect drift if baseline data provided
        if baseline_data is not None:
            try:
                drift_results = self.detect_feature_drift(
                    baseline_data, current_data, agent.config.feature_columns
                )
                report['drift_detection'] = drift_results
                
                # Check for significant drift
                drifted_features = [
                    feature for feature, metrics in drift_results.items()
                    if metrics['drift_detected']
                ]
                
                if drifted_features:
                    report['alerts'].append(f"Data drift detected in features: {drifted_features}")
                    report['recommendations'].append("Consider retraining model due to data drift")
            
            except Exception as e:
                report['alerts'].append(f"Drift detection failed: {str(e)}")
        
        # Store report
        self.save_monitoring_report(report)
        
        return report
    
    def save_monitoring_report(self, report: Dict[str, Any]) -> None:
        """Save monitoring report to storage"""
        try:
            # Load existing reports
            if os.path.exists(self.config.drift_storage_path):
                with open(self.config.drift_storage_path, 'r') as f:
                    reports = json.load(f)
            else:
                reports = []
            
            # Add new report
            reports.append(report)
            
            # Keep only last 100 reports
            reports = reports[-100:]
            
            # Save updated reports
            with open(self.config.drift_storage_path, 'w') as f:
                json.dump(reports, f, indent=2, default=str)
            
            logger.info(f"[STATUS] Monitoring report saved to {self.config.drift_storage_path}")
        
        except Exception as e:
            logger.error(f"Failed to save monitoring report: {str(e)}")
    
    def should_retrain(self, report: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Determine if model should be retrained based on monitoring report
        
        Args:
            report: Monitoring report
            
        Returns:
            Tuple of (should_retrain, reasons)
        """
        should_retrain = False
        reasons = []
        
        # Check alerts
        if report['alerts']:
            for alert in report['alerts']:
                if 'degradation' in alert.lower() or 'drift' in alert.lower():
                    should_retrain = True
                    reasons.append(alert)
        
        # Check drift detection
        if report['drift_detection']:
            drifted_count = sum(
                1 for metrics in report['drift_detection'].values()
                if metrics['drift_detected']
            )
            
            if drifted_count > len(report['drift_detection']) * 0.3:  # >30% features drifted
                should_retrain = True
                reasons.append(f"Significant drift detected in {drifted_count} features")
        
        return should_retrain, reasons

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Example usage of AI Training Monitor"""
    from ai_training_agent import AITrainingAgent
    
    # Initialize monitor
    monitor = AITrainingMonitor()
    
    # Load trained agent
    agent = AITrainingAgent()
    try:
        agent.load_models("ai_training_ensemble")
        print("[SUCCESS] Loaded trained models for monitoring")
    except FileNotFoundError:
        print("[ERROR] No trained models found. Please train models first.")
        return
    
    # Load sample data for monitoring
    data_file = "data/backtest/enhanced_strategy_results.parquet"
    if os.path.exists(data_file):
        df = pl.read_parquet(data_file)
        
        # Split data for baseline vs current comparison
        split_point = len(df) // 2
        baseline_data = df[:split_point]
        current_data = df[split_point:]
        
        # Generate monitoring report
        report = monitor.generate_monitoring_report(agent, current_data, baseline_data)
        
        print("\n[DEBUG] MONITORING REPORT")
        print("="*50)
        print(f"[STATUS] Current data: {report['data_summary']['current_data_rows']} rows")
        print(f"[WARN]  Alerts: {len(report['alerts'])}")
        print(f"[INFO] Recommendations: {len(report['recommendations'])}")
        
        if report['alerts']:
            print("\n🚨 ALERTS:")
            for alert in report['alerts']:
                print(f"   - {alert}")
        
        if report['recommendations']:
            print("\n[INFO] RECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"   - {rec}")
        
        # Check if retraining is needed
        should_retrain, reasons = monitor.should_retrain(report)
        if should_retrain:
            print(f"\n[WORKFLOW] RETRAINING RECOMMENDED:")
            for reason in reasons:
                print(f"   - {reason}")
        else:
            print("\n[SUCCESS] Model performance is stable")
    
    else:
        print(f"[ERROR] Data file not found: {data_file}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
