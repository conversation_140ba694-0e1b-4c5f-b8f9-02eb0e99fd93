#!/usr/bin/env python3
"""
📈 Live Stock Selection Agent
Scores and selects stocks based on ML predictions and quality metrics

Features:
- Scores stocks based on predicted returns (40%), risk-adjusted returns (30%),
  strategy fit confidence (20%), and data quality (10%)
- Ranks stocks by composite score
- Selects top 50 stocks ensuring diversification
- Applies risk management and quality filters
- Handles sector allocation and market cap distribution
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import yaml
import json

# Import data structures from other agents
from agents.live_ml_prediction_agent import PredictionResult
from agents.live_data_management_agent import DataQualityMetrics
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """Stock scoring result"""
    symbol: str
    predicted_return_score: float
    risk_adjusted_return_score: float
    strategy_fit_score: float
    data_quality_score: float
    composite_score: float
    rank: int
    is_selected: bool
    selection_reason: str

@dataclass
class SelectionResult:
    """Stock selection result"""
    selected_stocks: List[str]
    stock_scores: Dict[str, StockScore]
    selection_summary: Dict[str, Any]
    diversification_metrics: Dict[str, Any]
    risk_warnings: List[str]
    quality_report: Dict[str, Any]

class LiveStockSelectionAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for scoring and selecting stocks for live trading
    """

    def __init__(self, event_bus: Any, config: Any, session_id: str): # Modified constructor
        super().__init__("LiveStockSelectionAgent", event_bus, config, session_id) # Call super constructor
        self.selection_config = self.config['stock_selection']
        self.scoring_weights = self.selection_config['scoring_weights']
        self.selection_criteria = self.selection_config['selection_criteria']
        self.diversification = self.selection_config['diversification']
        self.risk_filters = self.selection_config['risk_filters']
        self.quality_filters = self.selection_config['quality_filters']

        # Selection results
        self.stock_scores: Dict[str, StockScore] = {}
        self.selected_stocks: List[str] = []
        self.selection_result: Optional[SelectionResult] = None

    async def initialize(self) -> bool:
        """Initialize the agent"""
        self.log_info("📈 Initializing Live Stock Selection Agent...")
        try:
            self.initialized = True
            self.log_info("✓ Live Stock Selection Agent initialized")
            return True
        except Exception as e:
            self.log_error(f"Failed to initialize Live Stock Selection Agent: {e}")
            self.initialized = False
            return False

    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live Stock Selection Agent started. Ready to select stocks on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live Stock Selection Agent...")
        self.running = False
        self.log_info("🧹 Live Stock Selection Agent stopped.")

    async def select_stocks(self,
                          predictions: Dict[str, PredictionResult],
                          quality_metrics: Dict[str, DataQualityMetrics],
                          market_data: Optional[Dict[str, pl.DataFrame]] = None) -> SelectionResult:
        """
        Select stocks based on predictions and quality metrics

        Args:
            predictions: ML predictions for each stock
            quality_metrics: Data quality metrics for each stock
            market_data: Optional market data for additional analysis

        Returns:
            SelectionResult with selected stocks and analysis
        """
        self.log_info(f"📈 Starting stock selection from {len(predictions)} candidates...")
        self.update_activity()

        try:
            # Step 1: Calculate scores for all stocks
            await self._calculate_stock_scores(predictions, quality_metrics, market_data)

            # Step 2: Apply filters
            filtered_stocks = self._apply_filters()

            # Step 3: Rank stocks by composite score
            ranked_stocks = self._rank_stocks(filtered_stocks)

            # Step 4: Apply diversification constraints
            selected_stocks = self._apply_diversification(ranked_stocks)

            # Step 5: Generate selection result
            selection_result = self._generate_selection_result(selected_stocks)

            self.selection_result = selection_result
            self.log_info(f"📈 Stock selection complete: {len(selection_result.selected_stocks)} stocks selected")

            return selection_result

        except Exception as e:
            self.log_error(f"Stock selection failed: {e}")
            raise

    async def _calculate_stock_scores(self,
                                    predictions: Dict[str, PredictionResult],
                                    quality_metrics: Dict[str, DataQualityMetrics],
                                    market_data: Optional[Dict[str, pl.DataFrame]]):
        """Calculate composite scores for all stocks"""
        self.log_info("🔢 Calculating stock scores...")

        for symbol in predictions.keys():
            try:
                prediction = predictions[symbol]
                quality = quality_metrics.get(symbol)

                if not prediction.is_valid:
                    self.log_debug(f"Skipping {symbol}: invalid prediction")
                    continue

                # Calculate individual score components
                predicted_return_score = self._calculate_predicted_return_score(prediction)
                risk_adjusted_return_score = self._calculate_risk_adjusted_return_score(prediction)
                strategy_fit_score = self._calculate_strategy_fit_score(prediction)
                data_quality_score = self._calculate_data_quality_score(quality)

                # Calculate composite score
                composite_score = (
                    predicted_return_score * self.scoring_weights['predicted_returns'] / 100 +
                    risk_adjusted_return_score * self.scoring_weights['risk_adjusted_returns'] / 100 +
                    strategy_fit_score * self.scoring_weights['strategy_fit_confidence'] / 100 +
                    data_quality_score * self.scoring_weights['data_quality_score'] / 100
                )

                # Store score
                self.stock_scores[symbol] = StockScore(
                    symbol=symbol,
                    predicted_return_score=predicted_return_score,
                    risk_adjusted_return_score=risk_adjusted_return_score,
                    strategy_fit_score=strategy_fit_score,
                    data_quality_score=data_quality_score,
                    composite_score=composite_score,
                    rank=0,  # Will be set during ranking
                    is_selected=False,
                    selection_reason=""
                )

                self.log_debug(f"Scored {symbol}: {composite_score:.3f}")

            except Exception as e:
                self.log_error(f"Error calculating score for {symbol}: {e}")
                continue

        self.log_info(f"🔢 Calculated scores for {len(self.stock_scores)} stocks")

    def _calculate_predicted_return_score(self, prediction: PredictionResult) -> float:
        """Calculate predicted return score (0-1)"""
        try:
            if prediction.expected_return is None:
                return 0.0

            # Normalize return to 0-1 scale
            # Assume returns range from -20% to +20%
            min_return = -0.20
            max_return = 0.20

            normalized_return = (prediction.expected_return - min_return) / (max_return - min_return)
            return max(0.0, min(1.0, normalized_return))

        except Exception as e:
            self.log_error(f"Predicted return score calculation failed: {e}")
            return 0.0

    def _calculate_risk_adjusted_return_score(self, prediction: PredictionResult) -> float:
        """Calculate risk-adjusted return score (Sharpe-like ratio)"""
        try:
            if prediction.expected_return is None:
                return 0.0

            # Get volatility from risk metrics
            volatility = prediction.risk_metrics.get('volatility', 0.02)  # Default 2%

            if volatility <= 0:
                return 0.0

            # Calculate Sharpe-like ratio
            risk_free_rate = 0.05  # 5% risk-free rate
            sharpe_ratio = (prediction.expected_return - risk_free_rate) / volatility

            # Normalize to 0-1 scale (assume Sharpe ratios range from -2 to +3)
            min_sharpe = -2.0
            max_sharpe = 3.0

            normalized_sharpe = (sharpe_ratio - min_sharpe) / (max_sharpe - min_sharpe)
            return max(0.0, min(1.0, normalized_sharpe))

        except Exception as e:
            self.log_error(f"Risk-adjusted return score calculation failed: {e}")
            return 0.0

    def _calculate_strategy_fit_score(self, prediction: PredictionResult) -> float:
        """Calculate strategy fit confidence score"""
        try:
            if not prediction.strategy_suitability:
                return 0.0

            # Use the maximum strategy suitability score
            max_suitability = max(prediction.strategy_suitability.values())

            # Weight by prediction confidence
            confidence_weight = prediction.expected_return_confidence or 0.5

            return max_suitability * confidence_weight

        except Exception as e:
            self.log_error(f"Strategy fit score calculation failed: {e}")
            return 0.0

    def _calculate_data_quality_score(self, quality: Optional[DataQualityMetrics]) -> float:
        """Calculate data quality score"""
        try:
            if quality is None:
                return 0.0

            return quality.quality_score

        except Exception as e:
            self.log_error(f"Data quality score calculation failed: {e}")
            return 0.0

    def _apply_filters(self) -> List[str]:
        """Apply risk and quality filters (slightly relaxed to target ~50 without hurting quality)."""
        self.log_info("🔍 Applying filters...")

        filtered_stocks = []

        min_score = float(self.selection_criteria.get('min_score_threshold', 0.3))
        min_quality = float(self.quality_filters.get('min_data_completeness', 0.7))

        # Slight relaxation margins
        relax_score = max(min_score - 0.02, 0.0)  # -0.02
        relax_quality = max(min_quality - 0.05, 0.0)  # -0.05

        for symbol, score in self.stock_scores.items():
            try:
                # Primary thresholds
                pass_score = score.composite_score >= min_score or score.composite_score >= relax_score
                pass_quality = score.data_quality_score >= min_quality or score.data_quality_score >= relax_quality

                if not (pass_score and pass_quality):
                    self.log_debug(f"Filtered {symbol}: score={score.composite_score:.3f}, quality={score.data_quality_score:.2f}")
                    continue

                filtered_stocks.append(symbol)

            except Exception as e:
                self.log_error(f"Error filtering {symbol}: {e}")
                continue

        self.log_info(f"🔍 Filtered to {len(filtered_stocks)} stocks (min_score={min_score}, min_quality={min_quality}, relax_score={relax_score}, relax_quality={relax_quality})")
        return filtered_stocks

    def _rank_stocks(self, filtered_stocks: List[str]) -> List[str]:
        """Rank stocks by composite score"""
        self.log_info("📊 Ranking stocks...")

        # Sort by composite score (descending)
        ranked_symbols = sorted(filtered_stocks,
                              key=lambda s: self.stock_scores[s].composite_score,
                              reverse=True)

        # Update ranks
        for i, symbol in enumerate(ranked_symbols):
            self.stock_scores[symbol].rank = i + 1

        self.log_info(f"📊 Ranked {len(ranked_symbols)} stocks")
        return ranked_symbols

    def _apply_diversification(self, ranked_stocks: List[str]) -> List[str]:
        """Apply diversification constraints"""
        self.log_info("🎯 Applying diversification constraints...")

        if not self.diversification['enabled']:
            # No diversification - just take top N
            top_n = self.selection_criteria['top_n_stocks']
            selected = ranked_stocks[:top_n]

            for symbol in selected:
                self.stock_scores[symbol].is_selected = True
                self.stock_scores[symbol].selection_reason = "Top score"

            return selected

        # Apply diversification logic
        selected_stocks = []
        sector_allocation = {}
        max_correlation = self.selection_criteria['max_correlation']

        for symbol in ranked_stocks:
            if len(selected_stocks) >= self.selection_criteria['top_n_stocks']:
                break

            # For now, use simplified diversification
            # In a full implementation, this would check:
            # - Sector allocation limits
            # - Market cap distribution
            # - Correlation constraints

            selected_stocks.append(symbol)

        # Ensure we have up to top_n by topping up with next best if filters were too strict
        top_n = int(self.selection_criteria.get('top_n_stocks', 50))
        if len(selected_stocks) < top_n:
            needed = top_n - len(selected_stocks)
            extras = [s for s in ranked_stocks if s not in selected_stocks][:needed]
            for s in extras:
                selected_stocks.append(s)
                self.stock_scores[s].is_selected = True
                self.stock_scores[s].selection_reason = "Top-up to reach target N"



        self.log_info(f"🎯 Selected {len(selected_stocks)} stocks with diversification")
        return selected_stocks

    def _generate_selection_result(self, selected_stocks: List[str]) -> SelectionResult:
        """Generate comprehensive selection result"""
        self.log_info("📋 Generating selection result...")

        # Calculate summary statistics
        all_scores = [score.composite_score for score in self.stock_scores.values()]
        selected_scores = [self.stock_scores[symbol].composite_score for symbol in selected_stocks]

        selection_summary = {
            'total_candidates': len(self.stock_scores),
            'selected_count': len(selected_stocks),
            'selection_rate': len(selected_stocks) / len(self.stock_scores) if self.stock_scores else 0,
            'avg_score_all': np.mean(all_scores) if all_scores else 0,
            'avg_score_selected': np.mean(selected_scores) if selected_scores else 0,
            'min_selected_score': min(selected_scores) if selected_scores else 0,
            'max_selected_score': max(selected_scores) if selected_scores else 0
        }

        # Diversification metrics (simplified)
        diversification_metrics = {
            'sector_distribution': {},  # Would be calculated with real sector data
            'market_cap_distribution': {},  # Would be calculated with real market cap data
            'correlation_analysis': {}  # Would be calculated with correlation matrix
        }

        # Risk warnings
        risk_warnings = []
        high_risk_threshold = 0.8

        for symbol in selected_stocks:
            score = self.stock_scores[symbol]
            if score.risk_adjusted_return_score < 0.3:
                risk_warnings.append(f"{symbol}: Low risk-adjusted return score")

        # Quality report
        quality_scores = [score.data_quality_score for score in self.stock_scores.values()]
        quality_report = {
            'avg_quality_score': np.mean(quality_scores) if quality_scores else 0,
            'min_quality_score': min(quality_scores) if quality_scores else 0,
            'stocks_with_low_quality': [
                symbol for symbol, score in self.stock_scores.items()
                if score.data_quality_score < 0.7
            ]
        }

        return SelectionResult(
            selected_stocks=selected_stocks,
            stock_scores=self.stock_scores.copy(),
            selection_summary=selection_summary,
            diversification_metrics=diversification_metrics,
            risk_warnings=risk_warnings,
            quality_report=quality_report
        )

    def get_selection_result(self) -> Optional[SelectionResult]:
        """Get the latest selection result"""
        return self.selection_result

    def get_top_stocks(self, n: int = 10) -> List[Tuple[str, float]]:
        """Get top N stocks by score"""
        sorted_stocks = sorted(self.stock_scores.items(),
                             key=lambda x: x[1].composite_score,
                             reverse=True)
        return [(symbol, score.composite_score) for symbol, score in sorted_stocks[:n]]

# Example usage and testing
async def main():
    """Example usage of Live Stock Selection Agent"""
    try:
        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        dummy_config = {
            'stock_selection': {
                'scoring_weights': {
                    'predicted_returns': 40,
                    'risk_adjusted_returns': 30,
                    'strategy_fit_confidence': 20,
                    'data_quality_score': 10
                },
                'selection_criteria': {
                    'min_score_threshold': 0.5,
                    'top_n_stocks': 50,
                    'max_correlation': 0.7
                },
                'diversification': {
                    'enabled': True,
                    'sector_limits': {'TECH': 0.3, 'FINANCE': 0.2},
                    'market_cap_bands': {'LARGE': 0.5, 'MID': 0.3, 'SMALL': 0.2}
                },
                'risk_filters': {
                    'max_volatility': 0.05,
                    'min_liquidity': 1000000
                },
                'quality_filters': {
                    'min_data_completeness': 0.8
                }
            }
        }
        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveStockSelectionAgent(event_bus, dummy_config, session_id)
        await agent.initialize()
        await agent.start()

        # Create sample predictions for testing
        sample_predictions = {}
        sample_quality_metrics = {}

        symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK", "WIPRO", "HDFCBANK", "ITC", "LT", "SBIN"]

        for i, symbol in enumerate(symbols):
            # Create sample prediction
            sample_predictions[symbol] = PredictionResult(
                symbol=symbol,
                expected_return=np.random.normal(0.05, 0.1),  # 5% mean return with 10% std
                expected_return_confidence=np.random.uniform(0.6, 0.9),
                risk_metrics={
                    'volatility': np.random.uniform(0.01, 0.05),
                    'expected_drawdown': np.random.uniform(0.05, 0.20)
                },
                strategy_suitability={
                    'momentum': np.random.uniform(0.3, 0.9),
                    'mean_reversion': np.random.uniform(0.3, 0.9),
                    'breakout': np.random.uniform(0.3, 0.9)
                },
                confidence_intervals={},
                model_versions={},
                prediction_quality=np.random.uniform(0.6, 0.95),
                is_valid=True
            )

            # Create sample quality metrics
            sample_quality_metrics[symbol] = DataQualityMetrics(
                symbol=symbol,
                total_points=np.random.randint(500, 1000),
                missing_points=np.random.randint(0, 50),
                missing_percentage=np.random.uniform(0, 5),
                outliers_detected=np.random.randint(0, 10),
                data_completeness=np.random.uniform(0.8, 1.0),
                date_range_coverage=np.random.uniform(0.9, 1.0),
                is_valid=True,
                quality_score=np.random.uniform(0.7, 0.95)
            )

        # Select stocks
        selection_result = await agent.select_stocks(sample_predictions, sample_quality_metrics)

        # Print results
        print(f"\n📈 Stock Selection Results:")
        print(f"Selected {len(selection_result.selected_stocks)} stocks from {selection_result.selection_summary['total_candidates']} candidates")
        print(f"Selection rate: {selection_result.selection_summary['selection_rate']*100:.1f}%")
        print(f"Average score (selected): {selection_result.selection_summary['avg_score_selected']:.3f}")

        print(f"\n🏆 Top 10 Selected Stocks:")
        top_stocks = agent.get_top_stocks(10)
        for i, (symbol, score) in enumerate(top_stocks[:10]):
            selected_mark = "✓" if symbol in selection_result.selected_stocks else " "
            print(f"  {i+1:2d}. {selected_mark} {symbol:10s} Score: {score:.3f}")

        print(f"\n⚠️  Risk Warnings: {len(selection_result.risk_warnings)}")
        for warning in selection_result.risk_warnings[:5]:  # Show first 5
            print(f"  • {warning}")

        print(f"\n📊 Quality Report:")
        print(f"  Average quality score: {selection_result.quality_report['avg_quality_score']:.3f}")
        print(f"  Stocks with low quality: {len(selection_result.quality_report['stocks_with_low_quality'])}")

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
