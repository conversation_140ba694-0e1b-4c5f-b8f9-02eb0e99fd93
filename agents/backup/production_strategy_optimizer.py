#!/usr/bin/env python3
"""
Production Strategy Optimizer
============================

Advanced strategy optimization system for production trading with:
- Real-time strategy performance monitoring
- Adaptive parameter optimization
- Market regime-aware strategy selection
- Multi-objective optimization (profit, risk, consistency)
- Strategy ensemble management
- Performance-based strategy allocation

Features:
- Genetic algorithm optimization
- Walk-forward analysis
- Monte Carlo simulation
- Regime-based strategy switching
- Real-time performance tracking
- Automatic strategy disabling/enabling
"""

import asyncio
import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import math
from scipy import optimize
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import TimeSeriesSplit

from .base_agent import BaseAgent
from utils.event_bus import EventBus, EventTypes

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classifications"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT = "breakout"

class StrategyState(Enum):
    """Strategy operational states"""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"
    TESTING = "testing"
    OPTIMIZING = "optimizing"

@dataclass
class StrategyPerformance:
    """Strategy performance metrics"""
    strategy_id: str
    symbol: str
    total_trades: int
    win_rate: float
    avg_return: float
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float
    expectancy: float
    consistency_score: float
    regime_performance: Dict[str, float]
    last_updated: datetime

@dataclass
class OptimizedStrategy:
    """Optimized strategy configuration"""
    strategy_id: str
    name: str
    parameters: Dict[str, Any]
    performance: StrategyPerformance
    state: StrategyState
    allocation_weight: float
    regime_suitability: Dict[MarketRegime, float]
    confidence_score: float
    last_optimized: datetime

class ProductionStrategyOptimizer(BaseAgent):
    """
    Production-ready strategy optimization system
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("ProductionStrategyOptimizer", event_bus, config, session_id)
        
        # Strategy management
        self.active_strategies = {}
        self.strategy_performance = {}
        self.strategy_allocations = {}
        
        # Market regime detection
        self.current_regime = MarketRegime.SIDEWAYS
        self.regime_history = []
        self.regime_confidence = 0.5
        
        # Optimization parameters
        self.optimization_interval = 3600  # 1 hour
        self.min_trades_for_optimization = 20
        self.performance_lookback_days = 30
        self.regime_lookback_days = 7
        
        # Performance thresholds
        self.min_sharpe_ratio = 0.5
        self.max_drawdown_threshold = 0.15  # 15%
        self.min_win_rate = 0.45  # 45%
        self.min_profit_factor = 1.2
        
        # Ensemble management
        self.max_active_strategies = 5
        self.strategy_correlation_threshold = 0.7
        
        # Real-time tracking
        self.trade_history = []
        self.performance_history = []
        
        logger.info(f"[INIT] {self.name} initialized with production optimization")
    
    async def start(self):
        """Start the strategy optimizer"""
        try:
            await super().start()
            
            # Initialize strategies
            await self._initialize_strategies()
            
            # Start optimization loops
            asyncio.create_task(self._optimization_loop())
            asyncio.create_task(self._regime_monitoring_loop())
            asyncio.create_task(self._performance_monitoring_loop())
            asyncio.create_task(self._allocation_rebalancing_loop())
            
            # Subscribe to events
            await self._subscribe_to_events()
            
            logger.info(f"[START] {self.name} started with optimization active")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            raise
    
    async def _initialize_strategies(self):
        """Initialize base strategies for optimization"""
        try:
            # Define base strategy templates
            base_strategies = {
                'momentum_rsi': {
                    'name': 'RSI Momentum',
                    'type': 'momentum',
                    'parameters': {
                        'rsi_period': 14,
                        'rsi_overbought': 70,
                        'rsi_oversold': 30,
                        'volume_threshold': 1.5,
                        'trend_filter': True
                    },
                    'regime_suitability': {
                        MarketRegime.TRENDING_UP: 0.8,
                        MarketRegime.TRENDING_DOWN: 0.8,
                        MarketRegime.SIDEWAYS: 0.3,
                        MarketRegime.HIGH_VOLATILITY: 0.6,
                        MarketRegime.LOW_VOLATILITY: 0.4,
                        MarketRegime.BREAKOUT: 0.9
                    }
                },
                'mean_reversion_bb': {
                    'name': 'Bollinger Bands Mean Reversion',
                    'type': 'mean_reversion',
                    'parameters': {
                        'bb_period': 20,
                        'bb_std': 2.0,
                        'rsi_period': 14,
                        'rsi_threshold': 30,
                        'volume_confirmation': True
                    },
                    'regime_suitability': {
                        MarketRegime.TRENDING_UP: 0.4,
                        MarketRegime.TRENDING_DOWN: 0.4,
                        MarketRegime.SIDEWAYS: 0.9,
                        MarketRegime.HIGH_VOLATILITY: 0.3,
                        MarketRegime.LOW_VOLATILITY: 0.8,
                        MarketRegime.BREAKOUT: 0.2
                    }
                },
                'breakout_volume': {
                    'name': 'Volume Breakout',
                    'type': 'breakout',
                    'parameters': {
                        'lookback_period': 20,
                        'breakout_threshold': 0.02,
                        'volume_multiplier': 2.0,
                        'atr_period': 14,
                        'confirmation_bars': 2
                    },
                    'regime_suitability': {
                        MarketRegime.TRENDING_UP: 0.7,
                        MarketRegime.TRENDING_DOWN: 0.7,
                        MarketRegime.SIDEWAYS: 0.5,
                        MarketRegime.HIGH_VOLATILITY: 0.9,
                        MarketRegime.LOW_VOLATILITY: 0.3,
                        MarketRegime.BREAKOUT: 1.0
                    }
                },
                'trend_following_ema': {
                    'name': 'EMA Trend Following',
                    'type': 'trend_following',
                    'parameters': {
                        'fast_ema': 12,
                        'slow_ema': 26,
                        'signal_ema': 9,
                        'trend_strength_threshold': 0.5,
                        'pullback_entry': True
                    },
                    'regime_suitability': {
                        MarketRegime.TRENDING_UP: 0.9,
                        MarketRegime.TRENDING_DOWN: 0.9,
                        MarketRegime.SIDEWAYS: 0.2,
                        MarketRegime.HIGH_VOLATILITY: 0.6,
                        MarketRegime.LOW_VOLATILITY: 0.7,
                        MarketRegime.BREAKOUT: 0.8
                    }
                },
                'scalping_vwap': {
                    'name': 'VWAP Scalping',
                    'type': 'scalping',
                    'parameters': {
                        'vwap_deviation_threshold': 0.001,
                        'volume_threshold': 1.2,
                        'time_window': 15,  # minutes
                        'profit_target': 0.003,
                        'stop_loss': 0.002
                    },
                    'regime_suitability': {
                        MarketRegime.TRENDING_UP: 0.6,
                        MarketRegime.TRENDING_DOWN: 0.6,
                        MarketRegime.SIDEWAYS: 0.8,
                        MarketRegime.HIGH_VOLATILITY: 0.7,
                        MarketRegime.LOW_VOLATILITY: 0.9,
                        MarketRegime.BREAKOUT: 0.4
                    }
                }
            }
            
            # Initialize optimized strategies
            for strategy_id, config in base_strategies.items():
                optimized_strategy = OptimizedStrategy(
                    strategy_id=strategy_id,
                    name=config['name'],
                    parameters=config['parameters'],
                    performance=None,  # Will be calculated
                    state=StrategyState.ACTIVE,
                    allocation_weight=1.0 / len(base_strategies),  # Equal weight initially
                    regime_suitability=config['regime_suitability'],
                    confidence_score=0.5,  # Initial confidence
                    last_optimized=datetime.now()
                )
                
                self.active_strategies[strategy_id] = optimized_strategy
            
            logger.info(f"[INIT] Initialized {len(self.active_strategies)} base strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize strategies: {e}")
    
    async def _optimization_loop(self):
        """Main optimization loop"""
        while self.is_running:
            try:
                await self._optimize_strategies()
                await self._update_strategy_allocations()
                await self._prune_underperforming_strategies()
                await self._generate_new_strategies()
                
                await asyncio.sleep(self.optimization_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Optimization loop failed: {e}")
                await asyncio.sleep(300)  # 5 minutes on error
    
    async def _regime_monitoring_loop(self):
        """Monitor market regime changes"""
        while self.is_running:
            try:
                await self._detect_market_regime()
                await self._adjust_strategies_for_regime()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"[ERROR] Regime monitoring failed: {e}")
                await asyncio.sleep(60)
    
    async def _performance_monitoring_loop(self):
        """Monitor strategy performance in real-time"""
        while self.is_running:
            try:
                await self._update_strategy_performance()
                await self._check_performance_thresholds()
                await self._log_performance_summary()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"[ERROR] Performance monitoring failed: {e}")
                await asyncio.sleep(30)
    
    async def _allocation_rebalancing_loop(self):
        """Rebalance strategy allocations"""
        while self.is_running:
            try:
                await self._rebalance_allocations()
                await asyncio.sleep(1800)  # Rebalance every 30 minutes
                
            except Exception as e:
                logger.error(f"[ERROR] Allocation rebalancing failed: {e}")
                await asyncio.sleep(300)
    
    async def _optimize_strategies(self):
        """Optimize strategy parameters using genetic algorithm"""
        try:
            for strategy_id, strategy in self.active_strategies.items():
                if strategy.state != StrategyState.ACTIVE:
                    continue
                
                # Check if optimization is needed
                time_since_optimization = datetime.now() - strategy.last_optimized
                if time_since_optimization.total_seconds() < 3600:  # 1 hour minimum
                    continue
                
                # Get recent performance data
                performance_data = await self._get_strategy_performance_data(strategy_id)
                
                if len(performance_data) < self.min_trades_for_optimization:
                    continue
                
                logger.info(f"[OPTIMIZE] Starting optimization for {strategy_id}")
                
                # Run optimization
                optimized_params = await self._genetic_algorithm_optimization(
                    strategy_id, strategy.parameters, performance_data
                )
                
                if optimized_params:
                    # Validate optimized parameters
                    validation_score = await self._validate_optimized_parameters(
                        strategy_id, optimized_params, performance_data
                    )
                    
                    if validation_score > strategy.confidence_score:
                        # Update strategy with optimized parameters
                        strategy.parameters = optimized_params
                        strategy.confidence_score = validation_score
                        strategy.last_optimized = datetime.now()
                        
                        logger.info(f"[OPTIMIZE] {strategy_id} optimized - "
                                  f"Confidence: {validation_score:.3f}")
                    else:
                        logger.warning(f"[OPTIMIZE] {strategy_id} optimization rejected - "
                                     f"Score: {validation_score:.3f}")
                
        except Exception as e:
            logger.error(f"[ERROR] Strategy optimization failed: {e}")
    
    async def _genetic_algorithm_optimization(
        self, 
        strategy_id: str, 
        current_params: Dict[str, Any], 
        performance_data: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Optimize strategy parameters using genetic algorithm"""
        try:
            # Define parameter bounds based on strategy type
            param_bounds = self._get_parameter_bounds(strategy_id)
            
            if not param_bounds:
                return None
            
            # Create initial population
            population_size = 20
            generations = 10
            mutation_rate = 0.1
            
            population = self._create_initial_population(
                current_params, param_bounds, population_size
            )
            
            best_params = current_params.copy()
            best_fitness = await self._evaluate_fitness(strategy_id, current_params, performance_data)
            
            for generation in range(generations):
                # Evaluate fitness for all individuals
                fitness_scores = []
                for individual in population:
                    fitness = await self._evaluate_fitness(strategy_id, individual, performance_data)
                    fitness_scores.append(fitness)
                    
                    if fitness > best_fitness:
                        best_fitness = fitness
                        best_params = individual.copy()
                
                # Selection, crossover, and mutation
                population = self._evolve_population(
                    population, fitness_scores, mutation_rate
                )
                
                logger.debug(f"[GA] Generation {generation + 1}: Best fitness = {best_fitness:.4f}")
            
            return best_params if best_fitness > 0 else None
            
        except Exception as e:
            logger.error(f"[ERROR] Genetic algorithm optimization failed: {e}")
            return None
    
    async def _detect_market_regime(self):
        """Detect current market regime"""
        try:
            # Get recent market data
            market_data = await self._get_recent_market_data()
            
            if len(market_data) < 50:
                return
            
            # Calculate regime indicators
            returns = np.diff(np.log(market_data['close']))
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
            
            # Trend strength
            sma_20 = market_data['close'].rolling(20).mean()
            sma_50 = market_data['close'].rolling(50).mean()
            trend_strength = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
            
            # Volume analysis
            avg_volume = market_data['volume'].rolling(20).mean()
            volume_ratio = market_data['volume'].iloc[-1] / avg_volume.iloc[-1]
            
            # Regime classification
            if abs(trend_strength) > 0.02 and volatility < 0.25:
                if trend_strength > 0:
                    new_regime = MarketRegime.TRENDING_UP
                else:
                    new_regime = MarketRegime.TRENDING_DOWN
            elif volatility > 0.35:
                new_regime = MarketRegime.HIGH_VOLATILITY
            elif volatility < 0.15:
                new_regime = MarketRegime.LOW_VOLATILITY
            elif volume_ratio > 2.0 and abs(returns[-1]) > 0.02:
                new_regime = MarketRegime.BREAKOUT
            else:
                new_regime = MarketRegime.SIDEWAYS
            
            # Update regime if changed
            if new_regime != self.current_regime:
                logger.info(f"[REGIME] Market regime changed: {self.current_regime.value} -> {new_regime.value}")
                self.current_regime = new_regime
                
                # Add to history
                self.regime_history.append({
                    'regime': new_regime,
                    'timestamp': datetime.now(),
                    'volatility': volatility,
                    'trend_strength': trend_strength,
                    'volume_ratio': volume_ratio
                })
                
                # Keep only recent history
                if len(self.regime_history) > 100:
                    self.regime_history = self.regime_history[-100:]
            
        except Exception as e:
            logger.error(f"[ERROR] Market regime detection failed: {e}")
    
    async def _adjust_strategies_for_regime(self):
        """Adjust strategy allocations based on current regime"""
        try:
            regime_adjustments = {}
            
            for strategy_id, strategy in self.active_strategies.items():
                # Get regime suitability score
                suitability = strategy.regime_suitability.get(self.current_regime, 0.5)
                
                # Adjust allocation based on suitability
                if suitability > 0.7:
                    adjustment = 1.2  # Increase allocation
                elif suitability > 0.5:
                    adjustment = 1.0  # Keep current allocation
                elif suitability > 0.3:
                    adjustment = 0.7  # Reduce allocation
                else:
                    adjustment = 0.3  # Significantly reduce allocation
                
                regime_adjustments[strategy_id] = adjustment
            
            # Normalize adjustments
            total_adjustment = sum(regime_adjustments.values())
            if total_adjustment > 0:
                for strategy_id in regime_adjustments:
                    normalized_weight = regime_adjustments[strategy_id] / total_adjustment
                    self.active_strategies[strategy_id].allocation_weight = normalized_weight
            
            logger.debug(f"[REGIME] Adjusted allocations for {self.current_regime.value}")
            
        except Exception as e:
            logger.error(f"[ERROR] Regime adjustment failed: {e}")
    
    def get_strategy_recommendations(self, symbol: str) -> List[Dict[str, Any]]:
        """Get strategy recommendations for a symbol"""
        try:
            recommendations = []
            
            for strategy_id, strategy in self.active_strategies.items():
                if strategy.state != StrategyState.ACTIVE:
                    continue
                
                # Calculate recommendation score
                regime_score = strategy.regime_suitability.get(self.current_regime, 0.5)
                confidence_score = strategy.confidence_score
                allocation_weight = strategy.allocation_weight
                
                # Performance bonus
                performance_bonus = 1.0
                if strategy.performance:
                    if strategy.performance.sharpe_ratio > 1.0:
                        performance_bonus = 1.2
                    elif strategy.performance.sharpe_ratio < 0.5:
                        performance_bonus = 0.8
                
                overall_score = (
                    regime_score * 0.4 +
                    confidence_score * 0.3 +
                    allocation_weight * 0.3
                ) * performance_bonus
                
                recommendations.append({
                    'strategy_id': strategy_id,
                    'strategy_name': strategy.name,
                    'parameters': strategy.parameters,
                    'score': overall_score,
                    'regime_suitability': regime_score,
                    'confidence': confidence_score,
                    'allocation_weight': allocation_weight,
                    'state': strategy.state.value
                })
            
            # Sort by score
            recommendations.sort(key=lambda x: x['score'], reverse=True)
            
            return recommendations[:3]  # Return top 3 recommendations
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get strategy recommendations: {e}")
            return []
