#!/usr/bin/env python3
"""
AI Training Agent Utilities
Helper functions and utilities for the AI Training Agent
"""

import os
import json
import logging
import yaml
import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import joblib
from datetime import datetime
import psutil

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CONFIGURATION UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def load_config(config_path: str = "config/ai_training_config.yaml") -> Dict[str, Any]:
    """
    Load configuration from YAML file
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    logger.info(f"[LIST] Configuration loaded from: {config_path}")
    return config

def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    Save configuration to YAML file
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration
    """
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    logger.info(f"💾 Configuration saved to: {config_path}")

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def validate_data(df: pl.DataFrame, required_columns: List[str]) -> Tuple[bool, List[str]]:
    """
    Validate that DataFrame contains required columns
    
    Args:
        df: DataFrame to validate
        required_columns: List of required column names
        
    Returns:
        Tuple of (is_valid, missing_columns)
    """
    missing_columns = [col for col in required_columns if col not in df.columns]
    is_valid = len(missing_columns) == 0
    
    if not is_valid:
        logger.warning(f"[WARN]  Missing columns: {missing_columns}")
    
    return is_valid, missing_columns

def check_data_quality(df: pl.DataFrame) -> Dict[str, Any]:
    """
    Check data quality metrics
    
    Args:
        df: DataFrame to check
        
    Returns:
        Dictionary of data quality metrics
    """
    quality_metrics = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'missing_values': {},
        'data_types': {},
        'duplicates': 0,
        'memory_usage_mb': 0
    }
    
    # Check missing values
    for col in df.columns:
        null_count = df[col].null_count()
        if null_count > 0:
            quality_metrics['missing_values'][col] = {
                'count': null_count,
                'percentage': (null_count / len(df)) * 100
            }
    
    # Check data types
    for col in df.columns:
        quality_metrics['data_types'][col] = str(df[col].dtype)
    
    # Check duplicates
    quality_metrics['duplicates'] = len(df) - len(df.unique())
    
    # Estimate memory usage
    quality_metrics['memory_usage_mb'] = df.estimated_size() / (1024 * 1024)
    
    logger.info(f"[STATUS] Data quality check completed: {quality_metrics['total_rows']} rows, {quality_metrics['total_columns']} columns")
    
    return quality_metrics

def clean_data(df: pl.DataFrame, 
               remove_duplicates: bool = True,
               fill_missing: str = "mean") -> pl.DataFrame:
    """
    Clean data by handling missing values and duplicates
    
    Args:
        df: DataFrame to clean
        remove_duplicates: Whether to remove duplicate rows
        fill_missing: Strategy for filling missing values ("mean", "median", "mode", "drop")
        
    Returns:
        Cleaned DataFrame
    """
    logger.info("🧹 Cleaning data...")
    
    original_rows = len(df)
    
    # Remove duplicates
    if remove_duplicates:
        df = df.unique()
        logger.info(f"   Removed {original_rows - len(df)} duplicate rows")
    
    # Handle missing values
    if fill_missing == "mean":
        df = df.fill_null(strategy="mean")
    elif fill_missing == "median":
        df = df.fill_null(strategy="median")
    elif fill_missing == "mode":
        # Fill with most frequent value for each column
        for col in df.columns:
            if df[col].null_count() > 0:
                mode_value = df[col].mode().first()
                df = df.with_columns(pl.col(col).fill_null(mode_value))
    elif fill_missing == "drop":
        df = df.drop_nulls()
        logger.info(f"   Dropped rows with missing values, {len(df)} rows remaining")
    
    logger.info(f"[SUCCESS] Data cleaning completed: {len(df)} rows remaining")
    return df

# ═══════════════════════════════════════════════════════════════════════════════
# [AGENT] MODEL UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def save_training_metrics(metrics: Dict[str, Any], 
                         file_path: str = "data/models/training_metrics.json") -> None:
    """
    Save training metrics to JSON file
    
    Args:
        metrics: Training metrics dictionary
        file_path: Path to save metrics
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # Add timestamp
    metrics['timestamp'] = datetime.now().isoformat()
    
    with open(file_path, 'w') as f:
        json.dump(metrics, f, indent=2, default=str)
    
    logger.info(f"[STATUS] Training metrics saved to: {file_path}")

def load_training_metrics(file_path: str = "data/models/training_metrics.json") -> Dict[str, Any]:
    """
    Load training metrics from JSON file
    
    Args:
        file_path: Path to metrics file
        
    Returns:
        Training metrics dictionary
    """
    if not os.path.exists(file_path):
        logger.warning(f"[WARN]  Metrics file not found: {file_path}")
        return {}
    
    with open(file_path, 'r') as f:
        metrics = json.load(f)
    
    logger.info(f"[STATUS] Training metrics loaded from: {file_path}")
    return metrics

def calculate_feature_importance_summary(feature_importance: Dict[str, Dict[str, float]]) -> Dict[str, float]:
    """
    Calculate summary of feature importance across all models
    
    Args:
        feature_importance: Dictionary of feature importance per model
        
    Returns:
        Average feature importance across all models
    """
    if not feature_importance:
        return {}
    
    # Get all feature names
    all_features = set()
    for model_importance in feature_importance.values():
        all_features.update(model_importance.keys())
    
    # Calculate average importance
    avg_importance = {}
    for feature in all_features:
        importances = []
        for model_importance in feature_importance.values():
            if feature in model_importance:
                importances.append(model_importance[feature])
        
        if importances:
            avg_importance[feature] = np.mean(importances)
    
    # Sort by importance
    avg_importance = dict(sorted(avg_importance.items(), key=lambda x: x[1], reverse=True))
    
    return avg_importance

# ═══════════════════════════════════════════════════════════════════════════════
# [SYSTEM] SYSTEM UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def get_system_info() -> Dict[str, Any]:
    """
    Get system information for monitoring
    
    Returns:
        Dictionary of system information
    """
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) if gpu_available else 0
    except ImportError:
        gpu_available = False
        gpu_count = 0
        gpu_memory = 0
    
    # Get disk usage with error handling for Windows
    try:
        disk_usage_percent = psutil.disk_usage('C:\\').percent
    except (SystemError, OSError):
        try:
            disk_usage_percent = psutil.disk_usage('.').percent
        except:
            disk_usage_percent = 0.0  # Fallback if disk usage can't be determined

    system_info = {
        'cpu_count': psutil.cpu_count(),
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_total_gb': psutil.virtual_memory().total / (1024**3),
        'memory_available_gb': psutil.virtual_memory().available / (1024**3),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_usage_percent': disk_usage_percent,
        'gpu_available': gpu_available,
        'gpu_count': gpu_count,
        'gpu_memory_gb': gpu_memory
    }
    
    return system_info

def check_dependencies() -> Dict[str, bool]:
    """
    Check if required dependencies are available
    
    Returns:
        Dictionary of dependency availability
    """
    dependencies = {
        'lightgbm': False,
        'optuna': False,
        'sklearn': False,
        'pytorch_tabnet': False,
        'torch': False,
        'polars': False,
        'cudf': False,
        'cupy': False,
        'fastapi': False
    }
    
    # Check each dependency
    try:
        import lightgbm
        dependencies['lightgbm'] = True
    except ImportError:
        pass
    
    try:
        import optuna
        dependencies['optuna'] = True
    except ImportError:
        pass
    
    try:
        import sklearn
        dependencies['sklearn'] = True
    except ImportError:
        pass
    
    try:
        import pytorch_tabnet
        dependencies['pytorch_tabnet'] = True
    except ImportError:
        pass
    
    try:
        import torch
        dependencies['torch'] = True
    except ImportError:
        pass
    
    try:
        import polars
        dependencies['polars'] = True
    except ImportError:
        pass
    
    try:
        import cudf
        dependencies['cudf'] = True
    except ImportError:
        pass
    
    try:
        import cupy
        dependencies['cupy'] = True
    except ImportError:
        pass
    
    try:
        import fastapi
        dependencies['fastapi'] = True
    except ImportError:
        pass
    
    return dependencies

def setup_logging(log_level: str = "INFO", 
                 log_file: Optional[str] = None) -> None:
    """
    Setup logging configuration
    
    Args:
        log_level: Logging level
        log_file: Optional log file path
    """
    # Create logs directory if logging to file
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )
    
    logger.info(f"📝 Logging configured: level={log_level}, file={log_file}")

# ═══════════════════════════════════════════════════════════════════════════════
# [TARGET] STRATEGY RANKING UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def calculate_pareto_frontier(strategies: List[Dict[str, Any]], 
                             objectives: List[str]) -> List[Dict[str, Any]]:
    """
    Calculate Pareto frontier for multi-objective optimization
    
    Args:
        strategies: List of strategy dictionaries with metrics
        objectives: List of objective names to optimize
        
    Returns:
        List of strategies on the Pareto frontier
    """
    if not strategies:
        return []
    
    pareto_strategies = []
    
    for i, strategy in enumerate(strategies):
        is_dominated = False
        
        for j, other_strategy in enumerate(strategies):
            if i == j:
                continue
            
            # Check if strategy is dominated by other_strategy
            dominates = True
            for obj in objectives:
                strategy_val = strategy.get('predicted_metrics', {}).get(obj, 0)
                other_val = other_strategy.get('predicted_metrics', {}).get(obj, 0)
                
                # For max_drawdown, lower is better (negative objective)
                if obj == 'max_drawdown':
                    if strategy_val > other_val:  # Higher drawdown is worse
                        dominates = False
                        break
                else:
                    if strategy_val < other_val:  # Lower value is worse
                        dominates = False
                        break
            
            if dominates:
                is_dominated = True
                break
        
        if not is_dominated:
            pareto_strategies.append(strategy)
    
    return pareto_strategies
