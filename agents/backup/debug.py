#!/usr/bin/env python3
"""
Diagnostic Sc<PERSON><PERSON> to Debug Why No Results Are Generated
Run this to understand what's happening with your data and strategies
"""

import polars as pl
import yaml
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

def diagnose_data_and_strategies():
    """Comprehensive diagnostic of data and strategies"""
    
    print("🔍 BACKTESTING DIAGNOSTIC REPORT")
    print("=" * 50)
    
    # 1. Check strategies file
    print("\n1. STRATEGIES ANALYSIS")
    print("-" * 30)
    
    strategies_file = None
    for path in ["config/strategies.yaml", "../config/strategies.yaml"]:
        if Path(path).exists():
            strategies_file = path
            break
    
    if not strategies_file:
        print("❌ No strategies file found!")
        return
    
    try:
        with open(strategies_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])
        print(f"✅ Found {len(strategies)} strategies")
        
        # Analyze first few strategies
        for i, strategy in enumerate(strategies[:3]):
            print(f"\n  Strategy {i+1}: {strategy.get('name', 'Unknown')}")
            print(f"    Long condition: {strategy.get('long', 'None')[:100]}...")
            print(f"    Short condition: {strategy.get('short', 'None')[:100]}...")
            
    except Exception as e:
        print(f"❌ Error reading strategies: {e}")
        return
    
    # 2. Check data files
    print("\n2. DATA FILES ANALYSIS")
    print("-" * 30)
    
    data_dir = None
    for path in ["data/features", "../data/features"]:
        if Path(path).exists():
            data_dir = path
            break
    
    if not data_dir:
        print("❌ No data directory found!")
        return
    
    parquet_files = list(Path(data_dir).glob("*.parquet"))
    print(f"✅ Found {len(parquet_files)} parquet files")
    
    if not parquet_files:
        print("❌ No parquet files found!")
        return
    
    # Analyze first file in detail
    first_file = parquet_files[0]
    print(f"\n  Analyzing sample file: {first_file.name}")
    
    try:
        df = pl.read_parquet(first_file)
        print(f"    Rows: {len(df)}")
        print(f"    Columns: {df.columns}")
        print(f"    Data types: {df.dtypes}")
        
        # Check for required columns
        required_cols = ['close', 'open', 'high', 'low', 'volume', 'datetime']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"    ❌ Missing required columns: {missing_cols}")
        else:
            print(f"    ✅ All required columns present")
        
        # Check data quality
        print(f"\n  Data Quality Check:")
        for col in ['close', 'open', 'high', 'low', 'volume']:
            if col in df.columns:
                null_count = df.select(pl.col(col).is_null().sum()).item()
                min_val = df.select(pl.col(col).min()).item()
                max_val = df.select(pl.col(col).max()).item()
                print(f"    {col}: nulls={null_count}, min={min_val:.2f}, max={max_val:.2f}")
        
        # Check for technical indicators
        tech_indicators = []
        for col in df.columns:
            if any(indicator in col.lower() for indicator in [
                'rsi', 'ema', 'sma', 'macd', 'bb', 'atr', 'vwap', 'cpr', 'supertrend',
                'stoch', 'cci', 'mfi', 'adx'
            ]):
                tech_indicators.append(col)
        
        print(f"\n  Technical Indicators Found: {len(tech_indicators)}")
        for indicator in tech_indicators[:10]:  # Show first 10
            print(f"    - {indicator}")
        if len(tech_indicators) > 10:
            print(f"    ... and {len(tech_indicators) - 10} more")
            
    except Exception as e:
        print(f"❌ Error reading data file: {e}")
        return
    
    # 3. Test signal generation
    print("\n3. SIGNAL GENERATION TEST")
    print("-" * 30)
    
    # Test first strategy on first file
    test_strategy = strategies[0]
    print(f"Testing strategy: {test_strategy['name']}")
    
    try:
        # Simple signal generation test
        long_condition = test_strategy.get('long', '')
        short_condition = test_strategy.get('short', '')
        
        print(f"Long condition: {long_condition}")
        print(f"Short condition: {short_condition}")
        
        # Test if basic columns work
        basic_test = df.select([
            pl.col('close').mean().alias('avg_close'),
            pl.col('volume').mean().alias('avg_volume'),
            (pl.col('close') > pl.col('close').shift(1)).sum().alias('up_days')
        ])
        
        print(f"Basic stats: {basic_test.to_dict()}")
        
        # Try to evaluate a simple condition
        try:
            if 'rsi' in long_condition.lower():
                # Check if RSI columns exist
                rsi_cols = [col for col in df.columns if 'rsi' in col.lower()]
                print(f"RSI columns available: {rsi_cols}")
                
            if 'ema' in long_condition.lower():
                # Check if EMA columns exist
                ema_cols = [col for col in df.columns if 'ema' in col.lower()]
                print(f"EMA columns available: {ema_cols}")
        
        except Exception as e:
            print(f"Signal evaluation error: {e}")
            
    except Exception as e:
        print(f"❌ Error in signal generation test: {e}")
    
    # 4. Recommendations
    print("\n4. RECOMMENDATIONS")
    print("-" * 30)
    
    if len(tech_indicators) == 0:
        print("❌ No technical indicators found in data!")
        print("   Your strategies require indicators like RSI, EMA, etc.")
        print("   Make sure your feature engineering step ran successfully.")
    else:
        print("✅ Technical indicators found - data looks good")
    
    if missing_cols:
        print(f"❌ Missing basic OHLCV columns: {missing_cols}")
        print("   Check your data preprocessing pipeline")
    else:
        print("✅ All basic OHLCV columns present")
    
    print("\n5. DEBUGGING STEPS")
    print("-" * 30)
    print("1. Run with debug logging:")
    print("   python run_optimized.py --symbol RELIANCE --timeframe 1min --max-strategies 1 --log-level DEBUG")
    print("\n2. Check if strategies match your data columns")
    print("3. Verify technical indicators are properly calculated")
    print("4. Try simpler strategies first (e.g., price-only conditions)")

if __name__ == "__main__":
    diagnose_data_and_strategies()