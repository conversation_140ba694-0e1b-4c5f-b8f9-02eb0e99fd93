"""
Risk Management Package - Modular Risk Management System

This module contains all risk management-related agents and modular components.
"""

# Legacy agents (for backward compatibility)
from .risk_agent import RiskManagementAgent
from .production_risk_manager import ProductionRiskManager

# New modular components will be imported as they are created
try:
    from .core.capital_allocator import CapitalAllocator
    from .core.position_sizer import PositionSizer
    from .core.pre_trade_filters import PreTradeFilters
    from .core.circuit_breakers import CircuitBreakers
    from .core.portfolio_monitor import PortfolioMonitor
    from .core.risk_calculator import RiskCalculator
    from .core.configuration_manager import ConfigurationManager
    MODULAR_COMPONENTS_AVAILABLE = True
except ImportError:
    MODULAR_COMPONENTS_AVAILABLE = False

__all__ = [
    # Legacy agents
    'RiskManagementAgent',
    'ProductionRiskManager',
    'MODULAR_COMPONENTS_AVAILABLE'
]

if MODULAR_COMPONENTS_AVAILABLE:
    __all__.extend([
        'CapitalAllocator',
        'PositionSizer',
        'PreTradeFilters',
        'CircuitBreakers',
        'PortfolioMonitor',
        'RiskCalculator',
        'ConfigurationManager'
    ])
