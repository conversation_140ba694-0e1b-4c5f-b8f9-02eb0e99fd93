#!/usr/bin/env python3
"""
Position Sizer Module

Advanced position sizing engine with multiple methodologies:
- Kelly Criterion with historical data
- Volatility-based sizing
- Risk parity approach
- Performance-based adaptive sizing
- Machine learning enhanced sizing
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

# Import utility modules
from utils.risk_models import (
    TradeRequest, Position, Portfolio, RiskMetrics,
    TradeDirection, ProductType
)

logger = logging.getLogger(__name__)


class PositionSizingMethod(Enum):
    """Position sizing methods"""
    FIXED_FRACTION = "fixed_fraction"
    KELLY_CRITERION = "kelly_criterion"
    VOLATILITY_ADJUSTED = "volatility_adjusted"
    RISK_PARITY = "risk_parity"
    ADAPTIVE = "adaptive"
    PERFORMANCE_BASED = "performance_based"
    ML_ENHANCED = "ml_enhanced"


@dataclass
class PositionSizeResult:
    """Position sizing calculation result"""
    quantity: int
    capital_allocated: float
    risk_amount: float
    method_used: str
    confidence: float
    sizing_factors: Dict[str, float]
    warnings: List[str]
    timestamp: datetime


@dataclass
class KellyParameters:
    """Kelly Criterion parameters"""
    win_rate: float
    avg_win: float
    avg_loss: float
    kelly_fraction: float
    confidence: float
    sample_size: int
    lookback_days: int


class PositionSizer:
    """
    Advanced Position Sizing Engine
    
    Features:
    - Multiple sizing methodologies
    - Real-time volatility adjustment
    - Historical performance integration
    - Machine learning enhancement
    - Risk-adjusted position sizing
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Position Sizer"""
        self.config = config
        self.sizing_config = config.get('position_sizing', {})
        
        # Sizing parameters
        self.default_method = PositionSizingMethod(
            self.sizing_config.get('default_method', 'adaptive')
        )
        self.max_position_size_percent = self.sizing_config.get('max_position_size_percent', 5.0)
        self.min_position_size_percent = self.sizing_config.get('min_position_size_percent', 0.1)
        
        # Kelly Criterion settings
        self.kelly_config = self.sizing_config.get('kelly_criterion', {})
        self.kelly_lookback_days = self.kelly_config.get('lookback_days', 30)
        self.kelly_max_fraction = self.kelly_config.get('max_fraction', 0.25)
        self.kelly_min_trades = self.kelly_config.get('min_trades', 10)
        
        # Volatility settings
        self.volatility_config = self.sizing_config.get('volatility_adjustment', {})
        self.volatility_lookback = self.volatility_config.get('lookback_days', 20)
        self.volatility_target = self.volatility_config.get('target_volatility', 0.02)
        
        # Performance tracking
        self.trade_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {}
        
        logger.info(f"[INIT] Position Sizer initialized with method: {self.default_method.value}")
    
    def calculate_position_size(
        self,
        trade_request: TradeRequest,
        available_capital: float,
        method: Optional[PositionSizingMethod] = None,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """
        Calculate optimal position size using specified method
        
        Args:
            trade_request: Trade request details
            available_capital: Available capital for allocation
            method: Position sizing method to use
            market_data: Additional market data for calculations
            
        Returns:
            PositionSizeResult with calculated position size
        """
        try:
            sizing_method = method or self.default_method
            warnings = []
            
            # Calculate basic risk parameters
            entry_price = trade_request.entry_price
            stop_loss = trade_request.stop_loss
            risk_per_share = abs(entry_price - stop_loss)
            
            if risk_per_share <= 0:
                return self._create_error_result("Invalid risk per share", trade_request)
            
            # Calculate position size based on method
            if sizing_method == PositionSizingMethod.FIXED_FRACTION:
                result = self._calculate_fixed_fraction_size(
                    trade_request, available_capital, risk_per_share
                )
            elif sizing_method == PositionSizingMethod.KELLY_CRITERION:
                result = self._calculate_kelly_size(
                    trade_request, available_capital, risk_per_share, market_data
                )
            elif sizing_method == PositionSizingMethod.VOLATILITY_ADJUSTED:
                result = self._calculate_volatility_adjusted_size(
                    trade_request, available_capital, risk_per_share, market_data
                )
            elif sizing_method == PositionSizingMethod.ADAPTIVE:
                result = self._calculate_adaptive_size(
                    trade_request, available_capital, risk_per_share, market_data
                )
            elif sizing_method == PositionSizingMethod.PERFORMANCE_BASED:
                result = self._calculate_performance_based_size(
                    trade_request, available_capital, risk_per_share, market_data
                )
            elif sizing_method == PositionSizingMethod.ML_ENHANCED:
                result = self._calculate_ml_enhanced_size(
                    trade_request, available_capital, risk_per_share, market_data
                )
            else:
                # Default to fixed fraction
                result = self._calculate_fixed_fraction_size(
                    trade_request, available_capital, risk_per_share
                )
                warnings.append(f"Method {sizing_method.value} not implemented, using fixed_fraction")
            
            # Apply position size limits
            result = self._apply_position_limits(result, available_capital, entry_price)
            
            # Add warnings to result
            result.warnings.extend(warnings)
            
            logger.debug(f"[SIZE] {trade_request.symbol}: {result.quantity} shares, "
                        f"${result.capital_allocated:,.0f} capital, method: {result.method_used}")
            
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating position size: {e}")
            return self._create_error_result(f"Calculation error: {str(e)}", trade_request)
    
    def _calculate_fixed_fraction_size(
        self, 
        trade_request: TradeRequest, 
        available_capital: float, 
        risk_per_share: float
    ) -> PositionSizeResult:
        """Calculate position size using fixed fraction method"""
        try:
            # Get fixed fraction parameters
            fixed_config = self.sizing_config.get('fixed_fraction', {})
            risk_percent = fixed_config.get('risk_percent', 1.0) / 100  # Convert to decimal
            
            # Calculate risk amount
            risk_amount = available_capital * risk_percent
            
            # Calculate quantity
            quantity = int(risk_amount / risk_per_share)
            capital_allocated = quantity * trade_request.entry_price
            
            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="fixed_fraction",
                confidence=0.8,
                sizing_factors={
                    'risk_percent': risk_percent * 100,
                    'risk_per_share': risk_per_share,
                    'available_capital': available_capital
                },
                warnings=[],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Fixed fraction calculation error: {e}")
            return self._create_error_result(str(e), trade_request)
    
    def _calculate_kelly_size(
        self, 
        trade_request: TradeRequest, 
        available_capital: float, 
        risk_per_share: float,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """Calculate position size using Kelly Criterion"""
        try:
            # Get historical performance for the symbol/strategy
            kelly_params = self._calculate_kelly_parameters(
                trade_request.symbol, 
                trade_request.strategy_name
            )
            
            if kelly_params is None or kelly_params.sample_size < self.kelly_min_trades:
                # Fall back to fixed fraction if insufficient data
                return self._calculate_fixed_fraction_size(
                    trade_request, available_capital, risk_per_share
                )
            
            # Apply Kelly fraction with safety limits
            kelly_fraction = min(kelly_params.kelly_fraction, self.kelly_max_fraction)
            kelly_fraction = max(kelly_fraction, 0.01)  # Minimum 1%
            
            # Calculate position size
            risk_amount = available_capital * kelly_fraction
            quantity = int(risk_amount / risk_per_share)
            capital_allocated = quantity * trade_request.entry_price
            
            warnings = []
            if kelly_params.sample_size < 20:
                warnings.append(f"Limited sample size: {kelly_params.sample_size} trades")
            
            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="kelly_criterion",
                confidence=kelly_params.confidence,
                sizing_factors={
                    'kelly_fraction': kelly_fraction * 100,
                    'win_rate': kelly_params.win_rate * 100,
                    'avg_win': kelly_params.avg_win,
                    'avg_loss': kelly_params.avg_loss,
                    'sample_size': kelly_params.sample_size
                },
                warnings=warnings,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Kelly calculation error: {e}")
            return self._calculate_fixed_fraction_size(
                trade_request, available_capital, risk_per_share
            )
    
    def _calculate_volatility_adjusted_size(
        self, 
        trade_request: TradeRequest, 
        available_capital: float, 
        risk_per_share: float,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """Calculate position size adjusted for volatility"""
        try:
            # Get volatility data
            volatility = self._get_symbol_volatility(trade_request.symbol, market_data)
            
            if volatility is None:
                # Fall back to fixed fraction
                return self._calculate_fixed_fraction_size(
                    trade_request, available_capital, risk_per_share
                )
            
            # Adjust position size based on volatility
            volatility_adjustment = self.volatility_target / volatility
            volatility_adjustment = np.clip(volatility_adjustment, 0.5, 2.0)  # Limit adjustment
            
            # Base risk amount
            base_risk_percent = self.sizing_config.get('base_risk_percent', 1.0) / 100
            adjusted_risk_percent = base_risk_percent * volatility_adjustment
            
            # Calculate position size
            risk_amount = available_capital * adjusted_risk_percent
            quantity = int(risk_amount / risk_per_share)
            capital_allocated = quantity * trade_request.entry_price
            
            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="volatility_adjusted",
                confidence=0.7,
                sizing_factors={
                    'volatility': volatility * 100,
                    'target_volatility': self.volatility_target * 100,
                    'volatility_adjustment': volatility_adjustment,
                    'adjusted_risk_percent': adjusted_risk_percent * 100
                },
                warnings=[],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Volatility adjustment error: {e}")
            return self._calculate_fixed_fraction_size(
                trade_request, available_capital, risk_per_share
            )
    
    def _calculate_adaptive_size(
        self, 
        trade_request: TradeRequest, 
        available_capital: float, 
        risk_per_share: float,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """Calculate position size using adaptive method combining multiple factors"""
        try:
            # Get multiple sizing estimates
            fixed_result = self._calculate_fixed_fraction_size(
                trade_request, available_capital, risk_per_share
            )
            
            kelly_result = self._calculate_kelly_size(
                trade_request, available_capital, risk_per_share, market_data
            )
            
            volatility_result = self._calculate_volatility_adjusted_size(
                trade_request, available_capital, risk_per_share, market_data
            )
            
            # Weight the results based on confidence
            weights = {
                'fixed': 0.3,
                'kelly': 0.4 if kelly_result.confidence > 0.6 else 0.2,
                'volatility': 0.3
            }
            
            # Normalize weights
            total_weight = sum(weights.values())
            weights = {k: v/total_weight for k, v in weights.items()}
            
            # Calculate weighted average
            weighted_quantity = (
                fixed_result.quantity * weights['fixed'] +
                kelly_result.quantity * weights['kelly'] +
                volatility_result.quantity * weights['volatility']
            )
            
            quantity = int(weighted_quantity)
            capital_allocated = quantity * trade_request.entry_price
            risk_amount = quantity * risk_per_share
            
            # Calculate combined confidence
            combined_confidence = (
                fixed_result.confidence * weights['fixed'] +
                kelly_result.confidence * weights['kelly'] +
                volatility_result.confidence * weights['volatility']
            )
            
            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="adaptive",
                confidence=combined_confidence,
                sizing_factors={
                    'fixed_quantity': fixed_result.quantity,
                    'kelly_quantity': kelly_result.quantity,
                    'volatility_quantity': volatility_result.quantity,
                    'weights': weights,
                    'weighted_quantity': weighted_quantity
                },
                warnings=[],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Adaptive sizing error: {e}")
            return self._calculate_fixed_fraction_size(
                trade_request, available_capital, risk_per_share
            )

    def _calculate_performance_based_size(
        self,
        trade_request: TradeRequest,
        available_capital: float,
        risk_per_share: float,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """Calculate position size based on recent performance"""
        try:
            # Get recent performance for this strategy/symbol
            recent_trades = [
                trade for trade in self.trade_history[-20:]  # Last 20 trades
                if trade.get('symbol') == trade_request.symbol and
                   trade.get('strategy') == trade_request.strategy_name
            ]

            if len(recent_trades) < 5:
                # Fall back to fixed fraction if insufficient data
                return self._calculate_fixed_fraction_size(
                    trade_request, available_capital, risk_per_share
                )

            # Calculate recent performance metrics
            recent_pnls = [trade.get('pnl', 0) for trade in recent_trades]
            win_rate = len([pnl for pnl in recent_pnls if pnl > 0]) / len(recent_pnls)
            avg_win = np.mean([pnl for pnl in recent_pnls if pnl > 0]) if any(pnl > 0 for pnl in recent_pnls) else 0
            avg_loss = abs(np.mean([pnl for pnl in recent_pnls if pnl < 0])) if any(pnl < 0 for pnl in recent_pnls) else 1

            # Performance-based adjustment factor
            if win_rate > 0.6 and avg_win > avg_loss:
                performance_multiplier = 1.2  # Increase size for good performance
            elif win_rate < 0.4 or avg_win < avg_loss * 0.5:
                performance_multiplier = 0.7  # Reduce size for poor performance
            else:
                performance_multiplier = 1.0  # Neutral performance

            # Base risk calculation
            base_risk_percent = self.sizing_config.get('base_risk_percent', 1.0) / 100
            adjusted_risk_percent = base_risk_percent * performance_multiplier

            # Calculate position size
            risk_amount = available_capital * adjusted_risk_percent
            quantity = int(risk_amount / risk_per_share)
            capital_allocated = quantity * trade_request.entry_price

            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="performance_based",
                confidence=0.8,
                sizing_factors={
                    'win_rate': win_rate * 100,
                    'avg_win': avg_win,
                    'avg_loss': avg_loss,
                    'performance_multiplier': performance_multiplier,
                    'recent_trades_count': len(recent_trades)
                },
                warnings=[],
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Performance-based sizing error: {e}")
            return self._calculate_fixed_fraction_size(
                trade_request, available_capital, risk_per_share
            )

    def _calculate_ml_enhanced_size(
        self,
        trade_request: TradeRequest,
        available_capital: float,
        risk_per_share: float,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionSizeResult:
        """Calculate position size using ML-enhanced risk prediction"""
        try:
            # This would integrate with actual ML models for risk prediction
            # For now, implement a sophisticated heuristic-based approach

            # Get market features for ML prediction
            features = self._extract_market_features(trade_request, market_data)

            # Predict risk score using simplified ML logic
            risk_score = self._predict_risk_score(features)

            # Predict success probability
            success_probability = self._predict_success_probability(trade_request, features)

            # Calculate ML-adjusted position size
            base_risk_percent = self.sizing_config.get('base_risk_percent', 1.0) / 100

            # Adjust based on risk score (0-1, lower is better)
            risk_adjustment = 1.0 - (risk_score * 0.5)  # Max 50% reduction

            # Adjust based on success probability (0-1, higher is better)
            success_adjustment = 0.5 + (success_probability * 0.5)  # 50% to 100%

            # Combined adjustment
            ml_multiplier = risk_adjustment * success_adjustment
            ml_multiplier = np.clip(ml_multiplier, 0.2, 1.5)  # Limit between 20% and 150%

            adjusted_risk_percent = base_risk_percent * ml_multiplier

            # Calculate position size
            risk_amount = available_capital * adjusted_risk_percent
            quantity = int(risk_amount / risk_per_share)
            capital_allocated = quantity * trade_request.entry_price

            # Calculate confidence based on feature quality
            confidence = self._calculate_ml_confidence(features, risk_score, success_probability)

            return PositionSizeResult(
                quantity=quantity,
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                method_used="ml_enhanced",
                confidence=confidence,
                sizing_factors={
                    'risk_score': risk_score,
                    'success_probability': success_probability,
                    'risk_adjustment': risk_adjustment,
                    'success_adjustment': success_adjustment,
                    'ml_multiplier': ml_multiplier,
                    'features_count': len(features)
                },
                warnings=[],
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] ML-enhanced sizing error: {e}")
            return self._calculate_adaptive_size(
                trade_request, available_capital, risk_per_share, market_data
            )

    def _extract_market_features(
        self,
        trade_request: TradeRequest,
        market_data: Optional[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract market features for ML prediction"""
        try:
            features = {}

            # Basic trade features
            features['risk_reward_ratio'] = abs(trade_request.take_profit - trade_request.entry_price) / abs(trade_request.entry_price - trade_request.stop_loss)
            features['position_size_percent'] = (trade_request.quantity * trade_request.entry_price) / 100000  # Assuming 100k capital

            # Market data features
            if market_data:
                features['volatility'] = market_data.get('volatility', 0.02)
                features['volume_ratio'] = market_data.get('volume', 1000000) / market_data.get('avg_volume', 1000000)
                features['price_momentum'] = market_data.get('price_change_percent', 0)
                features['bid_ask_spread'] = market_data.get('bid_ask_spread', 0.001)
            else:
                # Default values
                features['volatility'] = 0.02
                features['volume_ratio'] = 1.0
                features['price_momentum'] = 0.0
                features['bid_ask_spread'] = 0.001

            # Time-based features
            current_hour = datetime.now().hour
            features['market_session'] = 1.0 if 9 <= current_hour <= 15 else 0.5  # Market hours
            features['day_of_week'] = datetime.now().weekday() / 6.0  # Normalize to 0-1

            # Strategy-specific features
            strategy_performance = self._get_strategy_performance(trade_request.strategy_name)
            features['strategy_win_rate'] = strategy_performance.get('win_rate', 0.5)
            features['strategy_profit_factor'] = strategy_performance.get('profit_factor', 1.0)

            return features

        except Exception as e:
            logger.error(f"[ERROR] Error extracting market features: {e}")
            return {'default_feature': 0.5}

    def _predict_risk_score(self, features: Dict[str, float]) -> float:
        """Predict risk score using simplified ML logic"""
        try:
            # Simplified risk scoring based on features
            risk_factors = []

            # Volatility risk
            volatility = features.get('volatility', 0.02)
            risk_factors.append(min(1.0, volatility / 0.05))  # Normalize to 0-1

            # Liquidity risk
            volume_ratio = features.get('volume_ratio', 1.0)
            liquidity_risk = max(0, 1.0 - volume_ratio)  # Lower volume = higher risk
            risk_factors.append(liquidity_risk)

            # Market timing risk
            market_session = features.get('market_session', 1.0)
            timing_risk = 1.0 - market_session  # Outside market hours = higher risk
            risk_factors.append(timing_risk)

            # Strategy performance risk
            win_rate = features.get('strategy_win_rate', 0.5)
            strategy_risk = 1.0 - win_rate  # Lower win rate = higher risk
            risk_factors.append(strategy_risk)

            # Weighted average risk score
            weights = [0.3, 0.2, 0.2, 0.3]  # Volatility, liquidity, timing, strategy
            risk_score = sum(factor * weight for factor, weight in zip(risk_factors, weights))

            return np.clip(risk_score, 0.0, 1.0)

        except Exception as e:
            logger.error(f"[ERROR] Error predicting risk score: {e}")
            return 0.5  # Neutral risk score

    def _calculate_kelly_parameters(
        self,
        symbol: str,
        strategy_name: str
    ) -> Optional[KellyParameters]:
        """Calculate Kelly Criterion parameters from historical trades"""
        try:
            # Filter trades for this symbol/strategy
            relevant_trades = [
                trade for trade in self.trade_history
                if trade.get('symbol') == symbol and trade.get('strategy') == strategy_name
            ]

            if len(relevant_trades) < self.kelly_min_trades:
                return None

            # Calculate win rate and average win/loss
            wins = [trade for trade in relevant_trades if trade.get('pnl', 0) > 0]
            losses = [trade for trade in relevant_trades if trade.get('pnl', 0) < 0]

            if len(losses) == 0:  # Avoid division by zero
                return None

            win_rate = len(wins) / len(relevant_trades)
            avg_win = np.mean([trade['pnl'] for trade in wins]) if wins else 0
            avg_loss = abs(np.mean([trade['pnl'] for trade in losses]))

            # Calculate Kelly fraction: f = (bp - q) / b
            # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
            if avg_loss > 0:
                b = avg_win / avg_loss
                kelly_fraction = (b * win_rate - (1 - win_rate)) / b
            else:
                kelly_fraction = 0

            # Ensure Kelly fraction is reasonable
            kelly_fraction = max(0, min(kelly_fraction, self.kelly_max_fraction))

            # Calculate confidence based on sample size
            confidence = min(0.9, len(relevant_trades) / 50)  # Max confidence at 50+ trades

            return KellyParameters(
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                kelly_fraction=kelly_fraction,
                confidence=confidence,
                sample_size=len(relevant_trades),
                lookback_days=self.kelly_lookback_days
            )

        except Exception as e:
            logger.error(f"[ERROR] Error calculating Kelly parameters: {e}")
            return None

    def _get_symbol_volatility(
        self,
        symbol: str,
        market_data: Optional[Dict[str, Any]] = None
    ) -> Optional[float]:
        """Get volatility for symbol from market data or historical data"""
        try:
            if market_data and 'volatility' in market_data:
                return market_data['volatility']

            # Try to get from historical data (placeholder - would integrate with data source)
            # For now, return a reasonable default based on symbol type
            if symbol.endswith('.NS') or symbol in ['NIFTY', 'BANKNIFTY']:
                return 0.025  # 2.5% daily volatility for Indian stocks
            else:
                return 0.02   # 2% default volatility

        except Exception as e:
            logger.error(f"[ERROR] Error getting volatility for {symbol}: {e}")
            return None

    def _apply_position_limits(
        self,
        result: PositionSizeResult,
        available_capital: float,
        entry_price: float
    ) -> PositionSizeResult:
        """Apply position size limits and constraints"""
        try:
            # Calculate position value
            position_value = result.quantity * entry_price
            position_percent = (position_value / available_capital) * 100

            # Apply maximum position size limit
            if position_percent > self.max_position_size_percent:
                max_quantity = int((available_capital * self.max_position_size_percent / 100) / entry_price)
                result.quantity = max_quantity
                result.capital_allocated = max_quantity * entry_price
                result.warnings.append(f"Position size limited to {self.max_position_size_percent}%")

            # Apply minimum position size limit
            min_position_value = available_capital * self.min_position_size_percent / 100
            if result.capital_allocated < min_position_value:
                min_quantity = max(1, int(min_position_value / entry_price))
                result.quantity = min_quantity
                result.capital_allocated = min_quantity * entry_price
                result.warnings.append(f"Position size increased to minimum {self.min_position_size_percent}%")

            # Ensure quantity is at least 1
            if result.quantity < 1:
                result.quantity = 1
                result.capital_allocated = entry_price
                result.warnings.append("Position size set to minimum 1 share")

            return result

        except Exception as e:
            logger.error(f"[ERROR] Error applying position limits: {e}")
            return result

    def _create_error_result(self, error_message: str, trade_request: TradeRequest) -> PositionSizeResult:
        """Create error result for failed calculations"""
        return PositionSizeResult(
            quantity=0,
            capital_allocated=0.0,
            risk_amount=0.0,
            method_used="error",
            confidence=0.0,
            sizing_factors={'error': error_message},
            warnings=[error_message],
            timestamp=datetime.now()
        )

    def update_trade_history(self, trade_data: Dict[str, Any]):
        """Update trade history for Kelly Criterion calculations"""
        try:
            self.trade_history.append({
                'symbol': trade_data.get('symbol'),
                'strategy': trade_data.get('strategy'),
                'pnl': trade_data.get('pnl', 0),
                'entry_time': trade_data.get('entry_time'),
                'exit_time': trade_data.get('exit_time'),
                'quantity': trade_data.get('quantity'),
                'entry_price': trade_data.get('entry_price'),
                'exit_price': trade_data.get('exit_price')
            })

            # Keep only recent trades (last 1000 trades or 90 days)
            cutoff_date = datetime.now() - timedelta(days=90)
            self.trade_history = [
                trade for trade in self.trade_history[-1000:]
                if trade.get('exit_time', datetime.now()) > cutoff_date
            ]

        except Exception as e:
            logger.error(f"[ERROR] Error updating trade history: {e}")

    def get_sizing_summary(self) -> Dict[str, Any]:
        """Get position sizing configuration and statistics"""
        return {
            'default_method': self.default_method.value,
            'limits': {
                'max_position_size_percent': self.max_position_size_percent,
                'min_position_size_percent': self.min_position_size_percent
            },
            'kelly_settings': {
                'lookback_days': self.kelly_lookback_days,
                'max_fraction': self.kelly_max_fraction,
                'min_trades': self.kelly_min_trades
            },
            'volatility_settings': {
                'lookback_days': self.volatility_lookback,
                'target_volatility': self.volatility_target
            },
            'trade_history_count': len(self.trade_history),
            'performance_metrics': self.performance_metrics
        }
