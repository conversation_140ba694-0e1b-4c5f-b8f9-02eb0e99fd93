#!/usr/bin/env python3
"""
Risk Calculator Module

Comprehensive risk calculation engine:
- Risk-reward ratio calculations
- Position risk assessment
- Portfolio risk aggregation
- Margin requirement calculations
- Risk-adjusted performance metrics
- Real-time risk monitoring
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

# Import utility modules
from utils.risk_models import (
    TradeRequest, Position, Portfolio, RiskMetrics,
    TradeDirection, ProductType, ValidationResult, RiskLevel
)

logger = logging.getLogger(__name__)


class RiskCalculationType(Enum):
    """Types of risk calculations"""
    POSITION_RISK = "position_risk"
    PORTFOLIO_RISK = "portfolio_risk"
    MARGIN_RISK = "margin_risk"
    CONCENTRATION_RISK = "concentration_risk"
    CORRELATION_RISK = "correlation_risk"
    LIQUIDITY_RISK = "liquidity_risk"


@dataclass
class RiskCalculationResult:
    """Risk calculation result"""
    calculation_type: RiskCalculationType
    risk_amount: float
    risk_percent: float
    risk_level: RiskLevel
    details: Dict[str, Any]
    warnings: List[str]
    timestamp: datetime


@dataclass
class PositionRiskAssessment:
    """Comprehensive position risk assessment"""
    symbol: str
    quantity: int
    entry_price: float
    stop_loss: float
    take_profit: float
    risk_amount: float
    reward_amount: float
    risk_reward_ratio: float
    position_value: float
    risk_percent_of_capital: float
    risk_level: RiskLevel
    margin_required: float
    liquidity_score: float
    volatility_score: float
    overall_score: float
    recommendations: List[str]
    timestamp: datetime


class RiskCalculator:
    """
    Risk Calculator - Comprehensive risk assessment engine
    
    Features:
    - Multi-dimensional risk calculations
    - Real-time risk monitoring
    - Risk-adjusted metrics
    - Portfolio risk aggregation
    - Margin and liquidity risk
    - Performance attribution
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Risk Calculator"""
        self.config = config
        self.risk_config = config.get('risk_calculator', {})
        
        # Risk thresholds
        self.low_risk_threshold = self.risk_config.get('low_risk_threshold', 0.01)  # 1%
        self.medium_risk_threshold = self.risk_config.get('medium_risk_threshold', 0.02)  # 2%
        self.high_risk_threshold = self.risk_config.get('high_risk_threshold', 0.05)  # 5%
        
        # Portfolio parameters
        self.total_capital = config.get('capital_management', {}).get('initial_balance', 500000)
        self.max_portfolio_risk = self.risk_config.get('max_portfolio_risk', 0.08)  # 8%
        self.max_position_risk = self.risk_config.get('max_position_risk', 0.02)  # 2%
        
        # Risk factors
        self.volatility_weights = self.risk_config.get('volatility_weights', {
            'low': 0.5,
            'medium': 1.0,
            'high': 1.5,
            'extreme': 2.0
        })
        
        # Current portfolio state
        self.current_positions: Dict[str, Position] = {}
        self.portfolio_risk_cache: Optional[RiskCalculationResult] = None
        self.last_calculation_time: Optional[datetime] = None
        
        logger.info("[INIT] Risk Calculator initialized")
    
    def calculate_position_risk(
        self, 
        trade_request: TradeRequest,
        market_data: Optional[Dict[str, Any]] = None
    ) -> PositionRiskAssessment:
        """
        Calculate comprehensive risk assessment for a position
        
        Args:
            trade_request: Trade request details
            market_data: Real-time market data
            
        Returns:
            PositionRiskAssessment with detailed risk analysis
        """
        try:
            # Basic risk calculations
            entry_price = trade_request.entry_price
            stop_loss = trade_request.stop_loss
            take_profit = trade_request.take_profit
            quantity = trade_request.quantity
            
            # Calculate risk and reward amounts
            if trade_request.direction == TradeDirection.LONG:
                risk_amount = abs(entry_price - stop_loss) * quantity
                reward_amount = abs(take_profit - entry_price) * quantity
            else:  # SHORT
                risk_amount = abs(stop_loss - entry_price) * quantity
                reward_amount = abs(entry_price - take_profit) * quantity
            
            # Risk-reward ratio
            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
            
            # Position value and risk percentage
            position_value = quantity * entry_price
            risk_percent_of_capital = (risk_amount / self.total_capital) * 100
            
            # Determine risk level
            risk_level = self._determine_risk_level(risk_percent_of_capital / 100)
            
            # Calculate margin requirement
            margin_required = self._calculate_margin_requirement(trade_request)
            
            # Get market-based scores
            liquidity_score = self._calculate_liquidity_score(trade_request.symbol, market_data)
            volatility_score = self._calculate_volatility_score(trade_request.symbol, market_data)
            
            # Calculate overall risk score
            overall_score = self._calculate_overall_risk_score(
                risk_percent_of_capital / 100,
                risk_reward_ratio,
                liquidity_score,
                volatility_score
            )
            
            # Generate recommendations
            recommendations = self._generate_risk_recommendations(
                risk_level, risk_reward_ratio, liquidity_score, volatility_score
            )
            
            return PositionRiskAssessment(
                symbol=trade_request.symbol,
                quantity=quantity,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_amount=risk_amount,
                reward_amount=reward_amount,
                risk_reward_ratio=risk_reward_ratio,
                position_value=position_value,
                risk_percent_of_capital=risk_percent_of_capital,
                risk_level=risk_level,
                margin_required=margin_required,
                liquidity_score=liquidity_score,
                volatility_score=volatility_score,
                overall_score=overall_score,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating position risk: {e}")
            return self._create_error_assessment(trade_request, str(e))
    
    def calculate_portfolio_risk(
        self, 
        positions: Optional[Dict[str, Position]] = None,
        include_pending: bool = True
    ) -> RiskCalculationResult:
        """
        Calculate aggregate portfolio risk
        
        Args:
            positions: Current positions (uses cached if None)
            include_pending: Include pending orders in calculation
            
        Returns:
            RiskCalculationResult with portfolio risk metrics
        """
        try:
            if positions:
                self.current_positions = positions
            
            if not self.current_positions:
                return RiskCalculationResult(
                    calculation_type=RiskCalculationType.PORTFOLIO_RISK,
                    risk_amount=0.0,
                    risk_percent=0.0,
                    risk_level=RiskLevel.LOW,
                    details={},
                    warnings=[],
                    timestamp=datetime.now()
                )
            
            # Calculate total portfolio risk
            total_risk_amount = 0.0
            position_risks = {}
            sector_risks = {}
            correlation_adjustments = 0.0
            
            for pos_id, position in self.current_positions.items():
                # Calculate position risk
                position_risk = self._calculate_individual_position_risk(position)
                total_risk_amount += position_risk
                position_risks[pos_id] = position_risk
                
                # Aggregate sector risk
                sector = self._get_position_sector(position.symbol)
                sector_risks[sector] = sector_risks.get(sector, 0) + position_risk
            
            # Apply correlation adjustments (simplified)
            if len(self.current_positions) > 1:
                correlation_adjustments = self._calculate_correlation_adjustments(total_risk_amount)
                total_risk_amount += correlation_adjustments
            
            # Calculate risk percentage
            risk_percent = total_risk_amount / self.total_capital
            
            # Determine portfolio risk level
            risk_level = self._determine_risk_level(risk_percent)
            
            # Generate warnings
            warnings = []
            if risk_percent > self.max_portfolio_risk:
                warnings.append(f"Portfolio risk {risk_percent:.1%} exceeds maximum {self.max_portfolio_risk:.1%}")
            
            # Check concentration risk
            for sector, sector_risk in sector_risks.items():
                sector_percent = sector_risk / self.total_capital
                if sector_percent > 0.3:  # 30% sector concentration limit
                    warnings.append(f"High {sector} sector concentration: {sector_percent:.1%}")
            
            result = RiskCalculationResult(
                calculation_type=RiskCalculationType.PORTFOLIO_RISK,
                risk_amount=total_risk_amount,
                risk_percent=risk_percent,
                risk_level=risk_level,
                details={
                    'position_count': len(self.current_positions),
                    'position_risks': position_risks,
                    'sector_risks': sector_risks,
                    'correlation_adjustments': correlation_adjustments,
                    'largest_position_risk': max(position_risks.values()) if position_risks else 0,
                    'risk_concentration': max(sector_risks.values()) / total_risk_amount if total_risk_amount > 0 else 0
                },
                warnings=warnings,
                timestamp=datetime.now()
            )
            
            self.portfolio_risk_cache = result
            self.last_calculation_time = datetime.now()
            
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating portfolio risk: {e}")
            return RiskCalculationResult(
                RiskCalculationType.PORTFOLIO_RISK, 0, 0, RiskLevel.LOW, 
                {'error': str(e)}, [f"Calculation error: {str(e)}"], datetime.now()
            )
    
    def calculate_concentration_risk(self, positions: Optional[Dict[str, Position]] = None) -> RiskCalculationResult:
        """Calculate concentration risk across positions"""
        try:
            if positions:
                self.current_positions = positions
            
            if not self.current_positions:
                return RiskCalculationResult(
                    RiskCalculationType.CONCENTRATION_RISK, 0, 0, RiskLevel.LOW, {}, [], datetime.now()
                )
            
            # Calculate position values
            total_portfolio_value = 0
            position_values = {}
            sector_values = {}
            
            for pos_id, position in self.current_positions.items():
                position_value = position.quantity * position.current_price
                total_portfolio_value += position_value
                position_values[pos_id] = position_value
                
                sector = self._get_position_sector(position.symbol)
                sector_values[sector] = sector_values.get(sector, 0) + position_value
            
            # Calculate concentration metrics
            if total_portfolio_value == 0:
                return RiskCalculationResult(
                    RiskCalculationType.CONCENTRATION_RISK, 0, 0, RiskLevel.LOW, {}, [], datetime.now()
                )
            
            # Position concentration
            position_concentrations = {
                pos_id: (value / total_portfolio_value) 
                for pos_id, value in position_values.items()
            }
            max_position_concentration = max(position_concentrations.values())
            
            # Sector concentration
            sector_concentrations = {
                sector: (value / total_portfolio_value) 
                for sector, value in sector_values.items()
            }
            max_sector_concentration = max(sector_concentrations.values())
            
            # Calculate Herfindahl-Hirschman Index (HHI) for diversification
            hhi = sum(conc ** 2 for conc in position_concentrations.values())
            diversification_score = 1 - hhi  # Higher is more diversified
            
            # Determine concentration risk level
            concentration_risk = max(max_position_concentration, max_sector_concentration)
            risk_level = self._determine_concentration_risk_level(concentration_risk)
            
            # Generate warnings
            warnings = []
            if max_position_concentration > 0.2:  # 20% single position limit
                warnings.append(f"High single position concentration: {max_position_concentration:.1%}")
            
            if max_sector_concentration > 0.4:  # 40% sector limit
                warnings.append(f"High sector concentration: {max_sector_concentration:.1%}")
            
            if diversification_score < 0.5:
                warnings.append(f"Low diversification score: {diversification_score:.2f}")
            
            return RiskCalculationResult(
                calculation_type=RiskCalculationType.CONCENTRATION_RISK,
                risk_amount=concentration_risk * total_portfolio_value,
                risk_percent=concentration_risk,
                risk_level=risk_level,
                details={
                    'max_position_concentration': max_position_concentration,
                    'max_sector_concentration': max_sector_concentration,
                    'hhi_index': hhi,
                    'diversification_score': diversification_score,
                    'position_concentrations': position_concentrations,
                    'sector_concentrations': sector_concentrations
                },
                warnings=warnings,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating concentration risk: {e}")
            return RiskCalculationResult(
                RiskCalculationType.CONCENTRATION_RISK, 0, 0, RiskLevel.LOW,
                {'error': str(e)}, [f"Calculation error: {str(e)}"], datetime.now()
            )
    
    def _determine_risk_level(self, risk_percent: float) -> RiskLevel:
        """Determine risk level based on risk percentage"""
        if risk_percent <= self.low_risk_threshold:
            return RiskLevel.LOW
        elif risk_percent <= self.medium_risk_threshold:
            return RiskLevel.MEDIUM
        elif risk_percent <= self.high_risk_threshold:
            return RiskLevel.HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _determine_concentration_risk_level(self, concentration: float) -> RiskLevel:
        """Determine risk level for concentration"""
        if concentration <= 0.1:  # 10%
            return RiskLevel.LOW
        elif concentration <= 0.2:  # 20%
            return RiskLevel.MEDIUM
        elif concentration <= 0.4:  # 40%
            return RiskLevel.HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _calculate_margin_requirement(self, trade_request: TradeRequest) -> float:
        """Calculate margin requirement for position"""
        try:
            position_value = trade_request.quantity * trade_request.entry_price
            
            # Get margin multiplier based on product type
            if trade_request.product_type == ProductType.MIS:
                margin_multiplier = 3.5  # Intraday margin
            elif trade_request.product_type == ProductType.CNC:
                margin_multiplier = 1.0  # Full margin for delivery
            else:
                margin_multiplier = 2.0  # Conservative default
            
            margin_required = position_value / margin_multiplier
            return margin_required * 1.1  # Add 10% buffer
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating margin requirement: {e}")
            return trade_request.quantity * trade_request.entry_price  # Full margin as fallback
    
    def _calculate_liquidity_score(self, symbol: str, market_data: Optional[Dict[str, Any]]) -> float:
        """Calculate liquidity score (0-1, higher is better)"""
        try:
            if market_data and 'volume' in market_data:
                volume = market_data['volume']
                avg_volume = market_data.get('avg_volume', volume)
                
                # Volume-based liquidity score
                volume_ratio = volume / avg_volume if avg_volume > 0 else 1.0
                liquidity_score = min(1.0, volume_ratio)
            else:
                # Default scores based on symbol type
                if symbol in ['NIFTY', 'BANKNIFTY']:
                    liquidity_score = 1.0  # Highly liquid indices
                elif symbol in ['RELIANCE', 'TCS', 'HDFC', 'ICICIBANK']:
                    liquidity_score = 0.9  # Large cap stocks
                else:
                    liquidity_score = 0.7  # Default for other stocks
            
            return liquidity_score
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating liquidity score: {e}")
            return 0.5  # Neutral score
    
    def _calculate_volatility_score(self, symbol: str, market_data: Optional[Dict[str, Any]]) -> float:
        """Calculate volatility score (0-1, higher means more volatile)"""
        try:
            if market_data and 'volatility' in market_data:
                volatility = market_data['volatility']
                # Normalize volatility to 0-1 scale
                volatility_score = min(1.0, volatility / 0.05)  # 5% as max reference
            else:
                # Default volatility scores
                volatility_map = {
                    'NIFTY': 0.3,
                    'BANKNIFTY': 0.4,
                    'RELIANCE': 0.5,
                    'TCS': 0.4,
                    'HDFC': 0.5
                }
                volatility_score = volatility_map.get(symbol, 0.6)
            
            return volatility_score
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating volatility score: {e}")
            return 0.5  # Neutral score
    
    def _calculate_overall_risk_score(
        self, 
        risk_percent: float, 
        risk_reward_ratio: float,
        liquidity_score: float, 
        volatility_score: float
    ) -> float:
        """Calculate overall risk score (0-1, higher is riskier)"""
        try:
            # Weight different risk factors
            risk_component = min(1.0, risk_percent / self.high_risk_threshold)  # 0-1
            rr_component = max(0, 1 - (risk_reward_ratio / 3.0))  # 0-1, lower RR = higher risk
            liquidity_component = 1 - liquidity_score  # 0-1, lower liquidity = higher risk
            volatility_component = volatility_score  # 0-1, higher volatility = higher risk
            
            # Weighted average
            overall_score = (
                risk_component * 0.4 +
                rr_component * 0.3 +
                liquidity_component * 0.15 +
                volatility_component * 0.15
            )
            
            return min(1.0, overall_score)
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating overall risk score: {e}")
            return 0.5  # Neutral score

    def _generate_risk_recommendations(
        self,
        risk_level: RiskLevel,
        risk_reward_ratio: float,
        liquidity_score: float,
        volatility_score: float
    ) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        try:
            # Risk level recommendations
            if risk_level == RiskLevel.CRITICAL:
                recommendations.append("CRITICAL: Position size too large - reduce immediately")
            elif risk_level == RiskLevel.HIGH:
                recommendations.append("HIGH RISK: Consider reducing position size")

            # Risk-reward recommendations
            if risk_reward_ratio < 1.5:
                recommendations.append(f"Poor risk-reward ratio ({risk_reward_ratio:.2f}) - consider better entry/exit levels")
            elif risk_reward_ratio > 3.0:
                recommendations.append(f"Excellent risk-reward ratio ({risk_reward_ratio:.2f}) - favorable setup")

            # Liquidity recommendations
            if liquidity_score < 0.3:
                recommendations.append("LOW LIQUIDITY: May face execution difficulties - use limit orders")
            elif liquidity_score < 0.6:
                recommendations.append("MODERATE LIQUIDITY: Monitor execution carefully")

            # Volatility recommendations
            if volatility_score > 0.8:
                recommendations.append("HIGH VOLATILITY: Consider tighter stops and smaller position size")
            elif volatility_score < 0.2:
                recommendations.append("LOW VOLATILITY: May use slightly larger position size")

            return recommendations

        except Exception as e:
            logger.error(f"[ERROR] Error generating recommendations: {e}")
            return ["Error generating recommendations"]

    def _calculate_individual_position_risk(self, position: Position) -> float:
        """Calculate risk amount for individual position"""
        try:
            # Calculate current risk based on stop loss
            if hasattr(position, 'stop_loss') and position.stop_loss:
                risk_per_share = abs(position.current_price - position.stop_loss)
                return risk_per_share * position.quantity
            else:
                # Use default risk percentage if no stop loss
                position_value = position.quantity * position.current_price
                return position_value * 0.02  # 2% default risk

        except Exception as e:
            logger.error(f"[ERROR] Error calculating individual position risk: {e}")
            return 0.0

    def _calculate_correlation_adjustments(self, base_risk: float) -> float:
        """Calculate correlation-based risk adjustments"""
        try:
            # Simplified correlation adjustment
            # In practice, would use actual correlation matrix
            position_count = len(self.current_positions)

            if position_count <= 1:
                return 0.0

            # Assume some correlation between positions
            avg_correlation = 0.3  # 30% average correlation
            diversification_benefit = (1 - avg_correlation) * 0.1  # 10% max benefit

            # Reduce risk by diversification benefit
            adjustment = -base_risk * diversification_benefit
            return adjustment

        except Exception as e:
            logger.error(f"[ERROR] Error calculating correlation adjustments: {e}")
            return 0.0

    def _get_position_sector(self, symbol: str) -> str:
        """Get sector for position symbol"""
        try:
            # Simplified sector mapping
            sector_map = {
                'NIFTY': 'INDEX',
                'BANKNIFTY': 'INDEX',
                'RELIANCE': 'ENERGY',
                'ONGC': 'ENERGY',
                'IOC': 'ENERGY',
                'TCS': 'IT',
                'INFY': 'IT',
                'WIPRO': 'IT',
                'HCLTECH': 'IT',
                'HDFC': 'BANKING',
                'ICICIBANK': 'BANKING',
                'SBIN': 'BANKING',
                'AXISBANK': 'BANKING'
            }

            return sector_map.get(symbol, 'OTHER')

        except Exception as e:
            logger.error(f"[ERROR] Error getting sector for {symbol}: {e}")
            return 'UNKNOWN'

    def _create_error_assessment(self, trade_request: TradeRequest, error_msg: str) -> PositionRiskAssessment:
        """Create error assessment for failed calculations"""
        return PositionRiskAssessment(
            symbol=trade_request.symbol,
            quantity=0,
            entry_price=0.0,
            stop_loss=0.0,
            take_profit=0.0,
            risk_amount=0.0,
            reward_amount=0.0,
            risk_reward_ratio=0.0,
            position_value=0.0,
            risk_percent_of_capital=0.0,
            risk_level=RiskLevel.CRITICAL,
            margin_required=0.0,
            liquidity_score=0.0,
            volatility_score=0.0,
            overall_score=1.0,
            recommendations=[f"Calculation error: {error_msg}"],
            timestamp=datetime.now()
        )

    def update_positions(self, positions: Dict[str, Position]):
        """Update current positions for risk calculations"""
        self.current_positions = positions
        # Invalidate cache
        self.portfolio_risk_cache = None
        self.last_calculation_time = None

    def get_cached_portfolio_risk(self, max_age_minutes: int = 5) -> Optional[RiskCalculationResult]:
        """Get cached portfolio risk if still valid"""
        if not self.portfolio_risk_cache or not self.last_calculation_time:
            return None

        age = datetime.now() - self.last_calculation_time
        if age.total_seconds() / 60 > max_age_minutes:
            return None

        return self.portfolio_risk_cache

    def calculate_risk_adjusted_return(self, returns: List[float], risk_free_rate: float = 0.05) -> Dict[str, float]:
        """Calculate risk-adjusted performance metrics"""
        try:
            if not returns or len(returns) < 2:
                return {}

            returns_array = np.array(returns)

            # Basic metrics
            total_return = np.prod(1 + returns_array) - 1
            avg_return = np.mean(returns_array)
            volatility = np.std(returns_array)

            # Risk-adjusted metrics
            sharpe_ratio = (avg_return - risk_free_rate / 252) / volatility if volatility > 0 else 0

            # Sortino ratio (downside deviation)
            downside_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 0
            sortino_ratio = (avg_return - risk_free_rate / 252) / downside_deviation if downside_deviation > 0 else 0

            # Maximum drawdown
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # Calmar ratio
            calmar_ratio = avg_return / abs(max_drawdown) if max_drawdown != 0 else 0

            return {
                'total_return': total_return,
                'avg_daily_return': avg_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'max_drawdown': max_drawdown,
                'calmar_ratio': calmar_ratio
            }

        except Exception as e:
            logger.error(f"[ERROR] Error calculating risk-adjusted returns: {e}")
            return {}

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk calculator summary"""
        try:
            portfolio_risk = self.get_cached_portfolio_risk() or self.calculate_portfolio_risk()

            return {
                'calculator_config': {
                    'total_capital': self.total_capital,
                    'max_portfolio_risk': self.max_portfolio_risk,
                    'max_position_risk': self.max_position_risk,
                    'risk_thresholds': {
                        'low': self.low_risk_threshold,
                        'medium': self.medium_risk_threshold,
                        'high': self.high_risk_threshold
                    }
                },
                'current_portfolio': {
                    'position_count': len(self.current_positions),
                    'total_risk_amount': portfolio_risk.risk_amount,
                    'total_risk_percent': portfolio_risk.risk_percent * 100,
                    'risk_level': portfolio_risk.risk_level.value,
                    'warnings': portfolio_risk.warnings
                },
                'risk_breakdown': portfolio_risk.details,
                'last_calculation': self.last_calculation_time.isoformat() if self.last_calculation_time else None
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting risk summary: {e}")
            return {'error': str(e)}
