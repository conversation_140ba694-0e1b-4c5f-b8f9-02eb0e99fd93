#!/usr/bin/env python3
"""
Capital Allocator Module

Handles all capital allocation and management functions including:
- Total capital tracking
- Available capital calculation
- Capital utilization monitoring
- Margin requirement management
- Capital allocation per strategy/position
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# Import utility modules
from utils.risk_models import (
    TradeRequest, Position, Portfolio, RiskMetrics,
    TradeDirection, ProductType
)

logger = logging.getLogger(__name__)


class CapitalAllocationMethod(Enum):
    """Capital allocation methods"""
    EQUAL_WEIGHT = "equal_weight"
    RISK_PARITY = "risk_parity"
    VOLATILITY_ADJUSTED = "volatility_adjusted"
    PERFORMANCE_BASED = "performance_based"
    CUSTOM = "custom"


@dataclass
class CapitalAllocation:
    """Capital allocation result"""
    total_capital: float
    available_capital: float
    allocated_capital: float
    reserved_capital: float
    utilization_percent: float
    allocation_method: str
    allocation_details: Dict[str, Any]
    timestamp: datetime


@dataclass
class MarginRequirement:
    """Margin requirement calculation"""
    symbol: str
    quantity: int
    entry_price: float
    margin_required: float
    margin_multiplier: float
    product_type: ProductType
    exchange: str
    timestamp: datetime


class CapitalAllocator:
    """
    Capital Allocator - Manages capital allocation and utilization
    
    Features:
    - Real-time capital tracking
    - Dynamic allocation based on market conditions
    - Margin requirement calculations
    - Capital utilization monitoring
    - Emergency capital reserves
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Capital Allocator"""
        self.config = config
        self.capital_config = config.get('capital_management', {})
        
        # Capital parameters
        self.total_capital = self.capital_config.get('initial_balance', 500000)
        self.available_capital = self.total_capital
        self.allocated_capital = 0.0
        self.reserved_capital = self.total_capital * self.capital_config.get('emergency_cash_reserve', 0.15)
        
        # Allocation limits
        self.max_daily_risk_percent = self.capital_config.get('max_daily_risk', 0.02)
        self.max_position_risk_percent = self.capital_config.get('max_position_risk', 0.003)
        self.max_portfolio_heat_percent = self.capital_config.get('max_portfolio_heat', 0.08)
        
        # Margin settings
        self.margin_config = self.capital_config.get('margin', {})
        self.default_margin_multiplier = self.margin_config.get('intraday_multiplier', 3.5)
        self.margin_buffer_percent = self.margin_config.get('buffer_percent', 10.0)
        
        # Tracking
        self.active_allocations: Dict[str, float] = {}
        self.margin_requirements: Dict[str, MarginRequirement] = {}
        self.allocation_history: List[CapitalAllocation] = []
        
        logger.info(f"[INIT] Capital Allocator initialized with {self.total_capital:,.0f} total capital")
    
    def get_available_capital(self) -> float:
        """Get currently available capital for new positions"""
        return max(0, self.total_capital - self.allocated_capital - self.reserved_capital)
    
    def get_capital_utilization(self) -> float:
        """Get current capital utilization percentage"""
        return (self.allocated_capital / self.total_capital) * 100 if self.total_capital > 0 else 0
    
    def calculate_max_position_capital(self, risk_percent: Optional[float] = None) -> float:
        """Calculate maximum capital that can be allocated to a single position"""
        max_risk = risk_percent or self.max_position_risk_percent
        return self.total_capital * max_risk
    
    def calculate_margin_requirement(
        self, 
        symbol: str, 
        quantity: int, 
        entry_price: float,
        product_type: ProductType = ProductType.MIS,
        exchange: str = "NSE"
    ) -> MarginRequirement:
        """Calculate margin requirement for a position"""
        try:
            # Base margin calculation
            position_value = quantity * entry_price
            
            # Get margin multiplier based on product type
            if product_type == ProductType.MIS:
                margin_multiplier = self.default_margin_multiplier
            elif product_type == ProductType.CNC:
                margin_multiplier = 1.0  # Full margin for delivery
            else:
                margin_multiplier = 2.0  # Conservative default
            
            # Calculate required margin
            base_margin = position_value / margin_multiplier
            margin_with_buffer = base_margin * (1 + self.margin_buffer_percent / 100)
            
            return MarginRequirement(
                symbol=symbol,
                quantity=quantity,
                entry_price=entry_price,
                margin_required=margin_with_buffer,
                margin_multiplier=margin_multiplier,
                product_type=product_type,
                exchange=exchange,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating margin requirement: {e}")
            # Return conservative estimate
            return MarginRequirement(
                symbol=symbol,
                quantity=quantity,
                entry_price=entry_price,
                margin_required=quantity * entry_price,  # Full margin as fallback
                margin_multiplier=1.0,
                product_type=product_type,
                exchange=exchange,
                timestamp=datetime.now()
            )
    
    def allocate_capital(
        self, 
        trade_request: TradeRequest,
        allocation_method: CapitalAllocationMethod = CapitalAllocationMethod.RISK_PARITY
    ) -> Tuple[bool, float, str]:
        """
        Allocate capital for a trade request
        
        Returns:
            Tuple of (success, allocated_amount, reason)
        """
        try:
            # Calculate margin requirement
            margin_req = self.calculate_margin_requirement(
                trade_request.symbol,
                trade_request.quantity,
                trade_request.entry_price,
                trade_request.product_type
            )
            
            # Check if we have enough available capital
            available = self.get_available_capital()
            if margin_req.margin_required > available:
                return False, 0.0, f"Insufficient capital: need {margin_req.margin_required:,.0f}, have {available:,.0f}"
            
            # Check position size limits
            max_position_capital = self.calculate_max_position_capital()
            if margin_req.margin_required > max_position_capital:
                return False, 0.0, f"Position size exceeds limit: {margin_req.margin_required:,.0f} > {max_position_capital:,.0f}"
            
            # Allocate capital
            allocation_id = f"{trade_request.symbol}_{trade_request.signal_id}"
            self.active_allocations[allocation_id] = margin_req.margin_required
            self.allocated_capital += margin_req.margin_required
            self.margin_requirements[allocation_id] = margin_req
            
            # Record allocation
            allocation = CapitalAllocation(
                total_capital=self.total_capital,
                available_capital=self.get_available_capital(),
                allocated_capital=self.allocated_capital,
                reserved_capital=self.reserved_capital,
                utilization_percent=self.get_capital_utilization(),
                allocation_method=allocation_method.value,
                allocation_details={
                    'allocation_id': allocation_id,
                    'symbol': trade_request.symbol,
                    'margin_required': margin_req.margin_required,
                    'margin_multiplier': margin_req.margin_multiplier
                },
                timestamp=datetime.now()
            )
            self.allocation_history.append(allocation)
            
            logger.info(f"[ALLOC] Allocated {margin_req.margin_required:,.0f} for {trade_request.symbol}")
            return True, margin_req.margin_required, "Capital allocated successfully"
            
        except Exception as e:
            logger.error(f"[ERROR] Error allocating capital: {e}")
            return False, 0.0, f"Allocation error: {str(e)}"
    
    def release_capital(self, allocation_id: str) -> bool:
        """Release allocated capital when position is closed"""
        try:
            if allocation_id in self.active_allocations:
                released_amount = self.active_allocations.pop(allocation_id)
                self.allocated_capital -= released_amount
                
                # Remove margin requirement
                if allocation_id in self.margin_requirements:
                    del self.margin_requirements[allocation_id]
                
                logger.info(f"[RELEASE] Released {released_amount:,.0f} capital for {allocation_id}")
                return True
            else:
                logger.warning(f"[WARN] Allocation ID not found: {allocation_id}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Error releasing capital: {e}")
            return False
    
    def get_allocation_summary(self) -> Dict[str, Any]:
        """Get comprehensive capital allocation summary"""
        return {
            'total_capital': self.total_capital,
            'available_capital': self.get_available_capital(),
            'allocated_capital': self.allocated_capital,
            'reserved_capital': self.reserved_capital,
            'utilization_percent': self.get_capital_utilization(),
            'active_allocations_count': len(self.active_allocations),
            'active_allocations': dict(self.active_allocations),
            'margin_requirements': {k: {
                'symbol': v.symbol,
                'margin_required': v.margin_required,
                'margin_multiplier': v.margin_multiplier
            } for k, v in self.margin_requirements.items()},
            'limits': {
                'max_daily_risk_percent': self.max_daily_risk_percent * 100,
                'max_position_risk_percent': self.max_position_risk_percent * 100,
                'max_portfolio_heat_percent': self.max_portfolio_heat_percent * 100
            }
        }
