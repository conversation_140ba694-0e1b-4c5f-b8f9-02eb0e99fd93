#!/usr/bin/env python3
"""
Configuration Manager Module

Dynamic configuration management for risk management system:
- Real-time configuration updates
- Configuration validation
- Environment-specific settings
- Hot-reload capabilities
- Configuration versioning
- Audit trail for changes
"""

import logging
import yaml
import json
import os
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading
import time

logger = logging.getLogger(__name__)


class ConfigurationType(Enum):
    """Types of configuration sections"""
    CAPITAL_MANAGEMENT = "capital_management"
    POSITION_SIZING = "position_sizing"
    PRE_TRADE_FILTERS = "pre_trade_filters"
    CIRCUIT_BREAKERS = "circuit_breakers"
    PORTFOLIO_MONITOR = "portfolio_monitor"
    RISK_CALCULATOR = "risk_calculator"
    GENERAL = "general"


@dataclass
class ConfigurationChange:
    """Configuration change record"""
    section: str
    key: str
    old_value: Any
    new_value: Any
    changed_by: str
    timestamp: datetime
    reason: str


@dataclass
class ConfigurationValidation:
    """Configuration validation result"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    validated_config: Dict[str, Any]
    timestamp: datetime


class ConfigurationManager:
    """
    Configuration Manager - Dynamic configuration management
    
    Features:
    - Real-time configuration updates
    - Configuration validation and type checking
    - Hot-reload without system restart
    - Change auditing and rollback
    - Environment-specific configurations
    - Thread-safe operations
    """
    
    def __init__(self, config_path: str, auto_reload: bool = True):
        """Initialize Configuration Manager"""
        self.config_path = Path(config_path)
        self.auto_reload = auto_reload
        
        # Configuration state
        self.current_config: Dict[str, Any] = {}
        self.config_history: List[Dict[str, Any]] = []
        self.change_log: List[ConfigurationChange] = []
        
        # Validation rules
        self.validation_rules: Dict[str, Dict[str, Any]] = {}
        self._initialize_validation_rules()
        
        # Change callbacks
        self.change_callbacks: Dict[str, List[Callable]] = {}
        
        # Thread safety
        self.config_lock = threading.RLock()
        self.reload_thread = None
        self.stop_reload = threading.Event()
        
        # Load initial configuration
        self.load_configuration()
        
        # Start auto-reload if enabled
        if self.auto_reload:
            self.start_auto_reload()
        
        logger.info(f"[INIT] Configuration Manager initialized with {self.config_path}")
    
    def load_configuration(self) -> bool:
        """Load configuration from file"""
        try:
            with self.config_lock:
                if not self.config_path.exists():
                    logger.error(f"[ERROR] Configuration file not found: {self.config_path}")
                    return False
                
                # Load configuration based on file extension
                if self.config_path.suffix.lower() == '.yaml' or self.config_path.suffix.lower() == '.yml':
                    with open(self.config_path, 'r') as f:
                        new_config = yaml.safe_load(f)
                elif self.config_path.suffix.lower() == '.json':
                    with open(self.config_path, 'r') as f:
                        new_config = json.load(f)
                else:
                    logger.error(f"[ERROR] Unsupported configuration file format: {self.config_path.suffix}")
                    return False
                
                # Validate configuration
                validation = self.validate_configuration(new_config)
                if not validation.is_valid:
                    logger.error(f"[ERROR] Configuration validation failed: {validation.errors}")
                    return False
                
                # Store previous config for history
                if self.current_config:
                    self.config_history.append(self.current_config.copy())
                    # Keep only last 10 versions
                    self.config_history = self.config_history[-10:]
                
                # Update current configuration
                old_config = self.current_config.copy()
                self.current_config = validation.validated_config
                
                # Log changes
                self._log_configuration_changes(old_config, self.current_config, "system", "Configuration reload")
                
                # Notify callbacks
                self._notify_change_callbacks()
                
                logger.info("[SUCCESS] Configuration loaded successfully")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Error loading configuration: {e}")
            return False
    
    def save_configuration(self, backup: bool = True) -> bool:
        """Save current configuration to file"""
        try:
            with self.config_lock:
                # Create backup if requested
                if backup and self.config_path.exists():
                    backup_path = self.config_path.with_suffix(f'.backup.{int(time.time())}{self.config_path.suffix}')
                    import shutil
                    shutil.copy2(self.config_path, backup_path)
                    logger.info(f"[BACKUP] Configuration backed up to {backup_path}")
                
                # Save configuration
                if self.config_path.suffix.lower() in ['.yaml', '.yml']:
                    with open(self.config_path, 'w') as f:
                        yaml.dump(self.current_config, f, default_flow_style=False, indent=2)
                elif self.config_path.suffix.lower() == '.json':
                    with open(self.config_path, 'w') as f:
                        json.dump(self.current_config, f, indent=2)
                
                logger.info("[SUCCESS] Configuration saved successfully")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Error saving configuration: {e}")
            return False
    
    def update_configuration(
        self, 
        section: str, 
        updates: Dict[str, Any], 
        changed_by: str = "system",
        reason: str = "Configuration update"
    ) -> bool:
        """Update configuration section"""
        try:
            with self.config_lock:
                # Create updated configuration
                new_config = self.current_config.copy()
                
                if section not in new_config:
                    new_config[section] = {}
                
                # Apply updates
                old_section = new_config[section].copy()
                new_config[section].update(updates)
                
                # Validate updated configuration
                validation = self.validate_configuration(new_config)
                if not validation.is_valid:
                    logger.error(f"[ERROR] Configuration update validation failed: {validation.errors}")
                    return False
                
                # Log individual changes
                for key, new_value in updates.items():
                    old_value = old_section.get(key, None)
                    if old_value != new_value:
                        change = ConfigurationChange(
                            section=section,
                            key=key,
                            old_value=old_value,
                            new_value=new_value,
                            changed_by=changed_by,
                            timestamp=datetime.now(),
                            reason=reason
                        )
                        self.change_log.append(change)
                
                # Update configuration
                self.current_config = validation.validated_config
                
                # Notify callbacks for this section
                self._notify_section_callbacks(section)
                
                logger.info(f"[UPDATE] Configuration section '{section}' updated successfully")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Error updating configuration: {e}")
            return False
    
    def get_configuration(self, section: Optional[str] = None) -> Dict[str, Any]:
        """Get configuration or specific section"""
        with self.config_lock:
            if section:
                return self.current_config.get(section, {}).copy()
            return self.current_config.copy()
    
    def get_value(self, section: str, key: str, default: Any = None) -> Any:
        """Get specific configuration value"""
        with self.config_lock:
            return self.current_config.get(section, {}).get(key, default)
    
    def set_value(
        self, 
        section: str, 
        key: str, 
        value: Any, 
        changed_by: str = "system",
        reason: str = "Single value update"
    ) -> bool:
        """Set specific configuration value"""
        return self.update_configuration(section, {key: value}, changed_by, reason)
    
    def validate_configuration(self, config: Dict[str, Any]) -> ConfigurationValidation:
        """Validate configuration against rules"""
        try:
            errors = []
            warnings = []
            validated_config = config.copy()
            
            # Validate each section
            for section_name, section_config in config.items():
                if section_name in self.validation_rules:
                    section_errors, section_warnings = self._validate_section(
                        section_name, section_config
                    )
                    errors.extend(section_errors)
                    warnings.extend(section_warnings)
            
            # Check required sections
            required_sections = ['capital_management', 'pre_trade_filters', 'circuit_breakers']
            for required_section in required_sections:
                if required_section not in config:
                    errors.append(f"Required section '{required_section}' missing")
            
            return ConfigurationValidation(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                validated_config=validated_config,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating configuration: {e}")
            return ConfigurationValidation(
                False, [f"Validation error: {str(e)}"], [], {}, datetime.now()
            )
    
    def _validate_section(self, section_name: str, section_config: Dict[str, Any]) -> tuple:
        """Validate specific configuration section"""
        errors = []
        warnings = []
        
        try:
            rules = self.validation_rules.get(section_name, {})
            
            for key, value in section_config.items():
                if key in rules:
                    rule = rules[key]
                    
                    # Type validation
                    expected_type = rule.get('type')
                    if expected_type and not isinstance(value, expected_type):
                        errors.append(f"{section_name}.{key}: Expected {expected_type.__name__}, got {type(value).__name__}")
                    
                    # Range validation
                    if 'min' in rule and isinstance(value, (int, float)) and value < rule['min']:
                        errors.append(f"{section_name}.{key}: Value {value} below minimum {rule['min']}")
                    
                    if 'max' in rule and isinstance(value, (int, float)) and value > rule['max']:
                        errors.append(f"{section_name}.{key}: Value {value} above maximum {rule['max']}")
                    
                    # Choices validation
                    if 'choices' in rule and value not in rule['choices']:
                        errors.append(f"{section_name}.{key}: Value '{value}' not in allowed choices {rule['choices']}")
            
            # Check required keys
            required_keys = [k for k, v in rules.items() if v.get('required', False)]
            for required_key in required_keys:
                if required_key not in section_config:
                    errors.append(f"{section_name}.{required_key}: Required key missing")
            
        except Exception as e:
            errors.append(f"Section validation error for {section_name}: {str(e)}")
        
        return errors, warnings
    
    def _initialize_validation_rules(self):
        """Initialize configuration validation rules"""
        self.validation_rules = {
            'capital_management': {
                'initial_balance': {'type': (int, float), 'min': 1000, 'required': True},
                'max_daily_risk': {'type': float, 'min': 0.001, 'max': 0.1, 'required': True},
                'max_position_risk': {'type': float, 'min': 0.001, 'max': 0.05, 'required': True},
                'max_portfolio_heat': {'type': float, 'min': 0.01, 'max': 0.2, 'required': True},
                'emergency_cash_reserve': {'type': float, 'min': 0.05, 'max': 0.5}
            },
            'position_sizing': {
                'default_method': {'type': str, 'choices': ['fixed_fraction', 'kelly_criterion', 'volatility_adjusted', 'adaptive']},
                'max_position_size_percent': {'type': float, 'min': 0.1, 'max': 20.0},
                'min_position_size_percent': {'type': float, 'min': 0.01, 'max': 1.0}
            },
            'pre_trade_filters': {
                'min_risk_reward_ratio': {'type': float, 'min': 1.0, 'max': 5.0},
                'max_concurrent_positions': {'type': int, 'min': 1, 'max': 20},
                'max_positions_per_symbol': {'type': int, 'min': 1, 'max': 5},
                'max_sector_concentration': {'type': float, 'min': 0.1, 'max': 1.0}
            },
            'circuit_breakers': {
                'drawdown': {'type': dict},
                'daily_loss': {'type': dict},
                'consecutive_losses': {'type': dict},
                'portfolio_heat': {'type': dict}
            }
        }
    
    def register_change_callback(self, section: str, callback: Callable):
        """Register callback for configuration changes"""
        if section not in self.change_callbacks:
            self.change_callbacks[section] = []
        self.change_callbacks[section].append(callback)
        logger.info(f"[CALLBACK] Registered change callback for section '{section}'")
    
    def _notify_change_callbacks(self):
        """Notify all registered callbacks"""
        for section, callbacks in self.change_callbacks.items():
            self._notify_section_callbacks(section)
    
    def _notify_section_callbacks(self, section: str):
        """Notify callbacks for specific section"""
        if section in self.change_callbacks:
            section_config = self.current_config.get(section, {})
            for callback in self.change_callbacks[section]:
                try:
                    callback(section, section_config)
                except Exception as e:
                    logger.error(f"[ERROR] Error in change callback for {section}: {e}")
    
    def _log_configuration_changes(self, old_config: Dict, new_config: Dict, changed_by: str, reason: str):
        """Log configuration changes"""
        try:
            def compare_dicts(old_dict, new_dict, path=""):
                for key in set(old_dict.keys()) | set(new_dict.keys()):
                    current_path = f"{path}.{key}" if path else key
                    
                    if key not in old_dict:
                        # New key added
                        change = ConfigurationChange(
                            section=path or "root",
                            key=key,
                            old_value=None,
                            new_value=new_dict[key],
                            changed_by=changed_by,
                            timestamp=datetime.now(),
                            reason=f"{reason} - Added"
                        )
                        self.change_log.append(change)
                    elif key not in new_dict:
                        # Key removed
                        change = ConfigurationChange(
                            section=path or "root",
                            key=key,
                            old_value=old_dict[key],
                            new_value=None,
                            changed_by=changed_by,
                            timestamp=datetime.now(),
                            reason=f"{reason} - Removed"
                        )
                        self.change_log.append(change)
                    elif isinstance(old_dict[key], dict) and isinstance(new_dict[key], dict):
                        # Recursively compare nested dictionaries
                        compare_dicts(old_dict[key], new_dict[key], current_path)
                    elif old_dict[key] != new_dict[key]:
                        # Value changed
                        change = ConfigurationChange(
                            section=path or "root",
                            key=key,
                            old_value=old_dict[key],
                            new_value=new_dict[key],
                            changed_by=changed_by,
                            timestamp=datetime.now(),
                            reason=f"{reason} - Modified"
                        )
                        self.change_log.append(change)
            
            compare_dicts(old_config, new_config)
            
            # Keep only last 100 changes
            self.change_log = self.change_log[-100:]
            
        except Exception as e:
            logger.error(f"[ERROR] Error logging configuration changes: {e}")
    
    def start_auto_reload(self):
        """Start automatic configuration reload thread"""
        if self.reload_thread and self.reload_thread.is_alive():
            return
        
        def reload_worker():
            last_modified = self.config_path.stat().st_mtime if self.config_path.exists() else 0
            
            while not self.stop_reload.wait(5):  # Check every 5 seconds
                try:
                    if self.config_path.exists():
                        current_modified = self.config_path.stat().st_mtime
                        if current_modified > last_modified:
                            logger.info("[RELOAD] Configuration file changed, reloading...")
                            self.load_configuration()
                            last_modified = current_modified
                except Exception as e:
                    logger.error(f"[ERROR] Error in auto-reload: {e}")
        
        self.reload_thread = threading.Thread(target=reload_worker, daemon=True)
        self.reload_thread.start()
        logger.info("[RELOAD] Auto-reload thread started")
    
    def stop_auto_reload(self):
        """Stop automatic configuration reload"""
        if self.reload_thread:
            self.stop_reload.set()
            self.reload_thread.join(timeout=10)
            logger.info("[RELOAD] Auto-reload thread stopped")
    
    def get_change_history(self, section: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get configuration change history"""
        changes = self.change_log
        
        if section:
            changes = [c for c in changes if c.section == section]
        
        # Convert to dict format and limit results
        return [asdict(change) for change in changes[-limit:]]
    
    def rollback_to_previous(self) -> bool:
        """Rollback to previous configuration version"""
        try:
            with self.config_lock:
                if not self.config_history:
                    logger.warning("[ROLLBACK] No previous configuration version available")
                    return False
                
                # Get previous configuration
                previous_config = self.config_history[-1]
                
                # Validate previous configuration
                validation = self.validate_configuration(previous_config)
                if not validation.is_valid:
                    logger.error(f"[ROLLBACK] Previous configuration is invalid: {validation.errors}")
                    return False
                
                # Store current as history and rollback
                self.config_history.append(self.current_config.copy())
                self.current_config = validation.validated_config
                
                # Log rollback
                self._log_configuration_changes(
                    self.config_history[-1], self.current_config, "system", "Configuration rollback"
                )
                
                # Notify callbacks
                self._notify_change_callbacks()
                
                logger.info("[ROLLBACK] Configuration rolled back successfully")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Error during rollback: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get configuration manager status"""
        return {
            'config_path': str(self.config_path),
            'auto_reload_enabled': self.auto_reload,
            'auto_reload_active': self.reload_thread and self.reload_thread.is_alive(),
            'config_sections': list(self.current_config.keys()),
            'history_versions': len(self.config_history),
            'total_changes': len(self.change_log),
            'registered_callbacks': {section: len(callbacks) for section, callbacks in self.change_callbacks.items()},
            'last_modified': self.config_path.stat().st_mtime if self.config_path.exists() else None
        }
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_auto_reload()
