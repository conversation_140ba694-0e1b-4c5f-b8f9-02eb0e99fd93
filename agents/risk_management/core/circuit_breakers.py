#!/usr/bin/env python3
"""
Circuit Breakers Module

Advanced circuit breaker and emergency control system:
- Drawdown-based circuit breakers
- Loss-based emergency stops
- Market volatility triggers
- Time-based cooling periods
- Portfolio heat monitoring
- Kill switch functionality
"""

import logging
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import asyncio

# Import utility modules
from utils.risk_models import (
    TradeRequest, Position, Portfolio, RiskMetrics,
    TradeDirection, ProductType, RiskAlert
)

logger = logging.getLogger(__name__)


class CircuitBreakerType(Enum):
    """Types of circuit breakers"""
    DRAWDOWN = "drawdown"
    DAILY_LOSS = "daily_loss"
    POSITION_LOSS = "position_loss"
    VOLATILITY = "volatility"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    PORTFOLIO_HEAT = "portfolio_heat"
    MARKET_WIDE = "market_wide"
    EMERGENCY_STOP = "emergency_stop"


class CircuitBreakerAction(Enum):
    """Actions to take when circuit breaker triggers"""
    WARN = "warn"
    REDUCE_POSITION_SIZE = "reduce_position_size"
    STOP_NEW_TRADES = "stop_new_trades"
    CLOSE_LOSING_POSITIONS = "close_losing_positions"
    CLOSE_ALL_POSITIONS = "close_all_positions"
    SYSTEM_SHUTDOWN = "system_shutdown"


@dataclass
class CircuitBreakerRule:
    """Circuit breaker rule definition"""
    breaker_type: CircuitBreakerType
    threshold: float
    action: CircuitBreakerAction
    cooldown_minutes: int
    enabled: bool
    description: str
    severity: str  # 'low', 'medium', 'high', 'critical'


@dataclass
class CircuitBreakerEvent:
    """Circuit breaker trigger event"""
    breaker_type: CircuitBreakerType
    rule: CircuitBreakerRule
    trigger_value: float
    threshold: float
    action_taken: CircuitBreakerAction
    message: str
    timestamp: datetime
    cooldown_until: datetime
    metadata: Dict[str, Any]


class CircuitBreakers:
    """
    Circuit Breakers - Advanced risk protection system
    
    Features:
    - Multiple trigger conditions
    - Configurable actions and thresholds
    - Cooldown periods
    - Emergency stop functionality
    - Real-time monitoring
    - Event logging and alerts
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Circuit Breakers"""
        self.config = config
        self.breakers_config = config.get('circuit_breakers', {})
        
        # Initialize circuit breaker rules
        self.rules: Dict[CircuitBreakerType, CircuitBreakerRule] = {}
        self._initialize_rules()
        
        # State tracking
        self.active_breakers: Dict[CircuitBreakerType, CircuitBreakerEvent] = {}
        self.breaker_history: List[CircuitBreakerEvent] = []
        self.system_stopped = False
        self.emergency_stop_active = False
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        self.consecutive_losses = 0
        self.portfolio_heat = 0.0
        
        # Callbacks for actions
        self.action_callbacks: Dict[CircuitBreakerAction, Callable] = {}
        
        logger.info(f"[INIT] Circuit Breakers initialized with {len(self.rules)} rules")
    
    def _initialize_rules(self):
        """Initialize circuit breaker rules from configuration"""
        try:
            # Drawdown circuit breaker
            drawdown_config = self.breakers_config.get('drawdown', {})
            if drawdown_config.get('enabled', True):
                self.rules[CircuitBreakerType.DRAWDOWN] = CircuitBreakerRule(
                    breaker_type=CircuitBreakerType.DRAWDOWN,
                    threshold=drawdown_config.get('threshold', 0.05),  # 5% drawdown
                    action=CircuitBreakerAction(drawdown_config.get('action', 'stop_new_trades')),
                    cooldown_minutes=drawdown_config.get('cooldown_minutes', 30),
                    enabled=True,
                    description="Maximum drawdown protection",
                    severity='high'
                )
            
            # Daily loss circuit breaker
            daily_loss_config = self.breakers_config.get('daily_loss', {})
            if daily_loss_config.get('enabled', True):
                self.rules[CircuitBreakerType.DAILY_LOSS] = CircuitBreakerRule(
                    breaker_type=CircuitBreakerType.DAILY_LOSS,
                    threshold=daily_loss_config.get('threshold', 0.03),  # 3% daily loss
                    action=CircuitBreakerAction(daily_loss_config.get('action', 'close_losing_positions')),
                    cooldown_minutes=daily_loss_config.get('cooldown_minutes', 60),
                    enabled=True,
                    description="Daily loss limit protection",
                    severity='high'
                )
            
            # Consecutive losses circuit breaker
            consecutive_config = self.breakers_config.get('consecutive_losses', {})
            if consecutive_config.get('enabled', True):
                self.rules[CircuitBreakerType.CONSECUTIVE_LOSSES] = CircuitBreakerRule(
                    breaker_type=CircuitBreakerType.CONSECUTIVE_LOSSES,
                    threshold=consecutive_config.get('threshold', 5),  # 5 consecutive losses
                    action=CircuitBreakerAction(consecutive_config.get('action', 'reduce_position_size')),
                    cooldown_minutes=consecutive_config.get('cooldown_minutes', 15),
                    enabled=True,
                    description="Consecutive losses protection",
                    severity='medium'
                )
            
            # Portfolio heat circuit breaker
            heat_config = self.breakers_config.get('portfolio_heat', {})
            if heat_config.get('enabled', True):
                self.rules[CircuitBreakerType.PORTFOLIO_HEAT] = CircuitBreakerRule(
                    breaker_type=CircuitBreakerType.PORTFOLIO_HEAT,
                    threshold=heat_config.get('threshold', 0.08),  # 8% portfolio heat
                    action=CircuitBreakerAction(heat_config.get('action', 'stop_new_trades')),
                    cooldown_minutes=heat_config.get('cooldown_minutes', 20),
                    enabled=True,
                    description="Portfolio heat protection",
                    severity='medium'
                )
            
            # Volatility circuit breaker
            volatility_config = self.breakers_config.get('volatility', {})
            if volatility_config.get('enabled', True):
                self.rules[CircuitBreakerType.VOLATILITY] = CircuitBreakerRule(
                    breaker_type=CircuitBreakerType.VOLATILITY,
                    threshold=volatility_config.get('threshold', 0.06),  # 6% volatility
                    action=CircuitBreakerAction(volatility_config.get('action', 'warn')),
                    cooldown_minutes=volatility_config.get('cooldown_minutes', 10),
                    enabled=True,
                    description="High volatility protection",
                    severity='low'
                )
            
            # Emergency stop (manual trigger)
            self.rules[CircuitBreakerType.EMERGENCY_STOP] = CircuitBreakerRule(
                breaker_type=CircuitBreakerType.EMERGENCY_STOP,
                threshold=0.0,  # Manual trigger
                action=CircuitBreakerAction.SYSTEM_SHUTDOWN,
                cooldown_minutes=0,  # No cooldown for emergency stop
                enabled=True,
                description="Emergency stop - manual trigger",
                severity='critical'
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing circuit breaker rules: {e}")
    
    def check_circuit_breakers(
        self, 
        portfolio_metrics: Dict[str, Any],
        market_data: Optional[Dict[str, Any]] = None
    ) -> List[CircuitBreakerEvent]:
        """
        Check all circuit breaker conditions
        
        Args:
            portfolio_metrics: Current portfolio performance metrics
            market_data: Real-time market data
            
        Returns:
            List of triggered circuit breaker events
        """
        triggered_events = []
        
        try:
            # Update internal metrics
            self._update_metrics(portfolio_metrics)
            
            # Check each active rule
            for breaker_type, rule in self.rules.items():
                if not rule.enabled:
                    continue
                
                # Skip if breaker is in cooldown
                if self._is_in_cooldown(breaker_type):
                    continue
                
                # Check specific breaker condition
                triggered = False
                trigger_value = 0.0
                metadata = {}
                
                if breaker_type == CircuitBreakerType.DRAWDOWN:
                    trigger_value = self.current_drawdown
                    triggered = trigger_value > rule.threshold
                    metadata = {'current_drawdown': trigger_value, 'max_drawdown': self.max_drawdown}
                
                elif breaker_type == CircuitBreakerType.DAILY_LOSS:
                    trigger_value = abs(self.daily_pnl) if self.daily_pnl < 0 else 0
                    total_capital = portfolio_metrics.get('total_capital', 100000)
                    daily_loss_percent = trigger_value / total_capital
                    triggered = daily_loss_percent > rule.threshold
                    metadata = {'daily_pnl': self.daily_pnl, 'daily_loss_percent': daily_loss_percent}
                
                elif breaker_type == CircuitBreakerType.CONSECUTIVE_LOSSES:
                    trigger_value = self.consecutive_losses
                    triggered = trigger_value >= rule.threshold
                    metadata = {'consecutive_losses': trigger_value}
                
                elif breaker_type == CircuitBreakerType.PORTFOLIO_HEAT:
                    trigger_value = self.portfolio_heat
                    triggered = trigger_value > rule.threshold
                    metadata = {'portfolio_heat': trigger_value}
                
                elif breaker_type == CircuitBreakerType.VOLATILITY:
                    if market_data and 'volatility' in market_data:
                        trigger_value = market_data['volatility']
                        triggered = trigger_value > rule.threshold
                        metadata = {'market_volatility': trigger_value}
                
                # If triggered, create event and take action
                if triggered:
                    event = self._create_breaker_event(rule, trigger_value, metadata)
                    triggered_events.append(event)
                    
                    # Execute action
                    self._execute_breaker_action(event)
                    
                    # Add to active breakers
                    self.active_breakers[breaker_type] = event
                    self.breaker_history.append(event)
                    
                    logger.warning(f"[BREAKER] {breaker_type.value} triggered: {event.message}")
            
            return triggered_events
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking circuit breakers: {e}")
            return []
    
    def _update_metrics(self, portfolio_metrics: Dict[str, Any]):
        """Update internal performance metrics"""
        try:
            self.daily_pnl = portfolio_metrics.get('daily_pnl', 0.0)
            self.current_drawdown = portfolio_metrics.get('current_drawdown', 0.0)
            self.max_drawdown = max(self.max_drawdown, self.current_drawdown)
            self.portfolio_heat = portfolio_metrics.get('portfolio_heat', 0.0)
            
            # Update consecutive losses (simplified - would track actual trade results)
            if self.daily_pnl < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
                
        except Exception as e:
            logger.error(f"[ERROR] Error updating metrics: {e}")
    
    def _is_in_cooldown(self, breaker_type: CircuitBreakerType) -> bool:
        """Check if circuit breaker is in cooldown period"""
        if breaker_type not in self.active_breakers:
            return False
        
        event = self.active_breakers[breaker_type]
        return datetime.now() < event.cooldown_until
    
    def _create_breaker_event(
        self, 
        rule: CircuitBreakerRule, 
        trigger_value: float, 
        metadata: Dict[str, Any]
    ) -> CircuitBreakerEvent:
        """Create circuit breaker event"""
        cooldown_until = datetime.now() + timedelta(minutes=rule.cooldown_minutes)
        
        message = f"{rule.description}: {trigger_value:.1%} exceeds {rule.threshold:.1%}"
        if rule.breaker_type == CircuitBreakerType.CONSECUTIVE_LOSSES:
            message = f"{rule.description}: {int(trigger_value)} consecutive losses"
        
        return CircuitBreakerEvent(
            breaker_type=rule.breaker_type,
            rule=rule,
            trigger_value=trigger_value,
            threshold=rule.threshold,
            action_taken=rule.action,
            message=message,
            timestamp=datetime.now(),
            cooldown_until=cooldown_until,
            metadata=metadata
        )
    
    def _execute_breaker_action(self, event: CircuitBreakerEvent):
        """Execute circuit breaker action"""
        try:
            action = event.action_taken
            
            if action == CircuitBreakerAction.WARN:
                logger.warning(f"[WARNING] {event.message}")
            
            elif action == CircuitBreakerAction.STOP_NEW_TRADES:
                self.system_stopped = True
                logger.critical(f"[STOP] New trades stopped: {event.message}")
            
            elif action == CircuitBreakerAction.SYSTEM_SHUTDOWN:
                self.emergency_stop_active = True
                self.system_stopped = True
                logger.critical(f"[EMERGENCY] System shutdown: {event.message}")
            
            # Execute callback if registered
            if action in self.action_callbacks:
                try:
                    self.action_callbacks[action](event)
                except Exception as e:
                    logger.error(f"[ERROR] Error executing callback for {action.value}: {e}")
                    
        except Exception as e:
            logger.error(f"[ERROR] Error executing breaker action: {e}")
    
    def register_action_callback(self, action: CircuitBreakerAction, callback: Callable):
        """Register callback for circuit breaker action"""
        self.action_callbacks[action] = callback
        logger.info(f"[CALLBACK] Registered callback for {action.value}")
    
    def trigger_emergency_stop(self, reason: str = "Manual emergency stop"):
        """Manually trigger emergency stop"""
        try:
            rule = self.rules[CircuitBreakerType.EMERGENCY_STOP]
            event = CircuitBreakerEvent(
                breaker_type=CircuitBreakerType.EMERGENCY_STOP,
                rule=rule,
                trigger_value=1.0,
                threshold=0.0,
                action_taken=CircuitBreakerAction.SYSTEM_SHUTDOWN,
                message=reason,
                timestamp=datetime.now(),
                cooldown_until=datetime.now(),
                metadata={'manual_trigger': True, 'reason': reason}
            )
            
            self._execute_breaker_action(event)
            self.active_breakers[CircuitBreakerType.EMERGENCY_STOP] = event
            self.breaker_history.append(event)
            
            logger.critical(f"[EMERGENCY] Manual emergency stop triggered: {reason}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error triggering emergency stop: {e}")
    
    def reset_circuit_breaker(self, breaker_type: CircuitBreakerType):
        """Reset a specific circuit breaker"""
        try:
            if breaker_type in self.active_breakers:
                del self.active_breakers[breaker_type]
                logger.info(f"[RESET] Circuit breaker {breaker_type.value} reset")
            
            # Reset system state if no critical breakers active
            if breaker_type == CircuitBreakerType.EMERGENCY_STOP:
                self.emergency_stop_active = False
            
            critical_breakers = [
                CircuitBreakerType.EMERGENCY_STOP,
                CircuitBreakerType.DRAWDOWN,
                CircuitBreakerType.DAILY_LOSS
            ]
            
            if not any(bt in self.active_breakers for bt in critical_breakers):
                self.system_stopped = False
                logger.info("[RESET] System trading resumed")
                
        except Exception as e:
            logger.error(f"[ERROR] Error resetting circuit breaker: {e}")
    
    def is_trading_allowed(self) -> Tuple[bool, str]:
        """Check if trading is currently allowed"""
        if self.emergency_stop_active:
            return False, "Emergency stop active"
        
        if self.system_stopped:
            active_reasons = [
                event.message for event in self.active_breakers.values()
                if event.action_taken in [CircuitBreakerAction.STOP_NEW_TRADES, CircuitBreakerAction.SYSTEM_SHUTDOWN]
            ]
            return False, f"Trading stopped: {'; '.join(active_reasons)}"
        
        return True, "Trading allowed"
    
    def get_breaker_status(self) -> Dict[str, Any]:
        """Get comprehensive circuit breaker status"""
        return {
            'system_stopped': self.system_stopped,
            'emergency_stop_active': self.emergency_stop_active,
            'active_breakers': {
                bt.value: {
                    'message': event.message,
                    'triggered_at': event.timestamp.isoformat(),
                    'cooldown_until': event.cooldown_until.isoformat(),
                    'action': event.action_taken.value,
                    'severity': event.rule.severity
                }
                for bt, event in self.active_breakers.items()
            },
            'rules': {
                bt.value: {
                    'enabled': rule.enabled,
                    'threshold': rule.threshold,
                    'action': rule.action.value,
                    'cooldown_minutes': rule.cooldown_minutes,
                    'description': rule.description,
                    'severity': rule.severity
                }
                for bt, rule in self.rules.items()
            },
            'current_metrics': {
                'daily_pnl': self.daily_pnl,
                'current_drawdown': self.current_drawdown,
                'max_drawdown': self.max_drawdown,
                'consecutive_losses': self.consecutive_losses,
                'portfolio_heat': self.portfolio_heat
            },
            'recent_events': [
                {
                    'type': event.breaker_type.value,
                    'message': event.message,
                    'timestamp': event.timestamp.isoformat(),
                    'action': event.action_taken.value
                }
                for event in self.breaker_history[-10:]  # Last 10 events
            ]
        }
