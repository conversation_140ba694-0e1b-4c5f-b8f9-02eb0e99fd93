#!/usr/bin/env python3
"""
Pre-Trade Filters Module

Comprehensive pre-trade validation and filtering system:
- Risk-reward ratio validation
- Position concentration limits
- Volatility and liquidity checks
- Market regime filters
- Time-based restrictions
- Symbol blacklisting
- Correlation limits
"""

import logging
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, timedelta, time
from dataclasses import dataclass
from enum import Enum

# Import utility modules
from utils.risk_models import (
    TradeRequest, ValidationResult, ValidationStatus, RiskLevel,
    TradeDirection, ProductType
)

logger = logging.getLogger(__name__)


class FilterType(Enum):
    """Types of pre-trade filters"""
    RISK_REWARD = "risk_reward"
    POSITION_LIMIT = "position_limit"
    CONCENTRATION = "concentration"
    VOLATILITY = "volatility"
    LIQUIDITY = "liquidity"
    TIME_RESTRICTION = "time_restriction"
    BLACKLIST = "blacklist"
    CORRELATION = "correlation"
    MARKET_REGIME = "market_regime"


@dataclass
class FilterResult:
    """Result of a single filter check"""
    filter_type: FilterType
    passed: bool
    reason: str
    severity: str  # 'warning', 'error', 'critical'
    details: Dict[str, Any]
    timestamp: datetime


@dataclass
class ValidationSummary:
    """Summary of all filter validations"""
    overall_passed: bool
    total_filters: int
    passed_filters: int
    failed_filters: int
    warnings: List[str]
    errors: List[str]
    critical_issues: List[str]
    filter_results: List[FilterResult]
    validation_time_ms: float


class PreTradeFilters:
    """
    Pre-Trade Filters - Comprehensive trade validation system
    
    Features:
    - Multiple validation layers
    - Configurable filter parameters
    - Real-time market condition checks
    - Symbol and strategy specific rules
    - Time-based restrictions
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Pre-Trade Filters"""
        self.config = config
        self.filters_config = config.get('pre_trade_filters', {})
        
        # Risk-reward settings
        self.min_risk_reward_ratio = self.filters_config.get('min_risk_reward_ratio', 1.5)
        self.preferred_risk_reward_ratio = self.filters_config.get('preferred_risk_reward_ratio', 2.0)
        
        # Position limits
        self.max_concurrent_positions = self.filters_config.get('max_concurrent_positions', 5)
        self.max_positions_per_symbol = self.filters_config.get('max_positions_per_symbol', 1)
        self.max_sector_concentration = self.filters_config.get('max_sector_concentration', 0.3)
        
        # Volatility limits
        self.max_volatility_threshold = self.filters_config.get('max_volatility_threshold', 0.05)
        self.min_volatility_threshold = self.filters_config.get('min_volatility_threshold', 0.005)
        
        # Time restrictions
        self.trading_hours = self.filters_config.get('trading_hours', {
            'start': '09:15',
            'end': '15:30',
            'lunch_break': {'start': '12:00', 'end': '13:00'}
        })
        
        # Blacklists and restrictions
        self.blacklisted_symbols: Set[str] = set(self.filters_config.get('blacklisted_symbols', []))
        self.restricted_strategies: Set[str] = set(self.filters_config.get('restricted_strategies', []))
        
        # Current positions tracking
        self.active_positions: Dict[str, Any] = {}
        self.position_count_by_symbol: Dict[str, int] = {}
        self.sector_exposure: Dict[str, float] = {}
        
        logger.info(f"[INIT] Pre-Trade Filters initialized with {len(self.get_active_filters())} filters")
    
    def validate_trade(
        self, 
        trade_request: TradeRequest,
        current_positions: Optional[Dict[str, Any]] = None,
        market_data: Optional[Dict[str, Any]] = None
    ) -> ValidationSummary:
        """
        Validate trade request against all pre-trade filters
        
        Args:
            trade_request: Trade request to validate
            current_positions: Current portfolio positions
            market_data: Real-time market data
            
        Returns:
            ValidationSummary with detailed validation results
        """
        start_time = datetime.now()
        filter_results = []
        warnings = []
        errors = []
        critical_issues = []
        
        try:
            # Update current positions if provided
            if current_positions:
                self.active_positions = current_positions
                self._update_position_tracking()
            
            # Run all active filters
            active_filters = self.get_active_filters()
            
            for filter_type in active_filters:
                try:
                    if filter_type == FilterType.RISK_REWARD:
                        result = self._validate_risk_reward(trade_request)
                    elif filter_type == FilterType.POSITION_LIMIT:
                        result = self._validate_position_limits(trade_request)
                    elif filter_type == FilterType.CONCENTRATION:
                        result = self._validate_concentration(trade_request)
                    elif filter_type == FilterType.VOLATILITY:
                        result = self._validate_volatility(trade_request, market_data)
                    elif filter_type == FilterType.LIQUIDITY:
                        result = self._validate_liquidity(trade_request, market_data)
                    elif filter_type == FilterType.TIME_RESTRICTION:
                        result = self._validate_time_restrictions(trade_request)
                    elif filter_type == FilterType.BLACKLIST:
                        result = self._validate_blacklist(trade_request)
                    elif filter_type == FilterType.CORRELATION:
                        result = self._validate_correlation(trade_request)
                    elif filter_type == FilterType.MARKET_REGIME:
                        result = self._validate_market_regime(trade_request, market_data)
                    else:
                        continue
                    
                    filter_results.append(result)
                    
                    # Categorize issues
                    if not result.passed:
                        if result.severity == 'critical':
                            critical_issues.append(result.reason)
                        elif result.severity == 'error':
                            errors.append(result.reason)
                        else:
                            warnings.append(result.reason)
                            
                except Exception as e:
                    logger.error(f"[ERROR] Filter {filter_type.value} failed: {e}")
                    error_result = FilterResult(
                        filter_type=filter_type,
                        passed=False,
                        reason=f"Filter execution error: {str(e)}",
                        severity='error',
                        details={'error': str(e)},
                        timestamp=datetime.now()
                    )
                    filter_results.append(error_result)
                    errors.append(error_result.reason)
            
            # Determine overall validation result
            passed_filters = sum(1 for r in filter_results if r.passed)
            failed_filters = len(filter_results) - passed_filters
            overall_passed = len(critical_issues) == 0 and len(errors) == 0
            
            # Calculate validation time
            validation_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return ValidationSummary(
                overall_passed=overall_passed,
                total_filters=len(filter_results),
                passed_filters=passed_filters,
                failed_filters=failed_filters,
                warnings=warnings,
                errors=errors,
                critical_issues=critical_issues,
                filter_results=filter_results,
                validation_time_ms=validation_time
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Trade validation failed: {e}")
            validation_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return ValidationSummary(
                overall_passed=False,
                total_filters=0,
                passed_filters=0,
                failed_filters=1,
                warnings=[],
                errors=[f"Validation system error: {str(e)}"],
                critical_issues=[],
                filter_results=[],
                validation_time_ms=validation_time
            )
    
    def _validate_risk_reward(self, trade_request: TradeRequest) -> FilterResult:
        """Validate risk-reward ratio"""
        try:
            entry_price = trade_request.entry_price
            stop_loss = trade_request.stop_loss
            take_profit = trade_request.take_profit
            
            if trade_request.direction == TradeDirection.LONG:
                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
            else:  # SHORT
                risk = abs(stop_loss - entry_price)
                reward = abs(entry_price - take_profit)
            
            if risk <= 0:
                return FilterResult(
                    filter_type=FilterType.RISK_REWARD,
                    passed=False,
                    reason="Invalid risk calculation: risk <= 0",
                    severity='critical',
                    details={'risk': risk, 'reward': reward},
                    timestamp=datetime.now()
                )
            
            rr_ratio = reward / risk
            
            if rr_ratio < self.min_risk_reward_ratio:
                return FilterResult(
                    filter_type=FilterType.RISK_REWARD,
                    passed=False,
                    reason=f"Risk-reward ratio {rr_ratio:.2f} below minimum {self.min_risk_reward_ratio}",
                    severity='error',
                    details={'rr_ratio': rr_ratio, 'min_required': self.min_risk_reward_ratio},
                    timestamp=datetime.now()
                )
            
            severity = 'info' if rr_ratio >= self.preferred_risk_reward_ratio else 'warning'
            reason = f"Risk-reward ratio {rr_ratio:.2f} acceptable"
            
            return FilterResult(
                filter_type=FilterType.RISK_REWARD,
                passed=True,
                reason=reason,
                severity=severity,
                details={'rr_ratio': rr_ratio, 'risk': risk, 'reward': reward},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return FilterResult(
                filter_type=FilterType.RISK_REWARD,
                passed=False,
                reason=f"Risk-reward validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    def _validate_position_limits(self, trade_request: TradeRequest) -> FilterResult:
        """Validate position count limits"""
        try:
            current_positions = len(self.active_positions)
            symbol_positions = self.position_count_by_symbol.get(trade_request.symbol, 0)
            
            # Check total position limit
            if current_positions >= self.max_concurrent_positions:
                return FilterResult(
                    filter_type=FilterType.POSITION_LIMIT,
                    passed=False,
                    reason=f"Maximum concurrent positions reached: {current_positions}/{self.max_concurrent_positions}",
                    severity='error',
                    details={
                        'current_positions': current_positions,
                        'max_positions': self.max_concurrent_positions
                    },
                    timestamp=datetime.now()
                )
            
            # Check per-symbol position limit
            if symbol_positions >= self.max_positions_per_symbol:
                return FilterResult(
                    filter_type=FilterType.POSITION_LIMIT,
                    passed=False,
                    reason=f"Maximum positions for {trade_request.symbol} reached: {symbol_positions}/{self.max_positions_per_symbol}",
                    severity='error',
                    details={
                        'symbol': trade_request.symbol,
                        'symbol_positions': symbol_positions,
                        'max_per_symbol': self.max_positions_per_symbol
                    },
                    timestamp=datetime.now()
                )
            
            return FilterResult(
                filter_type=FilterType.POSITION_LIMIT,
                passed=True,
                reason="Position limits acceptable",
                severity='info',
                details={
                    'current_positions': current_positions,
                    'symbol_positions': symbol_positions,
                    'limits': {
                        'max_concurrent': self.max_concurrent_positions,
                        'max_per_symbol': self.max_positions_per_symbol
                    }
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return FilterResult(
                filter_type=FilterType.POSITION_LIMIT,
                passed=False,
                reason=f"Position limit validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_concentration(self, trade_request: TradeRequest) -> FilterResult:
        """Validate concentration risk limits"""
        try:
            # Get sector for the symbol (placeholder - would integrate with sector mapping)
            symbol_sector = self._get_symbol_sector(trade_request.symbol)
            current_sector_exposure = self.sector_exposure.get(symbol_sector, 0.0)

            # Estimate new exposure (simplified calculation)
            position_value = trade_request.quantity * trade_request.entry_price
            total_portfolio_value = sum(pos.get('value', 0) for pos in self.active_positions.values())
            total_portfolio_value += position_value  # Include new position

            new_sector_exposure = (current_sector_exposure + position_value) / total_portfolio_value if total_portfolio_value > 0 else 0

            if new_sector_exposure > self.max_sector_concentration:
                return FilterResult(
                    filter_type=FilterType.CONCENTRATION,
                    passed=False,
                    reason=f"Sector concentration risk: {symbol_sector} exposure {new_sector_exposure:.1%} exceeds {self.max_sector_concentration:.1%}",
                    severity='error',
                    details={
                        'sector': symbol_sector,
                        'current_exposure': current_sector_exposure,
                        'new_exposure': new_sector_exposure,
                        'max_allowed': self.max_sector_concentration
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.CONCENTRATION,
                passed=True,
                reason=f"Concentration risk acceptable: {symbol_sector} {new_sector_exposure:.1%}",
                severity='info',
                details={
                    'sector': symbol_sector,
                    'exposure': new_sector_exposure,
                    'limit': self.max_sector_concentration
                },
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.CONCENTRATION,
                passed=False,
                reason=f"Concentration validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_volatility(self, trade_request: TradeRequest, market_data: Optional[Dict[str, Any]]) -> FilterResult:
        """Validate volatility limits"""
        try:
            # Get volatility from market data or estimate
            volatility = None
            if market_data and 'volatility' in market_data:
                volatility = market_data['volatility']
            else:
                # Estimate volatility based on symbol type
                volatility = self._estimate_symbol_volatility(trade_request.symbol)

            if volatility is None:
                return FilterResult(
                    filter_type=FilterType.VOLATILITY,
                    passed=True,
                    reason="Volatility data not available, skipping check",
                    severity='warning',
                    details={'volatility': None},
                    timestamp=datetime.now()
                )

            # Check volatility limits
            if volatility > self.max_volatility_threshold:
                return FilterResult(
                    filter_type=FilterType.VOLATILITY,
                    passed=False,
                    reason=f"Volatility {volatility:.1%} exceeds maximum {self.max_volatility_threshold:.1%}",
                    severity='error',
                    details={
                        'volatility': volatility,
                        'max_threshold': self.max_volatility_threshold
                    },
                    timestamp=datetime.now()
                )

            if volatility < self.min_volatility_threshold:
                return FilterResult(
                    filter_type=FilterType.VOLATILITY,
                    passed=False,
                    reason=f"Volatility {volatility:.1%} below minimum {self.min_volatility_threshold:.1%}",
                    severity='warning',
                    details={
                        'volatility': volatility,
                        'min_threshold': self.min_volatility_threshold
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.VOLATILITY,
                passed=True,
                reason=f"Volatility {volatility:.1%} within acceptable range",
                severity='info',
                details={
                    'volatility': volatility,
                    'min_threshold': self.min_volatility_threshold,
                    'max_threshold': self.max_volatility_threshold
                },
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.VOLATILITY,
                passed=False,
                reason=f"Volatility validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_liquidity(self, trade_request: TradeRequest, market_data: Optional[Dict[str, Any]]) -> FilterResult:
        """Validate liquidity requirements"""
        try:
            # Get liquidity metrics from market data
            if market_data:
                volume = market_data.get('volume', 0)
                avg_volume = market_data.get('avg_volume', 0)
                bid_ask_spread = market_data.get('bid_ask_spread', 0)
            else:
                # Use defaults for major indices/stocks
                volume = 1000000  # Default volume
                avg_volume = 1000000
                bid_ask_spread = 0.001  # 0.1% spread

            # Check minimum volume requirement
            min_volume = self.filters_config.get('min_volume', 100000)
            if volume < min_volume:
                return FilterResult(
                    filter_type=FilterType.LIQUIDITY,
                    passed=False,
                    reason=f"Volume {volume:,} below minimum {min_volume:,}",
                    severity='error',
                    details={
                        'volume': volume,
                        'min_volume': min_volume,
                        'avg_volume': avg_volume
                    },
                    timestamp=datetime.now()
                )

            # Check bid-ask spread
            max_spread = self.filters_config.get('max_bid_ask_spread', 0.005)  # 0.5%
            if bid_ask_spread > max_spread:
                return FilterResult(
                    filter_type=FilterType.LIQUIDITY,
                    passed=False,
                    reason=f"Bid-ask spread {bid_ask_spread:.1%} exceeds maximum {max_spread:.1%}",
                    severity='warning',
                    details={
                        'bid_ask_spread': bid_ask_spread,
                        'max_spread': max_spread
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.LIQUIDITY,
                passed=True,
                reason="Liquidity requirements met",
                severity='info',
                details={
                    'volume': volume,
                    'avg_volume': avg_volume,
                    'bid_ask_spread': bid_ask_spread
                },
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.LIQUIDITY,
                passed=False,
                reason=f"Liquidity validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_time_restrictions(self, trade_request: TradeRequest) -> FilterResult:
        """Validate time-based trading restrictions"""
        try:
            current_time = datetime.now().time()

            # Parse trading hours
            start_time = time.fromisoformat(self.trading_hours['start'])
            end_time = time.fromisoformat(self.trading_hours['end'])

            # Check if within trading hours
            if not (start_time <= current_time <= end_time):
                return FilterResult(
                    filter_type=FilterType.TIME_RESTRICTION,
                    passed=False,
                    reason=f"Outside trading hours: {current_time} not in {start_time}-{end_time}",
                    severity='error',
                    details={
                        'current_time': str(current_time),
                        'trading_start': str(start_time),
                        'trading_end': str(end_time)
                    },
                    timestamp=datetime.now()
                )

            # Check lunch break if configured
            if 'lunch_break' in self.trading_hours:
                lunch_start = time.fromisoformat(self.trading_hours['lunch_break']['start'])
                lunch_end = time.fromisoformat(self.trading_hours['lunch_break']['end'])

                if lunch_start <= current_time <= lunch_end:
                    return FilterResult(
                        filter_type=FilterType.TIME_RESTRICTION,
                        passed=False,
                        reason=f"During lunch break: {current_time} in {lunch_start}-{lunch_end}",
                        severity='warning',
                        details={
                            'current_time': str(current_time),
                            'lunch_start': str(lunch_start),
                            'lunch_end': str(lunch_end)
                        },
                        timestamp=datetime.now()
                    )

            return FilterResult(
                filter_type=FilterType.TIME_RESTRICTION,
                passed=True,
                reason="Time restrictions satisfied",
                severity='info',
                details={'current_time': str(current_time)},
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.TIME_RESTRICTION,
                passed=False,
                reason=f"Time validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_blacklist(self, trade_request: TradeRequest) -> FilterResult:
        """Validate against blacklisted symbols and strategies"""
        try:
            # Check symbol blacklist
            if trade_request.symbol in self.blacklisted_symbols:
                return FilterResult(
                    filter_type=FilterType.BLACKLIST,
                    passed=False,
                    reason=f"Symbol {trade_request.symbol} is blacklisted",
                    severity='critical',
                    details={
                        'symbol': trade_request.symbol,
                        'blacklisted_symbols': list(self.blacklisted_symbols)
                    },
                    timestamp=datetime.now()
                )

            # Check strategy restrictions
            if trade_request.strategy_name in self.restricted_strategies:
                return FilterResult(
                    filter_type=FilterType.BLACKLIST,
                    passed=False,
                    reason=f"Strategy {trade_request.strategy_name} is restricted",
                    severity='error',
                    details={
                        'strategy': trade_request.strategy_name,
                        'restricted_strategies': list(self.restricted_strategies)
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.BLACKLIST,
                passed=True,
                reason="No blacklist violations",
                severity='info',
                details={
                    'symbol': trade_request.symbol,
                    'strategy': trade_request.strategy_name
                },
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.BLACKLIST,
                passed=False,
                reason=f"Blacklist validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_correlation(self, trade_request: TradeRequest) -> FilterResult:
        """Validate correlation limits with existing positions"""
        try:
            # Simplified correlation check - would integrate with correlation matrix
            similar_positions = 0
            for pos_id, position in self.active_positions.items():
                pos_symbol = position.get('symbol', '')

                # Check for same sector or similar symbols
                if self._symbols_correlated(trade_request.symbol, pos_symbol):
                    similar_positions += 1

            max_correlated = self.filters_config.get('max_correlated_positions', 3)
            if similar_positions >= max_correlated:
                return FilterResult(
                    filter_type=FilterType.CORRELATION,
                    passed=False,
                    reason=f"Too many correlated positions: {similar_positions}/{max_correlated}",
                    severity='warning',
                    details={
                        'correlated_positions': similar_positions,
                        'max_allowed': max_correlated
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.CORRELATION,
                passed=True,
                reason=f"Correlation acceptable: {similar_positions} correlated positions",
                severity='info',
                details={'correlated_positions': similar_positions},
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.CORRELATION,
                passed=False,
                reason=f"Correlation validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    def _validate_market_regime(self, trade_request: TradeRequest, market_data: Optional[Dict[str, Any]]) -> FilterResult:
        """Validate market regime conditions"""
        try:
            if not market_data:
                return FilterResult(
                    filter_type=FilterType.MARKET_REGIME,
                    passed=True,
                    reason="Market data not available, skipping regime check",
                    severity='warning',
                    details={},
                    timestamp=datetime.now()
                )

            # Get market regime indicators
            vix_level = market_data.get('vix', 20)  # Default VIX level
            market_trend = market_data.get('trend', 'neutral')  # bullish, bearish, neutral

            # Check VIX levels for high volatility periods
            high_vix_threshold = self.filters_config.get('high_vix_threshold', 30)
            if vix_level > high_vix_threshold:
                return FilterResult(
                    filter_type=FilterType.MARKET_REGIME,
                    passed=False,
                    reason=f"High market volatility: VIX {vix_level} > {high_vix_threshold}",
                    severity='warning',
                    details={
                        'vix_level': vix_level,
                        'threshold': high_vix_threshold,
                        'market_trend': market_trend
                    },
                    timestamp=datetime.now()
                )

            return FilterResult(
                filter_type=FilterType.MARKET_REGIME,
                passed=True,
                reason=f"Market regime acceptable: VIX {vix_level}, trend {market_trend}",
                severity='info',
                details={
                    'vix_level': vix_level,
                    'market_trend': market_trend
                },
                timestamp=datetime.now()
            )

        except Exception as e:
            return FilterResult(
                filter_type=FilterType.MARKET_REGIME,
                passed=False,
                reason=f"Market regime validation error: {str(e)}",
                severity='error',
                details={'error': str(e)},
                timestamp=datetime.now()
            )

    # Utility methods
    def _update_position_tracking(self):
        """Update position tracking from current positions"""
        try:
            self.position_count_by_symbol.clear()
            self.sector_exposure.clear()

            total_portfolio_value = 0
            sector_values = {}

            for pos_id, position in self.active_positions.items():
                symbol = position.get('symbol', '')
                value = position.get('value', 0)

                # Count positions per symbol
                self.position_count_by_symbol[symbol] = self.position_count_by_symbol.get(symbol, 0) + 1

                # Calculate sector exposure
                sector = self._get_symbol_sector(symbol)
                sector_values[sector] = sector_values.get(sector, 0) + value
                total_portfolio_value += value

            # Calculate sector exposure percentages
            if total_portfolio_value > 0:
                for sector, value in sector_values.items():
                    self.sector_exposure[sector] = value / total_portfolio_value

        except Exception as e:
            logger.error(f"[ERROR] Error updating position tracking: {e}")

    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for a symbol (placeholder - would integrate with sector mapping)"""
        try:
            # Simplified sector mapping - would use real sector data
            if symbol in ['NIFTY', 'BANKNIFTY']:
                return 'INDEX'
            elif symbol.startswith('BANK') or 'BANK' in symbol:
                return 'BANKING'
            elif symbol in ['RELIANCE', 'ONGC', 'IOC']:
                return 'ENERGY'
            elif symbol in ['TCS', 'INFY', 'WIPRO', 'HCLTECH']:
                return 'IT'
            elif symbol in ['HDFC', 'ICICIBANK', 'SBIN', 'AXISBANK']:
                return 'BANKING'
            else:
                return 'OTHER'

        except Exception as e:
            logger.error(f"[ERROR] Error getting sector for {symbol}: {e}")
            return 'UNKNOWN'

    def _estimate_symbol_volatility(self, symbol: str) -> Optional[float]:
        """Estimate volatility for a symbol"""
        try:
            # Simplified volatility estimates - would use real historical data
            volatility_map = {
                'NIFTY': 0.015,      # 1.5% daily volatility
                'BANKNIFTY': 0.020,  # 2.0% daily volatility
                'RELIANCE': 0.025,   # 2.5% daily volatility
                'TCS': 0.020,        # 2.0% daily volatility
                'HDFC': 0.022,       # 2.2% daily volatility
            }

            return volatility_map.get(symbol, 0.025)  # Default 2.5%

        except Exception as e:
            logger.error(f"[ERROR] Error estimating volatility for {symbol}: {e}")
            return None

    def _symbols_correlated(self, symbol1: str, symbol2: str) -> bool:
        """Check if two symbols are correlated (simplified)"""
        try:
            # Same symbol
            if symbol1 == symbol2:
                return True

            # Same sector
            if self._get_symbol_sector(symbol1) == self._get_symbol_sector(symbol2):
                return True

            # Banking stocks correlation
            banking_symbols = ['HDFC', 'ICICIBANK', 'SBIN', 'AXISBANK', 'KOTAKBANK']
            if symbol1 in banking_symbols and symbol2 in banking_symbols:
                return True

            # IT stocks correlation
            it_symbols = ['TCS', 'INFY', 'WIPRO', 'HCLTECH', 'TECHM']
            if symbol1 in it_symbols and symbol2 in it_symbols:
                return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Error checking correlation between {symbol1} and {symbol2}: {e}")
            return False

    def get_active_filters(self) -> List[FilterType]:
        """Get list of currently active filters"""
        active_filters = []

        # Check which filters are enabled in config
        if self.filters_config.get('risk_reward_enabled', True):
            active_filters.append(FilterType.RISK_REWARD)

        if self.filters_config.get('position_limits_enabled', True):
            active_filters.append(FilterType.POSITION_LIMIT)

        if self.filters_config.get('concentration_enabled', True):
            active_filters.append(FilterType.CONCENTRATION)

        if self.filters_config.get('volatility_enabled', True):
            active_filters.append(FilterType.VOLATILITY)

        if self.filters_config.get('liquidity_enabled', True):
            active_filters.append(FilterType.LIQUIDITY)

        if self.filters_config.get('time_restrictions_enabled', True):
            active_filters.append(FilterType.TIME_RESTRICTION)

        if self.filters_config.get('blacklist_enabled', True):
            active_filters.append(FilterType.BLACKLIST)

        if self.filters_config.get('correlation_enabled', True):
            active_filters.append(FilterType.CORRELATION)

        if self.filters_config.get('market_regime_enabled', True):
            active_filters.append(FilterType.MARKET_REGIME)

        return active_filters

    def add_to_blacklist(self, symbol: str):
        """Add symbol to blacklist"""
        self.blacklisted_symbols.add(symbol)
        logger.info(f"[BLACKLIST] Added {symbol} to blacklist")

    def remove_from_blacklist(self, symbol: str):
        """Remove symbol from blacklist"""
        self.blacklisted_symbols.discard(symbol)
        logger.info(f"[BLACKLIST] Removed {symbol} from blacklist")

    def update_position(self, position_id: str, position_data: Dict[str, Any]):
        """Update position data for tracking"""
        self.active_positions[position_id] = position_data
        self._update_position_tracking()

    def remove_position(self, position_id: str):
        """Remove position from tracking"""
        if position_id in self.active_positions:
            del self.active_positions[position_id]
            self._update_position_tracking()

    def get_filter_summary(self) -> Dict[str, Any]:
        """Get comprehensive filter configuration and status"""
        return {
            'active_filters': [f.value for f in self.get_active_filters()],
            'configuration': {
                'min_risk_reward_ratio': self.min_risk_reward_ratio,
                'max_concurrent_positions': self.max_concurrent_positions,
                'max_positions_per_symbol': self.max_positions_per_symbol,
                'max_sector_concentration': self.max_sector_concentration,
                'volatility_limits': {
                    'min': self.min_volatility_threshold,
                    'max': self.max_volatility_threshold
                },
                'trading_hours': self.trading_hours
            },
            'current_state': {
                'blacklisted_symbols': list(self.blacklisted_symbols),
                'restricted_strategies': list(self.restricted_strategies),
                'active_positions_count': len(self.active_positions),
                'position_count_by_symbol': dict(self.position_count_by_symbol),
                'sector_exposure': dict(self.sector_exposure)
            }
        }
