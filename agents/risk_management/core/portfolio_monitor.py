#!/usr/bin/env python3
"""
Portfolio Monitor Module

Real-time portfolio monitoring and analytics:
- Portfolio performance tracking
- Risk metrics calculation (VaR, CVaR)
- Correlation analysis
- Concentration risk monitoring
- Stress testing scenarios
- Real-time P&L tracking
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

# Import utility modules
from utils.risk_models import (
    TradeRequest, Position, Portfolio, RiskMetrics,
    TradeDirection, ProductType, PerformanceSnapshot
)

logger = logging.getLogger(__name__)


class RiskMetricType(Enum):
    """Types of risk metrics"""
    VAR = "value_at_risk"
    CVAR = "conditional_var"
    SHARPE_RATIO = "sharpe_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    VOLATILITY = "volatility"
    BETA = "beta"
    CORRELATION = "correlation"


@dataclass
class VaRResult:
    """Value at Risk calculation result"""
    var_1d: float
    var_5d: float
    var_10d: float
    confidence_level: float
    method: str
    timestamp: datetime


@dataclass
class StressTestResult:
    """Stress test scenario result"""
    scenario_name: str
    market_shock: float
    portfolio_impact: float
    impact_percent: float
    positions_affected: int
    worst_position: str
    worst_position_impact: float
    timestamp: datetime


@dataclass
class CorrelationAnalysis:
    """Portfolio correlation analysis"""
    correlation_matrix: Dict[str, Dict[str, float]]
    avg_correlation: float
    max_correlation: float
    min_correlation: float
    highly_correlated_pairs: List[Tuple[str, str, float]]
    diversification_ratio: float
    timestamp: datetime


class PortfolioMonitor:
    """
    Portfolio Monitor - Real-time portfolio analytics and risk monitoring
    
    Features:
    - Real-time P&L tracking
    - Risk metrics calculation (VaR, CVaR, Sharpe)
    - Correlation and concentration analysis
    - Stress testing scenarios
    - Performance attribution
    - Risk-adjusted returns
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Portfolio Monitor"""
        self.config = config
        self.monitor_config = config.get('portfolio_monitor', {})
        
        # Risk calculation parameters
        self.var_confidence_levels = self.monitor_config.get('var_confidence_levels', [0.95, 0.99])
        self.var_lookback_days = self.monitor_config.get('var_lookback_days', 252)
        self.correlation_lookback_days = self.monitor_config.get('correlation_lookback_days', 60)
        
        # Portfolio state
        self.positions: Dict[str, Position] = {}
        self.historical_returns: Dict[str, List[float]] = {}
        self.portfolio_history: List[PerformanceSnapshot] = []
        
        # Risk metrics cache
        self.current_var: Optional[VaRResult] = None
        self.current_correlation: Optional[CorrelationAnalysis] = None
        self.stress_test_results: List[StressTestResult] = []
        
        # Performance tracking
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.max_portfolio_value = 0.0
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        
        logger.info("[INIT] Portfolio Monitor initialized")
    
    def update_positions(self, positions: Dict[str, Position]):
        """Update current portfolio positions"""
        try:
            self.positions = positions.copy()
            self._calculate_portfolio_metrics()
            logger.debug(f"[UPDATE] Updated {len(positions)} positions")
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating positions: {e}")
    
    def add_position(self, position_id: str, position: Position):
        """Add new position to portfolio"""
        try:
            self.positions[position_id] = position
            self._calculate_portfolio_metrics()
            logger.debug(f"[ADD] Added position {position_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error adding position: {e}")
    
    def remove_position(self, position_id: str):
        """Remove position from portfolio"""
        try:
            if position_id in self.positions:
                del self.positions[position_id]
                self._calculate_portfolio_metrics()
                logger.debug(f"[REMOVE] Removed position {position_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error removing position: {e}")
    
    def calculate_var(
        self, 
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> VaRResult:
        """
        Calculate Value at Risk for the portfolio
        
        Args:
            confidence_level: Confidence level (0.95 = 95%)
            method: Calculation method ('historical', 'parametric', 'monte_carlo')
            
        Returns:
            VaRResult with VaR calculations
        """
        try:
            if not self.positions:
                return VaRResult(0, 0, 0, confidence_level, method, datetime.now())
            
            # Get portfolio returns
            portfolio_returns = self._get_portfolio_returns()
            
            if len(portfolio_returns) < 30:  # Need minimum data
                logger.warning("[VAR] Insufficient data for VaR calculation")
                return VaRResult(0, 0, 0, confidence_level, method, datetime.now())
            
            if method == "historical":
                var_1d = self._calculate_historical_var(portfolio_returns, confidence_level)
            elif method == "parametric":
                var_1d = self._calculate_parametric_var(portfolio_returns, confidence_level)
            else:
                var_1d = self._calculate_historical_var(portfolio_returns, confidence_level)
            
            # Scale to different time horizons
            var_5d = var_1d * math.sqrt(5)
            var_10d = var_1d * math.sqrt(10)
            
            result = VaRResult(
                var_1d=var_1d,
                var_5d=var_5d,
                var_10d=var_10d,
                confidence_level=confidence_level,
                method=method,
                timestamp=datetime.now()
            )
            
            self.current_var = result
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating VaR: {e}")
            return VaRResult(0, 0, 0, confidence_level, method, datetime.now())
    
    def _calculate_historical_var(self, returns: List[float], confidence_level: float) -> float:
        """Calculate historical VaR"""
        try:
            returns_array = np.array(returns)
            percentile = (1 - confidence_level) * 100
            var = np.percentile(returns_array, percentile)
            return abs(var)  # Return positive value
            
        except Exception as e:
            logger.error(f"[ERROR] Error in historical VaR calculation: {e}")
            return 0.0
    
    def _calculate_parametric_var(self, returns: List[float], confidence_level: float) -> float:
        """Calculate parametric VaR assuming normal distribution"""
        try:
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Z-score for confidence level
            from scipy import stats
            z_score = stats.norm.ppf(1 - confidence_level)
            
            var = abs(mean_return + z_score * std_return)
            return var
            
        except Exception as e:
            logger.error(f"[ERROR] Error in parametric VaR calculation: {e}")
            # Fallback to historical method
            return self._calculate_historical_var(returns, confidence_level)
    
    def calculate_correlation_analysis(self) -> CorrelationAnalysis:
        """Calculate portfolio correlation analysis"""
        try:
            if len(self.positions) < 2:
                return CorrelationAnalysis({}, 0, 0, 0, [], 1.0, datetime.now())
            
            # Get returns for each position
            position_returns = {}
            for pos_id, position in self.positions.items():
                symbol = position.symbol
                if symbol in self.historical_returns and len(self.historical_returns[symbol]) > 20:
                    position_returns[symbol] = self.historical_returns[symbol][-60:]  # Last 60 days
            
            if len(position_returns) < 2:
                return CorrelationAnalysis({}, 0, 0, 0, [], 1.0, datetime.now())
            
            # Calculate correlation matrix
            symbols = list(position_returns.keys())
            correlation_matrix = {}
            correlations = []
            highly_correlated_pairs = []
            
            for i, symbol1 in enumerate(symbols):
                correlation_matrix[symbol1] = {}
                for j, symbol2 in enumerate(symbols):
                    if i == j:
                        corr = 1.0
                    else:
                        returns1 = np.array(position_returns[symbol1])
                        returns2 = np.array(position_returns[symbol2])
                        
                        # Ensure same length
                        min_len = min(len(returns1), len(returns2))
                        returns1 = returns1[-min_len:]
                        returns2 = returns2[-min_len:]
                        
                        corr = np.corrcoef(returns1, returns2)[0, 1]
                        if np.isnan(corr):
                            corr = 0.0
                    
                    correlation_matrix[symbol1][symbol2] = corr
                    
                    if i < j:  # Avoid duplicates
                        correlations.append(corr)
                        if abs(corr) > 0.7:  # High correlation threshold
                            highly_correlated_pairs.append((symbol1, symbol2, corr))
            
            # Calculate summary statistics
            avg_correlation = np.mean(correlations) if correlations else 0
            max_correlation = np.max(correlations) if correlations else 0
            min_correlation = np.min(correlations) if correlations else 0
            
            # Calculate diversification ratio (simplified)
            diversification_ratio = 1.0 - abs(avg_correlation)
            
            result = CorrelationAnalysis(
                correlation_matrix=correlation_matrix,
                avg_correlation=avg_correlation,
                max_correlation=max_correlation,
                min_correlation=min_correlation,
                highly_correlated_pairs=highly_correlated_pairs,
                diversification_ratio=diversification_ratio,
                timestamp=datetime.now()
            )
            
            self.current_correlation = result
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating correlation analysis: {e}")
            return CorrelationAnalysis({}, 0, 0, 0, [], 1.0, datetime.now())
    
    def run_stress_tests(self, scenarios: Optional[Dict[str, float]] = None) -> List[StressTestResult]:
        """
        Run stress test scenarios on the portfolio
        
        Args:
            scenarios: Custom stress scenarios {name: market_shock_percent}
            
        Returns:
            List of stress test results
        """
        try:
            if not scenarios:
                scenarios = {
                    "Market Crash -10%": -0.10,
                    "Market Crash -20%": -0.20,
                    "Volatility Spike +50%": 0.05,  # Assume 5% impact from vol spike
                    "Sector Rotation": -0.05,
                    "Black Swan Event": -0.30
                }
            
            results = []
            
            for scenario_name, market_shock in scenarios.items():
                try:
                    result = self._calculate_stress_scenario(scenario_name, market_shock)
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Error in stress scenario {scenario_name}: {e}")
                    continue
            
            self.stress_test_results = results
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error running stress tests: {e}")
            return []
    
    def _calculate_stress_scenario(self, scenario_name: str, market_shock: float) -> StressTestResult:
        """Calculate impact of a stress scenario"""
        try:
            total_impact = 0.0
            positions_affected = 0
            worst_position = ""
            worst_position_impact = 0.0
            
            for pos_id, position in self.positions.items():
                # Simplified impact calculation - would use beta, correlation, etc.
                position_value = position.quantity * position.current_price
                
                # Apply market shock with some position-specific adjustment
                position_beta = self._get_position_beta(position.symbol)
                position_impact = position_value * market_shock * position_beta
                
                total_impact += position_impact
                positions_affected += 1
                
                if abs(position_impact) > abs(worst_position_impact):
                    worst_position = position.symbol
                    worst_position_impact = position_impact
            
            # Calculate portfolio impact percentage
            portfolio_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            impact_percent = (total_impact / portfolio_value) if portfolio_value > 0 else 0
            
            return StressTestResult(
                scenario_name=scenario_name,
                market_shock=market_shock,
                portfolio_impact=total_impact,
                impact_percent=impact_percent,
                positions_affected=positions_affected,
                worst_position=worst_position,
                worst_position_impact=worst_position_impact,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating stress scenario: {e}")
            return StressTestResult(scenario_name, market_shock, 0, 0, 0, "", 0, datetime.now())
    
    def _get_position_beta(self, symbol: str) -> float:
        """Get beta for a position (simplified)"""
        try:
            # Simplified beta mapping - would use real beta calculations
            beta_map = {
                'NIFTY': 1.0,
                'BANKNIFTY': 1.2,
                'RELIANCE': 0.8,
                'TCS': 0.7,
                'HDFC': 1.1,
                'ICICIBANK': 1.3,
                'INFY': 0.9
            }
            
            return beta_map.get(symbol, 1.0)  # Default beta of 1.0
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting beta for {symbol}: {e}")
            return 1.0
    
    def _calculate_portfolio_metrics(self):
        """Calculate current portfolio metrics"""
        try:
            if not self.positions:
                return
            
            # Calculate current portfolio value
            current_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            
            # Update max portfolio value
            self.max_portfolio_value = max(self.max_portfolio_value, current_value)
            
            # Calculate drawdown
            if self.max_portfolio_value > 0:
                self.current_drawdown = (self.max_portfolio_value - current_value) / self.max_portfolio_value
                self.max_drawdown = max(self.max_drawdown, self.current_drawdown)
            
            # Calculate total P&L
            total_cost = sum(pos.quantity * pos.entry_price for pos in self.positions.values())
            self.total_pnl = current_value - total_cost
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating portfolio metrics: {e}")
    
    def _get_portfolio_returns(self) -> List[float]:
        """Get historical portfolio returns"""
        try:
            # Simplified - would calculate actual portfolio returns from historical data
            if not self.portfolio_history:
                return []
            
            returns = []
            for i in range(1, len(self.portfolio_history)):
                prev_value = self.portfolio_history[i-1].total_value
                curr_value = self.portfolio_history[i].total_value
                
                if prev_value > 0:
                    daily_return = (curr_value - prev_value) / prev_value
                    returns.append(daily_return)
            
            return returns[-self.var_lookback_days:]  # Last N days
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting portfolio returns: {e}")
            return []
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        try:
            portfolio_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            
            return {
                'portfolio_value': portfolio_value,
                'total_pnl': self.total_pnl,
                'daily_pnl': self.daily_pnl,
                'current_drawdown': self.current_drawdown,
                'max_drawdown': self.max_drawdown,
                'position_count': len(self.positions),
                'positions': {
                    pos_id: {
                        'symbol': pos.symbol,
                        'quantity': pos.quantity,
                        'entry_price': pos.entry_price,
                        'current_price': pos.current_price,
                        'pnl': (pos.current_price - pos.entry_price) * pos.quantity,
                        'pnl_percent': ((pos.current_price - pos.entry_price) / pos.entry_price) * 100
                    }
                    for pos_id, pos in self.positions.items()
                },
                'risk_metrics': {
                    'var_1d': self.current_var.var_1d if self.current_var else 0,
                    'var_5d': self.current_var.var_5d if self.current_var else 0,
                    'avg_correlation': self.current_correlation.avg_correlation if self.current_correlation else 0,
                    'diversification_ratio': self.current_correlation.diversification_ratio if self.current_correlation else 1.0
                },
                'stress_tests': [
                    {
                        'scenario': result.scenario_name,
                        'impact_percent': result.impact_percent * 100,
                        'portfolio_impact': result.portfolio_impact
                    }
                    for result in self.stress_test_results[-5:]  # Last 5 stress tests
                ]
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting portfolio summary: {e}")
            return {}
