#!/usr/bin/env python3
"""
Live Trading Adapter
Integration with live trading agent for signal delivery and execution feedback
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from ..core.data_models import TradingSignal

logger = logging.getLogger(__name__)


@dataclass
class LiveTradingAdapterConfig:
    """Configuration for Live Trading Adapter"""
    
    # Trading settings
    live_trading_agent_endpoint: str = "live_trading_agent"
    enable_signal_delivery: bool = True
    signal_delivery_timeout_seconds: float = 10.0
    
    # Signal filtering
    min_confidence_for_live_trading: float = 0.7
    enable_risk_checks: bool = True
    max_signals_per_minute: int = 5
    
    # Feedback collection
    enable_execution_feedback: bool = True
    feedback_timeout_seconds: float = 30.0


class LiveTradingAdapter:
    """
    Live Trading Adapter for signal delivery and execution feedback
    """
    
    def __init__(self, config: LiveTradingAdapterConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Signal tracking
        self.delivered_signals = {}
        self.execution_feedback = {}
        
        # Rate limiting
        self.signal_delivery_times = []
        
        # Live trading agent connection
        self.live_trading_agent = None
        
    async def initialize(self) -> bool:
        """Initialize the live trading adapter"""
        try:
            self.logger.info("Initializing Live Trading Adapter...")
            
            # Initialize connection to live trading agent
            await self._initialize_live_trading_agent_connection()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Live Trading Adapter: {e}")
            return False
    
    async def _initialize_live_trading_agent_connection(self):
        """Initialize connection to live trading agent"""
        try:
            # In a real implementation, this would establish connection
            # to the live trading agent via event bus or direct API
            self.logger.info("Live trading agent connection initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing live trading agent connection: {e}")
    
    async def deliver_signal(self, signal: TradingSignal) -> bool:
        """
        Deliver trading signal to live trading agent
        """
        try:
            if not self.config.enable_signal_delivery:
                self.logger.info("Signal delivery is disabled")
                return False
            
            # Pre-delivery validation
            if not self._validate_signal_for_live_trading(signal):
                return False
            
            # Rate limiting check
            if not self._check_rate_limits():
                self.logger.warning("Rate limit exceeded for signal delivery")
                return False
            
            # Prepare signal for delivery
            signal_data = self._prepare_signal_data(signal)
            
            # Deliver to live trading agent
            delivery_success = await self._send_signal_to_live_trading_agent(signal_data)
            
            if delivery_success:
                # Track delivered signal
                self.delivered_signals[signal.signal_id] = {
                    'signal': signal,
                    'delivery_time': datetime.now(),
                    'status': 'delivered'
                }
                
                # Update rate limiting
                self.signal_delivery_times.append(datetime.now())
                
                self.logger.info(f"Signal delivered successfully: {signal.signal_id}")
                return True
            else:
                self.logger.error(f"Failed to deliver signal: {signal.signal_id}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error delivering signal: {e}")
            return False
    
    def _validate_signal_for_live_trading(self, signal: TradingSignal) -> bool:
        """Validate signal for live trading"""
        try:
            # Confidence check
            if signal.confidence < self.config.min_confidence_for_live_trading:
                self.logger.info(f"Signal confidence {signal.confidence} below threshold {self.config.min_confidence_for_live_trading}")
                return False
            
            # Risk checks
            if self.config.enable_risk_checks:
                if not signal.risk_check:
                    self.logger.info("Signal failed risk check")
                    return False
                
                if not signal.liquidity_check:
                    self.logger.info("Signal failed liquidity check")
                    return False
            
            # Time filter check
            if not signal.time_filter_check:
                self.logger.info("Signal failed time filter check")
                return False
            
            # Cooldown check
            if not signal.cooldown_check:
                self.logger.info("Signal failed cooldown check")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating signal for live trading: {e}")
            return False
    
    def _check_rate_limits(self) -> bool:
        """Check rate limiting for signal delivery"""
        try:
            current_time = datetime.now()
            
            # Clean old entries (older than 1 minute)
            self.signal_delivery_times = [
                t for t in self.signal_delivery_times
                if (current_time - t).total_seconds() <= 60
            ]
            
            # Check if under rate limit
            return len(self.signal_delivery_times) < self.config.max_signals_per_minute
            
        except Exception as e:
            self.logger.error(f"Error checking rate limits: {e}")
            return False
    
    def _prepare_signal_data(self, signal: TradingSignal) -> Dict[str, Any]:
        """Prepare signal data for delivery"""
        try:
            signal_data = {
                'signal_id': signal.signal_id,
                'symbol': signal.symbol,
                'strategy_name': signal.strategy_name,
                'signal_type': signal.signal_type.value if hasattr(signal.signal_type, 'value') else str(signal.signal_type),
                'action': signal.action.value if hasattr(signal.action, 'value') else str(signal.action),
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'quantity': signal.quantity,
                'confidence': signal.confidence,
                'risk_reward_ratio': signal.risk_reward_ratio,
                'capital_allocated': signal.capital_allocated,
                'risk_amount': signal.risk_amount,
                'timestamp': signal.timestamp.isoformat(),
                'context': signal.context,
                'expected_return': signal.expected_return,
                'expected_volatility': signal.expected_volatility,
                'sharpe_prediction': signal.sharpe_prediction
            }
            
            return signal_data
            
        except Exception as e:
            self.logger.error(f"Error preparing signal data: {e}")
            return {}
    
    async def _send_signal_to_live_trading_agent(self, signal_data: Dict[str, Any]) -> bool:
        """Send signal to live trading agent"""
        try:
            # In a real implementation, this would make API call to live trading agent
            # For now, simulate successful delivery
            
            self.logger.debug(f"Signal sent to live trading agent: {signal_data['signal_id']}")
            
            # Simulate some processing time
            await asyncio.sleep(0.1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending signal to live trading agent: {e}")
            return False
    
    async def get_execution_feedback(self, signal_id: str) -> Optional[Dict[str, Any]]:
        """
        Get execution feedback for a delivered signal
        """
        try:
            if not self.config.enable_execution_feedback:
                return None
            
            # Check if signal was delivered
            if signal_id not in self.delivered_signals:
                return None
            
            # Request feedback from live trading agent
            feedback = await self._request_execution_feedback(signal_id)
            
            if feedback:
                self.execution_feedback[signal_id] = feedback
                
                # Update signal status
                self.delivered_signals[signal_id]['status'] = feedback.get('execution_status', 'unknown')
            
            return feedback
            
        except Exception as e:
            self.logger.error(f"Error getting execution feedback: {e}")
            return None
    
    async def _request_execution_feedback(self, signal_id: str) -> Optional[Dict[str, Any]]:
        """Request execution feedback from live trading agent"""
        try:
            # In a real implementation, this would request feedback via API
            # For now, simulate feedback
            
            import random
            
            # Simulate different execution outcomes
            outcomes = ['executed', 'partially_filled', 'rejected', 'pending']
            execution_status = random.choice(outcomes)
            
            feedback = {
                'signal_id': signal_id,
                'execution_status': execution_status,
                'executed_quantity': random.randint(1, 100) if execution_status in ['executed', 'partially_filled'] else 0,
                'executed_price': 100.0 + random.uniform(-1.0, 1.0),
                'execution_time': datetime.now().isoformat(),
                'fees': random.uniform(0.1, 2.0),
                'slippage': random.uniform(0.0, 0.5),
                'rejection_reason': 'Insufficient margin' if execution_status == 'rejected' else None
            }
            
            return feedback
            
        except Exception as e:
            self.logger.error(f"Error requesting execution feedback: {e}")
            return None
    
    async def get_portfolio_status(self) -> Optional[Dict[str, Any]]:
        """
        Get current portfolio status from live trading agent
        """
        try:
            # In a real implementation, this would request portfolio status
            # For now, simulate portfolio data
            
            portfolio_status = {
                'total_capital': 100000.0,
                'available_capital': 85000.0,
                'used_capital': 15000.0,
                'current_positions': 5,
                'daily_pnl': 250.0,
                'total_pnl': 1500.0,
                'risk_utilization': 0.15,
                'last_updated': datetime.now().isoformat()
            }
            
            return portfolio_status
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio status: {e}")
            return None
    
    def get_delivery_stats(self) -> Dict[str, Any]:
        """Get signal delivery statistics"""
        try:
            total_delivered = len(self.delivered_signals)
            
            # Count by status
            status_counts = {}
            for signal_info in self.delivered_signals.values():
                status = signal_info['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Recent deliveries
            recent_deliveries = [
                {
                    'signal_id': signal_id,
                    'symbol': info['signal'].symbol,
                    'strategy': info['signal'].strategy_name,
                    'delivery_time': info['delivery_time'].isoformat(),
                    'status': info['status']
                }
                for signal_id, info in list(self.delivered_signals.items())[-10:]
            ]
            
            return {
                'total_signals_delivered': total_delivered,
                'signals_with_feedback': len(self.execution_feedback),
                'status_breakdown': status_counts,
                'recent_deliveries': recent_deliveries,
                'current_rate_limit_usage': len(self.signal_delivery_times)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting delivery stats: {e}")
            return {}
    
    async def cleanup(self):
        """Cleanup adapter resources"""
        try:
            self.delivered_signals.clear()
            self.execution_feedback.clear()
            self.signal_delivery_times.clear()
            self.logger.info("Live Trading Adapter cleaned up")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up Live Trading Adapter: {e}")
