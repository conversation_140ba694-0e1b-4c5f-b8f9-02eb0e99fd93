#!/usr/bin/env python3
"""
Market Data Adapter
Seamless integration with market monitoring agent for real-time data
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from ..core.data_models import SignalInput, OHLCVData, MarketIndicators

logger = logging.getLogger(__name__)


@dataclass
class MarketDataAdapterConfig:
    """Configuration for Market Data Adapter"""
    
    # Data source settings
    primary_source: str = "market_monitoring_agent"
    fallback_source: str = "historical_data"
    data_refresh_interval_seconds: float = 1.0
    
    # Data validation
    enable_data_validation: bool = True
    max_data_age_seconds: int = 30
    min_data_points: int = 50
    
    # Indicator requirements
    required_indicators: List[str] = None
    
    def __post_init__(self):
        if self.required_indicators is None:
            self.required_indicators = [
                "ema_5", "ema_20", "rsi_14", "macd", "macd_signal",
                "bb_upper", "bb_lower", "bb_middle", "atr"
            ]


class MarketDataAdapter:
    """
    Market Data Adapter for seamless integration with market monitoring agent
    """
    
    def __init__(self, config: MarketDataAdapterConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Data cache
        self.data_cache = {}
        self.last_update = {}
        
        # Market monitoring agent connection
        self.market_agent = None
        
    async def initialize(self) -> bool:
        """Initialize the market data adapter"""
        try:
            self.logger.info("Initializing Market Data Adapter...")
            
            # Initialize connection to market monitoring agent
            await self._initialize_market_agent_connection()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Market Data Adapter: {e}")
            return False
    
    async def _initialize_market_agent_connection(self):
        """Initialize connection to market monitoring agent"""
        try:
            # In a real implementation, this would establish connection
            # to the market monitoring agent via event bus or direct API
            self.logger.info("Market monitoring agent connection initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing market agent connection: {e}")
    
    async def get_signal_input(self, symbol: str, timeframe: str = "5min") -> Optional[SignalInput]:
        """
        Get signal input data for a symbol
        """
        try:
            # Get OHLCV data
            ohlcv_data = await self._get_ohlcv_data(symbol, timeframe)
            if not ohlcv_data:
                return None
            
            # Get indicators
            indicators = await self._get_indicators(symbol, timeframe)
            if not indicators:
                return None
            
            # Get market regime (simplified)
            market_regime = await self._get_market_regime(symbol)
            
            # Create signal input
            signal_input = SignalInput(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                ohlcv_data=ohlcv_data,
                indicators=indicators,
                market_regime=market_regime
            )
            
            return signal_input
            
        except Exception as e:
            self.logger.error(f"Error getting signal input for {symbol}: {e}")
            return None
    
    async def _get_ohlcv_data(self, symbol: str, timeframe: str) -> List[OHLCVData]:
        """Get OHLCV data from market monitoring agent"""
        try:
            # In a real implementation, this would fetch from market monitoring agent
            # For now, simulate OHLCV data
            
            ohlcv_data = []
            base_price = 100.0
            
            for i in range(100):  # 100 data points
                timestamp = datetime.now()
                open_price = base_price + (i * 0.1)
                high_price = open_price + 0.5
                low_price = open_price - 0.3
                close_price = open_price + 0.2
                volume = 10000 + (i * 100)
                
                ohlcv_data.append(OHLCVData(
                    timestamp=timestamp,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume
                ))
            
            return ohlcv_data
            
        except Exception as e:
            self.logger.error(f"Error getting OHLCV data: {e}")
            return []
    
    async def _get_indicators(self, symbol: str, timeframe: str) -> Optional[MarketIndicators]:
        """Get technical indicators from market monitoring agent"""
        try:
            # In a real implementation, this would fetch from market monitoring agent
            # For now, simulate indicators
            
            indicators = MarketIndicators(
                symbol=symbol,
                timestamp=datetime.now(),
                ema_5=100.5,
                ema_20=99.8,
                rsi_14=55.2,
                macd=0.15,
                macd_signal=0.12,
                bb_upper=102.0,
                bb_lower=98.0,
                bb_middle=100.0,
                atr=1.2
            )
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error getting indicators: {e}")
            return None
    
    async def _get_market_regime(self, symbol: str) -> str:
        """Get market regime from market monitoring agent"""
        try:
            # In a real implementation, this would fetch from market monitoring agent
            # For now, return default regime
            return "bull"
            
        except Exception as e:
            self.logger.error(f"Error getting market regime: {e}")
            return "sideways"
    
    def validate_data(self, signal_input: SignalInput) -> bool:
        """Validate signal input data quality"""
        try:
            if not self.config.enable_data_validation:
                return True
            
            # Check data age
            data_age = (datetime.now() - signal_input.timestamp).total_seconds()
            if data_age > self.config.max_data_age_seconds:
                self.logger.warning(f"Data too old: {data_age} seconds")
                return False
            
            # Check minimum data points
            if len(signal_input.ohlcv_data) < self.config.min_data_points:
                self.logger.warning(f"Insufficient data points: {len(signal_input.ohlcv_data)}")
                return False
            
            # Check required indicators
            for indicator in self.config.required_indicators:
                if not hasattr(signal_input.indicators, indicator):
                    self.logger.warning(f"Missing required indicator: {indicator}")
                    return False
                
                value = getattr(signal_input.indicators, indicator)
                if value is None:
                    self.logger.warning(f"Null value for indicator: {indicator}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating data: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup adapter resources"""
        try:
            self.data_cache.clear()
            self.last_update.clear()
            self.logger.info("Market Data Adapter cleaned up")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up Market Data Adapter: {e}")
