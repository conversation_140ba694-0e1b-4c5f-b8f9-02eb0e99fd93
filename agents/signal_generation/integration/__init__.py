#!/usr/bin/env python3
"""
Signal Generation Integration Module

Handles integration with other agents and external systems:
- Performance Analysis Gateway integration
- Risk Management Agent communication
- Strategy Evolution Agent feedback
- AI Training Agent model updates
- Market data provider connections
"""

from .performance_feedback_handler import (
    PerformanceFeedbackHandler,
    PerformanceAlert,
    AlertSeverity,
    ResponseAction,
    ResponseConfig,
    StrategyAdjustment
)

__all__ = [
    'PerformanceFeedbackHandler',
    'PerformanceAlert',
    'AlertSeverity',
    'ResponseAction',
    'ResponseConfig',
    'StrategyAdjustment'
]
