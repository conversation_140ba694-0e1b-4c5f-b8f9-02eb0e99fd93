#!/usr/bin/env python3
"""
Integration Manager
Unified manager for all external agent integrations
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from .market_data_adapter import MarketDataAdapter, MarketDataAdapterConfig
from .backtesting_adapter import BacktestingAdapter, BacktestingAdapterConfig
from .evolution_adapter import EvolutionAdapter, EvolutionAdapterConfig
from .live_trading_adapter import LiveTradingAdapter, LiveTradingAdapterConfig

from ..core.data_models import TradingSignal, SignalInput

logger = logging.getLogger(__name__)


@dataclass
class IntegrationManagerConfig:
    """Configuration for Integration Manager"""
    
    # Adapter configurations
    market_data_config: MarketDataAdapterConfig = None
    backtesting_config: BacktestingAdapterConfig = None
    evolution_config: EvolutionAdapterConfig = None
    live_trading_config: LiveTradingAdapterConfig = None
    
    # Integration settings
    enable_market_data_integration: bool = True
    enable_backtesting_integration: bool = True
    enable_evolution_integration: bool = True
    enable_live_trading_integration: bool = True
    
    # Health monitoring
    health_check_interval_seconds: int = 60
    max_adapter_failures: int = 3
    
    def __post_init__(self):
        if self.market_data_config is None:
            self.market_data_config = MarketDataAdapterConfig()
        if self.backtesting_config is None:
            self.backtesting_config = BacktestingAdapterConfig()
        if self.evolution_config is None:
            self.evolution_config = EvolutionAdapterConfig()
        if self.live_trading_config is None:
            self.live_trading_config = LiveTradingAdapterConfig()


class IntegrationManager:
    """
    Integration Manager for coordinating all external agent integrations
    """
    
    def __init__(self, config: IntegrationManagerConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize adapters
        self.market_data_adapter = None
        self.backtesting_adapter = None
        self.evolution_adapter = None
        self.live_trading_adapter = None
        
        # Health monitoring
        self.adapter_health = {}
        self.adapter_failures = {}
        
        # Integration statistics
        self.integration_stats = {
            'market_data_requests': 0,
            'backtesting_requests': 0,
            'evolution_submissions': 0,
            'live_trading_deliveries': 0,
            'total_errors': 0
        }
        
    async def initialize(self) -> bool:
        """Initialize all adapters"""
        try:
            self.logger.info("Initializing Integration Manager...")
            
            # Initialize market data adapter
            if self.config.enable_market_data_integration:
                self.market_data_adapter = MarketDataAdapter(self.config.market_data_config)
                if not await self.market_data_adapter.initialize():
                    self.logger.error("Failed to initialize Market Data Adapter")
                    return False
                self.adapter_health['market_data'] = True
                self.logger.info("Market Data Adapter initialized")
            
            # Initialize backtesting adapter
            if self.config.enable_backtesting_integration:
                self.backtesting_adapter = BacktestingAdapter(self.config.backtesting_config)
                if not await self.backtesting_adapter.initialize():
                    self.logger.error("Failed to initialize Backtesting Adapter")
                    return False
                self.adapter_health['backtesting'] = True
                self.logger.info("Backtesting Adapter initialized")
            
            # Initialize evolution adapter
            if self.config.enable_evolution_integration:
                self.evolution_adapter = EvolutionAdapter(self.config.evolution_config)
                if not await self.evolution_adapter.initialize():
                    self.logger.error("Failed to initialize Evolution Adapter")
                    return False
                self.adapter_health['evolution'] = True
                self.logger.info("Evolution Adapter initialized")
            
            # Initialize live trading adapter
            if self.config.enable_live_trading_integration:
                self.live_trading_adapter = LiveTradingAdapter(self.config.live_trading_config)
                if not await self.live_trading_adapter.initialize():
                    self.logger.error("Failed to initialize Live Trading Adapter")
                    return False
                self.adapter_health['live_trading'] = True
                self.logger.info("Live Trading Adapter initialized")
            
            self.logger.info("Integration Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing Integration Manager: {e}")
            return False
    
    async def get_market_data(self, symbol: str, timeframe: str = "5min") -> Optional[SignalInput]:
        """Get market data through market data adapter"""
        try:
            if not self.market_data_adapter:
                return None
            
            self.integration_stats['market_data_requests'] += 1
            
            signal_input = await self.market_data_adapter.get_signal_input(symbol, timeframe)
            
            if signal_input and self.market_data_adapter.validate_data(signal_input):
                return signal_input
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            self._record_adapter_failure('market_data')
            self.integration_stats['total_errors'] += 1
            return None
    
    async def get_strategy_performance(self, strategy_name: str, symbol: str = "ALL") -> Optional[Dict[str, Any]]:
        """Get strategy performance through backtesting adapter"""
        try:
            if not self.backtesting_adapter:
                return None
            
            self.integration_stats['backtesting_requests'] += 1
            
            performance_metrics = await self.backtesting_adapter.get_strategy_performance(
                strategy_name, symbol
            )
            
            if performance_metrics:
                return {
                    'strategy_name': performance_metrics.strategy_name,
                    'win_rate': performance_metrics.win_rate,
                    'total_return': performance_metrics.total_return,
                    'sharpe_ratio': performance_metrics.sharpe_ratio,
                    'max_drawdown': performance_metrics.max_drawdown,
                    'performance_score': performance_metrics.performance_score
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting strategy performance: {e}")
            self._record_adapter_failure('backtesting')
            self.integration_stats['total_errors'] += 1
            return None
    
    async def submit_strategy_for_evolution(self, strategy_config: Dict[str, Any], 
                                          performance_metrics: Dict[str, Any]) -> Optional[str]:
        """Submit strategy for evolution through evolution adapter"""
        try:
            if not self.evolution_adapter:
                return None
            
            self.integration_stats['evolution_submissions'] += 1
            
            evolution_id = await self.evolution_adapter.submit_strategy_for_evolution(
                strategy_config, performance_metrics
            )
            
            return evolution_id
            
        except Exception as e:
            self.logger.error(f"Error submitting strategy for evolution: {e}")
            self._record_adapter_failure('evolution')
            self.integration_stats['total_errors'] += 1
            return None
    
    async def deliver_signal_to_live_trading(self, signal: TradingSignal) -> bool:
        """Deliver signal to live trading through live trading adapter"""
        try:
            if not self.live_trading_adapter:
                return False
            
            self.integration_stats['live_trading_deliveries'] += 1
            
            success = await self.live_trading_adapter.deliver_signal(signal)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error delivering signal to live trading: {e}")
            self._record_adapter_failure('live_trading')
            self.integration_stats['total_errors'] += 1
            return False
    
    async def get_execution_feedback(self, signal_id: str) -> Optional[Dict[str, Any]]:
        """Get execution feedback from live trading adapter"""
        try:
            if not self.live_trading_adapter:
                return None
            
            feedback = await self.live_trading_adapter.get_execution_feedback(signal_id)
            
            return feedback
            
        except Exception as e:
            self.logger.error(f"Error getting execution feedback: {e}")
            self.integration_stats['total_errors'] += 1
            return None
    
    async def get_evolved_strategies(self) -> List[Dict[str, Any]]:
        """Get evolved strategies from evolution adapter"""
        try:
            if not self.evolution_adapter:
                return []
            
            evolved_strategies = await self.evolution_adapter.get_evolved_strategies()
            
            return evolved_strategies
            
        except Exception as e:
            self.logger.error(f"Error getting evolved strategies: {e}")
            self.integration_stats['total_errors'] += 1
            return []
    
    def _record_adapter_failure(self, adapter_name: str):
        """Record adapter failure for health monitoring"""
        try:
            if adapter_name not in self.adapter_failures:
                self.adapter_failures[adapter_name] = 0
            
            self.adapter_failures[adapter_name] += 1
            
            # Mark adapter as unhealthy if too many failures
            if self.adapter_failures[adapter_name] >= self.config.max_adapter_failures:
                self.adapter_health[adapter_name] = False
                self.logger.warning(f"Adapter {adapter_name} marked as unhealthy due to failures")
            
        except Exception as e:
            self.logger.error(f"Error recording adapter failure: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all adapters"""
        try:
            health_status = {
                'overall_health': True,
                'adapter_health': dict(self.adapter_health),
                'adapter_failures': dict(self.adapter_failures),
                'integration_stats': dict(self.integration_stats),
                'timestamp': datetime.now().isoformat()
            }
            
            # Check if any adapter is unhealthy
            if False in self.adapter_health.values():
                health_status['overall_health'] = False
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"Error performing health check: {e}")
            return {'overall_health': False, 'error': str(e)}
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """Get integration statistics"""
        return {
            'integration_stats': dict(self.integration_stats),
            'adapter_health': dict(self.adapter_health),
            'adapter_failures': dict(self.adapter_failures),
            'enabled_integrations': {
                'market_data': self.config.enable_market_data_integration,
                'backtesting': self.config.enable_backtesting_integration,
                'evolution': self.config.enable_evolution_integration,
                'live_trading': self.config.enable_live_trading_integration
            }
        }
    
    async def cleanup(self):
        """Cleanup all adapters"""
        try:
            self.logger.info("Cleaning up Integration Manager...")
            
            # Cleanup adapters
            if self.market_data_adapter:
                await self.market_data_adapter.cleanup()
            
            if self.backtesting_adapter:
                await self.backtesting_adapter.cleanup()
            
            if self.evolution_adapter:
                await self.evolution_adapter.cleanup()
            
            if self.live_trading_adapter:
                await self.live_trading_adapter.cleanup()
            
            # Clear tracking data
            self.adapter_health.clear()
            self.adapter_failures.clear()
            self.integration_stats = {
                'market_data_requests': 0,
                'backtesting_requests': 0,
                'evolution_submissions': 0,
                'live_trading_deliveries': 0,
                'total_errors': 0
            }
            
            self.logger.info("Integration Manager cleaned up")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up Integration Manager: {e}")
