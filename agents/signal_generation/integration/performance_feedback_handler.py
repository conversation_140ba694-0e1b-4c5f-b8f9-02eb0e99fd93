#!/usr/bin/env python3
"""
Performance Feedback Handler for Signal Generation Agent

Handles performance anomaly alerts and predictive maintenance warnings from the
Performance Analysis Gateway. Implements actionable responses like parameter
adjustments, signal generation pausing, and strategy modifications.

Features:
- Real-time performance anomaly detection
- Predictive maintenance warnings
- Dynamic parameter adjustment
- Strategy performance monitoring
- Automatic signal generation control
- Risk-based response mechanisms
"""

import asyncio
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class ResponseAction(Enum):
    """Response action types"""
    REDUCE_POSITION_SIZE = "reduce_position_size"
    TIGHTEN_STOP_LOSS = "tighten_stop_loss"
    PAUSE_STRATEGY = "pause_strategy"
    PAUSE_SYMBOL = "pause_symbol"
    SWITCH_TO_CONSERVATIVE = "switch_to_conservative"
    INCREASE_CONFIDENCE_THRESHOLD = "increase_confidence_threshold"
    REDUCE_RISK_REWARD = "reduce_risk_reward"
    TEMPORARY_SHUTDOWN = "temporary_shutdown"

@dataclass
class PerformanceAlert:
    """Performance alert from Performance Analysis Gateway"""
    alert_id: str
    alert_type: str  # 'anomaly', 'predictive_maintenance', 'threshold_breach'
    severity: AlertSeverity
    strategy: Optional[str]
    symbol: Optional[str]
    metric_name: str
    current_value: float
    threshold_value: float
    message: str
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResponseConfig:
    """Configuration for response actions"""
    action: ResponseAction
    parameters: Dict[str, Any]
    duration_minutes: Optional[int] = None
    conditions: Dict[str, Any] = field(default_factory=dict)

@dataclass
class StrategyAdjustment:
    """Strategy adjustment record"""
    strategy_name: str
    symbol: Optional[str]
    adjustment_type: str
    original_value: Any
    adjusted_value: Any
    timestamp: datetime
    reason: str
    duration_minutes: Optional[int] = None
    is_active: bool = True

class PerformanceFeedbackHandler:
    """
    Handles performance feedback and implements responsive actions
    """
    
    def __init__(self, signal_agent):
        self.signal_agent = signal_agent
        
        # Alert handling
        self.active_alerts = {}
        self.alert_history = []
        self.response_configs = {}
        
        # Strategy adjustments
        self.active_adjustments = {}
        self.adjustment_history = []
        
        # Performance tracking
        self.metrics = {
            'alerts_received': 0,
            'responses_executed': 0,
            'strategies_paused': 0,
            'adjustments_made': 0,
            'last_alert_time': None
        }
        
        # Initialize response configurations
        self._initialize_response_configs()
        
        logger.info("PerformanceFeedbackHandler initialized")

    def _initialize_response_configs(self):
        """Initialize default response configurations"""
        self.response_configs = {
            # Anomaly responses
            'low_win_rate': ResponseConfig(
                action=ResponseAction.INCREASE_CONFIDENCE_THRESHOLD,
                parameters={'threshold_increase': 0.1, 'max_threshold': 0.9},
                duration_minutes=60
            ),
            'high_drawdown': ResponseConfig(
                action=ResponseAction.REDUCE_POSITION_SIZE,
                parameters={'size_reduction_factor': 0.5, 'min_size_factor': 0.1},
                duration_minutes=120
            ),
            'negative_sharpe': ResponseConfig(
                action=ResponseAction.PAUSE_STRATEGY,
                parameters={},
                duration_minutes=240
            ),
            'excessive_losses': ResponseConfig(
                action=ResponseAction.TIGHTEN_STOP_LOSS,
                parameters={'stop_loss_reduction_factor': 0.8, 'min_stop_loss': 0.5},
                duration_minutes=180
            ),
            'volatility_spike': ResponseConfig(
                action=ResponseAction.SWITCH_TO_CONSERVATIVE,
                parameters={'conservative_mode': True},
                duration_minutes=60
            ),
            
            # Predictive maintenance responses
            'predicted_poor_performance': ResponseConfig(
                action=ResponseAction.REDUCE_POSITION_SIZE,
                parameters={'size_reduction_factor': 0.7},
                duration_minutes=480
            ),
            'strategy_degradation_warning': ResponseConfig(
                action=ResponseAction.INCREASE_CONFIDENCE_THRESHOLD,
                parameters={'threshold_increase': 0.15},
                duration_minutes=360
            ),
            'market_regime_mismatch': ResponseConfig(
                action=ResponseAction.PAUSE_STRATEGY,
                parameters={},
                duration_minutes=120
            )
        }

    async def handle_performance_alert(self, alert: PerformanceAlert) -> bool:
        """
        Handle incoming performance alert
        
        Args:
            alert: Performance alert from Performance Analysis Gateway
            
        Returns:
            bool: True if alert was handled successfully
        """
        try:
            logger.info(f"Received performance alert: {alert.alert_type} - {alert.severity.value}")
            
            # Store alert
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            self.metrics['alerts_received'] += 1
            self.metrics['last_alert_time'] = datetime.now()
            
            # Determine response based on alert type and severity
            response_config = self._get_response_config(alert)
            
            if response_config:
                # Execute response
                success = await self._execute_response(alert, response_config)
                
                if success:
                    self.metrics['responses_executed'] += 1
                    logger.info(f"Successfully executed response for alert {alert.alert_id}")
                else:
                    logger.error(f"Failed to execute response for alert {alert.alert_id}")
                
                return success
            else:
                logger.warning(f"No response configuration found for alert type: {alert.alert_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling performance alert: {e}")
            return False

    def _get_response_config(self, alert: PerformanceAlert) -> Optional[ResponseConfig]:
        """Get appropriate response configuration for alert"""
        
        # Map alert types to response configs
        alert_type_mapping = {
            'low_win_rate_anomaly': 'low_win_rate',
            'high_drawdown_anomaly': 'high_drawdown',
            'negative_sharpe_anomaly': 'negative_sharpe',
            'excessive_losses_anomaly': 'excessive_losses',
            'volatility_spike_anomaly': 'volatility_spike',
            'predicted_poor_performance': 'predicted_poor_performance',
            'strategy_degradation_warning': 'strategy_degradation_warning',
            'market_regime_mismatch': 'market_regime_mismatch'
        }
        
        config_key = alert_type_mapping.get(alert.alert_type)
        if config_key:
            base_config = self.response_configs.get(config_key)
            
            if base_config:
                # Adjust response based on severity
                adjusted_config = self._adjust_response_for_severity(base_config, alert.severity)
                return adjusted_config
        
        return None

    def _adjust_response_for_severity(self, config: ResponseConfig, severity: AlertSeverity) -> ResponseConfig:
        """Adjust response configuration based on alert severity"""
        
        # Create a copy of the config
        adjusted_config = ResponseConfig(
            action=config.action,
            parameters=config.parameters.copy(),
            duration_minutes=config.duration_minutes,
            conditions=config.conditions.copy()
        )
        
        # Adjust parameters based on severity
        if severity == AlertSeverity.CRITICAL:
            # More aggressive response for critical alerts
            if 'size_reduction_factor' in adjusted_config.parameters:
                adjusted_config.parameters['size_reduction_factor'] *= 0.7
            if 'threshold_increase' in adjusted_config.parameters:
                adjusted_config.parameters['threshold_increase'] *= 1.5
            if adjusted_config.duration_minutes:
                adjusted_config.duration_minutes = int(adjusted_config.duration_minutes * 1.5)
                
        elif severity == AlertSeverity.EMERGENCY:
            # Most aggressive response for emergency alerts
            adjusted_config.action = ResponseAction.TEMPORARY_SHUTDOWN
            adjusted_config.parameters = {'shutdown_duration_minutes': 60}
            adjusted_config.duration_minutes = 60
            
        elif severity == AlertSeverity.INFO:
            # Less aggressive response for info alerts
            if 'size_reduction_factor' in adjusted_config.parameters:
                adjusted_config.parameters['size_reduction_factor'] = min(0.9, adjusted_config.parameters['size_reduction_factor'] * 1.2)
            if 'threshold_increase' in adjusted_config.parameters:
                adjusted_config.parameters['threshold_increase'] *= 0.7
            if adjusted_config.duration_minutes:
                adjusted_config.duration_minutes = int(adjusted_config.duration_minutes * 0.7)
        
        return adjusted_config

    async def _execute_response(self, alert: PerformanceAlert, config: ResponseConfig) -> bool:
        """Execute response action"""
        try:
            action = config.action
            parameters = config.parameters
            
            if action == ResponseAction.REDUCE_POSITION_SIZE:
                return await self._reduce_position_size(alert, parameters)
                
            elif action == ResponseAction.TIGHTEN_STOP_LOSS:
                return await self._tighten_stop_loss(alert, parameters)
                
            elif action == ResponseAction.PAUSE_STRATEGY:
                return await self._pause_strategy(alert, config.duration_minutes)
                
            elif action == ResponseAction.PAUSE_SYMBOL:
                return await self._pause_symbol(alert, config.duration_minutes)
                
            elif action == ResponseAction.SWITCH_TO_CONSERVATIVE:
                return await self._switch_to_conservative_mode(alert, parameters)
                
            elif action == ResponseAction.INCREASE_CONFIDENCE_THRESHOLD:
                return await self._increase_confidence_threshold(alert, parameters)
                
            elif action == ResponseAction.REDUCE_RISK_REWARD:
                return await self._reduce_risk_reward(alert, parameters)
                
            elif action == ResponseAction.TEMPORARY_SHUTDOWN:
                return await self._temporary_shutdown(alert, parameters)
            
            else:
                logger.error(f"Unknown response action: {action}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing response action {config.action}: {e}")
            return False

    async def _reduce_position_size(self, alert: PerformanceAlert, parameters: Dict[str, Any]) -> bool:
        """Reduce position size for affected strategy/symbol"""
        try:
            reduction_factor = parameters.get('size_reduction_factor', 0.5)
            min_size_factor = parameters.get('min_size_factor', 0.1)
            
            # Apply to specific strategy or all strategies
            if alert.strategy:
                strategies = [alert.strategy]
            else:
                strategies = list(self.signal_agent.strategies.keys())
            
            for strategy_name in strategies:
                if strategy_name in self.signal_agent.strategies:
                    strategy = self.signal_agent.strategies[strategy_name]
                    
                    # Get current position size configuration
                    current_size = strategy.get('position_sizing', {}).get('base_size_percent', 2.0)
                    new_size = max(current_size * reduction_factor, min_size_factor)
                    
                    # Create adjustment record
                    adjustment = StrategyAdjustment(
                        strategy_name=strategy_name,
                        symbol=alert.symbol,
                        adjustment_type='position_size_reduction',
                        original_value=current_size,
                        adjusted_value=new_size,
                        timestamp=datetime.now(),
                        reason=f"Performance alert: {alert.alert_type}",
                        duration_minutes=parameters.get('duration_minutes')
                    )
                    
                    # Apply adjustment
                    strategy['position_sizing']['base_size_percent'] = new_size
                    self.active_adjustments[f"{strategy_name}_position_size"] = adjustment
                    self.adjustment_history.append(adjustment)
                    self.metrics['adjustments_made'] += 1
                    
                    logger.info(f"Reduced position size for {strategy_name}: {current_size}% -> {new_size}%")
            
            return True
            
        except Exception as e:
            logger.error(f"Error reducing position size: {e}")
            return False

    async def _tighten_stop_loss(self, alert: PerformanceAlert, parameters: Dict[str, Any]) -> bool:
        """Tighten stop loss for affected strategy/symbol"""
        try:
            reduction_factor = parameters.get('stop_loss_reduction_factor', 0.8)
            min_stop_loss = parameters.get('min_stop_loss', 0.5)
            
            # Apply to specific strategy or all strategies
            if alert.strategy:
                strategies = [alert.strategy]
            else:
                strategies = list(self.signal_agent.strategies.keys())
            
            for strategy_name in strategies:
                if strategy_name in self.signal_agent.strategies:
                    strategy = self.signal_agent.strategies[strategy_name]
                    
                    # Get current stop loss configuration
                    current_sl = strategy.get('risk_management', {}).get('stop_loss_percent', 2.0)
                    new_sl = max(current_sl * reduction_factor, min_stop_loss)
                    
                    # Create adjustment record
                    adjustment = StrategyAdjustment(
                        strategy_name=strategy_name,
                        symbol=alert.symbol,
                        adjustment_type='stop_loss_tightening',
                        original_value=current_sl,
                        adjusted_value=new_sl,
                        timestamp=datetime.now(),
                        reason=f"Performance alert: {alert.alert_type}",
                        duration_minutes=parameters.get('duration_minutes')
                    )
                    
                    # Apply adjustment
                    strategy['risk_management']['stop_loss_percent'] = new_sl
                    self.active_adjustments[f"{strategy_name}_stop_loss"] = adjustment
                    self.adjustment_history.append(adjustment)
                    self.metrics['adjustments_made'] += 1
                    
                    logger.info(f"Tightened stop loss for {strategy_name}: {current_sl}% -> {new_sl}%")
            
            return True
            
        except Exception as e:
            logger.error(f"Error tightening stop loss: {e}")
            return False

    async def _pause_strategy(self, alert: PerformanceAlert, duration_minutes: Optional[int]) -> bool:
        """Pause strategy for specified duration"""
        try:
            if not alert.strategy:
                logger.error("Cannot pause strategy: no strategy specified in alert")
                return False
            
            strategy_name = alert.strategy
            
            if strategy_name in self.signal_agent.strategies:
                # Create adjustment record
                adjustment = StrategyAdjustment(
                    strategy_name=strategy_name,
                    symbol=alert.symbol,
                    adjustment_type='strategy_pause',
                    original_value=True,  # Was active
                    adjusted_value=False,  # Now paused
                    timestamp=datetime.now(),
                    reason=f"Performance alert: {alert.alert_type}",
                    duration_minutes=duration_minutes
                )
                
                # Pause strategy
                self.signal_agent.strategies[strategy_name]['enabled'] = False
                self.active_adjustments[f"{strategy_name}_pause"] = adjustment
                self.adjustment_history.append(adjustment)
                self.metrics['strategies_paused'] += 1
                self.metrics['adjustments_made'] += 1
                
                logger.info(f"Paused strategy {strategy_name} for {duration_minutes} minutes")
                
                # Schedule reactivation if duration is specified
                if duration_minutes:
                    asyncio.create_task(self._schedule_strategy_reactivation(strategy_name, duration_minutes))
                
                return True
            else:
                logger.error(f"Strategy {strategy_name} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error pausing strategy: {e}")
            return False

    async def _pause_symbol(self, alert: PerformanceAlert, duration_minutes: Optional[int]) -> bool:
        """Pause signal generation for specific symbol"""
        try:
            if not alert.symbol:
                logger.error("Cannot pause symbol: no symbol specified in alert")
                return False
            
            symbol = alert.symbol
            
            # Add symbol to paused list (this would need to be implemented in the signal agent)
            if not hasattr(self.signal_agent, 'paused_symbols'):
                self.signal_agent.paused_symbols = set()
            
            self.signal_agent.paused_symbols.add(symbol)
            
            # Create adjustment record
            adjustment = StrategyAdjustment(
                strategy_name="ALL",
                symbol=symbol,
                adjustment_type='symbol_pause',
                original_value=True,  # Was active
                adjusted_value=False,  # Now paused
                timestamp=datetime.now(),
                reason=f"Performance alert: {alert.alert_type}",
                duration_minutes=duration_minutes
            )
            
            self.active_adjustments[f"{symbol}_pause"] = adjustment
            self.adjustment_history.append(adjustment)
            self.metrics['adjustments_made'] += 1
            
            logger.info(f"Paused symbol {symbol} for {duration_minutes} minutes")
            
            # Schedule reactivation if duration is specified
            if duration_minutes:
                asyncio.create_task(self._schedule_symbol_reactivation(symbol, duration_minutes))
            
            return True
            
        except Exception as e:
            logger.error(f"Error pausing symbol: {e}")
            return False

    async def _increase_confidence_threshold(self, alert: PerformanceAlert, parameters: Dict[str, Any]) -> bool:
        """Increase confidence threshold for signal generation"""
        try:
            threshold_increase = parameters.get('threshold_increase', 0.1)
            max_threshold = parameters.get('max_threshold', 0.9)
            
            # Apply to specific strategy or all strategies
            if alert.strategy:
                strategies = [alert.strategy]
            else:
                strategies = list(self.signal_agent.strategies.keys())
            
            for strategy_name in strategies:
                if strategy_name in self.signal_agent.strategies:
                    strategy = self.signal_agent.strategies[strategy_name]
                    
                    # Get current confidence threshold
                    current_threshold = strategy.get('signal_validation', {}).get('min_confidence', 0.6)
                    new_threshold = min(current_threshold + threshold_increase, max_threshold)
                    
                    # Create adjustment record
                    adjustment = StrategyAdjustment(
                        strategy_name=strategy_name,
                        symbol=alert.symbol,
                        adjustment_type='confidence_threshold_increase',
                        original_value=current_threshold,
                        adjusted_value=new_threshold,
                        timestamp=datetime.now(),
                        reason=f"Performance alert: {alert.alert_type}",
                        duration_minutes=parameters.get('duration_minutes')
                    )
                    
                    # Apply adjustment
                    if 'signal_validation' not in strategy:
                        strategy['signal_validation'] = {}
                    strategy['signal_validation']['min_confidence'] = new_threshold
                    
                    self.active_adjustments[f"{strategy_name}_confidence"] = adjustment
                    self.adjustment_history.append(adjustment)
                    self.metrics['adjustments_made'] += 1
                    
                    logger.info(f"Increased confidence threshold for {strategy_name}: {current_threshold} -> {new_threshold}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error increasing confidence threshold: {e}")
            return False

    async def _switch_to_conservative_mode(self, alert: PerformanceAlert, parameters: Dict[str, Any]) -> bool:
        """Switch to conservative trading mode"""
        try:
            # This would implement conservative mode settings
            # For now, we'll reduce position sizes and increase thresholds
            
            conservative_adjustments = [
                {'action': 'reduce_position_size', 'factor': 0.5},
                {'action': 'increase_confidence', 'increase': 0.2},
                {'action': 'tighten_stop_loss', 'factor': 0.7}
            ]
            
            success_count = 0
            
            for adjustment in conservative_adjustments:
                if adjustment['action'] == 'reduce_position_size':
                    if await self._reduce_position_size(alert, {'size_reduction_factor': adjustment['factor']}):
                        success_count += 1
                elif adjustment['action'] == 'increase_confidence':
                    if await self._increase_confidence_threshold(alert, {'threshold_increase': adjustment['increase']}):
                        success_count += 1
                elif adjustment['action'] == 'tighten_stop_loss':
                    if await self._tighten_stop_loss(alert, {'stop_loss_reduction_factor': adjustment['factor']}):
                        success_count += 1
            
            logger.info(f"Switched to conservative mode: {success_count}/{len(conservative_adjustments)} adjustments applied")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error switching to conservative mode: {e}")
            return False

    async def _temporary_shutdown(self, alert: PerformanceAlert, parameters: Dict[str, Any]) -> bool:
        """Temporarily shutdown signal generation"""
        try:
            shutdown_duration = parameters.get('shutdown_duration_minutes', 60)
            
            # Pause all strategies
            for strategy_name in self.signal_agent.strategies:
                self.signal_agent.strategies[strategy_name]['enabled'] = False
            
            logger.critical(f"EMERGENCY: Temporarily shutting down signal generation for {shutdown_duration} minutes")
            
            # Schedule reactivation
            asyncio.create_task(self._schedule_full_reactivation(shutdown_duration))
            
            return True
            
        except Exception as e:
            logger.error(f"Error in temporary shutdown: {e}")
            return False

    async def _schedule_strategy_reactivation(self, strategy_name: str, duration_minutes: int):
        """Schedule strategy reactivation after specified duration"""
        try:
            await asyncio.sleep(duration_minutes * 60)
            
            if strategy_name in self.signal_agent.strategies:
                self.signal_agent.strategies[strategy_name]['enabled'] = True
                logger.info(f"Reactivated strategy {strategy_name} after {duration_minutes} minutes")
                
                # Remove from active adjustments
                adjustment_key = f"{strategy_name}_pause"
                if adjustment_key in self.active_adjustments:
                    self.active_adjustments[adjustment_key].is_active = False
                    del self.active_adjustments[adjustment_key]
                    
        except Exception as e:
            logger.error(f"Error reactivating strategy {strategy_name}: {e}")

    async def _schedule_symbol_reactivation(self, symbol: str, duration_minutes: int):
        """Schedule symbol reactivation after specified duration"""
        try:
            await asyncio.sleep(duration_minutes * 60)
            
            if hasattr(self.signal_agent, 'paused_symbols') and symbol in self.signal_agent.paused_symbols:
                self.signal_agent.paused_symbols.remove(symbol)
                logger.info(f"Reactivated symbol {symbol} after {duration_minutes} minutes")
                
                # Remove from active adjustments
                adjustment_key = f"{symbol}_pause"
                if adjustment_key in self.active_adjustments:
                    self.active_adjustments[adjustment_key].is_active = False
                    del self.active_adjustments[adjustment_key]
                    
        except Exception as e:
            logger.error(f"Error reactivating symbol {symbol}: {e}")

    async def _schedule_full_reactivation(self, duration_minutes: int):
        """Schedule full system reactivation after emergency shutdown"""
        try:
            await asyncio.sleep(duration_minutes * 60)
            
            # Reactivate all strategies
            for strategy_name in self.signal_agent.strategies:
                self.signal_agent.strategies[strategy_name]['enabled'] = True
            
            logger.info(f"System reactivated after emergency shutdown of {duration_minutes} minutes")
            
        except Exception as e:
            logger.error(f"Error in full system reactivation: {e}")

    def get_feedback_handler_status(self) -> Dict[str, Any]:
        """Get current status of feedback handler"""
        return {
            'metrics': self.metrics,
            'active_alerts_count': len(self.active_alerts),
            'active_adjustments_count': len(self.active_adjustments),
            'active_adjustments': {k: {
                'strategy': adj.strategy_name,
                'symbol': adj.symbol,
                'type': adj.adjustment_type,
                'timestamp': adj.timestamp.isoformat(),
                'reason': adj.reason
            } for k, adj in self.active_adjustments.items()},
            'recent_alerts': [
                {
                    'alert_id': alert.alert_id,
                    'type': alert.alert_type,
                    'severity': alert.severity.value,
                    'strategy': alert.strategy,
                    'symbol': alert.symbol,
                    'timestamp': alert.timestamp.isoformat()
                } for alert in self.alert_history[-10:]  # Last 10 alerts
            ]
        }
