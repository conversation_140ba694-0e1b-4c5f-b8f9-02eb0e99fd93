#!/usr/bin/env python3
"""
Multi-Timeframe Fusion Component
Combines signals from multiple timeframes for robust signal generation
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import (
    TradingSignal, MultiTimeframeSignalResult, SignalType, SignalAction
)
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


class TimeframeWeight(Enum):
    """Timeframe weighting schemes"""
    EQUAL = "equal"
    HIGHER_TIMEFRAME_BIAS = "higher_bias"
    LOWER_TIMEFRAME_BIAS = "lower_bias"
    VOLUME_WEIGHTED = "volume_weighted"
    VOLATILITY_WEIGHTED = "volatility_weighted"


@dataclass
class MultiTimeframeFusionConfig(ComponentConfig):
    """Configuration for Multi-Timeframe Fusion"""

    # Timeframe settings
    primary_timeframe: str = "5min"
    secondary_timeframes: List[str] = None
    timeframe_hierarchy: Dict[str, int] = None

    # Fusion settings
    fusion_method: str = "weighted_average"  # "weighted_average", "majority_vote", "confidence_weighted"
    weighting_scheme: TimeframeWeight = TimeframeWeight.HIGHER_TIMEFRAME_BIAS
    min_timeframe_agreement: float = 0.6

    # Signal filtering
    require_primary_timeframe: bool = True
    min_supporting_timeframes: int = 1
    max_timeframe_age_minutes: int = 15

    # Confidence calculation
    consensus_boost_factor: float = 1.2
    disagreement_penalty_factor: float = 0.8
    timeframe_confidence_weights: Dict[str, float] = None

    # Quality thresholds
    min_consensus_strength: float = 0.5
    min_fused_confidence: float = 0.6

    def __post_init__(self):
        if self.secondary_timeframes is None:
            self.secondary_timeframes = ["1min", "15min", "1h"]

        if self.timeframe_hierarchy is None:
            self.timeframe_hierarchy = {
                "1min": 1,
                "5min": 2,
                "15min": 3,
                "1h": 4,
                "4h": 5,
                "1d": 6
            }

        if self.timeframe_confidence_weights is None:
            self.timeframe_confidence_weights = {
                "1min": 0.8,
                "5min": 1.0,
                "15min": 1.2,
                "1h": 1.4,
                "4h": 1.6,
                "1d": 1.8
            }


class TimeframeAnalyzer:
    """
    Analyzer for timeframe relationships and signal alignment
    """

    def __init__(self, config: MultiTimeframeFusionConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.TimeframeAnalyzer")

    def analyze_timeframe_alignment(self, signals: Dict[str, TradingSignal]) -> Dict[str, Any]:
        """
        Analyze alignment between different timeframe signals
        """
        try:
            if len(signals) < 2:
                return {
                    'alignment_score': 0.0,
                    'consensus_direction': None,
                    'agreement_ratio': 0.0,
                    'conflicting_signals': []
                }

            # Extract signal directions
            directions = {}
            confidences = {}

            for timeframe, signal in signals.items():
                if signal.signal_type == SignalType.LONG:
                    directions[timeframe] = 1
                elif signal.signal_type == SignalType.SHORT:
                    directions[timeframe] = -1
                else:
                    directions[timeframe] = 0

                confidences[timeframe] = signal.confidence

            # Calculate consensus
            direction_values = list(directions.values())
            consensus_direction = self._calculate_consensus_direction(direction_values, confidences)

            # Calculate agreement ratio
            agreement_count = sum(1 for d in direction_values if d == consensus_direction)
            agreement_ratio = agreement_count / len(direction_values)

            # Calculate alignment score
            alignment_score = self._calculate_alignment_score(directions, confidences)

            # Identify conflicting signals
            conflicting_signals = [
                timeframe for timeframe, direction in directions.items()
                if direction != consensus_direction and direction != 0
            ]

            return {
                'alignment_score': alignment_score,
                'consensus_direction': consensus_direction,
                'agreement_ratio': agreement_ratio,
                'conflicting_signals': conflicting_signals,
                'direction_breakdown': directions,
                'confidence_breakdown': confidences
            }

        except Exception as e:
            self.logger.error(f"Error analyzing timeframe alignment: {e}")
            return {
                'alignment_score': 0.0,
                'consensus_direction': None,
                'agreement_ratio': 0.0,
                'conflicting_signals': [],
                'error': str(e)
            }

    def _calculate_consensus_direction(self, directions: List[int],
                                     confidences: Dict[str, float]) -> int:
        """Calculate consensus direction weighted by confidence"""
        try:
            if not directions:
                return 0

            # Weight directions by confidence
            weighted_sum = 0
            total_weight = 0

            timeframes = list(confidences.keys())
            for i, direction in enumerate(directions):
                if i < len(timeframes):
                    timeframe = timeframes[i]
                    confidence = confidences[timeframe]
                    weight = confidence * self.config.timeframe_confidence_weights.get(timeframe, 1.0)

                    weighted_sum += direction * weight
                    total_weight += weight

            if total_weight == 0:
                return 0

            weighted_average = weighted_sum / total_weight

            # Convert to discrete direction
            if weighted_average > 0.1:
                return 1
            elif weighted_average < -0.1:
                return -1
            else:
                return 0

        except Exception:
            return 0

    def _calculate_alignment_score(self, directions: Dict[str, int],
                                 confidences: Dict[str, float]) -> float:
        """Calculate overall alignment score"""
        try:
            if len(directions) < 2:
                return 0.0

            # Calculate pairwise alignment scores
            timeframes = list(directions.keys())
            alignment_scores = []

            for i in range(len(timeframes)):
                for j in range(i + 1, len(timeframes)):
                    tf1, tf2 = timeframes[i], timeframes[j]
                    dir1, dir2 = directions[tf1], directions[tf2]
                    conf1, conf2 = confidences[tf1], confidences[tf2]

                    # Calculate alignment between two timeframes
                    if dir1 == dir2 and dir1 != 0:
                        # Same direction - high alignment
                        alignment = 1.0 * min(conf1, conf2)
                    elif dir1 == 0 or dir2 == 0:
                        # One neutral - medium alignment
                        alignment = 0.5 * max(conf1, conf2)
                    else:
                        # Opposite directions - low alignment
                        alignment = 0.0

                    alignment_scores.append(alignment)

            # Return average alignment score
            return np.mean(alignment_scores) if alignment_scores else 0.0

        except Exception:
            return 0.0


class SignalFuser:
    """
    Fuses signals from multiple timeframes into consensus signals
    """

    def __init__(self, config: MultiTimeframeFusionConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SignalFuser")

    def fuse_signals(self, signals: Dict[str, TradingSignal]) -> Optional[TradingSignal]:
        """
        Fuse multiple timeframe signals into a single consensus signal
        """
        try:
            if not signals:
                return None

            # Check if primary timeframe is required and present
            if self.config.require_primary_timeframe:
                if self.config.primary_timeframe not in signals:
                    self.logger.warning(f"Primary timeframe {self.config.primary_timeframe} not found")
                    return None

            # Filter signals by age
            valid_signals = self._filter_signals_by_age(signals)

            if len(valid_signals) < self.config.min_supporting_timeframes + 1:
                self.logger.warning("Insufficient supporting timeframes")
                return None

            # Apply fusion method
            if self.config.fusion_method == "weighted_average":
                return self._weighted_average_fusion(valid_signals)
            elif self.config.fusion_method == "majority_vote":
                return self._majority_vote_fusion(valid_signals)
            elif self.config.fusion_method == "confidence_weighted":
                return self._confidence_weighted_fusion(valid_signals)
            else:
                self.logger.warning(f"Unknown fusion method: {self.config.fusion_method}")
                return None

        except Exception as e:
            self.logger.error(f"Error fusing signals: {e}")
            return None

    def _filter_signals_by_age(self, signals: Dict[str, TradingSignal]) -> Dict[str, TradingSignal]:
        """Filter signals by maximum age"""
        try:
            current_time = datetime.now()
            max_age = timedelta(minutes=self.config.max_timeframe_age_minutes)

            valid_signals = {}
            for timeframe, signal in signals.items():
                age = current_time - signal.timestamp
                if age <= max_age:
                    valid_signals[timeframe] = signal
                else:
                    self.logger.debug(f"Signal from {timeframe} too old: {age}")

            return valid_signals

        except Exception as e:
            self.logger.error(f"Error filtering signals by age: {e}")
            return signals

    def _weighted_average_fusion(self, signals: Dict[str, TradingSignal]) -> Optional[TradingSignal]:
        """Fuse signals using weighted average method"""
        try:
            # Get primary signal as base
            primary_signal = signals.get(self.config.primary_timeframe)
            if not primary_signal:
                primary_signal = list(signals.values())[0]

            # Calculate weights for each timeframe
            weights = self._calculate_timeframe_weights(signals)

            # Weighted average of key metrics
            weighted_confidence = 0.0
            weighted_entry_price = 0.0
            weighted_stop_loss = 0.0
            weighted_take_profit = 0.0
            total_weight = 0.0

            # Determine consensus signal type
            signal_type_votes = {}

            for timeframe, signal in signals.items():
                weight = weights.get(timeframe, 1.0)

                weighted_confidence += signal.confidence * weight
                weighted_entry_price += signal.entry_price * weight
                weighted_stop_loss += signal.stop_loss * weight
                weighted_take_profit += signal.take_profit * weight
                total_weight += weight

                # Count signal type votes
                signal_type_votes[signal.signal_type] = signal_type_votes.get(signal.signal_type, 0) + weight

            if total_weight == 0:
                return None

            # Normalize weighted values
            fused_confidence = weighted_confidence / total_weight
            fused_entry_price = weighted_entry_price / total_weight
            fused_stop_loss = weighted_stop_loss / total_weight
            fused_take_profit = weighted_take_profit / total_weight

            # Determine consensus signal type
            consensus_signal_type = max(signal_type_votes, key=signal_type_votes.get)

            # Create fused signal
            fused_signal = self._create_fused_signal(
                primary_signal,
                consensus_signal_type,
                fused_confidence,
                fused_entry_price,
                fused_stop_loss,
                fused_take_profit,
                weights
            )

            return fused_signal

        except Exception as e:
            self.logger.error(f"Error in weighted average fusion: {e}")
            return None

    def _majority_vote_fusion(self, signals: Dict[str, TradingSignal]) -> Optional[TradingSignal]:
        """Fuse signals using majority vote method"""
        try:
            # Count votes for each signal type
            signal_type_votes = {}
            confidence_by_type = {}

            for timeframe, signal in signals.items():
                signal_type = signal.signal_type
                weight = self.config.timeframe_confidence_weights.get(timeframe, 1.0)

                signal_type_votes[signal_type] = signal_type_votes.get(signal_type, 0) + weight

                if signal_type not in confidence_by_type:
                    confidence_by_type[signal_type] = []
                confidence_by_type[signal_type].append(signal.confidence)

            # Find majority signal type
            majority_signal_type = max(signal_type_votes, key=signal_type_votes.get)
            majority_votes = signal_type_votes[majority_signal_type]
            total_votes = sum(signal_type_votes.values())

            # Check if majority threshold is met
            if majority_votes / total_votes < self.config.min_timeframe_agreement:
                self.logger.warning("Insufficient majority agreement")
                return None

            # Get signals of majority type
            majority_signals = {
                tf: signal for tf, signal in signals.items()
                if signal.signal_type == majority_signal_type
            }

            # Use weighted average for majority signals
            return self._weighted_average_fusion(majority_signals)

        except Exception as e:
            self.logger.error(f"Error in majority vote fusion: {e}")
            return None

    def _confidence_weighted_fusion(self, signals: Dict[str, TradingSignal]) -> Optional[TradingSignal]:
        """Fuse signals using confidence-weighted method"""
        try:
            # Sort signals by confidence
            sorted_signals = sorted(signals.items(), key=lambda x: x[1].confidence, reverse=True)

            # Use top confidence signals with exponential weighting
            weights = {}
            for i, (timeframe, signal) in enumerate(sorted_signals):
                # Exponential decay: higher confidence gets exponentially higher weight
                weight = signal.confidence * (0.8 ** i)
                weights[timeframe] = weight

            # Apply weighted average with confidence-based weights
            return self._apply_weighted_fusion(signals, weights)

        except Exception as e:
            self.logger.error(f"Error in confidence weighted fusion: {e}")
            return None

    def _calculate_timeframe_weights(self, signals: Dict[str, TradingSignal]) -> Dict[str, float]:
        """Calculate weights for each timeframe based on weighting scheme"""
        try:
            weights = {}

            if self.config.weighting_scheme == TimeframeWeight.EQUAL:
                # Equal weights
                for timeframe in signals.keys():
                    weights[timeframe] = 1.0

            elif self.config.weighting_scheme == TimeframeWeight.HIGHER_TIMEFRAME_BIAS:
                # Higher timeframes get higher weights
                for timeframe in signals.keys():
                    hierarchy_level = self.config.timeframe_hierarchy.get(timeframe, 1)
                    weights[timeframe] = hierarchy_level

            elif self.config.weighting_scheme == TimeframeWeight.LOWER_TIMEFRAME_BIAS:
                # Lower timeframes get higher weights
                max_level = max(self.config.timeframe_hierarchy.values())
                for timeframe in signals.keys():
                    hierarchy_level = self.config.timeframe_hierarchy.get(timeframe, 1)
                    weights[timeframe] = max_level - hierarchy_level + 1

            else:
                # Default to confidence-based weights
                for timeframe, signal in signals.items():
                    weights[timeframe] = signal.confidence

            return weights

        except Exception as e:
            self.logger.error(f"Error calculating timeframe weights: {e}")
            return {tf: 1.0 for tf in signals.keys()}

    def _apply_weighted_fusion(self, signals: Dict[str, TradingSignal],
                             weights: Dict[str, float]) -> Optional[TradingSignal]:
        """Apply weighted fusion with given weights"""
        try:
            # Get primary signal as base
            primary_signal = signals.get(self.config.primary_timeframe)
            if not primary_signal:
                primary_signal = list(signals.values())[0]

            # Weighted calculations
            weighted_confidence = 0.0
            weighted_entry_price = 0.0
            weighted_stop_loss = 0.0
            weighted_take_profit = 0.0
            total_weight = 0.0

            signal_type_votes = {}

            for timeframe, signal in signals.items():
                weight = weights.get(timeframe, 1.0)

                weighted_confidence += signal.confidence * weight
                weighted_entry_price += signal.entry_price * weight
                weighted_stop_loss += signal.stop_loss * weight
                weighted_take_profit += signal.take_profit * weight
                total_weight += weight

                signal_type_votes[signal.signal_type] = signal_type_votes.get(signal.signal_type, 0) + weight

            if total_weight == 0:
                return None

            # Normalize
            fused_confidence = weighted_confidence / total_weight
            fused_entry_price = weighted_entry_price / total_weight
            fused_stop_loss = weighted_stop_loss / total_weight
            fused_take_profit = weighted_take_profit / total_weight

            # Consensus signal type
            consensus_signal_type = max(signal_type_votes, key=signal_type_votes.get)

            return self._create_fused_signal(
                primary_signal,
                consensus_signal_type,
                fused_confidence,
                fused_entry_price,
                fused_stop_loss,
                fused_take_profit,
                weights
            )

        except Exception as e:
            self.logger.error(f"Error in weighted fusion: {e}")
            return None

    def _create_fused_signal(self, base_signal: TradingSignal, signal_type: SignalType,
                           confidence: float, entry_price: float, stop_loss: float,
                           take_profit: float, weights: Dict[str, float]) -> TradingSignal:
        """Create fused signal from fusion results"""
        try:
            # Determine action from signal type
            if signal_type == SignalType.LONG:
                action = SignalAction.BUY
            elif signal_type == SignalType.SHORT:
                action = SignalAction.SELL
            else:
                action = SignalAction.HOLD

            # Calculate risk-reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0.0

            # Create fused signal
            fused_signal = TradingSignal(
                signal_id=f"{base_signal.signal_id}_fused",
                symbol=base_signal.symbol,
                strategy_name=f"{base_signal.strategy_name}_multitf",
                signal_type=signal_type,
                action=action,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                quantity=base_signal.quantity,
                risk_reward_ratio=risk_reward_ratio,
                confidence=confidence,
                market_regime=base_signal.market_regime,
                timestamp=datetime.now(),
                capital_allocated=base_signal.capital_allocated,
                risk_amount=base_signal.risk_amount,
                position_size_method=base_signal.position_size_method,
                context={
                    **base_signal.context,
                    'fusion_method': self.config.fusion_method,
                    'timeframe_weights': weights,
                    'is_fused_signal': True
                }
            )

            return fused_signal

        except Exception as e:
            self.logger.error(f"Error creating fused signal: {e}")
            return base_signal


class MultiTimeframeFusion(BaseSignalComponent):
    """
    Multi-Timeframe Fusion Component

    Features:
    - Signal fusion from multiple timeframes
    - Configurable weighting schemes
    - Consensus calculation
    - Quality assessment
    - Timeframe alignment analysis
    """

    def __init__(self, config: MultiTimeframeFusionConfig, event_bus=None):
        super().__init__("MultiTimeframeFusion", config, event_bus)
        self.config = config

        # Initialize analyzers
        self.timeframe_analyzer = TimeframeAnalyzer(config)
        self.signal_fuser = SignalFuser(config)

        # Fusion statistics
        self.fusion_stats = {
            'signals_fused': 0,
            'consensus_achieved': 0,
            'disagreements_resolved': 0,
            'timeframes_processed': set()
        }

    async def _initialize_component(self) -> bool:
        """Initialize the multi-timeframe fusion component"""
        try:
            self.logger.info("Initializing Multi-Timeframe Fusion...")

            self.logger.info(f"Primary timeframe: {self.config.primary_timeframe}")
            self.logger.info(f"Secondary timeframes: {self.config.secondary_timeframes}")
            self.logger.info(f"Fusion method: {self.config.fusion_method}")
            self.logger.info(f"Weighting scheme: {self.config.weighting_scheme.value}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Multi-Timeframe Fusion: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup multi-timeframe fusion resources"""
        try:
            # Reset fusion statistics
            self.fusion_stats = {
                'signals_fused': 0,
                'consensus_achieved': 0,
                'disagreements_resolved': 0,
                'timeframes_processed': set()
            }

            self.logger.info("Multi-Timeframe Fusion cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Multi-Timeframe Fusion: {e}")

    async def fuse_timeframe_signals(self, signals: Dict[str, TradingSignal]) -> MultiTimeframeSignalResult:
        """
        Fuse signals from multiple timeframes
        """
        return await self.process_with_retry(
            "fuse_timeframe_signals",
            self._fuse_timeframe_signals_internal,
            signals
        )

    async def _fuse_timeframe_signals_internal(self, signals: Dict[str, TradingSignal]) -> MultiTimeframeSignalResult:
        """Internal timeframe signal fusion logic"""
        try:
            # Publish fusion started event
            await self.publish_event(
                SignalEventTypes.TIMEFRAME_FUSION_COMPLETED.value,
                {
                    'timeframes': list(signals.keys()),
                    'signal_count': len(signals)
                }
            )

            # Get primary signal
            primary_signal = signals.get(self.config.primary_timeframe)
            if not primary_signal and signals:
                primary_signal = list(signals.values())[0]

            if not primary_signal:
                return MultiTimeframeSignalResult(
                    primary_signal=None,
                    timeframe_signals=signals,
                    consensus_signal=None,
                    consensus_strength=0.0,
                    timeframe_agreement=0.0,
                    fusion_method=self.config.fusion_method,
                    fusion_details={'error': 'No primary signal available'}
                )

            # Analyze timeframe alignment
            alignment_analysis = self.timeframe_analyzer.analyze_timeframe_alignment(signals)

            # Calculate timeframe weights
            weights = self.signal_fuser._calculate_timeframe_weights(signals)

            # Fuse signals
            consensus_signal = self.signal_fuser.fuse_signals(signals)

            # Calculate consensus strength
            consensus_strength = self._calculate_consensus_strength(alignment_analysis, signals)

            # Update statistics
            self._update_fusion_stats(signals, consensus_signal is not None, alignment_analysis)

            result = MultiTimeframeSignalResult(
                primary_signal=primary_signal,
                timeframe_signals=signals,
                consensus_signal=consensus_signal,
                consensus_strength=consensus_strength,
                timeframe_agreement=alignment_analysis.get('agreement_ratio', 0.0),
                fusion_method=self.config.fusion_method,
                timeframe_weights=weights,
                fusion_details={
                    'alignment_analysis': alignment_analysis,
                    'fusion_method': self.config.fusion_method,
                    'weighting_scheme': self.config.weighting_scheme.value,
                    'timeframes_used': list(signals.keys()),
                    'consensus_achieved': consensus_signal is not None
                }
            )

            # Publish fusion completed event
            await self.publish_event(
                SignalEventTypes.TIMEFRAME_FUSION_COMPLETED.value,
                {
                    'timeframes': list(signals.keys()),
                    'consensus_achieved': consensus_signal is not None,
                    'consensus_strength': consensus_strength,
                    'agreement_ratio': alignment_analysis.get('agreement_ratio', 0.0)
                }
            )

            return result

        except Exception as e:
            self.logger.error(f"Error fusing timeframe signals: {e}")
            return MultiTimeframeSignalResult(
                primary_signal=primary_signal if 'primary_signal' in locals() else None,
                timeframe_signals=signals,
                consensus_signal=None,
                consensus_strength=0.0,
                timeframe_agreement=0.0,
                fusion_method=self.config.fusion_method,
                fusion_details={'error': str(e)}
            )

    def _calculate_consensus_strength(self, alignment_analysis: Dict[str, Any],
                                    signals: Dict[str, TradingSignal]) -> float:
        """Calculate consensus strength based on alignment and confidence"""
        try:
            alignment_score = alignment_analysis.get('alignment_score', 0.0)
            agreement_ratio = alignment_analysis.get('agreement_ratio', 0.0)

            # Average confidence of all signals
            avg_confidence = np.mean([signal.confidence for signal in signals.values()])

            # Consensus strength combines alignment, agreement, and confidence
            consensus_strength = (
                0.4 * alignment_score +
                0.4 * agreement_ratio +
                0.2 * avg_confidence
            )

            # Apply boost/penalty factors
            if agreement_ratio >= self.config.min_timeframe_agreement:
                consensus_strength *= self.config.consensus_boost_factor
            else:
                consensus_strength *= self.config.disagreement_penalty_factor

            return max(0.0, min(1.0, consensus_strength))

        except Exception as e:
            self.logger.error(f"Error calculating consensus strength: {e}")
            return 0.0

    def _update_fusion_stats(self, signals: Dict[str, TradingSignal],
                           consensus_achieved: bool, alignment_analysis: Dict[str, Any]):
        """Update fusion statistics"""
        try:
            self.fusion_stats['signals_fused'] += 1

            if consensus_achieved:
                self.fusion_stats['consensus_achieved'] += 1

            # Track disagreements resolved
            conflicting_signals = alignment_analysis.get('conflicting_signals', [])
            if conflicting_signals and consensus_achieved:
                self.fusion_stats['disagreements_resolved'] += 1

            # Track timeframes processed
            for timeframe in signals.keys():
                self.fusion_stats['timeframes_processed'].add(timeframe)

        except Exception as e:
            self.logger.error(f"Error updating fusion stats: {e}")

    async def analyze_timeframe_consensus(self, signals: Dict[str, TradingSignal]) -> Dict[str, Any]:
        """
        Analyze consensus between timeframes without fusion
        """
        try:
            alignment_analysis = self.timeframe_analyzer.analyze_timeframe_alignment(signals)
            consensus_strength = self._calculate_consensus_strength(alignment_analysis, signals)

            return {
                'alignment_analysis': alignment_analysis,
                'consensus_strength': consensus_strength,
                'recommendation': self._get_consensus_recommendation(consensus_strength, alignment_analysis),
                'timeframe_count': len(signals),
                'primary_timeframe_present': self.config.primary_timeframe in signals
            }

        except Exception as e:
            self.logger.error(f"Error analyzing timeframe consensus: {e}")
            return {'error': str(e)}

    def _get_consensus_recommendation(self, consensus_strength: float,
                                    alignment_analysis: Dict[str, Any]) -> str:
        """Get recommendation based on consensus analysis"""
        try:
            agreement_ratio = alignment_analysis.get('agreement_ratio', 0.0)

            if consensus_strength >= 0.8 and agreement_ratio >= 0.8:
                return "STRONG_CONSENSUS"
            elif consensus_strength >= 0.6 and agreement_ratio >= 0.6:
                return "MODERATE_CONSENSUS"
            elif consensus_strength >= 0.4:
                return "WEAK_CONSENSUS"
            else:
                return "NO_CONSENSUS"

        except Exception:
            return "UNKNOWN"

    def get_fusion_stats(self) -> Dict[str, Any]:
        """Get fusion statistics"""
        return {
            'fusion_stats': {
                **self.fusion_stats,
                'timeframes_processed': list(self.fusion_stats['timeframes_processed'])
            },
            'config': {
                'primary_timeframe': self.config.primary_timeframe,
                'secondary_timeframes': self.config.secondary_timeframes,
                'fusion_method': self.config.fusion_method,
                'weighting_scheme': self.config.weighting_scheme.value,
                'min_timeframe_agreement': self.config.min_timeframe_agreement,
                'consensus_boost_factor': self.config.consensus_boost_factor
            }
        }

    def reset_fusion_stats(self):
        """Reset fusion statistics"""
        self.fusion_stats = {
            'signals_fused': 0,
            'consensus_achieved': 0,
            'disagreements_resolved': 0,
            'timeframes_processed': set()
        }
        self.logger.info("Fusion statistics reset")