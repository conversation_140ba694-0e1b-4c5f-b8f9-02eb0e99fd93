#!/usr/bin/env python3
"""
Strategy Manager Component
Dynamic strategy management with performance-based prioritization and A/B testing
"""

import asyncio
import logging
import yaml
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from pathlib import Path

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import StrategyPerformanceMetrics
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


@dataclass
class StrategyManagerConfig(ComponentConfig):
    """Configuration for Strategy Manager"""

    # Strategy loading
    strategies_config_path: str = "agents/config/strategies.yaml"
    enable_dynamic_loading: bool = True
    auto_reload_strategies: bool = True
    reload_interval_minutes: int = 60

    # Performance tracking
    enable_performance_tracking: bool = True
    performance_window_days: int = 30
    min_trades_for_ranking: int = 10

    # Strategy prioritization
    enable_ranking_system: bool = True
    initial_ranking_score: int = 100
    ranking_update_frequency_hours: int = 6

    # A/B testing
    enable_ab_testing: bool = True
    ab_test_duration_days: int = 7
    ab_test_traffic_split: float = 0.1

    # Risk/reward configuration
    enable_risk_reward_ratios: bool = True
    default_risk_reward_ratio: float = 2.0


class StrategyManager(BaseSignalComponent):
    """
    Strategy Manager Component

    Features:
    - Dynamic strategy loading and management
    - Performance-based prioritization
    - A/B testing framework
    - Risk/reward ratio configuration
    - Strategy registry system
    """

    def __init__(self, config: StrategyManagerConfig, event_bus=None):
        super().__init__("StrategyManager", config, event_bus)
        self.config = config

        # Strategy storage
        self.strategies = {}
        self.strategy_performance = {}
        self.strategy_rankings = {}
        self.ab_tests = {}

        # Last reload time
        self.last_reload = None

    async def _initialize_component(self) -> bool:
        """Initialize the strategy manager"""
        try:
            self.logger.info("Initializing Strategy Manager...")

            # Load initial strategies
            await self.load_strategies()

            # Initialize rankings
            if self.config.enable_ranking_system:
                self._initialize_rankings()

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Strategy Manager: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup strategy manager resources"""
        try:
            self.strategies.clear()
            self.strategy_performance.clear()
            self.strategy_rankings.clear()
            self.ab_tests.clear()

            self.logger.info("Strategy Manager cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Strategy Manager: {e}")

    async def load_strategies(self) -> bool:
        """Load strategies from configuration"""
        try:
            config_path = Path(self.config.strategies_config_path)

            if not config_path.exists():
                self.logger.error(f"Strategies config file not found: {config_path}")
                return False

            with open(config_path, 'r') as f:
                strategies_config = yaml.safe_load(f)

            # Load strategies
            loaded_count = 0
            for strategy_name, strategy_config in strategies_config.get('strategies', {}).items():
                if self._validate_strategy_config(strategy_config):
                    self.strategies[strategy_name] = strategy_config
                    loaded_count += 1

                    # Initialize performance tracking
                    if strategy_name not in self.strategy_performance:
                        self.strategy_performance[strategy_name] = StrategyPerformanceMetrics(
                            strategy_name=strategy_name,
                            symbol="ALL",
                            timeframe="5min"
                        )
                else:
                    self.logger.warning(f"Invalid strategy config for {strategy_name}")

            self.last_reload = datetime.now()
            self.logger.info(f"Loaded {loaded_count} strategies")

            # Publish strategy loaded event
            await self.publish_event(
                SignalEventTypes.STRATEGY_LOADED.value,
                {
                    'strategies_loaded': loaded_count,
                    'total_strategies': len(self.strategies)
                }
            )

            return True

        except Exception as e:
            self.logger.error(f"Error loading strategies: {e}")
            return False

    def _validate_strategy_config(self, config: Dict[str, Any]) -> bool:
        """Validate strategy configuration"""
        try:
            required_fields = ['name', 'enabled']

            for field in required_fields:
                if field not in config:
                    return False

            # Validate risk/reward ratio if enabled
            if self.config.enable_risk_reward_ratios:
                if 'risk_reward_ratio' not in config:
                    config['risk_reward_ratio'] = self.config.default_risk_reward_ratio

            return True

        except Exception:
            return False

    def _initialize_rankings(self):
        """Initialize strategy rankings"""
        try:
            for strategy_name in self.strategies.keys():
                if strategy_name not in self.strategy_rankings:
                    self.strategy_rankings[strategy_name] = self.config.initial_ranking_score

            self.logger.info(f"Initialized rankings for {len(self.strategy_rankings)} strategies")

        except Exception as e:
            self.logger.error(f"Error initializing rankings: {e}")

    async def get_active_strategies(self, symbol: str = None) -> Dict[str, Dict[str, Any]]:
        """Get active strategies, optionally filtered by symbol"""
        try:
            # Check if auto-reload is needed
            if self.config.auto_reload_strategies and self._should_reload_strategies():
                await self.load_strategies()

            active_strategies = {}

            for strategy_name, strategy_config in self.strategies.items():
                if not strategy_config.get('enabled', False):
                    continue

                # Symbol filtering
                if symbol and 'symbols' in strategy_config:
                    if symbol not in strategy_config['symbols']:
                        continue

                # A/B testing filter
                if self._is_strategy_in_ab_test(strategy_name):
                    if not self._should_include_in_ab_test(strategy_name):
                        continue

                active_strategies[strategy_name] = strategy_config

            return active_strategies

        except Exception as e:
            self.logger.error(f"Error getting active strategies: {e}")
            return {}

    def get_manager_stats(self) -> Dict[str, Any]:
        """Get strategy manager statistics"""
        return {
            'total_strategies': len(self.strategies),
            'active_strategies': len([s for s in self.strategies.values() if s.get('enabled', False)]),
            'strategies_with_performance': len(self.strategy_performance),
            'active_ab_tests': len(self.ab_tests),
            'last_reload': self.last_reload.isoformat() if self.last_reload else None
        }