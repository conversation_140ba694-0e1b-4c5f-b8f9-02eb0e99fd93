#!/usr/bin/env python3
"""
Signal Validator Component
Comprehensive signal validation with real-time market depth integration
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, time
from dataclasses import dataclass

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import (
    TradingSignal, SignalValidationResult, MarketDepthData, MarketRegime
)
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


@dataclass
class SignalValidatorConfig(ComponentConfig):
    """Configuration for Signal Validator"""

    # Confidence thresholds
    min_confidence: float = 0.6
    high_confidence_threshold: float = 0.8

    # Time filters
    market_hours_only: bool = True
    market_open_time: str = "09:20"
    market_close_time: str = "15:00"
    avoid_first_minutes: int = 10
    avoid_last_minutes: int = 30

    # Liquidity checks
    enable_liquidity_checks: bool = True
    min_volume_ratio: float = 1.5
    min_daily_volume: int = 100000
    max_bid_ask_spread_percent: float = 0.5
    min_market_depth_levels: int = 3

    # Risk filters
    max_daily_risk_percent: float = 5.0
    max_position_correlation: float = 0.7
    max_sector_exposure_percent: float = 30.0

    # Cooldown settings
    minutes_between_signals: int = 5
    max_signals_per_symbol_per_day: int = 3
    max_signals_per_strategy_per_day: int = 10

    # Signal quality filters
    min_risk_reward_ratio: float = 1.5
    max_stop_loss_percent: float = 3.0
    min_profit_target_percent: float = 2.0

    # Market regime filters
    enable_regime_filters: bool = True
    allowed_regimes: List[str] = None

    # Integration settings
    use_risk_agent_validation: bool = True
    risk_agent_timeout_seconds: float = 5.0

    def __post_init__(self):
        if self.allowed_regimes is None:
            self.allowed_regimes = ["bull", "bear", "sideways"]


class LiquidityAnalyzer:
    """
    Real-time liquidity analyzer using market depth data
    """

    def __init__(self, config: SignalValidatorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.LiquidityAnalyzer")

        # Market depth cache
        self.depth_cache = {}
        self.volume_cache = {}

    async def analyze_liquidity(self, symbol: str, quantity: int,
                              market_depth: Optional[MarketDepthData] = None) -> Dict[str, Any]:
        """
        Analyze liquidity for a given symbol and quantity
        """
        try:
            liquidity_metrics = {
                'is_liquid': False,
                'liquidity_score': 0.0,
                'market_impact_cost': 0.0,
                'volume_check': False,
                'spread_check': False,
                'depth_check': False,
                'details': {}
            }

            # Volume analysis
            volume_metrics = await self._analyze_volume(symbol)
            liquidity_metrics['volume_check'] = volume_metrics['sufficient_volume']
            liquidity_metrics['details']['volume'] = volume_metrics

            # Spread analysis
            if market_depth:
                spread_metrics = self._analyze_spread(market_depth)
                liquidity_metrics['spread_check'] = spread_metrics['acceptable_spread']
                liquidity_metrics['details']['spread'] = spread_metrics

                # Market depth analysis
                depth_metrics = self._analyze_market_depth(market_depth, quantity)
                liquidity_metrics['depth_check'] = depth_metrics['sufficient_depth']
                liquidity_metrics['market_impact_cost'] = depth_metrics['market_impact_cost']
                liquidity_metrics['details']['depth'] = depth_metrics
            else:
                # Fallback without market depth data
                liquidity_metrics['spread_check'] = True
                liquidity_metrics['depth_check'] = True

            # Calculate overall liquidity score
            liquidity_metrics['liquidity_score'] = self._calculate_liquidity_score(liquidity_metrics)

            # Determine if liquid enough
            liquidity_metrics['is_liquid'] = (
                liquidity_metrics['volume_check'] and
                liquidity_metrics['spread_check'] and
                liquidity_metrics['depth_check'] and
                liquidity_metrics['liquidity_score'] >= 0.6
            )

            return liquidity_metrics

        except Exception as e:
            self.logger.error(f"Error analyzing liquidity for {symbol}: {e}")
            return {
                'is_liquid': False,
                'liquidity_score': 0.0,
                'market_impact_cost': 0.0,
                'volume_check': False,
                'spread_check': False,
                'depth_check': False,
                'details': {'error': str(e)}
            }

    async def _analyze_volume(self, symbol: str) -> Dict[str, Any]:
        """Analyze volume metrics"""
        try:
            # In a real implementation, this would fetch actual volume data
            # For now, simulate volume analysis

            current_volume = self._get_current_volume(symbol)
            avg_volume = self._get_average_volume(symbol)

            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            return {
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'sufficient_volume': (
                    current_volume >= self.config.min_daily_volume and
                    volume_ratio >= self.config.min_volume_ratio
                )
            }

        except Exception as e:
            self.logger.error(f"Error analyzing volume: {e}")
            return {
                'current_volume': 0,
                'average_volume': 0,
                'volume_ratio': 0,
                'sufficient_volume': False
            }

    def _analyze_spread(self, market_depth: MarketDepthData) -> Dict[str, Any]:
        """Analyze bid-ask spread"""
        try:
            spread_percent = market_depth.spread_percent

            return {
                'spread': market_depth.spread,
                'spread_percent': spread_percent,
                'best_bid': market_depth.best_bid,
                'best_ask': market_depth.best_ask,
                'acceptable_spread': spread_percent <= self.config.max_bid_ask_spread_percent
            }

        except Exception as e:
            self.logger.error(f"Error analyzing spread: {e}")
            return {
                'spread': 0,
                'spread_percent': 100,
                'best_bid': 0,
                'best_ask': 0,
                'acceptable_spread': False
            }

    def _analyze_market_depth(self, market_depth: MarketDepthData, quantity: int) -> Dict[str, Any]:
        """Analyze market depth for order execution"""
        try:
            # Calculate market impact cost
            market_impact_cost = self._calculate_market_impact(market_depth, quantity)

            # Check depth levels
            bid_levels = len(market_depth.bid_levels)
            ask_levels = len(market_depth.ask_levels)

            sufficient_depth = (
                bid_levels >= self.config.min_market_depth_levels and
                ask_levels >= self.config.min_market_depth_levels
            )

            return {
                'bid_levels': bid_levels,
                'ask_levels': ask_levels,
                'total_bid_volume': market_depth.total_bid_volume,
                'total_ask_volume': market_depth.total_ask_volume,
                'market_impact_cost': market_impact_cost,
                'sufficient_depth': sufficient_depth
            }

        except Exception as e:
            self.logger.error(f"Error analyzing market depth: {e}")
            return {
                'bid_levels': 0,
                'ask_levels': 0,
                'total_bid_volume': 0,
                'total_ask_volume': 0,
                'market_impact_cost': 0.0,
                'sufficient_depth': False
            }

    def _calculate_market_impact(self, market_depth: MarketDepthData, quantity: int) -> float:
        """Calculate estimated market impact cost"""
        try:
            # Simplified market impact calculation
            if quantity <= market_depth.bid_size + market_depth.ask_size:
                return 0.0

            # Estimate impact based on order book depth
            remaining_quantity = quantity - (market_depth.bid_size + market_depth.ask_size)
            total_depth_volume = market_depth.total_bid_volume + market_depth.total_ask_volume

            if total_depth_volume == 0:
                return 0.05  # 5% impact if no depth data

            impact_ratio = remaining_quantity / total_depth_volume
            market_impact = min(0.02, impact_ratio * 0.1)  # Cap at 2%

            return market_impact

        except Exception:
            return 0.01  # 1% default impact

    def _calculate_liquidity_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall liquidity score"""
        try:
            score = 0.0

            # Volume component (40%)
            if metrics['volume_check']:
                score += 0.4

            # Spread component (30%)
            if metrics['spread_check']:
                score += 0.3

            # Depth component (30%)
            if metrics['depth_check']:
                score += 0.3

            # Adjust based on market impact
            impact_penalty = min(0.2, metrics['market_impact_cost'] * 10)
            score -= impact_penalty

            return max(0.0, min(1.0, score))

        except Exception:
            return 0.0

    def _get_current_volume(self, symbol: str) -> int:
        """Get current volume for symbol"""
        # In a real implementation, this would fetch from market data
        return 500000

    def _get_average_volume(self, symbol: str) -> int:
        """Get average volume for symbol"""
        # In a real implementation, this would fetch historical average
        return 300000


class SignalValidator(BaseSignalComponent):
    """
    Enhanced Signal Validator Component

    Features:
    - Comprehensive signal validation
    - Real-time liquidity analysis
    - Time-based filtering
    - Risk management integration
    - Market regime validation
    - Cooldown management
    """

    def __init__(self, config: SignalValidatorConfig, event_bus=None):
        super().__init__("SignalValidator", config, event_bus)
        self.config = config

        # Initialize liquidity analyzer
        self.liquidity_analyzer = LiquidityAnalyzer(config)

        # Signal tracking
        self.signal_history = {}
        self.daily_signal_counts = {}
        self.strategy_signal_counts = {}

        # Risk agent integration
        self.risk_agent = None

    async def _initialize_component(self) -> bool:
        """Initialize the signal validator"""
        try:
            self.logger.info("Initializing Signal Validator...")

            if self.config.enable_liquidity_checks:
                self.logger.info("Liquidity checks enabled")

            if self.config.use_risk_agent_validation:
                self.logger.info("Risk agent validation enabled")

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Signal Validator: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup signal validator resources"""
        try:
            # Clear tracking data
            self.signal_history.clear()
            self.daily_signal_counts.clear()
            self.strategy_signal_counts.clear()

            self.logger.info("Signal Validator cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Signal Validator: {e}")

    async def validate_signal(self, signal: TradingSignal,
                            market_depth: Optional[MarketDepthData] = None) -> SignalValidationResult:
        """
        Comprehensive signal validation
        """
        return await self.process_with_retry(
            "validate_signal",
            self._validate_signal_internal,
            signal,
            market_depth
        )

    async def _validate_signal_internal(self, signal: TradingSignal,
                                      market_depth: Optional[MarketDepthData] = None) -> SignalValidationResult:
        """Internal signal validation logic"""
        try:
            # Publish validation started event
            await self.publish_event(
                SignalEventTypes.SIGNAL_VALIDATED.value,
                {
                    'signal_id': signal.signal_id,
                    'symbol': signal.symbol,
                    'strategy': signal.strategy_name
                }
            )

            validation_details = {}
            rejection_reasons = []
            validation_score = 1.0

            # 1. Confidence check
            confidence_result = self._validate_confidence(signal)
            validation_details['confidence'] = confidence_result
            if not confidence_result['passed']:
                rejection_reasons.append(confidence_result['reason'])
                validation_score *= 0.5

            # 2. Time filter check
            time_result = self._validate_time_filters(signal)
            validation_details['time_filter'] = time_result
            if not time_result['passed']:
                rejection_reasons.append(time_result['reason'])
                validation_score *= 0.3

            # 3. Signal quality check
            quality_result = self._validate_signal_quality(signal)
            validation_details['quality'] = quality_result
            if not quality_result['passed']:
                rejection_reasons.append(quality_result['reason'])
                validation_score *= 0.4

            # 4. Cooldown check
            cooldown_result = self._validate_cooldown(signal)
            validation_details['cooldown'] = cooldown_result
            if not cooldown_result['passed']:
                rejection_reasons.append(cooldown_result['reason'])
                validation_score *= 0.2

            # 5. Market regime check
            regime_result = self._validate_market_regime(signal)
            validation_details['regime'] = regime_result
            if not regime_result['passed']:
                rejection_reasons.append(regime_result['reason'])
                validation_score *= 0.6

            # 6. Liquidity check
            liquidity_result = await self._validate_liquidity(signal, market_depth)
            validation_details['liquidity'] = liquidity_result
            if not liquidity_result['passed']:
                rejection_reasons.append(liquidity_result['reason'])
                validation_score *= 0.7

            # 7. Risk agent validation (if enabled)
            if self.config.use_risk_agent_validation and self.risk_agent:
                risk_result = await self._validate_with_risk_agent(signal)
                validation_details['risk_agent'] = risk_result
                if not risk_result['passed']:
                    rejection_reasons.append(risk_result['reason'])
                    validation_score *= 0.3

            # Determine overall validation result
            is_valid = len(rejection_reasons) == 0
            rejection_reason = "; ".join(rejection_reasons) if rejection_reasons else None

            # Update signal tracking
            if is_valid:
                self._update_signal_tracking(signal)

            result = SignalValidationResult(
                is_valid=is_valid,
                rejection_reason=rejection_reason,
                validation_score=validation_score,
                liquidity_metrics=liquidity_result.get('metrics', {}),
                time_filter_result=time_result['passed'],
                cooldown_result=cooldown_result['passed'],
                validation_details=validation_details
            )

            # Publish validation completed event
            event_type = SignalEventTypes.SIGNAL_VALIDATED.value if is_valid else SignalEventTypes.SIGNAL_REJECTED.value
            await self.publish_event(
                event_type,
                {
                    'signal_id': signal.signal_id,
                    'symbol': signal.symbol,
                    'strategy': signal.strategy_name,
                    'is_valid': is_valid,
                    'validation_score': validation_score,
                    'rejection_reason': rejection_reason
                }
            )

            return result

        except Exception as e:
            self.logger.error(f"Error validating signal {signal.signal_id}: {e}")
            return SignalValidationResult(
                is_valid=False,
                rejection_reason=f"Validation error: {str(e)}",
                validation_score=0.0,
                validation_details={'error': str(e)}
            )

    def _validate_confidence(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate signal confidence"""
        try:
            passed = signal.confidence >= self.config.min_confidence

            return {
                'passed': passed,
                'confidence': signal.confidence,
                'min_required': self.config.min_confidence,
                'reason': f"Confidence {signal.confidence:.3f} below minimum {self.config.min_confidence}" if not passed else None
            }

        except Exception as e:
            return {
                'passed': False,
                'confidence': 0.0,
                'reason': f"Confidence validation error: {str(e)}"
            }

    def _validate_time_filters(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate time-based filters"""
        try:
            if not self.config.market_hours_only:
                return {'passed': True, 'reason': None}

            signal_time = signal.timestamp.time()

            # Parse market hours
            market_open = time.fromisoformat(self.config.market_open_time)
            market_close = time.fromisoformat(self.config.market_close_time)

            # Adjust for avoid periods
            adjusted_open = time(
                hour=market_open.hour,
                minute=market_open.minute + self.config.avoid_first_minutes
            )
            adjusted_close = time(
                hour=market_close.hour,
                minute=market_close.minute - self.config.avoid_last_minutes
            )

            # Check if signal is within allowed time
            passed = adjusted_open <= signal_time <= adjusted_close

            return {
                'passed': passed,
                'signal_time': signal_time.isoformat(),
                'market_open': market_open.isoformat(),
                'market_close': market_close.isoformat(),
                'adjusted_open': adjusted_open.isoformat(),
                'adjusted_close': adjusted_close.isoformat(),
                'reason': f"Signal time {signal_time} outside market hours {adjusted_open}-{adjusted_close}" if not passed else None
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f"Time filter validation error: {str(e)}"
            }

    def _validate_signal_quality(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate signal quality metrics"""
        try:
            reasons = []

            # Risk-reward ratio check
            if signal.risk_reward_ratio < self.config.min_risk_reward_ratio:
                reasons.append(f"RR ratio {signal.risk_reward_ratio:.2f} below minimum {self.config.min_risk_reward_ratio}")

            # Stop loss percentage check
            stop_loss_pct = abs(signal.entry_price - signal.stop_loss) / signal.entry_price * 100
            if stop_loss_pct > self.config.max_stop_loss_percent:
                reasons.append(f"Stop loss {stop_loss_pct:.2f}% exceeds maximum {self.config.max_stop_loss_percent}%")

            # Profit target percentage check
            profit_target_pct = abs(signal.take_profit - signal.entry_price) / signal.entry_price * 100
            if profit_target_pct < self.config.min_profit_target_percent:
                reasons.append(f"Profit target {profit_target_pct:.2f}% below minimum {self.config.min_profit_target_percent}%")

            passed = len(reasons) == 0

            return {
                'passed': passed,
                'risk_reward_ratio': signal.risk_reward_ratio,
                'stop_loss_percent': stop_loss_pct,
                'profit_target_percent': profit_target_pct,
                'reason': "; ".join(reasons) if reasons else None
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f"Quality validation error: {str(e)}"
            }

    def _validate_cooldown(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate signal cooldown periods"""
        try:
            symbol_strategy_key = f"{signal.symbol}_{signal.strategy_name}"

            # Check last signal time for this symbol-strategy combination
            if symbol_strategy_key in self.signal_history:
                last_signal_time = self.signal_history[symbol_strategy_key]
                time_diff = (signal.timestamp - last_signal_time).total_seconds() / 60

                if time_diff < self.config.minutes_between_signals:
                    return {
                        'passed': False,
                        'time_since_last_signal_minutes': time_diff,
                        'required_cooldown_minutes': self.config.minutes_between_signals,
                        'reason': f"Cooldown violation: {time_diff:.1f}min < {self.config.minutes_between_signals}min required"
                    }

            # Check daily signal limits
            today = signal.timestamp.date()
            symbol_today_key = f"{signal.symbol}_{today}"
            strategy_today_key = f"{signal.strategy_name}_{today}"

            symbol_count = self.daily_signal_counts.get(symbol_today_key, 0)
            strategy_count = self.strategy_signal_counts.get(strategy_today_key, 0)

            if symbol_count >= self.config.max_signals_per_symbol_per_day:
                return {
                    'passed': False,
                    'symbol_signals_today': symbol_count,
                    'max_symbol_signals': self.config.max_signals_per_symbol_per_day,
                    'reason': f"Daily symbol limit exceeded: {symbol_count}/{self.config.max_signals_per_symbol_per_day}"
                }

            if strategy_count >= self.config.max_signals_per_strategy_per_day:
                return {
                    'passed': False,
                    'strategy_signals_today': strategy_count,
                    'max_strategy_signals': self.config.max_signals_per_strategy_per_day,
                    'reason': f"Daily strategy limit exceeded: {strategy_count}/{self.config.max_signals_per_strategy_per_day}"
                }

            return {
                'passed': True,
                'symbol_signals_today': symbol_count,
                'strategy_signals_today': strategy_count,
                'reason': None
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f"Cooldown validation error: {str(e)}"
            }

    def _validate_market_regime(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate market regime compatibility"""
        try:
            if not self.config.enable_regime_filters:
                return {'passed': True, 'reason': None}

            regime_str = signal.market_regime.value if hasattr(signal.market_regime, 'value') else str(signal.market_regime)

            passed = regime_str in self.config.allowed_regimes

            return {
                'passed': passed,
                'current_regime': regime_str,
                'allowed_regimes': self.config.allowed_regimes,
                'reason': f"Market regime '{regime_str}' not in allowed regimes {self.config.allowed_regimes}" if not passed else None
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f"Regime validation error: {str(e)}"
            }

    async def _validate_liquidity(self, signal: TradingSignal,
                                market_depth: Optional[MarketDepthData] = None) -> Dict[str, Any]:
        """Validate liquidity requirements"""
        try:
            if not self.config.enable_liquidity_checks:
                return {'passed': True, 'reason': None, 'metrics': {}}

            # Analyze liquidity
            liquidity_metrics = await self.liquidity_analyzer.analyze_liquidity(
                signal.symbol, signal.quantity, market_depth
            )

            passed = liquidity_metrics['is_liquid']
            reason = None

            if not passed:
                reasons = []
                if not liquidity_metrics['volume_check']:
                    reasons.append("insufficient volume")
                if not liquidity_metrics['spread_check']:
                    reasons.append("spread too wide")
                if not liquidity_metrics['depth_check']:
                    reasons.append("insufficient market depth")

                reason = f"Liquidity issues: {', '.join(reasons)}"

            return {
                'passed': passed,
                'metrics': liquidity_metrics,
                'reason': reason
            }

        except Exception as e:
            return {
                'passed': False,
                'metrics': {},
                'reason': f"Liquidity validation error: {str(e)}"
            }

    async def _validate_with_risk_agent(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validate signal with risk management agent"""
        try:
            if not self.risk_agent:
                return {'passed': True, 'reason': 'Risk agent not available'}

            # This would integrate with the actual risk management agent
            # For now, simulate risk validation

            # Basic risk checks
            position_risk_pct = (signal.risk_amount / 100000) * 100  # Assuming 100k capital

            if position_risk_pct > 2.0:  # 2% max risk per position
                return {
                    'passed': False,
                    'position_risk_percent': position_risk_pct,
                    'reason': f"Position risk {position_risk_pct:.2f}% exceeds 2% limit"
                }

            return {
                'passed': True,
                'position_risk_percent': position_risk_pct,
                'reason': None
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f"Risk agent validation error: {str(e)}"
            }

    def _update_signal_tracking(self, signal: TradingSignal):
        """Update signal tracking for cooldown management"""
        try:
            symbol_strategy_key = f"{signal.symbol}_{signal.strategy_name}"
            self.signal_history[symbol_strategy_key] = signal.timestamp

            # Update daily counts
            today = signal.timestamp.date()
            symbol_today_key = f"{signal.symbol}_{today}"
            strategy_today_key = f"{signal.strategy_name}_{today}"

            self.daily_signal_counts[symbol_today_key] = self.daily_signal_counts.get(symbol_today_key, 0) + 1
            self.strategy_signal_counts[strategy_today_key] = self.strategy_signal_counts.get(strategy_today_key, 0) + 1

        except Exception as e:
            self.logger.error(f"Error updating signal tracking: {e}")

    def reset_daily_tracking(self):
        """Reset daily tracking (call at start of each trading day)"""
        self.daily_signal_counts.clear()
        self.strategy_signal_counts.clear()
        self.logger.info("Daily signal tracking reset")

    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        return {
            'total_signals_tracked': len(self.signal_history),
            'daily_signal_counts': dict(self.daily_signal_counts),
            'strategy_signal_counts': dict(self.strategy_signal_counts),
            'config': {
                'min_confidence': self.config.min_confidence,
                'cooldown_minutes': self.config.minutes_between_signals,
                'max_signals_per_symbol': self.config.max_signals_per_symbol_per_day,
                'max_signals_per_strategy': self.config.max_signals_per_strategy_per_day
            }
        }