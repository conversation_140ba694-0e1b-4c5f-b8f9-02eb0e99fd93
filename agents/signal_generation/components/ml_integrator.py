#!/usr/bin/env python3
"""
ML Integrator Component
Seamless integration of advanced ML models for signal enhancement
"""

import asyncio
import logging
import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import (
    TradingSignal, SignalInput, StrategyPerformanceMetrics
)
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


@dataclass
class MLIntegratorConfig(ComponentConfig):
    """Configuration for ML Integrator"""

    # Model settings
    enable_ml_enhancement: bool = True
    model_types: List[str] = None
    model_ensemble_method: str = "weighted_average"  # "weighted_average", "voting", "stacking"

    # Prediction settings
    confidence_enhancement_enabled: bool = True
    direction_prediction_enabled: bool = True
    profitability_prediction_enabled: bool = True
    volatility_prediction_enabled: bool = True

    # Feature engineering
    feature_window_size: int = 50
    technical_indicators_enabled: bool = True
    market_regime_features_enabled: bool = True
    volume_profile_features_enabled: bool = True

    # Model performance
    model_update_frequency_hours: int = 24
    min_prediction_confidence: float = 0.6
    prediction_timeout_seconds: float = 10.0

    # Integration settings
    async_prediction_enabled: bool = True
    fallback_on_ml_failure: bool = True
    cache_predictions: bool = True
    cache_ttl_minutes: int = 30

    def __post_init__(self):
        if self.model_types is None:
            self.model_types = [
                "transformer_signal_enhancer",
                "ensemble_manager",
                "volatility_predictor",
                "regime_classifier"
            ]


class FeatureEngineer:
    """
    Feature engineering for ML models
    """

    def __init__(self, config: MLIntegratorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.FeatureEngineer")

    def engineer_features(self, signal_input: SignalInput,
                         strategy_performance: Optional[StrategyPerformanceMetrics] = None) -> Dict[str, Any]:
        """
        Engineer features for ML model input
        """
        try:
            features = {}

            # Basic signal features
            features.update(self._extract_basic_features(signal_input))

            # Technical indicator features
            if self.config.technical_indicators_enabled:
                features.update(self._extract_technical_features(signal_input))

            # Market regime features
            if self.config.market_regime_features_enabled:
                features.update(self._extract_regime_features(signal_input))

            # Volume profile features
            if self.config.volume_profile_features_enabled:
                features.update(self._extract_volume_features(signal_input))

            # Strategy performance features
            if strategy_performance:
                features.update(self._extract_performance_features(strategy_performance))

            # Time-based features
            features.update(self._extract_time_features(signal_input))

            return features

        except Exception as e:
            self.logger.error(f"Error engineering features: {e}")
            return {}

    def _extract_basic_features(self, signal_input: SignalInput) -> Dict[str, Any]:
        """Extract basic market features"""
        try:
            if not signal_input.ohlcv_data:
                return {}

            latest_candle = signal_input.ohlcv_data[-1]

            # Price features
            features = {
                'current_price': latest_candle.close,
                'price_change': latest_candle.close - latest_candle.open,
                'price_change_pct': (latest_candle.close - latest_candle.open) / latest_candle.open * 100,
                'high_low_range': latest_candle.high - latest_candle.low,
                'high_low_range_pct': (latest_candle.high - latest_candle.low) / latest_candle.close * 100,
                'volume': latest_candle.volume,
                'timestamp_hour': latest_candle.timestamp.hour,
                'timestamp_minute': latest_candle.timestamp.minute
            }

            # Multi-period features
            if len(signal_input.ohlcv_data) >= 5:
                recent_closes = [c.close for c in signal_input.ohlcv_data[-5:]]
                features.update({
                    'price_trend_5': (recent_closes[-1] - recent_closes[0]) / recent_closes[0] * 100,
                    'price_volatility_5': np.std(recent_closes) / np.mean(recent_closes) * 100
                })

            if len(signal_input.ohlcv_data) >= 20:
                recent_closes = [c.close for c in signal_input.ohlcv_data[-20:]]
                recent_volumes = [c.volume for c in signal_input.ohlcv_data[-20:]]
                features.update({
                    'price_trend_20': (recent_closes[-1] - recent_closes[0]) / recent_closes[0] * 100,
                    'price_volatility_20': np.std(recent_closes) / np.mean(recent_closes) * 100,
                    'volume_trend_20': (recent_volumes[-1] - np.mean(recent_volumes[:-1])) / np.mean(recent_volumes[:-1]) * 100
                })

            return features

        except Exception as e:
            self.logger.error(f"Error extracting basic features: {e}")
            return {}

    def _extract_technical_features(self, signal_input: SignalInput) -> Dict[str, Any]:
        """Extract technical indicator features"""
        try:
            indicators = signal_input.indicators
            features = {}

            # Moving averages
            if hasattr(indicators, 'ema_5') and indicators.ema_5:
                features['ema_5'] = indicators.ema_5
            if hasattr(indicators, 'ema_20') and indicators.ema_20:
                features['ema_20'] = indicators.ema_20
                if indicators.ema_5:
                    features['ema_5_20_ratio'] = indicators.ema_5 / indicators.ema_20

            # Oscillators
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                features['rsi_14'] = indicators.rsi_14
                features['rsi_overbought'] = 1 if indicators.rsi_14 > 70 else 0
                features['rsi_oversold'] = 1 if indicators.rsi_14 < 30 else 0

            if hasattr(indicators, 'stoch_k') and indicators.stoch_k:
                features['stoch_k'] = indicators.stoch_k
            if hasattr(indicators, 'stoch_d') and indicators.stoch_d:
                features['stoch_d'] = indicators.stoch_d

            # MACD
            if hasattr(indicators, 'macd') and indicators.macd:
                features['macd'] = indicators.macd
            if hasattr(indicators, 'macd_signal') and indicators.macd_signal:
                features['macd_signal'] = indicators.macd_signal
                if indicators.macd:
                    features['macd_histogram'] = indicators.macd - indicators.macd_signal

            # Volatility
            if hasattr(indicators, 'atr') and indicators.atr:
                features['atr'] = indicators.atr
                if signal_input.ohlcv_data:
                    current_price = signal_input.ohlcv_data[-1].close
                    features['atr_pct'] = indicators.atr / current_price * 100

            return features

        except Exception as e:
            self.logger.error(f"Error extracting technical features: {e}")
            return {}

    def _extract_regime_features(self, signal_input: SignalInput) -> Dict[str, Any]:
        """Extract market regime features"""
        try:
            features = {}

            if signal_input.market_regime:
                regime_str = signal_input.market_regime.value if hasattr(signal_input.market_regime, 'value') else str(signal_input.market_regime)

                # One-hot encode market regime
                features['regime_bull'] = 1 if regime_str == 'bull' else 0
                features['regime_bear'] = 1 if regime_str == 'bear' else 0
                features['regime_sideways'] = 1 if regime_str == 'sideways' else 0
                features['regime_volatile'] = 1 if regime_str == 'volatile' else 0

            return features

        except Exception as e:
            self.logger.error(f"Error extracting regime features: {e}")
            return {}

    def _extract_volume_features(self, signal_input: SignalInput) -> Dict[str, Any]:
        """Extract volume profile features"""
        try:
            features = {}

            if not signal_input.ohlcv_data or len(signal_input.ohlcv_data) < 10:
                return features

            # Volume analysis
            recent_volumes = [c.volume for c in signal_input.ohlcv_data[-10:]]
            current_volume = signal_input.ohlcv_data[-1].volume
            avg_volume = np.mean(recent_volumes[:-1])

            features.update({
                'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1.0,
                'volume_spike': 1 if current_volume > avg_volume * 2 else 0,
                'volume_trend': (current_volume - recent_volumes[0]) / recent_volumes[0] * 100 if recent_volumes[0] > 0 else 0
            })

            return features

        except Exception as e:
            self.logger.error(f"Error extracting volume features: {e}")
            return {}

    def _extract_performance_features(self, performance: StrategyPerformanceMetrics) -> Dict[str, Any]:
        """Extract strategy performance features"""
        try:
            return {
                'win_rate': performance.win_rate,
                'total_return': performance.total_return,
                'sharpe_ratio': performance.sharpe_ratio,
                'max_drawdown': performance.max_drawdown,
                'recent_performance': performance.recent_return,
                'performance_score': performance.performance_score
            }
        except Exception as e:
            self.logger.error(f"Error extracting performance features: {e}")
            return {}

    def _extract_time_features(self, signal_input: SignalInput) -> Dict[str, Any]:
        """Extract time-based features"""
        try:
            timestamp = signal_input.timestamp
            return {
                'hour_of_day': timestamp.hour,
                'minute_of_hour': timestamp.minute,
                'day_of_week': timestamp.weekday(),
                'is_market_open': 1 if 9 <= timestamp.hour <= 15 else 0
            }
        except Exception as e:
            self.logger.error(f"Error extracting time features: {e}")
            return {}


class MLIntegrator(BaseSignalComponent):
    """
    ML Integrator Component for seamless ML model integration
    """

    def __init__(self, config: MLIntegratorConfig, event_bus=None):
        super().__init__("MLIntegrator", config, event_bus)
        self.config = config
        self.feature_engineer = FeatureEngineer(config)

    async def _initialize_component(self) -> bool:
        """Initialize the ML integrator"""
        try:
            self.logger.info("Initializing ML Integrator...")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize ML Integrator: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup ML integrator resources"""
        try:
            self.logger.info("ML Integrator cleaned up")
        except Exception as e:
            self.logger.error(f"Error cleaning up ML Integrator: {e}")

    async def enhance_signal(self, signal: TradingSignal,
                           signal_input: SignalInput) -> TradingSignal:
        """Enhance signal using ML models"""
        try:
            # For now, return enhanced signal with improved confidence
            enhanced_signal = signal
            if self.config.confidence_enhancement_enabled:
                enhanced_signal.confidence = min(1.0, signal.confidence * 1.1)

            return enhanced_signal

        except Exception as e:
            self.logger.error(f"Error enhancing signal: {e}")
            return signal