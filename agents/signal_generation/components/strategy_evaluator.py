#!/usr/bin/env python3
"""
Strategy Evaluator Component
Enhanced strategy evaluation engine with native Polars expression evaluation
"""

import asyncio
import logging
import polars as pl
import ast
import operator
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import (
    SignalInput, StrategyEvaluationResult, SignalType, MarketIndicators
)
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


@dataclass
class StrategyEvaluatorConfig(ComponentConfig):
    """Configuration for Strategy Evaluator"""
    enable_compilation: bool = True
    cache_compiled_expressions: bool = True
    expression_timeout_seconds: float = 5.0
    enable_safety_checks: bool = True
    max_expression_complexity: int = 100
    enable_parallel_evaluation: bool = True

    # Allowed functions and operators
    allowed_functions: List[str] = None
    allowed_operators: List[str] = None

    def __post_init__(self):
        if self.allowed_functions is None:
            self.allowed_functions = [
                'rolling', 'shift', 'mean', 'std', 'max', 'min', 'sum',
                'abs', 'round', 'floor', 'ceil', 'log', 'exp', 'sqrt'
            ]
        if self.allowed_operators is None:
            self.allowed_operators = [
                '>', '<', '>=', '<=', '==', '!=', '&', '|', '+', '-', '*', '/', '%'
            ]


class PolarsExpressionEvaluator:
    """
    Native Polars expression evaluator for strategy conditions
    Replaces the simplified pandas fallback approach
    """

    def __init__(self, config: StrategyEvaluatorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.PolarsExpressionEvaluator")

        # Compiled expression cache
        self.compiled_cache = {} if config.cache_compiled_expressions else None

        # Safe operators mapping
        self.safe_operators = {
            ast.Add: operator.add,
            ast.Sub: operator.sub,
            ast.Mult: operator.mul,
            ast.Div: operator.truediv,
            ast.Mod: operator.mod,
            ast.Pow: operator.pow,
            ast.Lt: operator.lt,
            ast.LtE: operator.le,
            ast.Gt: operator.gt,
            ast.GtE: operator.ge,
            ast.Eq: operator.eq,
            ast.NotEq: operator.ne,
            ast.And: operator.and_,
            ast.Or: operator.or_,
            ast.Not: operator.not_,
            ast.USub: operator.neg,
            ast.UAdd: operator.pos,
        }

        # Safe functions mapping
        self.safe_functions = {
            'abs': abs,
            'round': round,
            'min': min,
            'max': max,
            'sum': sum,
            'len': len,
        }

    def evaluate_condition(self, df: pl.DataFrame, condition: str) -> bool:
        """
        Evaluate strategy condition using native Polars expressions
        """
        try:
            if not condition or len(df) == 0:
                return False

            # Check cache first
            if self.compiled_cache and condition in self.compiled_cache:
                compiled_expr = self.compiled_cache[condition]
                return self._execute_compiled_expression(df, compiled_expr)

            # Parse and validate expression
            if self.config.enable_safety_checks:
                if not self._validate_expression_safety(condition):
                    self.logger.warning(f"Unsafe expression rejected: {condition}")
                    return False

            # Convert to Polars-compatible expression
            polars_expr = self._convert_to_polars_expression(condition)

            # Execute expression
            result = self._execute_polars_expression(df, polars_expr)

            # Cache compiled expression
            if self.compiled_cache:
                self.compiled_cache[condition] = polars_expr

            return result

        except Exception as e:
            self.logger.error(f"Error evaluating condition '{condition}': {e}")
            return False

    def _validate_expression_safety(self, expression: str) -> bool:
        """Validate expression for safety"""
        try:
            # Parse AST
            tree = ast.parse(expression, mode='eval')

            # Check complexity
            node_count = sum(1 for _ in ast.walk(tree))
            if node_count > self.config.max_expression_complexity:
                return False

            # Check for dangerous operations
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if hasattr(node.func, 'id'):
                        func_name = node.func.id
                        if func_name not in self.config.allowed_functions:
                            return False
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    return False
                elif isinstance(node, ast.Attribute):
                    # Allow only specific attributes
                    if not self._is_safe_attribute(node):
                        return False

            return True

        except Exception:
            return False

    def _is_safe_attribute(self, node: ast.Attribute) -> bool:
        """Check if attribute access is safe"""
        safe_attributes = [
            'rolling', 'shift', 'mean', 'std', 'max', 'min', 'sum',
            'abs', 'round', 'floor', 'ceil'
        ]
        return node.attr in safe_attributes

    def _convert_to_polars_expression(self, condition: str) -> str:
        """Convert condition to Polars-compatible expression"""
        # Replace common patterns
        polars_condition = condition

        # Replace pandas-style rolling operations
        polars_condition = polars_condition.replace('.rolling(', '.rolling(window=')

        # Replace boolean operators
        polars_condition = polars_condition.replace(' & ', ' and ')
        polars_condition = polars_condition.replace(' | ', ' or ')

        # Handle column references - ensure they're properly quoted
        # This is a simplified approach - in production, you'd want more sophisticated parsing

        return polars_condition

    def _execute_polars_expression(self, df: pl.DataFrame, expression: str) -> bool:
        """Execute Polars expression and return boolean result"""
        try:
            # Get the last row for evaluation
            last_row = df.tail(1)

            if len(last_row) == 0:
                return False

            # Create evaluation context with column values
            eval_context = {}
            for col in df.columns:
                if col in last_row.columns:
                    eval_context[col] = last_row[col].item()

            # Add rolling and other operations context
            eval_context.update(self._create_rolling_context(df))

            # Evaluate expression
            result = eval(expression, {"__builtins__": {}}, eval_context)

            return bool(result)

        except Exception as e:
            self.logger.error(f"Error executing Polars expression: {e}")
            return False

    def _create_rolling_context(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Create context for rolling operations"""
        context = {}

        # Add common rolling operations
        for col in df.columns:
            if col in ['close', 'high', 'low', 'open', 'volume']:
                try:
                    # Add rolling means
                    context[f"{col}_rolling_mean_5"] = df[col].rolling(window=5).mean().tail(1).item()
                    context[f"{col}_rolling_mean_10"] = df[col].rolling(window=10).mean().tail(1).item()
                    context[f"{col}_rolling_mean_20"] = df[col].rolling(window=20).mean().tail(1).item()

                    # Add rolling std
                    context[f"{col}_rolling_std_20"] = df[col].rolling(window=20).std().tail(1).item()

                except Exception:
                    pass  # Skip if not enough data

        return context

    def _execute_compiled_expression(self, df: pl.DataFrame, compiled_expr: str) -> bool:
        """Execute cached compiled expression"""
        return self._execute_polars_expression(df, compiled_expr)


class StrategyEvaluator(BaseSignalComponent):
    """
    Enhanced Strategy Evaluator Component

    Features:
    - Native Polars expression evaluation
    - Expression compilation and caching
    - Safety validation
    - Performance tracking
    - Parallel evaluation support
    """

    def __init__(self, config: StrategyEvaluatorConfig, event_bus=None):
        super().__init__("StrategyEvaluator", config, event_bus)
        self.config = config

        # Initialize expression evaluator
        self.expression_evaluator = PolarsExpressionEvaluator(config)

        # Strategy cache
        self.strategy_cache = {}
        self.performance_cache = {}

    async def _initialize_component(self) -> bool:
        """Initialize the strategy evaluator"""
        try:
            self.logger.info("Initializing Strategy Evaluator...")

            # Initialize expression evaluator
            if self.config.enable_compilation:
                self.logger.info("Expression compilation enabled")

            if self.config.enable_parallel_evaluation:
                self.logger.info("Parallel evaluation enabled")

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Strategy Evaluator: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup strategy evaluator resources"""
        try:
            # Clear caches
            if self.expression_evaluator.compiled_cache:
                self.expression_evaluator.compiled_cache.clear()

            self.strategy_cache.clear()
            self.performance_cache.clear()

            self.logger.info("Strategy Evaluator cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Strategy Evaluator: {e}")

    async def evaluate_strategy(self, signal_input: SignalInput, strategy: Dict[str, Any]) -> StrategyEvaluationResult:
        """
        Evaluate a single strategy against market data
        """
        return await self.process_with_retry(
            "evaluate_strategy",
            self._evaluate_strategy_internal,
            signal_input,
            strategy
        )

    async def _evaluate_strategy_internal(self, signal_input: SignalInput, strategy: Dict[str, Any]) -> StrategyEvaluationResult:
        """Internal strategy evaluation logic"""
        start_time = datetime.now()
        strategy_name = strategy.get('name', 'unknown')

        try:
            # Publish evaluation started event
            await self.publish_event(
                SignalEventTypes.STRATEGY_EVALUATION_STARTED.value,
                {
                    'strategy_name': strategy_name,
                    'symbol': signal_input.symbol,
                    'timestamp': signal_input.timestamp.isoformat()
                }
            )

            # Create DataFrame from signal input
            df = self._create_dataframe_from_input(signal_input)

            if len(df) < 50:  # Need sufficient data
                return StrategyEvaluationResult(
                    strategy_name=strategy_name,
                    symbol=signal_input.symbol,
                    timestamp=signal_input.timestamp,
                    confidence_score=0.0,
                    condition_details={'error': 'Insufficient data'}
                )

            # Evaluate conditions
            long_condition = False
            short_condition = False
            exit_condition = False

            # Evaluate long condition
            long_expr = strategy.get('long', strategy.get('entry_long', ''))
            if long_expr:
                long_condition = self.expression_evaluator.evaluate_condition(df, long_expr)

            # Evaluate short condition
            short_expr = strategy.get('short', strategy.get('entry_short', ''))
            if short_expr:
                short_condition = self.expression_evaluator.evaluate_condition(df, short_expr)

            # Evaluate exit conditions
            exit_long_expr = strategy.get('long_exit', strategy.get('exit_long', ''))
            exit_short_expr = strategy.get('short_exit', strategy.get('exit_short', ''))

            if exit_long_expr or exit_short_expr:
                exit_condition = (
                    self.expression_evaluator.evaluate_condition(df, exit_long_expr) if exit_long_expr else False
                ) or (
                    self.expression_evaluator.evaluate_condition(df, exit_short_expr) if exit_short_expr else False
                )

            # Calculate signal strength and confidence
            signal_strength = self._calculate_signal_strength(df, strategy, long_condition, short_condition)
            confidence_score = self._calculate_confidence_score(df, strategy, signal_input.market_regime)

            # Get historical performance
            historical_performance = self._get_historical_performance(strategy_name, signal_input.symbol)

            # Calculate evaluation time
            evaluation_time_ms = (datetime.now() - start_time).total_seconds() * 1000

            result = StrategyEvaluationResult(
                strategy_name=strategy_name,
                symbol=signal_input.symbol,
                timestamp=signal_input.timestamp,
                long_condition=long_condition,
                short_condition=short_condition,
                exit_condition=exit_condition,
                signal_strength=signal_strength,
                confidence_score=confidence_score,
                historical_performance=historical_performance,
                condition_details={
                    'long_expr': long_expr,
                    'short_expr': short_expr,
                    'exit_long_expr': exit_long_expr,
                    'exit_short_expr': exit_short_expr,
                    'data_points': len(df)
                },
                evaluation_time_ms=evaluation_time_ms
            )

            # Publish evaluation completed event
            await self.publish_event(
                SignalEventTypes.STRATEGY_EVALUATION_COMPLETED.value,
                {
                    'strategy_name': strategy_name,
                    'symbol': signal_input.symbol,
                    'result': {
                        'long_condition': long_condition,
                        'short_condition': short_condition,
                        'signal_strength': signal_strength,
                        'confidence_score': confidence_score
                    }
                }
            )

            return result

        except Exception as e:
            self.logger.error(f"Error evaluating strategy {strategy_name}: {e}")
            return StrategyEvaluationResult(
                strategy_name=strategy_name,
                symbol=signal_input.symbol,
                timestamp=signal_input.timestamp,
                confidence_score=0.0,
                condition_details={'error': str(e)}
            )

    def _create_dataframe_from_input(self, signal_input: SignalInput) -> pl.DataFrame:
        """Create Polars DataFrame from signal input"""
        try:
            # Convert OHLCV data
            df_data = {
                'timestamp': [candle.timestamp for candle in signal_input.ohlcv_data],
                'open': [candle.open for candle in signal_input.ohlcv_data],
                'high': [candle.high for candle in signal_input.ohlcv_data],
                'low': [candle.low for candle in signal_input.ohlcv_data],
                'close': [candle.close for candle in signal_input.ohlcv_data],
                'volume': [candle.volume for candle in signal_input.ohlcv_data]
            }

            df = pl.DataFrame(df_data)

            # Add indicators
            indicators = signal_input.indicators
            indicator_columns = {}

            # Add all available indicators
            for attr_name in dir(indicators):
                if not attr_name.startswith('_') and attr_name not in ['symbol', 'timestamp']:
                    value = getattr(indicators, attr_name)
                    if value is not None and isinstance(value, (int, float)):
                        indicator_columns[attr_name] = value

            # Add indicator columns (broadcast to all rows)
            for name, value in indicator_columns.items():
                df = df.with_columns(pl.lit(value).alias(name))

            return df

        except Exception as e:
            self.logger.error(f"Error creating DataFrame from input: {e}")
            return pl.DataFrame()

    def _calculate_signal_strength(self, df: pl.DataFrame, strategy: Dict[str, Any],
                                 long_condition: bool, short_condition: bool) -> float:
        """Calculate signal strength based on conditions and market data"""
        try:
            if not long_condition and not short_condition:
                return 0.0

            strength = 0.5  # Base strength

            # Adjust based on volume
            if 'volume' in df.columns and len(df) > 20:
                current_volume = df['volume'].tail(1).item()
                avg_volume = df['volume'].tail(20).mean()

                if current_volume > avg_volume * 1.5:
                    strength += 0.2
                elif current_volume < avg_volume * 0.5:
                    strength -= 0.1

            # Adjust based on volatility
            if 'atr' in df.columns:
                atr = df['atr'].tail(1).item()
                if atr > 0:
                    # Higher volatility can increase signal strength for breakout strategies
                    volatility_factor = min(0.2, atr / df['close'].tail(1).item() * 10)
                    strength += volatility_factor

            # Adjust based on trend alignment
            if 'ema_5' in df.columns and 'ema_20' in df.columns:
                ema_5 = df['ema_5'].tail(1).item()
                ema_20 = df['ema_20'].tail(1).item()

                if long_condition and ema_5 > ema_20:
                    strength += 0.1
                elif short_condition and ema_5 < ema_20:
                    strength += 0.1

            return max(0.0, min(1.0, strength))

        except Exception as e:
            self.logger.error(f"Error calculating signal strength: {e}")
            return 0.5

    def _calculate_confidence_score(self, df: pl.DataFrame, strategy: Dict[str, Any],
                                  market_regime) -> float:
        """Calculate confidence score for the strategy"""
        try:
            confidence = 0.5  # Base confidence

            # Adjust based on historical performance
            strategy_name = strategy.get('name', '')
            if strategy_name in self.performance_cache:
                perf = self.performance_cache[strategy_name]
                win_rate = perf.get('win_rate', 0.5)
                sharpe_ratio = perf.get('sharpe_ratio', 0.0)

                # Adjust based on win rate
                confidence += (win_rate - 0.5) * 0.4

                # Adjust based on Sharpe ratio
                if sharpe_ratio > 1.0:
                    confidence += 0.1
                elif sharpe_ratio < 0:
                    confidence -= 0.2

            # Adjust based on market regime
            if market_regime:
                regime_confidence = {
                    'bull': 0.7,
                    'bear': 0.6,
                    'sideways': 0.5,
                    'volatile': 0.4
                }.get(market_regime.value if hasattr(market_regime, 'value') else str(market_regime), 0.5)

                confidence = (confidence + regime_confidence) / 2

            # Adjust based on data quality
            if len(df) > 100:
                confidence += 0.05
            elif len(df) < 50:
                confidence -= 0.1

            return max(0.0, min(1.0, confidence))

        except Exception as e:
            self.logger.error(f"Error calculating confidence score: {e}")
            return 0.5

    def _get_historical_performance(self, strategy_name: str, symbol: str) -> Dict[str, float]:
        """Get historical performance metrics for strategy"""
        try:
            cache_key = f"{strategy_name}_{symbol}"

            if cache_key in self.performance_cache:
                return self.performance_cache[cache_key]

            # In a real implementation, this would query a database
            # For now, return default values
            default_performance = {
                'total_trades': 0,
                'win_rate': 0.5,
                'total_return': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'profit_factor': 1.0
            }

            # Cache the result
            self.performance_cache[cache_key] = default_performance

            return default_performance

        except Exception as e:
            self.logger.error(f"Error getting historical performance: {e}")
            return {}

    async def evaluate_multiple_strategies(self, signal_input: SignalInput,
                                         strategies: List[Dict[str, Any]]) -> List[StrategyEvaluationResult]:
        """Evaluate multiple strategies in parallel"""
        try:
            if self.config.enable_parallel_evaluation and len(strategies) > 1:
                # Evaluate strategies in parallel
                tasks = [
                    self.evaluate_strategy(signal_input, strategy)
                    for strategy in strategies
                ]

                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Filter out exceptions
                valid_results = []
                for result in results:
                    if isinstance(result, StrategyEvaluationResult):
                        valid_results.append(result)
                    else:
                        self.logger.error(f"Strategy evaluation failed: {result}")

                return valid_results
            else:
                # Evaluate strategies sequentially
                results = []
                for strategy in strategies:
                    result = await self.evaluate_strategy(signal_input, strategy)
                    results.append(result)

                return results

        except Exception as e:
            self.logger.error(f"Error evaluating multiple strategies: {e}")
            return []

    def get_expression_cache_stats(self) -> Dict[str, Any]:
        """Get expression cache statistics"""
        if not self.expression_evaluator.compiled_cache:
            return {'cache_enabled': False}

        return {
            'cache_enabled': True,
            'cached_expressions': len(self.expression_evaluator.compiled_cache),
            'cache_keys': list(self.expression_evaluator.compiled_cache.keys())
        }

    def clear_caches(self):
        """Clear all caches"""
        if self.expression_evaluator.compiled_cache:
            self.expression_evaluator.compiled_cache.clear()

        self.strategy_cache.clear()
        self.performance_cache.clear()

        self.logger.info("All caches cleared")