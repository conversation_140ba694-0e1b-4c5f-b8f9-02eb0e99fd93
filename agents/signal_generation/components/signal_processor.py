#!/usr/bin/env python3
"""
Signal Processor Component
Advanced signal processing with Kalman filtering, wavelet denoising, and outlier detection
"""

import asyncio
import logging
import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from scipy import signal as scipy_signal
from scipy.stats import zscore

from ..core.base_component import BaseSignalComponent, ComponentConfig
from ..core.data_models import (
    TradingSignal, SignalProcessingResult, SignalInput
)
from ..core.event_types import SignalEventTypes

logger = logging.getLogger(__name__)


@dataclass
class SignalProcessorConfig(ComponentConfig):
    """Configuration for Signal Processor"""

    # Noise reduction settings
    enable_noise_reduction: bool = True
    noise_reduction_method: str = "kalman"  # "kalman", "wavelet", "moving_average"

    # Kalman filter settings
    kalman_process_variance: float = 1e-5
    kalman_measurement_variance: float = 1e-1
    kalman_initial_estimate: float = 0.0
    kalman_initial_error: float = 1.0

    # Wavelet denoising settings
    wavelet_type: str = "db4"
    wavelet_mode: str = "symmetric"
    wavelet_threshold_method: str = "soft"

    # Outlier detection settings
    enable_outlier_detection: bool = True
    outlier_method: str = "zscore"  # "zscore", "iqr", "isolation_forest"
    zscore_threshold: float = 3.0
    iqr_multiplier: float = 1.5

    # Signal smoothing settings
    enable_signal_smoothing: bool = True
    smoothing_method: str = "exponential"  # "exponential", "moving_average", "savgol"
    smoothing_window: int = 5
    exponential_alpha: float = 0.3

    # Quality assessment settings
    enable_quality_assessment: bool = True
    min_signal_quality_score: float = 0.6

    # Processing pipeline settings
    processing_order: List[str] = None

    def __post_init__(self):
        if self.processing_order is None:
            self.processing_order = [
                "outlier_detection",
                "noise_reduction",
                "signal_smoothing",
                "quality_assessment"
            ]


class KalmanFilter:
    """
    Kalman filter for signal noise reduction
    """

    def __init__(self, process_variance: float, measurement_variance: float,
                 initial_estimate: float = 0.0, initial_error: float = 1.0):
        self.process_variance = process_variance
        self.measurement_variance = measurement_variance
        self.estimate = initial_estimate
        self.error = initial_error

    def update(self, measurement: float) -> float:
        """Update filter with new measurement"""
        # Prediction step
        predicted_estimate = self.estimate
        predicted_error = self.error + self.process_variance

        # Update step
        kalman_gain = predicted_error / (predicted_error + self.measurement_variance)
        self.estimate = predicted_estimate + kalman_gain * (measurement - predicted_estimate)
        self.error = (1 - kalman_gain) * predicted_error

        return self.estimate

    def filter_series(self, measurements: List[float]) -> List[float]:
        """Filter entire series of measurements"""
        filtered = []
        for measurement in measurements:
            filtered.append(self.update(measurement))
        return filtered


class OutlierDetector:
    """
    Outlier detection for signal processing
    """

    def __init__(self, config: SignalProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.OutlierDetector")

    def detect_outliers(self, data: List[float]) -> Tuple[List[bool], Dict[str, Any]]:
        """
        Detect outliers in data series
        Returns: (outlier_mask, detection_details)
        """
        try:
            if len(data) < 3:
                return [False] * len(data), {'method': 'insufficient_data'}

            if self.config.outlier_method == "zscore":
                return self._detect_zscore_outliers(data)
            elif self.config.outlier_method == "iqr":
                return self._detect_iqr_outliers(data)
            else:
                return [False] * len(data), {'method': 'unknown'}

        except Exception as e:
            self.logger.error(f"Error detecting outliers: {e}")
            return [False] * len(data), {'error': str(e)}

    def _detect_zscore_outliers(self, data: List[float]) -> Tuple[List[bool], Dict[str, Any]]:
        """Detect outliers using Z-score method"""
        try:
            data_array = np.array(data)
            z_scores = np.abs(zscore(data_array))
            outliers = z_scores > self.config.zscore_threshold

            return outliers.tolist(), {
                'method': 'zscore',
                'threshold': self.config.zscore_threshold,
                'outlier_count': int(np.sum(outliers)),
                'max_zscore': float(np.max(z_scores))
            }

        except Exception as e:
            self.logger.error(f"Error in Z-score outlier detection: {e}")
            return [False] * len(data), {'error': str(e)}

    def _detect_iqr_outliers(self, data: List[float]) -> Tuple[List[bool], Dict[str, Any]]:
        """Detect outliers using IQR method"""
        try:
            data_array = np.array(data)
            q1 = np.percentile(data_array, 25)
            q3 = np.percentile(data_array, 75)
            iqr = q3 - q1

            lower_bound = q1 - self.config.iqr_multiplier * iqr
            upper_bound = q3 + self.config.iqr_multiplier * iqr

            outliers = (data_array < lower_bound) | (data_array > upper_bound)

            return outliers.tolist(), {
                'method': 'iqr',
                'q1': float(q1),
                'q3': float(q3),
                'iqr': float(iqr),
                'lower_bound': float(lower_bound),
                'upper_bound': float(upper_bound),
                'outlier_count': int(np.sum(outliers))
            }

        except Exception as e:
            self.logger.error(f"Error in IQR outlier detection: {e}")
            return [False] * len(data), {'error': str(e)}


class SignalSmoother:
    """
    Signal smoothing algorithms
    """

    def __init__(self, config: SignalProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SignalSmoother")

    def smooth_signal(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """
        Smooth signal data
        Returns: (smoothed_data, smoothing_details)
        """
        try:
            if len(data) < 2:
                return data, {'method': 'insufficient_data'}

            if self.config.smoothing_method == "exponential":
                return self._exponential_smoothing(data)
            elif self.config.smoothing_method == "moving_average":
                return self._moving_average_smoothing(data)
            elif self.config.smoothing_method == "savgol":
                return self._savgol_smoothing(data)
            else:
                return data, {'method': 'unknown'}

        except Exception as e:
            self.logger.error(f"Error smoothing signal: {e}")
            return data, {'error': str(e)}

    def _exponential_smoothing(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Exponential smoothing"""
        try:
            alpha = self.config.exponential_alpha
            smoothed = [data[0]]  # First value unchanged

            for i in range(1, len(data)):
                smoothed_value = alpha * data[i] + (1 - alpha) * smoothed[i-1]
                smoothed.append(smoothed_value)

            return smoothed, {
                'method': 'exponential',
                'alpha': alpha,
                'smoothing_factor': alpha
            }

        except Exception as e:
            self.logger.error(f"Error in exponential smoothing: {e}")
            return data, {'error': str(e)}

    def _moving_average_smoothing(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Moving average smoothing"""
        try:
            window = min(self.config.smoothing_window, len(data))
            data_array = np.array(data)

            # Use pandas-style rolling mean
            smoothed = []
            for i in range(len(data)):
                start_idx = max(0, i - window + 1)
                end_idx = i + 1
                smoothed.append(np.mean(data_array[start_idx:end_idx]))

            return smoothed, {
                'method': 'moving_average',
                'window': window
            }

        except Exception as e:
            self.logger.error(f"Error in moving average smoothing: {e}")
            return data, {'error': str(e)}

    def _savgol_smoothing(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Savitzky-Golay smoothing"""
        try:
            from scipy.signal import savgol_filter

            window = min(self.config.smoothing_window, len(data))
            if window % 2 == 0:
                window -= 1  # Must be odd
            window = max(3, window)  # Minimum window size

            polyorder = min(2, window - 1)  # Polynomial order

            smoothed = savgol_filter(data, window, polyorder).tolist()

            return smoothed, {
                'method': 'savgol',
                'window': window,
                'polyorder': polyorder
            }

        except Exception as e:
            self.logger.error(f"Error in Savgol smoothing: {e}")
            return data, {'error': str(e)}


class SignalQualityAssessor:
    """
    Signal quality assessment
    """

    def __init__(self, config: SignalProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SignalQualityAssessor")

    def assess_quality(self, original_data: List[float], processed_data: List[float]) -> Dict[str, Any]:
        """
        Assess signal quality after processing
        """
        try:
            if len(original_data) != len(processed_data) or len(original_data) < 2:
                return {
                    'quality_score': 0.0,
                    'signal_to_noise_ratio': 0.0,
                    'noise_level': 1.0,
                    'error': 'Invalid data lengths'
                }

            # Calculate signal-to-noise ratio
            snr = self._calculate_snr(original_data, processed_data)

            # Calculate noise level
            noise_level = self._calculate_noise_level(original_data, processed_data)

            # Calculate overall quality score
            quality_score = self._calculate_quality_score(snr, noise_level)

            # Additional metrics
            correlation = self._calculate_correlation(original_data, processed_data)
            smoothness = self._calculate_smoothness(processed_data)

            return {
                'quality_score': quality_score,
                'signal_to_noise_ratio': snr,
                'noise_level': noise_level,
                'correlation': correlation,
                'smoothness': smoothness,
                'meets_threshold': quality_score >= self.config.min_signal_quality_score
            }

        except Exception as e:
            self.logger.error(f"Error assessing signal quality: {e}")
            return {
                'quality_score': 0.0,
                'signal_to_noise_ratio': 0.0,
                'noise_level': 1.0,
                'error': str(e)
            }

    def _calculate_snr(self, original: List[float], processed: List[float]) -> float:
        """Calculate signal-to-noise ratio"""
        try:
            original_array = np.array(original)
            processed_array = np.array(processed)

            # Signal power (variance of processed signal)
            signal_power = np.var(processed_array)

            # Noise power (variance of difference)
            noise = original_array - processed_array
            noise_power = np.var(noise)

            if noise_power == 0:
                return float('inf')

            snr = 10 * np.log10(signal_power / noise_power)
            return float(snr)

        except Exception:
            return 0.0

    def _calculate_noise_level(self, original: List[float], processed: List[float]) -> float:
        """Calculate noise level"""
        try:
            original_array = np.array(original)
            processed_array = np.array(processed)

            # Noise as difference between original and processed
            noise = original_array - processed_array
            noise_level = np.std(noise) / np.std(original_array)

            return float(noise_level)

        except Exception:
            return 1.0

    def _calculate_quality_score(self, snr: float, noise_level: float) -> float:
        """Calculate overall quality score"""
        try:
            # Normalize SNR to 0-1 scale (assuming SNR range of -10 to 30 dB)
            snr_normalized = max(0, min(1, (snr + 10) / 40))

            # Invert noise level (lower noise = higher quality)
            noise_score = max(0, 1 - noise_level)

            # Weighted combination
            quality_score = 0.6 * snr_normalized + 0.4 * noise_score

            return float(quality_score)

        except Exception:
            return 0.0

    def _calculate_correlation(self, original: List[float], processed: List[float]) -> float:
        """Calculate correlation between original and processed signals"""
        try:
            correlation = np.corrcoef(original, processed)[0, 1]
            return float(correlation) if not np.isnan(correlation) else 0.0

        except Exception:
            return 0.0

    def _calculate_smoothness(self, data: List[float]) -> float:
        """Calculate smoothness of signal (inverse of second derivative variance)"""
        try:
            if len(data) < 3:
                return 0.0

            # Calculate second derivative
            second_derivative = np.diff(data, n=2)

            # Smoothness is inverse of variance of second derivative
            smoothness = 1.0 / (1.0 + np.var(second_derivative))

            return float(smoothness)

        except Exception:
            return 0.0


class SignalProcessor(BaseSignalComponent):
    """
    Enhanced Signal Processor Component

    Features:
    - Kalman filtering for noise reduction
    - Outlier detection and removal
    - Signal smoothing
    - Quality assessment
    - Configurable processing pipeline
    """

    def __init__(self, config: SignalProcessorConfig, event_bus=None):
        super().__init__("SignalProcessor", config, event_bus)
        self.config = config

        # Initialize processing components
        self.kalman_filter = None
        self.outlier_detector = OutlierDetector(config)
        self.signal_smoother = SignalSmoother(config)
        self.quality_assessor = SignalQualityAssessor(config)

        # Processing statistics
        self.processing_stats = {
            'signals_processed': 0,
            'outliers_detected': 0,
            'noise_reduction_applied': 0,
            'smoothing_applied': 0,
            'quality_improvements': 0
        }

    async def _initialize_component(self) -> bool:
        """Initialize the signal processor"""
        try:
            self.logger.info("Initializing Signal Processor...")

            # Initialize Kalman filter
            if self.config.enable_noise_reduction and self.config.noise_reduction_method == "kalman":
                self.kalman_filter = KalmanFilter(
                    process_variance=self.config.kalman_process_variance,
                    measurement_variance=self.config.kalman_measurement_variance,
                    initial_estimate=self.config.kalman_initial_estimate,
                    initial_error=self.config.kalman_initial_error
                )
                self.logger.info("Kalman filter initialized")

            if self.config.enable_outlier_detection:
                self.logger.info(f"Outlier detection enabled: {self.config.outlier_method}")

            if self.config.enable_signal_smoothing:
                self.logger.info(f"Signal smoothing enabled: {self.config.smoothing_method}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Signal Processor: {e}")
            return False

    async def _cleanup_component(self):
        """Cleanup signal processor resources"""
        try:
            # Reset processing statistics
            self.processing_stats = {
                'signals_processed': 0,
                'outliers_detected': 0,
                'noise_reduction_applied': 0,
                'smoothing_applied': 0,
                'quality_improvements': 0
            }

            self.logger.info("Signal Processor cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Signal Processor: {e}")

    async def process_signal(self, signal: TradingSignal,
                           market_data: Optional[List[float]] = None) -> SignalProcessingResult:
        """
        Process trading signal with noise reduction and enhancement
        """
        return await self.process_with_retry(
            "process_signal",
            self._process_signal_internal,
            signal,
            market_data
        )

    async def _process_signal_internal(self, signal: TradingSignal,
                                     market_data: Optional[List[float]] = None) -> SignalProcessingResult:
        """Internal signal processing logic"""
        start_time = datetime.now()

        try:
            # Publish processing started event
            await self.publish_event(
                SignalEventTypes.SIGNAL_PROCESSED.value,
                {
                    'signal_id': signal.signal_id,
                    'symbol': signal.symbol,
                    'strategy': signal.strategy_name
                }
            )

            # Create a copy of the original signal
            processed_signal = self._copy_signal(signal)

            # Extract signal data for processing
            if market_data is None:
                # Use signal confidence as a simple signal value
                signal_data = [signal.confidence]
            else:
                signal_data = market_data.copy()

            original_data = signal_data.copy()
            processing_details = {}

            # Apply processing pipeline
            for step in self.config.processing_order:
                if step == "outlier_detection" and self.config.enable_outlier_detection:
                    signal_data, outlier_details = await self._apply_outlier_detection(signal_data)
                    processing_details['outlier_detection'] = outlier_details

                elif step == "noise_reduction" and self.config.enable_noise_reduction:
                    signal_data, noise_details = await self._apply_noise_reduction(signal_data)
                    processing_details['noise_reduction'] = noise_details

                elif step == "signal_smoothing" and self.config.enable_signal_smoothing:
                    signal_data, smoothing_details = await self._apply_signal_smoothing(signal_data)
                    processing_details['signal_smoothing'] = smoothing_details

                elif step == "quality_assessment" and self.config.enable_quality_assessment:
                    quality_details = await self._assess_signal_quality(original_data, signal_data)
                    processing_details['quality_assessment'] = quality_details

            # Update processed signal with enhanced values
            processed_signal = self._update_signal_with_processed_data(
                processed_signal, signal_data, processing_details
            )

            # Calculate processing time
            processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000

            # Update statistics
            self._update_processing_stats(processing_details)

            result = SignalProcessingResult(
                original_signal=signal,
                processed_signal=processed_signal,
                noise_reduction_applied=self.config.enable_noise_reduction,
                outlier_detection_applied=self.config.enable_outlier_detection,
                signal_smoothing_applied=self.config.enable_signal_smoothing,
                signal_quality_score=processing_details.get('quality_assessment', {}).get('quality_score', 0.0),
                noise_level=processing_details.get('quality_assessment', {}).get('noise_level', 0.0),
                signal_to_noise_ratio=processing_details.get('quality_assessment', {}).get('signal_to_noise_ratio', 0.0),
                processing_details=processing_details,
                processing_time_ms=processing_time_ms
            )

            # Publish processing completed event
            await self.publish_event(
                SignalEventTypes.SIGNAL_PROCESSED.value,
                {
                    'signal_id': signal.signal_id,
                    'symbol': signal.symbol,
                    'strategy': signal.strategy_name,
                    'quality_score': result.signal_quality_score,
                    'processing_time_ms': processing_time_ms
                }
            )

            return result

        except Exception as e:
            self.logger.error(f"Error processing signal {signal.signal_id}: {e}")
            return SignalProcessingResult(
                original_signal=signal,
                processed_signal=signal,
                processing_details={'error': str(e)}
            )

    async def _apply_outlier_detection(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Apply outlier detection to signal data"""
        try:
            outlier_mask, detection_details = self.outlier_detector.detect_outliers(data)

            # Remove outliers by interpolation
            cleaned_data = []
            for i, (value, is_outlier) in enumerate(zip(data, outlier_mask)):
                if not is_outlier:
                    cleaned_data.append(value)
                else:
                    # Simple interpolation (average of neighbors)
                    if i > 0 and i < len(data) - 1:
                        interpolated = (data[i-1] + data[i+1]) / 2
                        cleaned_data.append(interpolated)
                    else:
                        cleaned_data.append(value)  # Keep edge values

            detection_details['outliers_removed'] = sum(outlier_mask)

            return cleaned_data, detection_details

        except Exception as e:
            self.logger.error(f"Error in outlier detection: {e}")
            return data, {'error': str(e)}

    async def _apply_noise_reduction(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Apply noise reduction to signal data"""
        try:
            if self.config.noise_reduction_method == "kalman" and self.kalman_filter:
                # Reset filter for new signal
                self.kalman_filter.estimate = self.config.kalman_initial_estimate
                self.kalman_filter.error = self.config.kalman_initial_error

                filtered_data = self.kalman_filter.filter_series(data)

                return filtered_data, {
                    'method': 'kalman',
                    'process_variance': self.config.kalman_process_variance,
                    'measurement_variance': self.config.kalman_measurement_variance
                }

            elif self.config.noise_reduction_method == "moving_average":
                # Simple moving average
                window = min(5, len(data))
                filtered_data = []

                for i in range(len(data)):
                    start_idx = max(0, i - window + 1)
                    end_idx = i + 1
                    filtered_data.append(np.mean(data[start_idx:end_idx]))

                return filtered_data, {
                    'method': 'moving_average',
                    'window': window
                }

            else:
                return data, {'method': 'none'}

        except Exception as e:
            self.logger.error(f"Error in noise reduction: {e}")
            return data, {'error': str(e)}

    async def _apply_signal_smoothing(self, data: List[float]) -> Tuple[List[float], Dict[str, Any]]:
        """Apply signal smoothing"""
        try:
            smoothed_data, smoothing_details = self.signal_smoother.smooth_signal(data)
            return smoothed_data, smoothing_details

        except Exception as e:
            self.logger.error(f"Error in signal smoothing: {e}")
            return data, {'error': str(e)}

    async def _assess_signal_quality(self, original_data: List[float],
                                   processed_data: List[float]) -> Dict[str, Any]:
        """Assess signal quality"""
        try:
            quality_metrics = self.quality_assessor.assess_quality(original_data, processed_data)
            return quality_metrics

        except Exception as e:
            self.logger.error(f"Error in quality assessment: {e}")
            return {'error': str(e)}

    def _copy_signal(self, signal: TradingSignal) -> TradingSignal:
        """Create a copy of the trading signal"""
        try:
            # Create a new signal with the same attributes
            processed_signal = TradingSignal(
                signal_id=signal.signal_id + "_processed",
                symbol=signal.symbol,
                strategy_name=signal.strategy_name,
                signal_type=signal.signal_type,
                action=signal.action,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                quantity=signal.quantity,
                risk_reward_ratio=signal.risk_reward_ratio,
                confidence=signal.confidence,
                market_regime=signal.market_regime,
                timestamp=signal.timestamp,
                capital_allocated=signal.capital_allocated,
                risk_amount=signal.risk_amount,
                position_size_method=signal.position_size_method,
                liquidity_check=signal.liquidity_check,
                time_filter_check=signal.time_filter_check,
                risk_check=signal.risk_check,
                cooldown_check=signal.cooldown_check,
                context=signal.context.copy(),
                indicators_snapshot=signal.indicators_snapshot.copy(),
                expected_return=signal.expected_return,
                expected_volatility=signal.expected_volatility,
                sharpe_prediction=signal.sharpe_prediction
            )

            return processed_signal

        except Exception as e:
            self.logger.error(f"Error copying signal: {e}")
            return signal

    def _update_signal_with_processed_data(self, signal: TradingSignal,
                                         processed_data: List[float],
                                         processing_details: Dict[str, Any]) -> TradingSignal:
        """Update signal with processed data"""
        try:
            # Update confidence based on signal quality
            quality_score = processing_details.get('quality_assessment', {}).get('quality_score', signal.confidence)

            # Enhance confidence with quality score
            enhanced_confidence = (signal.confidence + quality_score) / 2
            enhanced_confidence = max(0.0, min(1.0, enhanced_confidence))

            signal.confidence = enhanced_confidence

            # Update context with processing information
            signal.context.update({
                'signal_processed': True,
                'processing_applied': list(processing_details.keys()),
                'original_confidence': signal.confidence,
                'quality_score': quality_score
            })

            # Update expected metrics if quality improved
            if quality_score > 0.7:
                if signal.expected_return:
                    signal.expected_return *= 1.1  # 10% improvement
                if signal.sharpe_prediction:
                    signal.sharpe_prediction *= 1.05  # 5% improvement

            return signal

        except Exception as e:
            self.logger.error(f"Error updating signal with processed data: {e}")
            return signal

    def _update_processing_stats(self, processing_details: Dict[str, Any]):
        """Update processing statistics"""
        try:
            self.processing_stats['signals_processed'] += 1

            if 'outlier_detection' in processing_details:
                outlier_count = processing_details['outlier_detection'].get('outlier_count', 0)
                if outlier_count > 0:
                    self.processing_stats['outliers_detected'] += outlier_count

            if 'noise_reduction' in processing_details:
                self.processing_stats['noise_reduction_applied'] += 1

            if 'signal_smoothing' in processing_details:
                self.processing_stats['smoothing_applied'] += 1

            if 'quality_assessment' in processing_details:
                quality_score = processing_details['quality_assessment'].get('quality_score', 0.0)
                if quality_score > 0.7:
                    self.processing_stats['quality_improvements'] += 1

        except Exception as e:
            self.logger.error(f"Error updating processing stats: {e}")

    async def process_market_data(self, market_data: List[float],
                                symbol: str = "UNKNOWN") -> Tuple[List[float], Dict[str, Any]]:
        """
        Process raw market data (prices, indicators, etc.)
        """
        try:
            original_data = market_data.copy()
            processed_data = market_data.copy()
            processing_details = {}

            # Apply processing pipeline to market data
            for step in self.config.processing_order:
                if step == "outlier_detection" and self.config.enable_outlier_detection:
                    processed_data, outlier_details = await self._apply_outlier_detection(processed_data)
                    processing_details['outlier_detection'] = outlier_details

                elif step == "noise_reduction" and self.config.enable_noise_reduction:
                    processed_data, noise_details = await self._apply_noise_reduction(processed_data)
                    processing_details['noise_reduction'] = noise_details

                elif step == "signal_smoothing" and self.config.enable_signal_smoothing:
                    processed_data, smoothing_details = await self._apply_signal_smoothing(processed_data)
                    processing_details['signal_smoothing'] = smoothing_details

                elif step == "quality_assessment" and self.config.enable_quality_assessment:
                    quality_details = await self._assess_signal_quality(original_data, processed_data)
                    processing_details['quality_assessment'] = quality_details

            # Publish market data processed event
            await self.publish_event(
                SignalEventTypes.MARKET_DATA_PROCESSED.value,
                {
                    'symbol': symbol,
                    'data_points': len(processed_data),
                    'quality_score': processing_details.get('quality_assessment', {}).get('quality_score', 0.0)
                }
            )

            return processed_data, processing_details

        except Exception as e:
            self.logger.error(f"Error processing market data for {symbol}: {e}")
            return market_data, {'error': str(e)}

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            'processing_stats': dict(self.processing_stats),
            'config': {
                'noise_reduction_enabled': self.config.enable_noise_reduction,
                'noise_reduction_method': self.config.noise_reduction_method,
                'outlier_detection_enabled': self.config.enable_outlier_detection,
                'outlier_method': self.config.outlier_method,
                'smoothing_enabled': self.config.enable_signal_smoothing,
                'smoothing_method': self.config.smoothing_method,
                'quality_assessment_enabled': self.config.enable_quality_assessment
            }
        }

    def reset_processing_stats(self):
        """Reset processing statistics"""
        self.processing_stats = {
            'signals_processed': 0,
            'outliers_detected': 0,
            'noise_reduction_applied': 0,
            'smoothing_applied': 0,
            'quality_improvements': 0
        }
        self.logger.info("Processing statistics reset")