#!/usr/bin/env python3
"""
Signal Generation Core Module
Modular components for advanced signal generation and processing
"""

from .base_component import BaseSignalComponent
from .data_models import (
    SignalInput,
    TradingSignal,
    SignalValidationResult,
    PositionSizingResult,
    StrategyEvaluationResult,
    SignalProcessingResult
)
from .event_types import SignalEventTypes

__all__ = [
    'BaseSignalComponent',
    'SignalInput',
    'TradingSignal',
    'SignalValidationResult',
    'PositionSizingResult',
    'StrategyEvaluationResult',
    'SignalProcessingResult',
    'SignalEventTypes'
]