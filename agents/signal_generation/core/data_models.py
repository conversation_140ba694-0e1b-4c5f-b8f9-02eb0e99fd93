#!/usr/bin/env python3
"""
Signal Generation Data Models
Core data structures for the modular signal generation system
"""

import polars as pl
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum


class SignalType(Enum):
    """Signal types"""
    LONG = 1
    SHORT = -1
    EXIT = 0
    HOLD = 2


class SignalAction(Enum):
    """Signal actions"""
    BUY = "BUY"
    SELL = "SELL"
    EXIT = "EXIT"
    HOLD = "HOLD"


class PositionSizingMethod(Enum):
    """Position sizing methods"""
    KELLY = "kelly"
    FIXED_FRACTION = "fixed_fraction"
    VOLATILITY_SCALED = "volatility_scaled"
    RISK_PARITY = "risk_parity"


class MarketRegime(Enum):
    """Market regime types"""
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"


@dataclass
class OHLCV:
    """OHLCV candle data"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    symbol: str = ""


@dataclass
class MarketIndicators:
    """Market technical indicators"""
    symbol: str
    timestamp: datetime

    # Moving averages
    ema_5: Optional[float] = None
    ema_10: Optional[float] = None
    ema_13: Optional[float] = None
    ema_20: Optional[float] = None
    ema_21: Optional[float] = None
    ema_30: Optional[float] = None
    ema_50: Optional[float] = None
    ema_100: Optional[float] = None
    sma_20: Optional[float] = None

    # Oscillators
    rsi_5: Optional[float] = None
    rsi_14: Optional[float] = None
    stoch_k: Optional[float] = None
    stoch_d: Optional[float] = None
    cci: Optional[float] = None
    mfi: Optional[float] = None

    # MACD
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None

    # Trend indicators
    adx: Optional[float] = None
    supertrend: Optional[float] = None

    # Volatility indicators
    atr: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_middle: Optional[float] = None

    # Volume indicators
    vwap: Optional[float] = None

    # Support/Resistance
    donchian_high: Optional[float] = None
    donchian_low: Optional[float] = None


@dataclass
class SignalInput:
    """Input data for signal generation"""
    symbol: str
    timestamp: datetime
    ohlcv_data: List[OHLCV]
    indicators: MarketIndicators
    market_regime: Optional[MarketRegime] = None
    strategy_config: Dict[str, Any] = field(default_factory=dict)
    capital_info: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TradingSignal:
    """Generated trading signal"""
    signal_id: str
    symbol: str
    strategy_name: str
    signal_type: SignalType
    action: SignalAction
    entry_price: float
    stop_loss: float
    take_profit: float
    quantity: int
    risk_reward_ratio: float
    confidence: float
    market_regime: MarketRegime
    timestamp: datetime

    # Position sizing details
    capital_allocated: float
    risk_amount: float
    position_size_method: PositionSizingMethod

    # Validation flags
    liquidity_check: bool = False
    time_filter_check: bool = False
    risk_check: bool = False
    cooldown_check: bool = False

    # Context and metadata
    context: Dict[str, Any] = field(default_factory=dict)
    indicators_snapshot: Dict[str, float] = field(default_factory=dict)

    # Performance tracking
    expected_return: Optional[float] = None
    expected_volatility: Optional[float] = None
    sharpe_prediction: Optional[float] = None


@dataclass
class SignalValidationResult:
    """Result of signal validation"""
    is_valid: bool
    rejection_reason: Optional[str] = None
    validation_score: float = 0.0
    risk_metrics: Dict[str, float] = field(default_factory=dict)
    liquidity_metrics: Dict[str, float] = field(default_factory=dict)
    time_filter_result: bool = True
    cooldown_result: bool = True

    # Detailed validation results
    validation_details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionSizingResult:
    """Result of position sizing calculation"""
    quantity: int
    capital_allocated: float
    risk_amount: float
    kelly_fraction: Optional[float] = None
    max_position_value: float = 0.0
    method_used: PositionSizingMethod = PositionSizingMethod.FIXED_FRACTION

    # Risk metrics
    position_risk_percent: float = 0.0
    portfolio_risk_percent: float = 0.0
    leverage_ratio: float = 1.0

    # Calculation details
    calculation_details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategyEvaluationResult:
    """Result of strategy evaluation"""
    strategy_name: str
    symbol: str
    timestamp: datetime

    # Evaluation results
    long_condition: bool = False
    short_condition: bool = False
    exit_condition: bool = False

    # Signal strength and confidence
    signal_strength: float = 0.0
    confidence_score: float = 0.0

    # Strategy performance metrics
    historical_performance: Dict[str, float] = field(default_factory=dict)

    # Evaluation details
    condition_details: Dict[str, Any] = field(default_factory=dict)
    evaluation_time_ms: float = 0.0


@dataclass
class SignalProcessingResult:
    """Result of signal processing and enhancement"""
    original_signal: TradingSignal
    processed_signal: TradingSignal

    # Processing applied
    noise_reduction_applied: bool = False
    outlier_detection_applied: bool = False
    signal_smoothing_applied: bool = False

    # Enhancement metrics
    signal_quality_score: float = 0.0
    noise_level: float = 0.0
    signal_to_noise_ratio: float = 0.0

    # Processing details
    processing_details: Dict[str, Any] = field(default_factory=dict)
    processing_time_ms: float = 0.0


@dataclass
class MultiTimeframeSignalResult:
    """Result of multi-timeframe signal fusion"""
    primary_signal: TradingSignal
    timeframe_signals: Dict[str, TradingSignal] = field(default_factory=dict)

    # Fusion results
    consensus_signal: Optional[TradingSignal] = None
    consensus_strength: float = 0.0
    timeframe_agreement: float = 0.0

    # Fusion details
    fusion_method: str = "weighted_average"
    timeframe_weights: Dict[str, float] = field(default_factory=dict)
    fusion_details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategyPerformanceMetrics:
    """Strategy performance metrics for dynamic management"""
    strategy_name: str
    symbol: str
    timeframe: str

    # Performance metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0

    # Return metrics
    total_return: float = 0.0
    average_return: float = 0.0
    best_trade: float = 0.0
    worst_trade: float = 0.0

    # Risk metrics
    max_drawdown: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0

    # Trade metrics
    average_trade_duration: float = 0.0
    profit_factor: float = 0.0
    expectancy: float = 0.0

    # Recent performance
    recent_performance_window: int = 30
    recent_win_rate: float = 0.0
    recent_return: float = 0.0
    recent_sharpe: float = 0.0

    # Ranking and scoring
    performance_score: float = 0.0
    ranking: int = 0
    confidence_score: float = 0.0

    # Metadata
    last_updated: datetime = field(default_factory=datetime.now)
    last_trade_time: Optional[datetime] = None

    # Kelly Criterion data
    kelly_fraction: float = 0.0
    kelly_win_rate: float = 0.0
    kelly_avg_win: float = 0.0
    kelly_avg_loss: float = 0.0


@dataclass
class MarketDepthData:
    """Market depth data for liquidity analysis"""
    symbol: str
    timestamp: datetime

    # Bid/Ask data
    best_bid: float
    best_ask: float
    bid_size: int
    ask_size: int
    spread: float
    spread_percent: float

    # Order book depth
    bid_levels: List[Dict[str, float]] = field(default_factory=list)
    ask_levels: List[Dict[str, float]] = field(default_factory=list)

    # Liquidity metrics
    total_bid_volume: int = 0
    total_ask_volume: int = 0
    market_impact_cost: float = 0.0
    liquidity_score: float = 0.0