#!/usr/bin/env python3
"""
Base Signal Component
Abstract base class for all signal generation components
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ComponentConfig:
    """Base configuration for signal components"""
    enabled: bool = True
    timeout_seconds: float = 30.0
    retry_attempts: int = 3
    cache_enabled: bool = True
    cache_ttl_seconds: int = 300
    performance_tracking: bool = True


class BaseSignalComponent(ABC):
    """
    Abstract base class for all signal generation components

    Provides common functionality:
    - Async initialization and cleanup
    - Error handling and retries
    - Performance monitoring
    - Configuration management
    - Event publishing
    """

    def __init__(self, name: str, config: ComponentConfig, event_bus=None):
        self.name = name
        self.config = config
        self.event_bus = event_bus
        self.logger = logging.getLogger(f"{__name__}.{name}")

        # State management
        self.is_initialized = False
        self.is_running = False
        self.last_error = None

        # Performance tracking
        self.performance_metrics = {
            'calls_total': 0,
            'calls_success': 0,
            'calls_failed': 0,
            'avg_processing_time_ms': 0.0,
            'last_call_time': None
        }

        # Cache for results
        self.cache = {} if config.cache_enabled else None

    async def initialize(self) -> bool:
        """Initialize the component"""
        try:
            self.logger.info(f"Initializing {self.name}...")

            # Component-specific initialization
            success = await self._initialize_component()

            if success:
                self.is_initialized = True
                self.logger.info(f"{self.name} initialized successfully")
            else:
                self.logger.error(f"Failed to initialize {self.name}")

            return success

        except Exception as e:
            self.logger.error(f"Error initializing {self.name}: {e}")
            self.last_error = e
            return False

    async def cleanup(self):
        """Cleanup component resources"""
        try:
            self.logger.info(f"Cleaning up {self.name}...")

            # Component-specific cleanup
            await self._cleanup_component()

            self.is_initialized = False
            self.is_running = False

            self.logger.info(f"{self.name} cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error cleaning up {self.name}: {e}")

    async def process_with_retry(self, operation_name: str, operation_func, *args, **kwargs):
        """Execute operation with retry logic and performance tracking"""
        start_time = datetime.now()

        for attempt in range(self.config.retry_attempts):
            try:
                self.performance_metrics['calls_total'] += 1

                # Execute operation with timeout
                result = await asyncio.wait_for(
                    operation_func(*args, **kwargs),
                    timeout=self.config.timeout_seconds
                )

                # Update performance metrics
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                self._update_performance_metrics(processing_time, success=True)

                return result

            except asyncio.TimeoutError:
                self.logger.warning(f"{operation_name} timed out (attempt {attempt + 1})")
                if attempt == self.config.retry_attempts - 1:
                    self._update_performance_metrics(0, success=False)
                    raise

            except Exception as e:
                self.logger.error(f"{operation_name} failed (attempt {attempt + 1}): {e}")
                if attempt == self.config.retry_attempts - 1:
                    self._update_performance_metrics(0, success=False)
                    self.last_error = e
                    raise

                # Wait before retry
                await asyncio.sleep(0.1 * (attempt + 1))

    def _update_performance_metrics(self, processing_time_ms: float, success: bool):
        """Update performance metrics"""
        if success:
            self.performance_metrics['calls_success'] += 1
        else:
            self.performance_metrics['calls_failed'] += 1

        # Update average processing time
        current_avg = self.performance_metrics['avg_processing_time_ms']
        total_calls = self.performance_metrics['calls_total']

        if total_calls > 0:
            self.performance_metrics['avg_processing_time_ms'] = (
                (current_avg * (total_calls - 1) + processing_time_ms) / total_calls
            )

        self.performance_metrics['last_call_time'] = datetime.now()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get component performance metrics"""
        return self.performance_metrics.copy()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        if not self.cache:
            return {'cache_enabled': False}

        return {
            'cache_enabled': True,
            'cache_size': len(self.cache),
            'cache_ttl_seconds': self.config.cache_ttl_seconds
        }

    async def publish_event(self, event_type: str, data: Dict[str, Any]):
        """Publish event to event bus"""
        if self.event_bus:
            try:
                await self.event_bus.publish(event_type, {
                    'source': self.name,
                    'timestamp': datetime.now().isoformat(),
                    'data': data
                })
            except Exception as e:
                self.logger.error(f"Failed to publish event {event_type}: {e}")

    @abstractmethod
    async def _initialize_component(self) -> bool:
        """Component-specific initialization logic"""
        pass

    @abstractmethod
    async def _cleanup_component(self):
        """Component-specific cleanup logic"""
        pass