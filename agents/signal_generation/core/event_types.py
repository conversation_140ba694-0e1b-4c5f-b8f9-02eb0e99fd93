#!/usr/bin/env python3
"""
Signal Generation Event Types
Event types for the modular signal generation system
"""

from enum import Enum


class SignalEventTypes(Enum):
    """Event types for signal generation system"""

    # Market data events
    MARKET_DATA_RECEIVED = "signal.market_data.received"
    MARKET_DATA_PROCESSED = "signal.market_data.processed"
    MARKET_REGIME_CHANGED = "signal.market_regime.changed"

    # Strategy evaluation events
    STRATEGY_EVALUATION_STARTED = "signal.strategy.evaluation.started"
    STRATEGY_EVALUATION_COMPLETED = "signal.strategy.evaluation.completed"
    STRATEGY_CONDITION_MET = "signal.strategy.condition.met"
    STRATEGY_PERFORMANCE_UPDATED = "signal.strategy.performance.updated"

    # Signal generation events
    SIGNAL_GENERATED = "signal.generated"
    SIGNAL_VALIDATED = "signal.validated"
    SIGNAL_REJECTED = "signal.rejected"
    SIGNAL_PROCESSED = "signal.processed"
    SIGNAL_ENHANCED = "signal.enhanced"

    # Position sizing events
    POSITION_SIZE_CALCULATED = "signal.position.size.calculated"
    KELLY_FRACTION_UPDATED = "signal.kelly.fraction.updated"
    RISK_LIMITS_CHECKED = "signal.risk.limits.checked"

    # Multi-timeframe events
    TIMEFRAME_SIGNAL_GENERATED = "signal.timeframe.generated"
    TIMEFRAME_CONSENSUS_REACHED = "signal.timeframe.consensus"
    TIMEFRAME_FUSION_COMPLETED = "signal.timeframe.fusion.completed"

    # Signal processing events
    SIGNAL_NOISE_REDUCED = "signal.processing.noise.reduced"
    SIGNAL_SMOOTHED = "signal.processing.smoothed"
    OUTLIER_DETECTED = "signal.processing.outlier.detected"

    # ML model events
    ML_PREDICTION_REQUESTED = "signal.ml.prediction.requested"
    ML_PREDICTION_COMPLETED = "signal.ml.prediction.completed"
    ML_MODEL_UPDATED = "signal.ml.model.updated"
    ML_CONFIDENCE_CALCULATED = "signal.ml.confidence.calculated"

    # Strategy management events
    STRATEGY_LOADED = "signal.strategy.loaded"
    STRATEGY_UPDATED = "signal.strategy.updated"
    STRATEGY_DISABLED = "signal.strategy.disabled"
    STRATEGY_RANKING_UPDATED = "signal.strategy.ranking.updated"

    # Performance events
    PERFORMANCE_METRICS_UPDATED = "signal.performance.metrics.updated"
    COMPONENT_PERFORMANCE_TRACKED = "signal.component.performance.tracked"
    SYSTEM_HEALTH_CHECK = "signal.system.health.check"

    # Error and warning events
    COMPONENT_ERROR = "signal.component.error"
    VALIDATION_WARNING = "signal.validation.warning"
    TIMEOUT_WARNING = "signal.timeout.warning"
    RESOURCE_WARNING = "signal.resource.warning"

    # Integration events
    RISK_AGENT_VALIDATION = "signal.risk.agent.validation"
    EXECUTION_AGENT_SIGNAL = "signal.execution.agent.signal"
    MARKET_MONITORING_UPDATE = "signal.market.monitoring.update"

    # Configuration events
    CONFIG_UPDATED = "signal.config.updated"
    COMPONENT_INITIALIZED = "signal.component.initialized"
    COMPONENT_SHUTDOWN = "signal.component.shutdown"


class SignalPriority(Enum):
    """Signal priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class SignalStatus(Enum):
    """Signal processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    VALIDATED = "validated"
    REJECTED = "rejected"
    SENT_TO_EXECUTION = "sent_to_execution"
    EXECUTED = "executed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class ComponentStatus(Enum):
    """Component status"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    SHUTDOWN = "shutdown"