#!/usr/bin/env python3
"""
Signal Generation Gateway
Unified interface for the modular signal generation system
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from .signal_generation.core.base_component import ComponentConfig
from .signal_generation.core.data_models import (
    SignalInput, TradingSignal, SignalValidationResult,
    PositionSizingResult, StrategyEvaluationResult, SignalType, SignalAction
)
from .signal_generation.core.event_types import SignalEventTypes

from .signal_generation.components.strategy_evaluator import StrategyEvaluator, StrategyEvaluatorConfig
from .signal_generation.components.position_sizer import PositionSizer, PositionSizerConfig
from .signal_generation.components.signal_validator import SignalValidator, SignalValidatorConfig
from .signal_generation.components.signal_processor import SignalProcessor, SignalProcessorConfig
from .signal_generation.components.multi_timeframe_fusion import MultiTimeframeFusion, MultiTimeframeFusionConfig
from .signal_generation.components.ml_integrator import MLIntegrator, MLIntegratorConfig
from .signal_generation.components.strategy_manager import StrategyManager, StrategyManagerConfig
from .signal_generation.integration.performance_feedback_handler import (
    PerformanceFeedbackHandler, PerformanceAlert, AlertSeverity, ResponseAction
)

logger = logging.getLogger(__name__)


@dataclass
class SignalGenerationGatewayConfig:
    """Configuration for Signal Generation Gateway"""

    # Component configurations
    strategy_evaluator_config: StrategyEvaluatorConfig = None
    position_sizer_config: PositionSizerConfig = None
    signal_validator_config: SignalValidatorConfig = None
    signal_processor_config: SignalProcessorConfig = None
    multi_timeframe_config: MultiTimeframeFusionConfig = None
    ml_integrator_config: MLIntegratorConfig = None
    strategy_manager_config: StrategyManagerConfig = None

    # Gateway settings
    enable_async_processing: bool = True
    max_concurrent_signals: int = 10
    signal_timeout_seconds: float = 30.0

    # Processing pipeline
    enable_signal_processing: bool = True
    enable_multi_timeframe_fusion: bool = True
    enable_ml_enhancement: bool = True

    def __post_init__(self):
        # Initialize default configurations if not provided
        if self.strategy_evaluator_config is None:
            self.strategy_evaluator_config = StrategyEvaluatorConfig()
        if self.position_sizer_config is None:
            self.position_sizer_config = PositionSizerConfig()
        if self.signal_validator_config is None:
            self.signal_validator_config = SignalValidatorConfig()
        if self.signal_processor_config is None:
            self.signal_processor_config = SignalProcessorConfig()
        if self.multi_timeframe_config is None:
            self.multi_timeframe_config = MultiTimeframeFusionConfig()
        if self.ml_integrator_config is None:
            self.ml_integrator_config = MLIntegratorConfig()
        if self.strategy_manager_config is None:
            self.strategy_manager_config = StrategyManagerConfig()


class SignalGenerationGateway:
    """
    Signal Generation Gateway

    Unified interface for the modular signal generation system.
    Orchestrates all components and provides a clean API for other agents.
    """

    def __init__(self, config: SignalGenerationGatewayConfig, event_bus=None):
        self.config = config
        self.event_bus = event_bus
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.strategy_evaluator = StrategyEvaluator(config.strategy_evaluator_config, event_bus)
        self.position_sizer = PositionSizer(config.position_sizer_config, event_bus)
        self.signal_validator = SignalValidator(config.signal_validator_config, event_bus)
        self.signal_processor = SignalProcessor(config.signal_processor_config, event_bus)
        self.multi_timeframe_fusion = MultiTimeframeFusion(config.multi_timeframe_config, event_bus)
        self.ml_integrator = MLIntegrator(config.ml_integrator_config, event_bus)
        self.strategy_manager = StrategyManager(config.strategy_manager_config, event_bus)

        # Initialize performance feedback handler
        self.performance_feedback_handler = PerformanceFeedbackHandler(self)

        # Gateway state
        self.is_initialized = False
        self.is_running = False

        # Processing statistics
        self.stats = {
            'signals_processed': 0,
            'signals_generated': 0,
            'signals_validated': 0,
            'signals_rejected': 0,
            'processing_errors': 0
        }

    async def initialize(self) -> bool:
        """Initialize the signal generation gateway"""
        try:
            self.logger.info("Initializing Signal Generation Gateway...")

            # Initialize all components
            components = [
                self.strategy_manager,
                self.strategy_evaluator,
                self.position_sizer,
                self.signal_validator,
                self.signal_processor,
                self.multi_timeframe_fusion,
                self.ml_integrator
            ]

            for component in components:
                if not await component.initialize():
                    self.logger.error(f"Failed to initialize {component.name}")
                    return False
                self.logger.info(f"Initialized {component.name}")

            self.is_initialized = True
            self.is_running = True

            self.logger.info("Signal Generation Gateway initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing Signal Generation Gateway: {e}")
            return False

    async def cleanup(self):
        """Cleanup gateway resources"""
        try:
            self.logger.info("Cleaning up Signal Generation Gateway...")

            self.is_running = False

            # Cleanup all components
            components = [
                self.ml_integrator,
                self.multi_timeframe_fusion,
                self.signal_processor,
                self.signal_validator,
                self.position_sizer,
                self.strategy_evaluator,
                self.strategy_manager
            ]

            for component in components:
                await component.cleanup()

            self.is_initialized = False

            self.logger.info("Signal Generation Gateway cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up Signal Generation Gateway: {e}")

    async def generate_signal(self, signal_input: SignalInput) -> Optional[TradingSignal]:
        """
        Generate trading signal from market data

        Main entry point for signal generation
        """
        try:
            if not self.is_initialized or not self.is_running:
                self.logger.error("Gateway not initialized or not running")
                return None

            self.stats['signals_processed'] += 1

            # Get active strategies
            active_strategies = await self.strategy_manager.get_active_strategies(signal_input.symbol)

            if not active_strategies:
                self.logger.warning(f"No active strategies for symbol {signal_input.symbol}")
                return None

            # Evaluate strategies
            strategy_results = await self.strategy_evaluator.evaluate_multiple_strategies(
                signal_input, list(active_strategies.values())
            )

            # Find best strategy result
            best_result = self._select_best_strategy_result(strategy_results)

            if not best_result or not (best_result.long_condition or best_result.short_condition):
                return None

            # Generate signal from best strategy
            signal = await self._create_signal_from_strategy_result(best_result, signal_input)

            if not signal:
                return None

            # Process signal through pipeline
            processed_signal = await self._process_signal_pipeline(signal, signal_input)

            if processed_signal:
                self.stats['signals_generated'] += 1

            return processed_signal

        except Exception as e:
            self.logger.error(f"Error generating signal: {e}")
            self.stats['processing_errors'] += 1
            return None

    def _select_best_strategy_result(self, results: List[StrategyEvaluationResult]) -> Optional[StrategyEvaluationResult]:
        """Select the best strategy result based on confidence and signal strength"""
        try:
            if not results:
                return None

            # Filter results with valid conditions
            valid_results = [
                r for r in results
                if r.long_condition or r.short_condition
            ]

            if not valid_results:
                return None

            # Sort by combined score (confidence + signal strength)
            scored_results = []
            for result in valid_results:
                score = (result.confidence_score * 0.6) + (result.signal_strength * 0.4)
                scored_results.append((score, result))

            # Return highest scoring result
            scored_results.sort(key=lambda x: x[0], reverse=True)
            return scored_results[0][1]

        except Exception as e:
            self.logger.error(f"Error selecting best strategy result: {e}")
            return None

    async def _create_signal_from_strategy_result(self, result: StrategyEvaluationResult,
                                                signal_input: SignalInput) -> Optional[TradingSignal]:
        """Create trading signal from strategy evaluation result"""
        try:
            # Determine signal type and action
            if result.long_condition:
                signal_type = SignalType.LONG
                action = SignalAction.BUY
            elif result.short_condition:
                signal_type = SignalType.SHORT
                action = SignalAction.SELL
            else:
                return None

            # Get current price
            if not signal_input.ohlcv_data:
                return None

            current_price = signal_input.ohlcv_data[-1].close

            # Calculate stop loss and take profit (simplified)
            if signal_type == SignalType.LONG:
                stop_loss = current_price * 0.98  # 2% stop loss
                take_profit = current_price * 1.04  # 4% take profit
            else:
                stop_loss = current_price * 1.02  # 2% stop loss
                take_profit = current_price * 0.96  # 4% take profit

            # Calculate risk-reward ratio
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0.0

            # Create signal
            signal = TradingSignal(
                signal_id=f"{result.strategy_name}_{signal_input.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=signal_input.symbol,
                strategy_name=result.strategy_name,
                signal_type=signal_type,
                action=action,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                quantity=1,  # Will be updated by position sizer
                risk_reward_ratio=risk_reward_ratio,
                confidence=result.confidence_score,
                market_regime=signal_input.market_regime,
                timestamp=signal_input.timestamp,
                capital_allocated=0.0,  # Will be updated by position sizer
                risk_amount=0.0,  # Will be updated by position sizer
                position_size_method=self.config.position_sizer_config.default_method,
                context={
                    'strategy_evaluation': {
                        'signal_strength': result.signal_strength,
                        'evaluation_time_ms': result.evaluation_time_ms,
                        'condition_details': result.condition_details
                    }
                },
                indicators_snapshot={
                    attr: getattr(signal_input.indicators, attr)
                    for attr in dir(signal_input.indicators)
                    if not attr.startswith('_') and attr not in ['symbol', 'timestamp']
                    and getattr(signal_input.indicators, attr) is not None
                }
            )

            return signal

        except Exception as e:
            self.logger.error(f"Error creating signal from strategy result: {e}")
            return None

    async def _process_signal_pipeline(self, signal: TradingSignal,
                                     signal_input: SignalInput) -> Optional[TradingSignal]:
        """Process signal through the enhancement pipeline"""
        try:
            current_signal = signal

            # 1. Signal processing (noise reduction, smoothing)
            if self.config.enable_signal_processing:
                processing_result = await self.signal_processor.process_signal(current_signal)
                if processing_result.processed_signal:
                    current_signal = processing_result.processed_signal

            # 2. ML enhancement
            if self.config.enable_ml_enhancement:
                current_signal = await self.ml_integrator.enhance_signal(current_signal, signal_input)

            # 3. Position sizing
            position_result = await self.position_sizer.calculate_position_size(
                signal_input,
                {'name': current_signal.strategy_name},
                current_signal.entry_price,
                current_signal.stop_loss
            )

            # Update signal with position sizing
            current_signal.quantity = position_result.quantity
            current_signal.capital_allocated = position_result.capital_allocated
            current_signal.risk_amount = position_result.risk_amount

            # 4. Signal validation
            validation_result = await self.signal_validator.validate_signal(current_signal)

            if not validation_result.is_valid:
                self.logger.info(f"Signal rejected: {validation_result.rejection_reason}")
                self.stats['signals_rejected'] += 1
                return None

            self.stats['signals_validated'] += 1

            # Update signal validation flags
            current_signal.liquidity_check = True
            current_signal.time_filter_check = validation_result.time_filter_result
            current_signal.risk_check = True
            current_signal.cooldown_check = validation_result.cooldown_result

            return current_signal

        except Exception as e:
            self.logger.error(f"Error in signal pipeline: {e}")
            return None

    async def generate_multi_timeframe_signal(self, timeframe_inputs: Dict[str, SignalInput]) -> Optional[TradingSignal]:
        """Generate signal using multi-timeframe analysis"""
        try:
            if not self.config.enable_multi_timeframe_fusion:
                self.logger.warning("Multi-timeframe fusion is disabled")
                return None

            # Generate signals for each timeframe
            timeframe_signals = {}

            for timeframe, signal_input in timeframe_inputs.items():
                signal = await self.generate_signal(signal_input)
                if signal:
                    timeframe_signals[timeframe] = signal

            if len(timeframe_signals) < 2:
                self.logger.warning("Insufficient timeframe signals for fusion")
                return None

            # Fuse timeframe signals
            fusion_result = await self.multi_timeframe_fusion.fuse_timeframe_signals(timeframe_signals)

            return fusion_result.consensus_signal

        except Exception as e:
            self.logger.error(f"Error generating multi-timeframe signal: {e}")
            return None

    def get_gateway_stats(self) -> Dict[str, Any]:
        """Get gateway statistics"""
        return {
            'gateway_stats': dict(self.stats),
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'component_stats': {
                'strategy_evaluator': self.strategy_evaluator.get_performance_metrics(),
                'position_sizer': self.position_sizer.get_portfolio_status(),
                'signal_validator': self.signal_validator.get_validation_stats(),
                'signal_processor': self.signal_processor.get_processing_stats(),
                'multi_timeframe_fusion': self.multi_timeframe_fusion.get_fusion_stats(),
                'strategy_manager': self.strategy_manager.get_manager_stats()
            },
            'performance_feedback': self.performance_feedback_handler.get_feedback_handler_status()
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # PERFORMANCE ANALYSIS INTEGRATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def handle_performance_alert(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle performance alert from Performance Analysis Gateway

        Args:
            alert_data: Alert data from Performance Analysis Gateway

        Returns:
            Response indicating success/failure
        """
        try:
            # Create PerformanceAlert object
            alert = PerformanceAlert(
                alert_id=alert_data.get('alert_id', f"alert_{datetime.now().timestamp()}"),
                alert_type=alert_data.get('alert_type', 'unknown'),
                severity=AlertSeverity(alert_data.get('severity', 'warning')),
                strategy=alert_data.get('strategy'),
                symbol=alert_data.get('symbol'),
                metric_name=alert_data.get('metric_name', ''),
                current_value=alert_data.get('current_value', 0.0),
                threshold_value=alert_data.get('threshold_value', 0.0),
                message=alert_data.get('message', ''),
                timestamp=datetime.fromisoformat(alert_data.get('timestamp', datetime.now().isoformat())),
                metadata=alert_data.get('metadata', {})
            )

            # Handle the alert
            success = await self.performance_feedback_handler.handle_performance_alert(alert)

            if success:
                self.log_info(f"Successfully handled performance alert: {alert.alert_id}")
                return {
                    'status': 'success',
                    'message': f'Performance alert {alert.alert_id} handled successfully',
                    'alert_id': alert.alert_id
                }
            else:
                self.log_error(f"Failed to handle performance alert: {alert.alert_id}")
                return {
                    'status': 'error',
                    'message': f'Failed to handle performance alert {alert.alert_id}',
                    'alert_id': alert.alert_id
                }

        except Exception as e:
            self.log_error(f"Error handling performance alert: {e}")
            return {
                'status': 'error',
                'message': f'Error handling performance alert: {str(e)}'
            }

    async def register_with_performance_analysis_gateway(self, gateway_endpoint: str) -> bool:
        """
        Register with Performance Analysis Gateway for alerts

        Args:
            gateway_endpoint: Performance Analysis Gateway endpoint

        Returns:
            bool: True if registration successful
        """
        try:
            import aiohttp

            registration_data = {
                'agent_type': 'signal_generation',
                'agent_id': 'signal_generation_gateway',
                'callback_endpoint': f"http://localhost:{self.config.api_port}/performance_alert",
                'alert_types': [
                    'low_win_rate_anomaly',
                    'high_drawdown_anomaly',
                    'negative_sharpe_anomaly',
                    'excessive_losses_anomaly',
                    'volatility_spike_anomaly',
                    'predicted_poor_performance',
                    'strategy_degradation_warning',
                    'market_regime_mismatch'
                ]
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{gateway_endpoint}/register_agent",
                    json=registration_data,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        self.log_info("Successfully registered with Performance Analysis Gateway")
                        return True
                    else:
                        self.log_error(f"Failed to register with Performance Analysis Gateway: {response.status}")
                        return False

        except Exception as e:
            self.log_error(f"Error registering with Performance Analysis Gateway: {e}")
            return False

    def get_strategy_adjustments_summary(self) -> Dict[str, Any]:
        """Get summary of current strategy adjustments"""
        return self.performance_feedback_handler.get_feedback_handler_status()

    async def manually_adjust_strategy_parameters(self, strategy_name: str, adjustments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Manually adjust strategy parameters

        Args:
            strategy_name: Name of strategy to adjust
            adjustments: Dictionary of parameter adjustments

        Returns:
            Response indicating success/failure
        """
        try:
            if strategy_name not in self.strategy_manager.strategies:
                return {
                    'status': 'error',
                    'message': f'Strategy {strategy_name} not found'
                }

            # Apply adjustments
            strategy = self.strategy_manager.strategies[strategy_name]
            original_values = {}

            for param_path, new_value in adjustments.items():
                # Parse parameter path (e.g., "position_sizing.base_size_percent")
                path_parts = param_path.split('.')
                current_dict = strategy

                # Navigate to the parameter
                for part in path_parts[:-1]:
                    if part not in current_dict:
                        current_dict[part] = {}
                    current_dict = current_dict[part]

                # Store original value and set new value
                param_name = path_parts[-1]
                original_values[param_path] = current_dict.get(param_name)
                current_dict[param_name] = new_value

            self.log_info(f"Manually adjusted parameters for strategy {strategy_name}: {adjustments}")

            return {
                'status': 'success',
                'message': f'Successfully adjusted parameters for strategy {strategy_name}',
                'strategy': strategy_name,
                'adjustments': adjustments,
                'original_values': original_values
            }

        except Exception as e:
            self.log_error(f"Error adjusting strategy parameters: {e}")
            return {
                'status': 'error',
                'message': f'Error adjusting strategy parameters: {str(e)}'
            }

    def reset_stats(self):
        """Reset gateway statistics"""
        self.stats = {
            'signals_processed': 0,
            'signals_generated': 0,
            'signals_validated': 0,
            'signals_rejected': 0,
            'processing_errors': 0
        }
        self.logger.info("Gateway statistics reset")