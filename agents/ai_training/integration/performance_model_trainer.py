#!/usr/bin/env python3
"""
Performance Model Trainer for AI Training Agent

Handles training of ML models specifically for performance analysis predictions:
- Sharpe Ratio prediction models
- ROI prediction models
- Drawdown prediction models
- Profitability classification models
- Anomaly detection models
- Predictive maintenance models

Features:
- Specialized model architectures for performance metrics
- Real-time model updates and retraining
- Model lifecycle management
- Performance prediction accuracy tracking
- Integration with Performance Analysis Gateway
"""

import asyncio
import logging
import numpy as np
import pandas as pd
import polars as pl
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import joblib
from pathlib import Path

# ML imports
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
import lightgbm as lgb
import optuna

logger = logging.getLogger(__name__)

class ModelType(Enum):
    """Types of performance prediction models"""
    SHARPE_RATIO = "sharpe_ratio"
    ROI = "roi"
    DRAWDOWN = "drawdown"
    PROFITABILITY = "profitability"
    WIN_RATE = "win_rate"
    VOLATILITY = "volatility"
    ANOMALY_DETECTION = "anomaly_detection"
    PREDICTIVE_MAINTENANCE = "predictive_maintenance"

@dataclass
class ModelConfig:
    """Configuration for performance prediction models"""
    model_type: ModelType
    algorithm: str  # 'lightgbm', 'random_forest', 'linear'
    hyperparameters: Dict[str, Any] = field(default_factory=dict)
    feature_columns: List[str] = field(default_factory=list)
    target_column: str = ""
    is_classification: bool = False
    retrain_frequency_hours: int = 24
    min_samples_for_training: int = 100

@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_type: ModelType
    accuracy_score: float
    r2_score: Optional[float] = None
    mse: Optional[float] = None
    classification_report: Optional[Dict] = None
    training_samples: int = 0
    last_trained: datetime = field(default_factory=datetime.now)
    prediction_count: int = 0

class PerformanceModelTrainer:
    """
    Specialized trainer for performance analysis prediction models
    """
    
    def __init__(self, ai_training_agent):
        self.ai_training_agent = ai_training_agent
        
        # Model storage
        self.performance_models = {}
        self.model_scalers = {}
        self.model_encoders = {}
        
        # Model configurations
        self.model_configs = self._initialize_model_configs()
        
        # Performance tracking
        self.model_performance = {}
        
        # Training data cache
        self.training_data_cache = {}
        
        # Configuration
        self.config = {
            'models_directory': 'models/performance_analysis',
            'min_training_samples': 100,
            'retrain_threshold_accuracy': 0.8,
            'hyperparameter_optimization': True,
            'cross_validation_folds': 5
        }
        
        # Ensure models directory exists
        Path(self.config['models_directory']).mkdir(parents=True, exist_ok=True)
        
        logger.info("PerformanceModelTrainer initialized")

    def _initialize_model_configs(self) -> Dict[ModelType, ModelConfig]:
        """Initialize default model configurations"""
        configs = {}
        
        # Sharpe Ratio prediction
        configs[ModelType.SHARPE_RATIO] = ModelConfig(
            model_type=ModelType.SHARPE_RATIO,
            algorithm='lightgbm',
            feature_columns=[
                'total_trades', 'win_rate', 'avg_win', 'avg_loss', 'profit_factor',
                'volatility', 'max_drawdown', 'avg_holding_period', 'market_volatility',
                'strategy_age_days', 'recent_performance_trend'
            ],
            target_column='sharpe_ratio',
            is_classification=False,
            retrain_frequency_hours=12
        )
        
        # ROI prediction
        configs[ModelType.ROI] = ModelConfig(
            model_type=ModelType.ROI,
            algorithm='lightgbm',
            feature_columns=[
                'total_trades', 'win_rate', 'profit_factor', 'expectancy',
                'avg_win', 'avg_loss', 'volatility', 'max_drawdown',
                'market_trend', 'strategy_complexity', 'capital_utilization'
            ],
            target_column='roi_percent',
            is_classification=False,
            retrain_frequency_hours=12
        )
        
        # Drawdown prediction
        configs[ModelType.DRAWDOWN] = ModelConfig(
            model_type=ModelType.DRAWDOWN,
            algorithm='random_forest',
            feature_columns=[
                'volatility', 'consecutive_losses', 'position_size_avg',
                'leverage_ratio', 'correlation_with_market', 'strategy_type',
                'risk_per_trade', 'stop_loss_frequency', 'market_regime'
            ],
            target_column='max_drawdown_percent',
            is_classification=False,
            retrain_frequency_hours=6
        )
        
        # Profitability classification
        configs[ModelType.PROFITABILITY] = ModelConfig(
            model_type=ModelType.PROFITABILITY,
            algorithm='lightgbm',
            feature_columns=[
                'win_rate', 'profit_factor', 'expectancy', 'sharpe_ratio',
                'total_trades', 'avg_holding_period', 'market_conditions',
                'strategy_parameters_hash', 'recent_win_streak'
            ],
            target_column='is_profitable',
            is_classification=True,
            retrain_frequency_hours=24
        )
        
        # Anomaly detection
        configs[ModelType.ANOMALY_DETECTION] = ModelConfig(
            model_type=ModelType.ANOMALY_DETECTION,
            algorithm='random_forest',
            feature_columns=[
                'performance_deviation', 'volume_anomaly', 'timing_anomaly',
                'correlation_break', 'volatility_spike', 'drawdown_speed',
                'win_rate_change', 'profit_factor_change'
            ],
            target_column='is_anomaly',
            is_classification=True,
            retrain_frequency_hours=6
        )
        
        return configs

    async def train_performance_models(self, training_data: pl.DataFrame) -> Dict[str, Any]:
        """
        Train all performance prediction models
        
        Args:
            training_data: Historical performance data
            
        Returns:
            Training results summary
        """
        try:
            logger.info("Starting performance models training...")
            
            results = {}
            
            for model_type, config in self.model_configs.items():
                try:
                    # Check if we have enough data
                    if len(training_data) < config.min_samples_for_training:
                        logger.warning(f"Insufficient data for {model_type.value}: {len(training_data)} < {config.min_samples_for_training}")
                        continue
                    
                    # Train individual model
                    model_result = await self._train_single_model(training_data, config)
                    results[model_type.value] = model_result
                    
                    logger.info(f"Trained {model_type.value} model: accuracy={model_result.get('accuracy', 'N/A')}")
                    
                except Exception as e:
                    logger.error(f"Error training {model_type.value} model: {e}")
                    results[model_type.value] = {'error': str(e)}
            
            # Save all models
            await self._save_all_models()
            
            logger.info(f"Performance models training completed: {len(results)} models trained")
            
            return {
                'status': 'success',
                'models_trained': len(results),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in performance models training: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def _train_single_model(self, data: pl.DataFrame, config: ModelConfig) -> Dict[str, Any]:
        """Train a single performance prediction model"""
        try:
            # Prepare data
            X, y = self._prepare_model_data(data, config)
            
            if len(X) < config.min_samples_for_training:
                raise ValueError(f"Insufficient samples: {len(X)} < {config.min_samples_for_training}")
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y if config.is_classification else None
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Store scaler
            self.model_scalers[config.model_type] = scaler
            
            # Train model
            if self.config['hyperparameter_optimization']:
                model = await self._train_with_hyperparameter_optimization(
                    X_train_scaled, y_train, X_test_scaled, y_test, config
                )
            else:
                model = self._train_default_model(X_train_scaled, y_train, config)
            
            # Evaluate model
            evaluation = self._evaluate_model(model, X_test_scaled, y_test, config)
            
            # Store model and performance
            self.performance_models[config.model_type] = model
            self.model_performance[config.model_type] = ModelPerformance(
                model_type=config.model_type,
                accuracy_score=evaluation['accuracy'],
                r2_score=evaluation.get('r2_score'),
                mse=evaluation.get('mse'),
                classification_report=evaluation.get('classification_report'),
                training_samples=len(X_train),
                last_trained=datetime.now()
            )
            
            return {
                'status': 'success',
                'model_type': config.model_type.value,
                'algorithm': config.algorithm,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'accuracy': evaluation['accuracy'],
                'r2_score': evaluation.get('r2_score'),
                'mse': evaluation.get('mse')
            }
            
        except Exception as e:
            logger.error(f"Error training {config.model_type.value} model: {e}")
            raise

    def _prepare_model_data(self, data: pl.DataFrame, config: ModelConfig) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for model training"""
        try:
            # Convert to pandas for easier manipulation
            df = data.to_pandas()
            
            # Check if target column exists
            if config.target_column not in df.columns:
                raise ValueError(f"Target column '{config.target_column}' not found in data")
            
            # Filter available feature columns
            available_features = [col for col in config.feature_columns if col in df.columns]
            
            if not available_features:
                raise ValueError(f"No feature columns available for {config.model_type.value}")
            
            logger.info(f"Using {len(available_features)} features for {config.model_type.value}: {available_features}")
            
            # Extract features and target
            X = df[available_features].values
            y = df[config.target_column].values
            
            # Handle missing values
            X = np.nan_to_num(X, nan=0.0)
            y = np.nan_to_num(y, nan=0.0)
            
            # For classification tasks, encode labels if needed
            if config.is_classification and y.dtype == 'object':
                encoder = LabelEncoder()
                y = encoder.fit_transform(y)
                self.model_encoders[config.model_type] = encoder
            
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing data for {config.model_type.value}: {e}")
            raise

    async def _train_with_hyperparameter_optimization(self, X_train: np.ndarray, y_train: np.ndarray,
                                                    X_test: np.ndarray, y_test: np.ndarray,
                                                    config: ModelConfig):
        """Train model with hyperparameter optimization using Optuna"""
        try:
            def objective(trial):
                if config.algorithm == 'lightgbm':
                    params = {
                        'objective': 'binary' if config.is_classification else 'regression',
                        'metric': 'binary_logloss' if config.is_classification else 'rmse',
                        'boosting_type': 'gbdt',
                        'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                        'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                        'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                        'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                        'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                        'verbosity': -1
                    }
                    
                    if config.is_classification:
                        model = lgb.LGBMClassifier(**params, random_state=42)
                    else:
                        model = lgb.LGBMRegressor(**params, random_state=42)
                        
                elif config.algorithm == 'random_forest':
                    params = {
                        'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                        'max_depth': trial.suggest_int('max_depth', 3, 20),
                        'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 20),
                        'random_state': 42
                    }
                    
                    if config.is_classification:
                        model = RandomForestClassifier(**params)
                    else:
                        model = RandomForestRegressor(**params)
                
                # Train and evaluate
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                
                if config.is_classification:
                    return accuracy_score(y_test, y_pred)
                else:
                    return -mean_squared_error(y_test, y_pred)  # Negative because Optuna maximizes
            
            # Run optimization
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=50, timeout=300)  # 5 minutes max
            
            # Train final model with best parameters
            best_params = study.best_params
            
            if config.algorithm == 'lightgbm':
                best_params.update({
                    'objective': 'binary' if config.is_classification else 'regression',
                    'metric': 'binary_logloss' if config.is_classification else 'rmse',
                    'boosting_type': 'gbdt',
                    'verbosity': -1
                })
                
                if config.is_classification:
                    model = lgb.LGBMClassifier(**best_params, random_state=42)
                else:
                    model = lgb.LGBMRegressor(**best_params, random_state=42)
                    
            elif config.algorithm == 'random_forest':
                best_params['random_state'] = 42
                
                if config.is_classification:
                    model = RandomForestClassifier(**best_params)
                else:
                    model = RandomForestRegressor(**best_params)
            
            model.fit(X_train, y_train)
            
            logger.info(f"Hyperparameter optimization completed for {config.model_type.value}: {study.best_value:.4f}")
            
            return model
            
        except Exception as e:
            logger.error(f"Error in hyperparameter optimization for {config.model_type.value}: {e}")
            # Fall back to default model
            return self._train_default_model(X_train, y_train, config)

    def _train_default_model(self, X_train: np.ndarray, y_train: np.ndarray, config: ModelConfig):
        """Train model with default parameters"""
        try:
            if config.algorithm == 'lightgbm':
                if config.is_classification:
                    model = lgb.LGBMClassifier(random_state=42, verbosity=-1)
                else:
                    model = lgb.LGBMRegressor(random_state=42, verbosity=-1)
                    
            elif config.algorithm == 'random_forest':
                if config.is_classification:
                    model = RandomForestClassifier(n_estimators=100, random_state=42)
                else:
                    model = RandomForestRegressor(n_estimators=100, random_state=42)
                    
            elif config.algorithm == 'linear':
                if config.is_classification:
                    model = LogisticRegression(random_state=42, max_iter=1000)
                else:
                    model = LinearRegression()
            
            model.fit(X_train, y_train)
            return model
            
        except Exception as e:
            logger.error(f"Error training default model for {config.model_type.value}: {e}")
            raise

    def _evaluate_model(self, model, X_test: np.ndarray, y_test: np.ndarray, config: ModelConfig) -> Dict[str, Any]:
        """Evaluate trained model"""
        try:
            y_pred = model.predict(X_test)
            
            evaluation = {}
            
            if config.is_classification:
                accuracy = accuracy_score(y_test, y_pred)
                evaluation['accuracy'] = accuracy
                
                # Classification report
                try:
                    from sklearn.metrics import classification_report
                    report = classification_report(y_test, y_pred, output_dict=True)
                    evaluation['classification_report'] = report
                except:
                    pass
                    
            else:
                # Regression metrics
                mse = mean_squared_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                evaluation['mse'] = mse
                evaluation['r2_score'] = r2
                evaluation['accuracy'] = r2  # Use R² as accuracy for regression
            
            return evaluation
            
        except Exception as e:
            logger.error(f"Error evaluating model for {config.model_type.value}: {e}")
            return {'accuracy': 0.0}

    async def predict_performance_metric(self, features: Dict[str, Any], model_type: ModelType) -> Dict[str, Any]:
        """
        Make performance prediction using trained model
        
        Args:
            features: Feature dictionary
            model_type: Type of prediction to make
            
        Returns:
            Prediction result with confidence
        """
        try:
            if model_type not in self.performance_models:
                raise ValueError(f"Model {model_type.value} not trained")
            
            model = self.performance_models[model_type]
            scaler = self.model_scalers[model_type]
            config = self.model_configs[model_type]
            
            # Prepare features
            feature_values = []
            for feature_name in config.feature_columns:
                value = features.get(feature_name, 0.0)
                feature_values.append(value)
            
            # Scale features
            X = np.array([feature_values])
            X_scaled = scaler.transform(X)
            
            # Make prediction
            prediction = model.predict(X_scaled)[0]
            
            # Get prediction confidence/probability
            confidence = 0.8  # Default confidence
            
            if hasattr(model, 'predict_proba') and config.is_classification:
                probabilities = model.predict_proba(X_scaled)[0]
                confidence = max(probabilities)
            elif hasattr(model, 'predict') and not config.is_classification:
                # For regression, use a simple confidence based on training performance
                model_perf = self.model_performance.get(model_type)
                if model_perf and model_perf.r2_score:
                    confidence = min(max(model_perf.r2_score, 0.1), 0.95)
            
            # Update prediction count
            if model_type in self.model_performance:
                self.model_performance[model_type].prediction_count += 1
            
            return {
                'prediction': float(prediction),
                'confidence': float(confidence),
                'model_type': model_type.value,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error making prediction for {model_type.value}: {e}")
            return {
                'prediction': 0.0,
                'confidence': 0.0,
                'error': str(e),
                'model_type': model_type.value,
                'timestamp': datetime.now().isoformat()
            }

    async def _save_all_models(self):
        """Save all trained models to disk"""
        try:
            models_dir = Path(self.config['models_directory'])
            
            for model_type, model in self.performance_models.items():
                # Save model
                model_path = models_dir / f"{model_type.value}_model.joblib"
                joblib.dump(model, model_path)
                
                # Save scaler
                if model_type in self.model_scalers:
                    scaler_path = models_dir / f"{model_type.value}_scaler.joblib"
                    joblib.dump(self.model_scalers[model_type], scaler_path)
                
                # Save encoder if exists
                if model_type in self.model_encoders:
                    encoder_path = models_dir / f"{model_type.value}_encoder.joblib"
                    joblib.dump(self.model_encoders[model_type], encoder_path)
            
            # Save model performance
            performance_path = models_dir / "model_performance.joblib"
            joblib.dump(self.model_performance, performance_path)
            
            logger.info(f"Saved {len(self.performance_models)} performance models")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")

    async def load_models(self):
        """Load trained models from disk"""
        try:
            models_dir = Path(self.config['models_directory'])
            
            if not models_dir.exists():
                logger.warning("Models directory does not exist")
                return False
            
            loaded_count = 0
            
            for model_type in ModelType:
                model_path = models_dir / f"{model_type.value}_model.joblib"
                scaler_path = models_dir / f"{model_type.value}_scaler.joblib"
                encoder_path = models_dir / f"{model_type.value}_encoder.joblib"
                
                if model_path.exists():
                    # Load model
                    self.performance_models[model_type] = joblib.load(model_path)
                    
                    # Load scaler
                    if scaler_path.exists():
                        self.model_scalers[model_type] = joblib.load(scaler_path)
                    
                    # Load encoder
                    if encoder_path.exists():
                        self.model_encoders[model_type] = joblib.load(encoder_path)
                    
                    loaded_count += 1
            
            # Load model performance
            performance_path = models_dir / "model_performance.joblib"
            if performance_path.exists():
                self.model_performance = joblib.load(performance_path)
            
            logger.info(f"Loaded {loaded_count} performance models")
            return loaded_count > 0
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False

    def get_model_trainer_status(self) -> Dict[str, Any]:
        """Get current status of model trainer"""
        return {
            'models_trained': len(self.performance_models),
            'model_types': [model_type.value for model_type in self.performance_models.keys()],
            'model_performance': {
                model_type.value: {
                    'accuracy': perf.accuracy_score,
                    'r2_score': perf.r2_score,
                    'training_samples': perf.training_samples,
                    'last_trained': perf.last_trained.isoformat(),
                    'prediction_count': perf.prediction_count
                } for model_type, perf in self.model_performance.items()
            },
            'config': self.config
        }
