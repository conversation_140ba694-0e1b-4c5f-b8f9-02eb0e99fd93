#!/usr/bin/env python3
"""
Execution Worker Pool - Dynamic Worker Management for Multi-Agent Trading System

This module implements the execution worker pool that manages multiple execution workers
based on the MAX_TRADES configuration. Each worker handles one trade at a time and is
assigned specific symbols to prevent over-trading.

Features:
🏭 Dynamic Worker Pool
- Create/destroy workers based on MAX_TRADES
- Symbol-based worker assignment
- Load balancing and failover

⚙️ Trade Execution Management
- One trade per worker maximum
- State-based execution control
- Position tracking and monitoring

🔄 Integration with State Manager
- Seamless integration with WorkerStateManager
- Real-time state synchronization
- Recovery and fault tolerance
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import uuid

from .worker_state_manager import WorkerStateManager, WorkerState, WorkerInfo
from .execution_agent import ExecutionAgent
from ..signal_generation.signal_generation_agent import TradingSignal
from ..risk_management.risk_models import RiskAssessment

logger = logging.getLogger(__name__)


@dataclass
class ExecutionWorker:
    """Individual execution worker"""
    worker_id: str
    assigned_symbols: List[str]
    execution_agent: ExecutionAgent
    current_trade_id: Optional[str] = None
    is_active: bool = False
    last_activity: datetime = None
    
    def __post_init__(self):
        if self.last_activity is None:
            self.last_activity = datetime.now()


class ExecutionWorkerPool:
    """
    Manages a pool of execution workers for the multi-agent trading system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Execution Worker Pool"""
        self.config = config or {}
        
        # Core components
        self.state_manager: Optional[WorkerStateManager] = None
        self.workers: Dict[str, ExecutionWorker] = {}
        
        # Configuration
        self.max_trades = int(os.getenv('MAX_TRADES', '5'))
        self.trading_mode = os.getenv('TRADING_MODE', 'paper')
        
        # Event callbacks
        self.on_trade_completed: Optional[Callable] = None
        self.on_trade_failed: Optional[Callable] = None
        self.on_worker_error: Optional[Callable] = None
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info(f"[INIT] Execution Worker Pool initialized - MAX_TRADES: {self.max_trades}")
    
    async def initialize(self, symbol_universe: List[str] = None) -> bool:
        """Initialize the worker pool"""
        try:
            logger.info("[INIT] Initializing Execution Worker Pool...")
            
            # Initialize state manager
            self.state_manager = WorkerStateManager(self.config)
            if not await self.state_manager.initialize(symbol_universe):
                logger.error("[ERROR] Failed to initialize state manager")
                return False
            
            # Create execution workers
            await self._create_execution_workers()
            
            # Start monitoring
            await self._start_monitoring()
            
            self.running = True
            logger.info(f"[SUCCESS] Worker pool initialized with {len(self.workers)} workers")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize worker pool: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the worker pool"""
        try:
            logger.info("[SHUTDOWN] Shutting down Execution Worker Pool...")
            self.running = False
            
            # Cancel monitoring
            if self.monitoring_task:
                self.monitoring_task.cancel()
            
            # Shutdown all workers
            for worker in self.workers.values():
                try:
                    if hasattr(worker.execution_agent, 'shutdown'):
                        await worker.execution_agent.shutdown()
                except Exception as e:
                    logger.error(f"[ERROR] Error shutting down worker {worker.worker_id}: {e}")
            
            # Shutdown state manager
            if self.state_manager:
                await self.state_manager.shutdown()
            
            logger.info("[SUCCESS] Worker pool shutdown complete")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during worker pool shutdown: {e}")
    
    async def _create_execution_workers(self):
        """Create execution workers based on state manager configuration"""
        try:
            logger.info("[WORKERS] Creating execution workers...")
            
            # Get worker information from state manager
            system_status = self.state_manager.get_system_status()
            worker_details = self.state_manager.get_worker_details()
            
            if 'workers' not in worker_details:
                logger.error("[ERROR] No worker details available from state manager")
                return
            
            # Create execution agent for each worker
            for worker_id in worker_details['workers'].keys():
                worker_info = self.state_manager.get_worker_details(worker_id)
                
                if 'error' in worker_info:
                    logger.error(f"[ERROR] Failed to get worker info for {worker_id}: {worker_info['error']}")
                    continue
                
                # Create execution agent for this worker
                execution_agent = ExecutionAgent(
                    config_path="config/execution_config.yaml",
                    trading_mode=self.trading_mode,
                    trading_config=self.config.get('trading_config', {})
                )
                
                # Initialize the execution agent
                if not await execution_agent.initialize():
                    logger.error(f"[ERROR] Failed to initialize execution agent for {worker_id}")
                    continue
                
                # Create worker
                worker = ExecutionWorker(
                    worker_id=worker_id,
                    assigned_symbols=worker_info.get('assigned_symbols', []),
                    execution_agent=execution_agent
                )
                
                self.workers[worker_id] = worker
                logger.info(f"[WORKER] Created execution worker: {worker_id} with {len(worker.assigned_symbols)} symbols")
            
            logger.info(f"[SUCCESS] Created {len(self.workers)} execution workers")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create execution workers: {e}")
            raise
    
    async def _start_monitoring(self):
        """Start background monitoring tasks"""
        try:
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("[MONITORING] Background monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start monitoring: {e}")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
                # Sync worker states with state manager
                await self._sync_worker_states()
                
                # Check for worker health
                await self._check_worker_health()
                
                # Update worker activity
                await self._update_worker_activity()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Monitoring loop error: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _sync_worker_states(self):
        """Synchronize worker states with state manager"""
        try:
            if not self.state_manager:
                return
            
            # Get current state from state manager
            worker_details = self.state_manager.get_worker_details()
            
            if 'workers' not in worker_details:
                return
            
            # Update worker states
            for worker_id, state_info in worker_details['workers'].items():
                if worker_id in self.workers:
                    worker = self.workers[worker_id]
                    
                    # Update active status based on state
                    worker.is_active = (state_info.get('state') == 'ACTIVE')
                    
                    # Update current trade if any
                    if state_info.get('active_position'):
                        # Worker has active position
                        if not worker.current_trade_id:
                            worker.current_trade_id = f"trade_{worker_id}_{datetime.now().timestamp()}"
                    else:
                        # No active position
                        worker.current_trade_id = None
                    
                    worker.last_activity = datetime.now()
                    
        except Exception as e:
            logger.error(f"[ERROR] Error syncing worker states: {e}")
    
    async def _check_worker_health(self):
        """Check health of all workers"""
        try:
            for worker_id, worker in self.workers.items():
                # Check if worker is responsive
                if hasattr(worker.execution_agent, 'health_check'):
                    try:
                        health_ok = await worker.execution_agent.health_check()
                        if not health_ok:
                            logger.warning(f"[HEALTH] Worker {worker_id} health check failed")
                            await self._handle_worker_health_issue(worker_id)
                    except Exception as e:
                        logger.error(f"[HEALTH] Health check error for worker {worker_id}: {e}")
                        await self._handle_worker_health_issue(worker_id)
                
        except Exception as e:
            logger.error(f"[ERROR] Error checking worker health: {e}")
    
    async def _handle_worker_health_issue(self, worker_id: str):
        """Handle worker health issues"""
        try:
            logger.warning(f"[HEALTH] Handling health issue for worker {worker_id}")
            
            # Notify state manager of worker error
            if self.state_manager:
                await self.state_manager.handle_worker_error(worker_id, "Health check failed")
            
            # Trigger callback if set
            if self.on_worker_error:
                await self.on_worker_error(worker_id, "Health check failed")
                
        except Exception as e:
            logger.error(f"[ERROR] Error handling worker health issue for {worker_id}: {e}")
    
    async def _update_worker_activity(self):
        """Update worker activity timestamps"""
        try:
            for worker in self.workers.values():
                if worker.is_active:
                    worker.last_activity = datetime.now()
                    
        except Exception as e:
            logger.error(f"[ERROR] Error updating worker activity: {e}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # PUBLIC API METHODS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def execute_trade(self, signal: TradingSignal, risk_assessment: RiskAssessment) -> Dict[str, Any]:
        """Execute a trade using available worker"""
        try:
            logger.info(f"[EXECUTE] Processing trade request for {signal.symbol}")
            
            # Check if we can accept new trades
            if not self.state_manager.can_accept_new_trade(signal.symbol):
                return {
                    "success": False,
                    "message": "No available worker for this symbol or system at capacity",
                    "worker_id": None
                }
            
            # Get available worker for this symbol
            worker_info = self.state_manager.get_available_worker(signal.symbol)
            if not worker_info:
                return {
                    "success": False,
                    "message": f"No available worker for symbol {signal.symbol}",
                    "worker_id": None
                }
            
            # Get the execution worker
            execution_worker = self.workers.get(worker_info.worker_id)
            if not execution_worker:
                return {
                    "success": False,
                    "message": f"Execution worker {worker_info.worker_id} not found",
                    "worker_id": worker_info.worker_id
                }
            
            # Generate trade ID
            trade_id = f"trade_{signal.symbol}_{datetime.now().timestamp()}"
            
            # Assign trade to worker in state manager
            assignment_success = await self.state_manager.assign_trade_to_worker(
                worker_info.worker_id, trade_id, signal.symbol
            )
            
            if not assignment_success:
                return {
                    "success": False,
                    "message": f"Failed to assign trade to worker {worker_info.worker_id}",
                    "worker_id": worker_info.worker_id
                }
            
            # Execute trade using the worker's execution agent
            execution_result = await execution_worker.execution_agent.execute_signal(signal)
            
            # Update worker state
            execution_worker.current_trade_id = trade_id
            execution_worker.is_active = True
            execution_worker.last_activity = datetime.now()
            
            # Start monitoring this trade
            asyncio.create_task(self._monitor_trade_execution(
                worker_info.worker_id, trade_id, signal, execution_result
            ))
            
            return {
                "success": execution_result[0] if isinstance(execution_result, tuple) else execution_result.get('success', False),
                "message": execution_result[1] if isinstance(execution_result, tuple) else execution_result.get('message', ''),
                "worker_id": worker_info.worker_id,
                "trade_id": trade_id,
                "execution_details": execution_result
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Trade execution failed for {signal.symbol}: {e}")
            return {
                "success": False,
                "message": f"Trade execution error: {str(e)}",
                "worker_id": None
            }

    async def _monitor_trade_execution(self, worker_id: str, trade_id: str, signal: TradingSignal, execution_result: Any):
        """Monitor trade execution and handle completion"""
        try:
            logger.info(f"[MONITOR] Starting trade monitoring for {trade_id} on worker {worker_id}")

            # Get the execution worker
            worker = self.workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found for trade monitoring")
                return

            # Monitor the trade execution
            # This would typically involve checking order status, position updates, etc.
            # For now, we'll implement a simplified monitoring approach

            trade_completed = False
            trade_pnl = 0.0
            monitoring_start = datetime.now()
            max_monitoring_time = timedelta(hours=8)  # Maximum intraday holding time

            while not trade_completed and (datetime.now() - monitoring_start) < max_monitoring_time:
                try:
                    # Check if trade is still active
                    if not worker.is_active or worker.current_trade_id != trade_id:
                        logger.info(f"[MONITOR] Trade {trade_id} no longer active on worker {worker_id}")
                        break

                    # Check execution agent for trade status
                    if hasattr(worker.execution_agent, 'get_trade_status'):
                        trade_status = await worker.execution_agent.get_trade_status(trade_id)

                        if trade_status and trade_status.get('completed'):
                            trade_completed = True
                            trade_pnl = trade_status.get('pnl', 0.0)
                            logger.info(f"[MONITOR] Trade {trade_id} completed with PnL: ₹{trade_pnl:.2f}")
                            break

                    # Wait before next check
                    await asyncio.sleep(30)  # Check every 30 seconds

                except Exception as e:
                    logger.error(f"[ERROR] Error monitoring trade {trade_id}: {e}")
                    await asyncio.sleep(60)  # Wait longer on error

            # Handle trade completion
            if trade_completed or (datetime.now() - monitoring_start) >= max_monitoring_time:
                await self._handle_trade_completion(worker_id, trade_id, trade_pnl, trade_completed)

        except Exception as e:
            logger.error(f"[ERROR] Trade monitoring failed for {trade_id}: {e}")
            await self._handle_trade_completion(worker_id, trade_id, 0.0, False)

    async def _handle_trade_completion(self, worker_id: str, trade_id: str, pnl: float, success: bool):
        """Handle trade completion and worker state transition"""
        try:
            logger.info(f"[COMPLETE] Handling trade completion for {trade_id} on worker {worker_id}")

            # Update worker state
            worker = self.workers.get(worker_id)
            if worker:
                worker.current_trade_id = None
                worker.is_active = False
                worker.last_activity = datetime.now()

            # Complete trade in state manager
            if self.state_manager:
                completion_success = await self.state_manager.complete_worker_trade(worker_id, pnl)
                if not completion_success:
                    logger.error(f"[ERROR] Failed to complete trade in state manager for worker {worker_id}")

            # Trigger completion callback
            if success and self.on_trade_completed:
                await self.on_trade_completed(worker_id, trade_id, pnl)
            elif not success and self.on_trade_failed:
                await self.on_trade_failed(worker_id, trade_id, "Trade monitoring timeout or error")

            logger.info(f"[SUCCESS] Trade completion handled for {trade_id}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling trade completion for {trade_id}: {e}")

    def get_pool_status(self) -> Dict[str, Any]:
        """Get current pool status"""
        try:
            if not self.state_manager:
                return {"status": "not_initialized"}

            # Get system status from state manager
            system_status = self.state_manager.get_system_status()

            # Add worker pool specific information
            active_workers = sum(1 for w in self.workers.values() if w.is_active)
            total_workers = len(self.workers)

            pool_status = {
                "pool_running": self.running,
                "total_execution_workers": total_workers,
                "active_execution_workers": active_workers,
                "idle_execution_workers": total_workers - active_workers,
                "worker_details": {}
            }

            # Add individual worker status
            for worker_id, worker in self.workers.items():
                pool_status["worker_details"][worker_id] = {
                    "is_active": worker.is_active,
                    "current_trade": worker.current_trade_id,
                    "assigned_symbols_count": len(worker.assigned_symbols),
                    "last_activity": worker.last_activity.isoformat()
                }

            # Combine with system status
            return {**system_status, **pool_status}

        except Exception as e:
            logger.error(f"[ERROR] Error getting pool status: {e}")
            return {"status": "error", "error": str(e)}

    def get_worker_assignments(self) -> Dict[str, List[str]]:
        """Get current worker symbol assignments"""
        try:
            assignments = {}
            for worker_id, worker in self.workers.items():
                assignments[worker_id] = worker.assigned_symbols.copy()
            return assignments

        except Exception as e:
            logger.error(f"[ERROR] Error getting worker assignments: {e}")
            return {}

    async def force_complete_trade(self, worker_id: str, reason: str = "Manual intervention") -> bool:
        """Force complete a trade on a specific worker"""
        try:
            worker = self.workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found for force completion")
                return False

            if not worker.is_active or not worker.current_trade_id:
                logger.warning(f"[WARNING] Worker {worker_id} has no active trade to complete")
                return False

            logger.warning(f"[FORCE] Force completing trade {worker.current_trade_id} on worker {worker_id}: {reason}")

            # Try to get current PnL from execution agent
            current_pnl = 0.0
            if hasattr(worker.execution_agent, 'get_current_pnl'):
                try:
                    current_pnl = await worker.execution_agent.get_current_pnl(worker.current_trade_id)
                except Exception as e:
                    logger.error(f"[ERROR] Failed to get current PnL: {e}")

            # Handle completion
            await self._handle_trade_completion(worker_id, worker.current_trade_id, current_pnl, False)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to force complete trade for worker {worker_id}: {e}")
            return False

    async def emergency_stop_all_trades(self) -> Dict[str, bool]:
        """Emergency stop all active trades"""
        try:
            logger.warning("[EMERGENCY] Initiating emergency stop of all active trades")

            results = {}

            for worker_id, worker in self.workers.items():
                if worker.is_active and worker.current_trade_id:
                    try:
                        success = await self.force_complete_trade(worker_id, "Emergency stop")
                        results[worker_id] = success

                        # Also try to cancel orders if possible
                        if hasattr(worker.execution_agent, 'cancel_all_orders'):
                            await worker.execution_agent.cancel_all_orders()

                    except Exception as e:
                        logger.error(f"[ERROR] Emergency stop failed for worker {worker_id}: {e}")
                        results[worker_id] = False
                else:
                    results[worker_id] = True  # No active trade to stop

            # Emergency shutdown of state manager
            if self.state_manager:
                await self.state_manager.emergency_shutdown_all_workers()

            logger.warning("[EMERGENCY] Emergency stop completed")
            return results

        except Exception as e:
            logger.error(f"[ERROR] Emergency stop failed: {e}")
            return {}

    def set_callbacks(self, on_trade_completed: Callable = None,
                     on_trade_failed: Callable = None,
                     on_worker_error: Callable = None):
        """Set event callbacks"""
        self.on_trade_completed = on_trade_completed
        self.on_trade_failed = on_trade_failed
        self.on_worker_error = on_worker_error

        logger.info("[CALLBACKS] Event callbacks configured")

    async def rebalance_workers(self) -> bool:
        """Trigger worker rebalancing"""
        try:
            logger.info("[REBALANCE] Triggering worker rebalancing...")

            if not self.state_manager:
                logger.error("[ERROR] State manager not available for rebalancing")
                return False

            # This would trigger rebalancing in the state manager
            # For now, we'll implement a placeholder

            # Get current assignments
            current_assignments = self.get_worker_assignments()

            # Update worker assignments from state manager
            worker_details = self.state_manager.get_worker_details()

            if 'workers' in worker_details:
                for worker_id in worker_details['workers'].keys():
                    worker_info = self.state_manager.get_worker_details(worker_id)
                    if worker_id in self.workers and 'assigned_symbols' in worker_info:
                        self.workers[worker_id].assigned_symbols = worker_info['assigned_symbols']

            logger.info("[SUCCESS] Worker rebalancing completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Worker rebalancing failed: {e}")
            return False


# ═══════════════════════════════════════════════════════════════════════════════
# UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def create_execution_worker_pool(config: Dict[str, Any] = None) -> ExecutionWorkerPool:
    """Factory function to create ExecutionWorkerPool instance"""
    return ExecutionWorkerPool(config)


async def main():
    """Test function for ExecutionWorkerPool"""
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create and initialize pool
    pool = ExecutionWorkerPool()

    try:
        # Initialize with test symbols
        test_symbols = ['RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'ICICIBANK-EQ']
        await pool.initialize(test_symbols)

        # Print pool status
        status = pool.get_pool_status()
        print(f"Pool Status: {status}")

        # Print worker assignments
        assignments = pool.get_worker_assignments()
        print(f"Worker Assignments: {assignments}")

        # Wait a bit then shutdown
        await asyncio.sleep(5)
        await pool.shutdown()

    except Exception as e:
        print(f"Test failed: {e}")
        await pool.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
