#!/usr/bin/env python3
"""
Worker State Management System
Implements the core worker state management logic for the multi-agent trading system
as described in the system architecture requirements.

Features:
🏭 Dynamic Worker Pool Management
- Create/destroy workers based on MAX_TRADES configuration
- Symbol assignment and load balancing
- Worker lifecycle management

📊 State Management
- IDLE/ACTIVE/COOLDOWN state transitions
- State persistence and recovery
- Performance tracking per worker

🔄 Recovery & Fault Tolerance
- System restart recovery
- Position reconciliation with broker
- State consistency validation
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import polars as pl

logger = logging.getLogger(__name__)


class WorkerState(Enum):
    """Worker state enumeration"""
    IDLE = "IDLE"
    ACTIVE = "ACTIVE"
    COOLDOWN = "COOLDOWN"
    ERROR = "ERROR"
    SHUTDOWN = "SHUTDOWN"


class CooldownType(Enum):
    """Cooldown type enumeration"""
    TIME_BASED = "time_based"
    DAILY_LIMIT = "daily_limit"
    PERFORMANCE_BASED = "performance_based"
    NO_COOLDOWN = "no_cooldown"


@dataclass
class WorkerInfo:
    """Worker information and state"""
    worker_id: str
    assigned_symbols: List[str]
    current_state: WorkerState
    active_position_id: Optional[str] = None
    last_trade_date: Optional[str] = None
    trades_completed_today: int = 0
    cooldown_expiry_time: Optional[datetime] = None
    performance_metrics: Dict[str, float] = None
    created_at: datetime = None
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.performance_metrics is None:
            self.performance_metrics = {
                'total_trades': 0,
                'winning_trades': 0,
                'total_pnl': 0.0,
                'avg_trade_duration_minutes': 0.0,
                'success_rate': 0.0,
                'sharpe_ratio': 0.0
            }
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_updated is None:
            self.last_updated = datetime.now()


@dataclass
class SystemState:
    """Overall system state"""
    max_trades: int
    active_workers: Dict[str, WorkerInfo]
    symbol_assignments: Dict[str, str]  # symbol -> worker_id
    last_backup_time: datetime
    system_start_time: datetime
    total_trades_today: int = 0
    daily_pnl: float = 0.0
    
    def __post_init__(self):
        if self.system_start_time is None:
            self.system_start_time = datetime.now()


class WorkerStateManager:
    """
    Manages worker states, assignments, and lifecycle for the multi-agent trading system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Worker State Manager"""
        self.config = config or {}
        
        # Load configuration from environment
        self.max_trades = int(os.getenv('MAX_TRADES', '5'))
        self.cooldown_type = CooldownType(os.getenv('WORKER_COOLDOWN_TYPE', 'time_based'))
        self.cooldown_minutes = int(os.getenv('WORKER_COOLDOWN_MINUTES', '30'))
        self.symbol_assignment_strategy = os.getenv('SYMBOL_ASSIGNMENT_STRATEGY', 'equal_distribution')
        
        # State persistence configuration
        self.persistence_type = os.getenv('STATE_PERSISTENCE_TYPE', 'file')
        self.persistence_path = Path(os.getenv('STATE_PERSISTENCE_PATH', 'data/system_state'))
        self.backup_interval_minutes = int(os.getenv('STATE_BACKUP_INTERVAL_MINUTES', '5'))
        
        # Initialize system state
        self.system_state: Optional[SystemState] = None
        self.symbol_universe: List[str] = []
        
        # Background tasks
        self.backup_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info(f"[INIT] Worker State Manager initialized - MAX_TRADES: {self.max_trades}")
    
    async def initialize(self, symbol_universe: List[str] = None) -> bool:
        """Initialize the worker state management system"""
        try:
            logger.info("[INIT] Initializing Worker State Manager...")
            
            # Set symbol universe
            if symbol_universe:
                self.symbol_universe = symbol_universe
            else:
                # Load from configuration or use default
                self.symbol_universe = self._load_symbol_universe()
            
            # Ensure persistence directory exists
            self.persistence_path.mkdir(parents=True, exist_ok=True)
            
            # Try to recover existing state
            recovered = await self._recover_system_state()
            
            if not recovered:
                # Create new system state
                await self._create_new_system_state()
            
            # Validate and reconcile state
            await self._validate_system_state()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.running = True
            logger.info(f"[SUCCESS] Worker State Manager initialized with {len(self.system_state.active_workers)} workers")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Worker State Manager: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the worker state manager"""
        try:
            logger.info("[SHUTDOWN] Shutting down Worker State Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.backup_task:
                self.backup_task.cancel()
            if self.cleanup_task:
                self.cleanup_task.cancel()
            
            # Final state backup
            await self._backup_system_state()
            
            # Set all workers to shutdown state
            for worker in self.system_state.active_workers.values():
                worker.current_state = WorkerState.SHUTDOWN
                worker.last_updated = datetime.now()
            
            logger.info("[SUCCESS] Worker State Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during Worker State Manager shutdown: {e}")
    
    def _load_symbol_universe(self) -> List[str]:
        """Load symbol universe from configuration or default"""
        # This would typically load from a configuration file or database
        # For now, return a default set of liquid Indian stocks
        return [
            'RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'HINDUNILVR-EQ',
            'ICICIBANK-EQ', 'KOTAKBANK-EQ', 'SBIN-EQ', 'BHARTIARTL-EQ', 'ITC-EQ',
            'ASIANPAINT-EQ', 'LT-EQ', 'AXISBANK-EQ', 'MARUTI-EQ', 'SUNPHARMA-EQ',
            'TITAN-EQ', 'ULTRACEMCO-EQ', 'WIPRO-EQ', 'NESTLEIND-EQ', 'POWERGRID-EQ'
        ]
    
    async def _recover_system_state(self) -> bool:
        """Attempt to recover system state from persistence"""
        try:
            state_file = self.persistence_path / "system_state.json"
            
            if not state_file.exists():
                logger.info("[RECOVERY] No existing state file found")
                return False
            
            # Load state from file
            with open(state_file, 'r') as f:
                state_data = json.load(f)
            
            # Reconstruct system state
            workers = {}
            for worker_id, worker_data in state_data.get('active_workers', {}).items():
                # Convert datetime strings back to datetime objects
                if worker_data.get('cooldown_expiry_time'):
                    worker_data['cooldown_expiry_time'] = datetime.fromisoformat(worker_data['cooldown_expiry_time'])
                if worker_data.get('created_at'):
                    worker_data['created_at'] = datetime.fromisoformat(worker_data['created_at'])
                if worker_data.get('last_updated'):
                    worker_data['last_updated'] = datetime.fromisoformat(worker_data['last_updated'])
                
                worker_data['current_state'] = WorkerState(worker_data['current_state'])
                workers[worker_id] = WorkerInfo(**worker_data)
            
            self.system_state = SystemState(
                max_trades=state_data['max_trades'],
                active_workers=workers,
                symbol_assignments=state_data.get('symbol_assignments', {}),
                last_backup_time=datetime.fromisoformat(state_data['last_backup_time']),
                system_start_time=datetime.fromisoformat(state_data['system_start_time']),
                total_trades_today=state_data.get('total_trades_today', 0),
                daily_pnl=state_data.get('daily_pnl', 0.0)
            )
            
            logger.info(f"[RECOVERY] System state recovered with {len(workers)} workers")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to recover system state: {e}")
            return False
    
    async def _create_new_system_state(self):
        """Create new system state with fresh workers"""
        try:
            logger.info("[INIT] Creating new system state...")
            
            # Create workers
            workers = {}
            symbol_assignments = {}
            
            # Distribute symbols among workers
            symbols_per_worker = len(self.symbol_universe) // self.max_trades
            remaining_symbols = len(self.symbol_universe) % self.max_trades
            
            symbol_index = 0
            for i in range(self.max_trades):
                worker_id = f"worker_{i+1:03d}"
                
                # Calculate symbols for this worker
                num_symbols = symbols_per_worker + (1 if i < remaining_symbols else 0)
                assigned_symbols = self.symbol_universe[symbol_index:symbol_index + num_symbols]
                symbol_index += num_symbols
                
                # Create worker
                worker = WorkerInfo(
                    worker_id=worker_id,
                    assigned_symbols=assigned_symbols,
                    current_state=WorkerState.IDLE
                )
                workers[worker_id] = worker
                
                # Update symbol assignments
                for symbol in assigned_symbols:
                    symbol_assignments[symbol] = worker_id
                
                logger.info(f"[WORKER] Created {worker_id} with {len(assigned_symbols)} symbols")
            
            # Create system state
            self.system_state = SystemState(
                max_trades=self.max_trades,
                active_workers=workers,
                symbol_assignments=symbol_assignments,
                last_backup_time=datetime.now(),
                system_start_time=datetime.now()
            )
            
            logger.info(f"[SUCCESS] Created new system state with {len(workers)} workers")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create new system state: {e}")
            raise

    async def _validate_system_state(self):
        """Validate and reconcile system state"""
        try:
            logger.info("[VALIDATION] Validating system state...")

            # Check if we need to adjust worker count
            if len(self.system_state.active_workers) != self.max_trades:
                logger.warning(f"[VALIDATION] Worker count mismatch: {len(self.system_state.active_workers)} vs {self.max_trades}")
                await self._adjust_worker_count()

            # Check for new trading day
            today = datetime.now().strftime('%Y-%m-%d')
            needs_daily_reset = False

            for worker in self.system_state.active_workers.values():
                if worker.last_trade_date and worker.last_trade_date != today:
                    needs_daily_reset = True
                    break

            if needs_daily_reset:
                await self._reset_daily_counters()

            # Reconcile with broker positions (if available)
            await self._reconcile_with_broker_positions()

            logger.info("[SUCCESS] System state validation complete")

        except Exception as e:
            logger.error(f"[ERROR] System state validation failed: {e}")
            raise

    async def _adjust_worker_count(self):
        """Adjust worker count to match MAX_TRADES configuration"""
        try:
            current_count = len(self.system_state.active_workers)
            target_count = self.max_trades

            if current_count < target_count:
                # Add workers
                for i in range(current_count, target_count):
                    worker_id = f"worker_{i+1:03d}"
                    worker = WorkerInfo(
                        worker_id=worker_id,
                        assigned_symbols=[],  # Will be assigned in rebalance
                        current_state=WorkerState.IDLE
                    )
                    self.system_state.active_workers[worker_id] = worker
                    logger.info(f"[WORKER] Added new worker: {worker_id}")

                # Rebalance symbol assignments
                await self._rebalance_symbol_assignments()

            elif current_count > target_count:
                # Remove excess workers (only if they're IDLE)
                workers_to_remove = []
                for worker_id, worker in self.system_state.active_workers.items():
                    if len(workers_to_remove) >= (current_count - target_count):
                        break
                    if worker.current_state == WorkerState.IDLE:
                        workers_to_remove.append(worker_id)

                for worker_id in workers_to_remove:
                    # Reassign symbols from removed worker
                    removed_worker = self.system_state.active_workers[worker_id]
                    if removed_worker.assigned_symbols:
                        await self._reassign_symbols(removed_worker.assigned_symbols)

                    del self.system_state.active_workers[worker_id]
                    logger.info(f"[WORKER] Removed worker: {worker_id}")

                if len(workers_to_remove) < (current_count - target_count):
                    logger.warning(f"[WARNING] Could only remove {len(workers_to_remove)} workers, {current_count - target_count - len(workers_to_remove)} workers still active")

        except Exception as e:
            logger.error(f"[ERROR] Failed to adjust worker count: {e}")
            raise

    async def _reset_daily_counters(self):
        """Reset daily counters for new trading day"""
        try:
            logger.info("[RESET] Resetting daily counters for new trading day...")

            today = datetime.now().strftime('%Y-%m-%d')

            for worker in self.system_state.active_workers.values():
                worker.trades_completed_today = 0
                worker.last_trade_date = today

                # Reset cooldown if it's daily limit based
                if (self.cooldown_type == CooldownType.DAILY_LIMIT and
                    worker.current_state == WorkerState.COOLDOWN):
                    worker.current_state = WorkerState.IDLE
                    worker.cooldown_expiry_time = None

                worker.last_updated = datetime.now()

            # Reset system daily counters
            self.system_state.total_trades_today = 0
            self.system_state.daily_pnl = 0.0

            logger.info("[SUCCESS] Daily counters reset complete")

        except Exception as e:
            logger.error(f"[ERROR] Failed to reset daily counters: {e}")

    async def _reconcile_with_broker_positions(self):
        """Reconcile system state with actual broker positions"""
        try:
            logger.info("[RECONCILE] Reconciling with broker positions...")

            # This would typically query the broker API for open positions
            # For now, we'll implement a placeholder that can be extended

            # TODO: Implement broker API integration
            # open_positions = await self._get_broker_positions()

            # For each open position, ensure corresponding worker is in ACTIVE state
            # For each ACTIVE worker without position, set to IDLE

            logger.info("[SUCCESS] Position reconciliation complete")

        except Exception as e:
            logger.error(f"[ERROR] Position reconciliation failed: {e}")

    async def _rebalance_symbol_assignments(self):
        """Rebalance symbol assignments across workers"""
        try:
            logger.info("[REBALANCE] Rebalancing symbol assignments...")

            # Clear existing assignments
            self.system_state.symbol_assignments.clear()
            for worker in self.system_state.active_workers.values():
                worker.assigned_symbols.clear()

            # Redistribute symbols
            if self.symbol_assignment_strategy == 'equal_distribution':
                await self._distribute_symbols_equally()
            elif self.symbol_assignment_strategy == 'performance_based':
                await self._distribute_symbols_by_performance()
            else:
                await self._distribute_symbols_equally()  # Default fallback

            logger.info("[SUCCESS] Symbol rebalancing complete")

        except Exception as e:
            logger.error(f"[ERROR] Symbol rebalancing failed: {e}")

    async def _distribute_symbols_equally(self):
        """Distribute symbols equally among workers"""
        workers = list(self.system_state.active_workers.values())
        symbols_per_worker = len(self.symbol_universe) // len(workers)
        remaining_symbols = len(self.symbol_universe) % len(workers)

        symbol_index = 0
        for i, worker in enumerate(workers):
            num_symbols = symbols_per_worker + (1 if i < remaining_symbols else 0)
            assigned_symbols = self.symbol_universe[symbol_index:symbol_index + num_symbols]
            symbol_index += num_symbols

            worker.assigned_symbols = assigned_symbols
            worker.last_updated = datetime.now()

            # Update symbol assignments mapping
            for symbol in assigned_symbols:
                self.system_state.symbol_assignments[symbol] = worker.worker_id

    async def _distribute_symbols_by_performance(self):
        """Distribute symbols based on worker performance"""
        # Sort workers by performance (success rate, PnL, etc.)
        workers = sorted(
            self.system_state.active_workers.values(),
            key=lambda w: w.performance_metrics.get('success_rate', 0.0),
            reverse=True
        )

        # Give better performing workers more symbols
        total_symbols = len(self.symbol_universe)
        symbol_index = 0

        for i, worker in enumerate(workers):
            # Performance-weighted distribution
            if i == 0:  # Best performer gets more
                num_symbols = int(total_symbols * 0.3)
            elif i < len(workers) // 2:  # Top half
                num_symbols = int(total_symbols * 0.25)
            else:  # Bottom half
                num_symbols = int(total_symbols * 0.15)

            # Ensure we don't exceed available symbols
            num_symbols = min(num_symbols, total_symbols - symbol_index)

            if num_symbols > 0:
                assigned_symbols = self.symbol_universe[symbol_index:symbol_index + num_symbols]
                symbol_index += num_symbols

                worker.assigned_symbols = assigned_symbols
                worker.last_updated = datetime.now()

                # Update symbol assignments mapping
                for symbol in assigned_symbols:
                    self.system_state.symbol_assignments[symbol] = worker.worker_id

        # Distribute remaining symbols equally among all workers
        if symbol_index < total_symbols:
            remaining_symbols = self.symbol_universe[symbol_index:]
            for i, symbol in enumerate(remaining_symbols):
                worker = workers[i % len(workers)]
                worker.assigned_symbols.append(symbol)
                self.system_state.symbol_assignments[symbol] = worker.worker_id

    async def _reassign_symbols(self, symbols_to_reassign: List[str]):
        """Reassign symbols from removed worker to existing workers"""
        available_workers = [w for w in self.system_state.active_workers.values()
                           if w.current_state != WorkerState.SHUTDOWN]

        if not available_workers:
            logger.warning("[WARNING] No available workers for symbol reassignment")
            return

        # Distribute symbols among available workers
        for i, symbol in enumerate(symbols_to_reassign):
            worker = available_workers[i % len(available_workers)]
            worker.assigned_symbols.append(symbol)
            self.system_state.symbol_assignments[symbol] = worker.worker_id
            worker.last_updated = datetime.now()

    async def _start_background_tasks(self):
        """Start background maintenance tasks"""
        try:
            # Start periodic backup task
            self.backup_task = asyncio.create_task(self._periodic_backup_loop())

            # Start cleanup task for expired cooldowns
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())

            logger.info("[TASKS] Background tasks started")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start background tasks: {e}")

    async def _periodic_backup_loop(self):
        """Periodic backup of system state"""
        while self.running:
            try:
                await asyncio.sleep(self.backup_interval_minutes * 60)
                await self._backup_system_state()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Backup loop error: {e}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _cleanup_loop(self):
        """Cleanup expired cooldowns and maintenance"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                await self._process_cooldown_expirations()
                await self._update_performance_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Cleanup loop error: {e}")
                await asyncio.sleep(60)

    async def _process_cooldown_expirations(self):
        """Process expired cooldowns and transition workers to IDLE"""
        try:
            now = datetime.now()

            for worker in self.system_state.active_workers.values():
                if (worker.current_state == WorkerState.COOLDOWN and
                    worker.cooldown_expiry_time and
                    now >= worker.cooldown_expiry_time):

                    worker.current_state = WorkerState.IDLE
                    worker.cooldown_expiry_time = None
                    worker.last_updated = now

                    logger.info(f"[COOLDOWN] Worker {worker.worker_id} cooldown expired, now IDLE")

        except Exception as e:
            logger.error(f"[ERROR] Error processing cooldown expirations: {e}")

    async def _update_performance_metrics(self):
        """Update performance metrics for workers"""
        try:
            # This would typically calculate performance metrics from trade history
            # For now, we'll implement a placeholder
            pass

        except Exception as e:
            logger.error(f"[ERROR] Error updating performance metrics: {e}")

    async def _backup_system_state(self):
        """Backup system state to persistence layer"""
        try:
            if not self.system_state:
                return

            # Prepare state data for serialization
            state_data = {
                'max_trades': self.system_state.max_trades,
                'active_workers': {},
                'symbol_assignments': self.system_state.symbol_assignments,
                'last_backup_time': datetime.now().isoformat(),
                'system_start_time': self.system_state.system_start_time.isoformat(),
                'total_trades_today': self.system_state.total_trades_today,
                'daily_pnl': self.system_state.daily_pnl
            }

            # Convert workers to serializable format
            for worker_id, worker in self.system_state.active_workers.items():
                worker_data = asdict(worker)

                # Convert datetime objects to ISO strings
                if worker_data.get('cooldown_expiry_time'):
                    worker_data['cooldown_expiry_time'] = worker.cooldown_expiry_time.isoformat()
                if worker_data.get('created_at'):
                    worker_data['created_at'] = worker.created_at.isoformat()
                if worker_data.get('last_updated'):
                    worker_data['last_updated'] = worker.last_updated.isoformat()

                # Convert enum to string
                worker_data['current_state'] = worker.current_state.value

                state_data['active_workers'][worker_id] = worker_data

            # Save to file
            state_file = self.persistence_path / "system_state.json"
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)

            # Update backup time
            self.system_state.last_backup_time = datetime.now()

            logger.debug(f"[BACKUP] System state backed up to {state_file}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to backup system state: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # PUBLIC API METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def get_available_worker(self, symbol: str) -> Optional[WorkerInfo]:
        """Get an available worker for the given symbol"""
        try:
            # First, check if there's a worker assigned to this symbol
            if symbol in self.system_state.symbol_assignments:
                worker_id = self.system_state.symbol_assignments[symbol]
                worker = self.system_state.active_workers.get(worker_id)

                if worker and worker.current_state == WorkerState.IDLE:
                    return worker

            # If no specific worker or worker is busy, return None
            # This enforces the one-trade-per-worker rule
            return None

        except Exception as e:
            logger.error(f"[ERROR] Error getting available worker for {symbol}: {e}")
            return None

    async def assign_trade_to_worker(self, worker_id: str, position_id: str, symbol: str) -> bool:
        """Assign a trade to a worker and transition to ACTIVE state"""
        try:
            worker = self.system_state.active_workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found")
                return False

            if worker.current_state != WorkerState.IDLE:
                logger.error(f"[ERROR] Worker {worker_id} is not IDLE (current: {worker.current_state})")
                return False

            # Assign trade
            worker.current_state = WorkerState.ACTIVE
            worker.active_position_id = position_id
            worker.last_updated = datetime.now()

            # Update system counters
            self.system_state.total_trades_today += 1
            worker.trades_completed_today += 1
            worker.last_trade_date = datetime.now().strftime('%Y-%m-%d')

            logger.info(f"[ASSIGN] Trade {position_id} assigned to worker {worker_id} for {symbol}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to assign trade to worker {worker_id}: {e}")
            return False

    async def complete_worker_trade(self, worker_id: str, trade_pnl: float = 0.0) -> bool:
        """Complete a worker's trade and transition to appropriate state"""
        try:
            worker = self.system_state.active_workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found")
                return False

            if worker.current_state != WorkerState.ACTIVE:
                logger.error(f"[ERROR] Worker {worker_id} is not ACTIVE (current: {worker.current_state})")
                return False

            # Update performance metrics
            worker.performance_metrics['total_trades'] += 1
            worker.performance_metrics['total_pnl'] += trade_pnl
            if trade_pnl > 0:
                worker.performance_metrics['winning_trades'] += 1

            # Calculate success rate
            if worker.performance_metrics['total_trades'] > 0:
                worker.performance_metrics['success_rate'] = (
                    worker.performance_metrics['winning_trades'] /
                    worker.performance_metrics['total_trades']
                )

            # Update system PnL
            self.system_state.daily_pnl += trade_pnl

            # Clear active position
            worker.active_position_id = None
            worker.last_updated = datetime.now()

            # Determine next state based on cooldown type
            if self.cooldown_type == CooldownType.NO_COOLDOWN:
                worker.current_state = WorkerState.IDLE
            elif self.cooldown_type == CooldownType.TIME_BASED:
                worker.current_state = WorkerState.COOLDOWN
                worker.cooldown_expiry_time = datetime.now() + timedelta(minutes=self.cooldown_minutes)
            elif self.cooldown_type == CooldownType.DAILY_LIMIT:
                # Check if daily limit reached
                daily_limit = int(os.getenv('PAPER_TRADING_MAX_TRADES_PER_DAY', '50'))
                if worker.trades_completed_today >= daily_limit:
                    worker.current_state = WorkerState.COOLDOWN
                    # Cooldown until next day
                    tomorrow = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0) + timedelta(days=1)
                    worker.cooldown_expiry_time = tomorrow
                else:
                    worker.current_state = WorkerState.IDLE
            elif self.cooldown_type == CooldownType.PERFORMANCE_BASED:
                # Longer cooldown for losses
                if trade_pnl < 0:
                    cooldown_minutes = self.cooldown_minutes * 2
                else:
                    cooldown_minutes = self.cooldown_minutes

                worker.current_state = WorkerState.COOLDOWN
                worker.cooldown_expiry_time = datetime.now() + timedelta(minutes=cooldown_minutes)

            logger.info(f"[COMPLETE] Worker {worker_id} trade completed, PnL: ₹{trade_pnl:.2f}, State: {worker.current_state}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to complete worker trade for {worker_id}: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and metrics"""
        try:
            if not self.system_state:
                return {"status": "not_initialized"}

            # Count workers by state
            state_counts = {}
            for state in WorkerState:
                state_counts[state.value] = 0

            for worker in self.system_state.active_workers.values():
                state_counts[worker.current_state.value] += 1

            # Calculate system metrics
            total_trades = sum(w.performance_metrics.get('total_trades', 0)
                             for w in self.system_state.active_workers.values())
            total_winning_trades = sum(w.performance_metrics.get('winning_trades', 0)
                                     for w in self.system_state.active_workers.values())

            system_success_rate = (total_winning_trades / total_trades) if total_trades > 0 else 0.0

            return {
                "status": "running" if self.running else "stopped",
                "max_trades": self.system_state.max_trades,
                "total_workers": len(self.system_state.active_workers),
                "worker_states": state_counts,
                "total_symbols": len(self.symbol_universe),
                "assigned_symbols": len(self.system_state.symbol_assignments),
                "system_metrics": {
                    "total_trades_today": self.system_state.total_trades_today,
                    "daily_pnl": self.system_state.daily_pnl,
                    "system_success_rate": system_success_rate,
                    "total_lifetime_trades": total_trades
                },
                "last_backup": self.system_state.last_backup_time.isoformat() if self.system_state.last_backup_time else None,
                "system_uptime_hours": (datetime.now() - self.system_state.system_start_time).total_seconds() / 3600
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting system status: {e}")
            return {"status": "error", "error": str(e)}

    def get_worker_details(self, worker_id: str = None) -> Dict[str, Any]:
        """Get detailed information about workers"""
        try:
            if not self.system_state:
                return {"error": "System not initialized"}

            if worker_id:
                # Get specific worker details
                worker = self.system_state.active_workers.get(worker_id)
                if not worker:
                    return {"error": f"Worker {worker_id} not found"}

                return {
                    "worker_id": worker.worker_id,
                    "state": worker.current_state.value,
                    "assigned_symbols": worker.assigned_symbols,
                    "active_position": worker.active_position_id,
                    "trades_today": worker.trades_completed_today,
                    "last_trade_date": worker.last_trade_date,
                    "cooldown_expiry": worker.cooldown_expiry_time.isoformat() if worker.cooldown_expiry_time else None,
                    "performance": worker.performance_metrics,
                    "created_at": worker.created_at.isoformat(),
                    "last_updated": worker.last_updated.isoformat()
                }
            else:
                # Get all workers summary
                workers_summary = {}
                for wid, worker in self.system_state.active_workers.items():
                    workers_summary[wid] = {
                        "state": worker.current_state.value,
                        "symbols_count": len(worker.assigned_symbols),
                        "trades_today": worker.trades_completed_today,
                        "success_rate": worker.performance_metrics.get('success_rate', 0.0),
                        "total_pnl": worker.performance_metrics.get('total_pnl', 0.0),
                        "active_position": worker.active_position_id is not None
                    }

                return {"workers": workers_summary}

        except Exception as e:
            logger.error(f"[ERROR] Error getting worker details: {e}")
            return {"error": str(e)}

    async def handle_worker_error(self, worker_id: str, error_message: str):
        """Handle worker error and transition to error state"""
        try:
            worker = self.system_state.active_workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found for error handling")
                return

            logger.error(f"[WORKER_ERROR] Worker {worker_id} error: {error_message}")

            # Transition to error state
            worker.current_state = WorkerState.ERROR
            worker.last_updated = datetime.now()

            # Clear active position if any
            if worker.active_position_id:
                logger.warning(f"[WARNING] Clearing active position {worker.active_position_id} due to worker error")
                worker.active_position_id = None

            # TODO: Implement error recovery logic
            # For now, we'll set a cooldown and then return to IDLE
            worker.cooldown_expiry_time = datetime.now() + timedelta(minutes=10)

            # Schedule recovery
            asyncio.create_task(self._recover_worker_from_error(worker_id))

        except Exception as e:
            logger.error(f"[ERROR] Failed to handle worker error for {worker_id}: {e}")

    async def _recover_worker_from_error(self, worker_id: str):
        """Attempt to recover worker from error state"""
        try:
            await asyncio.sleep(600)  # Wait 10 minutes

            worker = self.system_state.active_workers.get(worker_id)
            if worker and worker.current_state == WorkerState.ERROR:
                worker.current_state = WorkerState.IDLE
                worker.cooldown_expiry_time = None
                worker.last_updated = datetime.now()

                logger.info(f"[RECOVERY] Worker {worker_id} recovered from error state")

        except Exception as e:
            logger.error(f"[ERROR] Failed to recover worker {worker_id} from error: {e}")

    def can_accept_new_trade(self, symbol: str) -> bool:
        """Check if system can accept a new trade for the given symbol"""
        try:
            if not self.system_state or not self.running:
                return False

            # Check if there's an available worker for this symbol
            worker = self.get_available_worker(symbol)
            return worker is not None

        except Exception as e:
            logger.error(f"[ERROR] Error checking trade acceptance for {symbol}: {e}")
            return False

    def get_active_trades_count(self) -> int:
        """Get count of currently active trades"""
        try:
            if not self.system_state:
                return 0

            return sum(1 for worker in self.system_state.active_workers.values()
                      if worker.current_state == WorkerState.ACTIVE)

        except Exception as e:
            logger.error(f"[ERROR] Error getting active trades count: {e}")
            return 0

    async def force_worker_state_change(self, worker_id: str, new_state: WorkerState, reason: str = "Manual override"):
        """Force a worker state change (admin function)"""
        try:
            worker = self.system_state.active_workers.get(worker_id)
            if not worker:
                logger.error(f"[ERROR] Worker {worker_id} not found for state change")
                return False

            old_state = worker.current_state
            worker.current_state = new_state
            worker.last_updated = datetime.now()

            # Clear position if transitioning away from ACTIVE
            if old_state == WorkerState.ACTIVE and new_state != WorkerState.ACTIVE:
                worker.active_position_id = None

            # Clear cooldown if transitioning to IDLE
            if new_state == WorkerState.IDLE:
                worker.cooldown_expiry_time = None

            logger.info(f"[FORCE] Worker {worker_id} state changed: {old_state.value} → {new_state.value} ({reason})")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to force worker state change for {worker_id}: {e}")
            return False

    async def emergency_shutdown_all_workers(self):
        """Emergency shutdown of all workers"""
        try:
            logger.warning("[EMERGENCY] Initiating emergency shutdown of all workers")

            for worker in self.system_state.active_workers.values():
                worker.current_state = WorkerState.SHUTDOWN
                worker.last_updated = datetime.now()

                if worker.active_position_id:
                    logger.warning(f"[EMERGENCY] Worker {worker.worker_id} has active position {worker.active_position_id}")

            # Stop background tasks
            self.running = False

            # Final backup
            await self._backup_system_state()

            logger.warning("[EMERGENCY] All workers shutdown complete")

        except Exception as e:
            logger.error(f"[ERROR] Emergency shutdown failed: {e}")


# ═══════════════════════════════════════════════════════════════════════════════
# UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def create_worker_state_manager(config: Dict[str, Any] = None) -> WorkerStateManager:
    """Factory function to create WorkerStateManager instance"""
    return WorkerStateManager(config)


async def main():
    """Test function for WorkerStateManager"""
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create and initialize manager
    manager = WorkerStateManager()

    try:
        # Initialize with test symbols
        test_symbols = ['RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'ICICIBANK-EQ']
        await manager.initialize(test_symbols)

        # Print system status
        status = manager.get_system_status()
        print(f"System Status: {json.dumps(status, indent=2)}")

        # Test worker assignment
        worker = manager.get_available_worker('RELIANCE-EQ')
        if worker:
            print(f"Available worker for RELIANCE-EQ: {worker.worker_id}")

            # Assign trade
            success = await manager.assign_trade_to_worker(worker.worker_id, "test_position_1", "RELIANCE-EQ")
            print(f"Trade assignment success: {success}")

            # Complete trade
            await asyncio.sleep(1)
            success = await manager.complete_worker_trade(worker.worker_id, 150.0)
            print(f"Trade completion success: {success}")

        # Wait a bit then shutdown
        await asyncio.sleep(2)
        await manager.shutdown()

    except Exception as e:
        print(f"Test failed: {e}")
        await manager.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
