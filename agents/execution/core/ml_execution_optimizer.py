#!/usr/bin/env python3
"""
ML-based Execution Optimization Module

Advanced machine learning-driven execution optimization including:
- Dynamic slippage and market impact prediction
- Adaptive order sizing based on real-time conditions
- Optimal execution algorithm selection
- Real-time profitability and drawdown re-evaluation
- Market regime detection and adaptation
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import asyncio
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import joblib
import os

logger = logging.getLogger(__name__)


class ExecutionAlgorithm(Enum):
    """Available execution algorithms"""
    MARKET = "market"
    LIMIT = "limit"
    VWAP = "vwap"
    TWAP = "twap"
    POV = "pov"  # Percentage of Volume
    ICEBERG = "iceberg"
    ADAPTIVE = "adaptive"


class MarketRegime(Enum):
    """Market regime classifications"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    OPENING = "opening"
    CLOSING = "closing"


@dataclass
class SlippagePrediction:
    """Slippage prediction result"""
    predicted_slippage_bps: float
    confidence: float
    market_impact_bps: float
    optimal_chunk_size: int
    execution_urgency: float
    features_used: Dict[str, float]
    timestamp: datetime


@dataclass
class ExecutionRecommendation:
    """Execution algorithm recommendation"""
    algorithm: ExecutionAlgorithm
    parameters: Dict[str, Any]
    confidence: float
    expected_slippage_bps: float
    expected_duration_seconds: float
    reasoning: str
    timestamp: datetime


@dataclass
class MarketRegimeAnalysis:
    """Market regime analysis result"""
    current_regime: MarketRegime
    regime_confidence: float
    volatility_percentile: float
    trend_strength: float
    liquidity_score: float
    regime_duration_minutes: float
    next_regime_probability: Dict[MarketRegime, float]
    timestamp: datetime


class MLExecutionOptimizer:
    """
    ML-based Execution Optimizer
    
    Features:
    - Real-time slippage prediction using market microstructure data
    - Adaptive order sizing based on liquidity and volatility
    - Execution algorithm selection based on market conditions
    - Market regime detection for strategy adaptation
    - Dynamic stop-loss and take-profit adjustment
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize ML Execution Optimizer"""
        self.config = config
        self.ml_config = config.get('ml_execution', {})
        
        # Model paths
        self.model_dir = self.ml_config.get('model_directory', 'models/execution/')
        os.makedirs(self.model_dir, exist_ok=True)
        
        # ML Models
        self.slippage_model: Optional[RandomForestRegressor] = None
        self.regime_model: Optional[GradientBoostingClassifier] = None
        self.impact_model: Optional[RandomForestRegressor] = None
        self.scalers: Dict[str, StandardScaler] = {}
        
        # Historical data for model training
        self.execution_history: List[Dict[str, Any]] = []
        self.market_data_history: List[Dict[str, Any]] = []
        
        # Real-time state
        self.current_regime: Optional[MarketRegimeAnalysis] = None
        self.regime_history: List[MarketRegimeAnalysis] = []
        
        # Performance tracking
        self.prediction_accuracy: Dict[str, float] = {}
        self.model_performance: Dict[str, Dict[str, float]] = {}
        
        # Configuration parameters
        self.min_training_samples = self.ml_config.get('min_training_samples', 100)
        self.retrain_frequency_hours = self.ml_config.get('retrain_frequency_hours', 24)
        self.feature_window_minutes = self.ml_config.get('feature_window_minutes', 30)
        
        logger.info("[INIT] ML Execution Optimizer initialized")
    
    async def setup(self):
        """Setup ML models and load historical data"""
        try:
            # Load pre-trained models if available
            await self._load_models()
            
            # Load historical execution data
            await self._load_historical_data()
            
            # Initialize models if not loaded
            if not self.slippage_model:
                await self._initialize_models()
            
            # Start background tasks
            asyncio.create_task(self._model_retraining_loop())
            asyncio.create_task(self._regime_monitoring_loop())
            
            logger.info("[SUCCESS] ML Execution Optimizer setup completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] ML Execution Optimizer setup failed: {e}")
            return False
    
    async def predict_slippage(
        self, 
        symbol: str, 
        quantity: int, 
        side: str,
        market_data: Dict[str, Any]
    ) -> SlippagePrediction:
        """
        Predict slippage for a given order using ML models
        
        Args:
            symbol: Trading symbol
            quantity: Order quantity
            side: BUY or SELL
            market_data: Real-time market data
            
        Returns:
            SlippagePrediction with expected slippage and recommendations
        """
        try:
            # Extract features for prediction
            features = await self._extract_slippage_features(symbol, quantity, side, market_data)
            
            if not self.slippage_model:
                # Fallback to heuristic prediction
                return await self._heuristic_slippage_prediction(symbol, quantity, side, market_data, features)
            
            # Prepare features for model
            feature_array = self._prepare_features_for_model(features, 'slippage')
            
            # Predict slippage
            predicted_slippage_bps = self.slippage_model.predict([feature_array])[0]
            
            # Calculate confidence based on model uncertainty
            confidence = self._calculate_prediction_confidence(feature_array, 'slippage')
            
            # Predict market impact
            market_impact_bps = await self._predict_market_impact(features)
            
            # Calculate optimal chunk size
            optimal_chunk_size = await self._calculate_optimal_chunk_size(quantity, features)
            
            # Determine execution urgency
            execution_urgency = self._calculate_execution_urgency(features)
            
            return SlippagePrediction(
                predicted_slippage_bps=max(0, predicted_slippage_bps),
                confidence=confidence,
                market_impact_bps=market_impact_bps,
                optimal_chunk_size=optimal_chunk_size,
                execution_urgency=execution_urgency,
                features_used=features,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Slippage prediction failed: {e}")
            # Return conservative fallback prediction
            return SlippagePrediction(
                predicted_slippage_bps=10.0,  # 10 bps conservative estimate
                confidence=0.3,
                market_impact_bps=5.0,
                optimal_chunk_size=min(quantity, 100),
                execution_urgency=0.5,
                features_used={},
                timestamp=datetime.now()
            )
    
    async def recommend_execution_algorithm(
        self, 
        symbol: str, 
        quantity: int, 
        side: str,
        urgency: float,
        market_data: Dict[str, Any]
    ) -> ExecutionRecommendation:
        """
        Recommend optimal execution algorithm based on market conditions
        
        Args:
            symbol: Trading symbol
            quantity: Order quantity
            side: BUY or SELL
            urgency: Execution urgency (0-1, higher = more urgent)
            market_data: Real-time market data
            
        Returns:
            ExecutionRecommendation with algorithm and parameters
        """
        try:
            # Get current market regime
            regime_analysis = await self.detect_market_regime(symbol, market_data)
            
            # Extract market condition features
            features = await self._extract_execution_features(symbol, quantity, side, market_data)
            
            # Determine optimal algorithm based on conditions
            algorithm, parameters, reasoning = await self._select_optimal_algorithm(
                features, regime_analysis, urgency
            )
            
            # Predict expected slippage for this algorithm
            slippage_prediction = await self.predict_slippage(symbol, quantity, side, market_data)
            
            # Estimate execution duration
            expected_duration = self._estimate_execution_duration(
                algorithm, quantity, features, parameters
            )
            
            # Calculate confidence based on historical performance
            confidence = self._calculate_algorithm_confidence(algorithm, features)
            
            return ExecutionRecommendation(
                algorithm=algorithm,
                parameters=parameters,
                confidence=confidence,
                expected_slippage_bps=slippage_prediction.predicted_slippage_bps,
                expected_duration_seconds=expected_duration,
                reasoning=reasoning,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Algorithm recommendation failed: {e}")
            # Return conservative fallback
            return ExecutionRecommendation(
                algorithm=ExecutionAlgorithm.LIMIT,
                parameters={'limit_price_offset_bps': 5},
                confidence=0.5,
                expected_slippage_bps=8.0,
                expected_duration_seconds=60.0,
                reasoning="Fallback recommendation due to error",
                timestamp=datetime.now()
            )
    
    async def detect_market_regime(
        self, 
        symbol: str, 
        market_data: Dict[str, Any]
    ) -> MarketRegimeAnalysis:
        """
        Detect current market regime using ML and technical analysis
        
        Args:
            symbol: Trading symbol
            market_data: Real-time market data
            
        Returns:
            MarketRegimeAnalysis with regime classification
        """
        try:
            # Extract regime detection features
            features = await self._extract_regime_features(symbol, market_data)
            
            if not self.regime_model:
                # Fallback to heuristic regime detection
                return await self._heuristic_regime_detection(symbol, market_data, features)
            
            # Prepare features for model
            feature_array = self._prepare_features_for_model(features, 'regime')
            
            # Predict regime
            regime_probs = self.regime_model.predict_proba([feature_array])[0]
            regime_classes = self.regime_model.classes_
            
            # Get most likely regime
            max_prob_idx = np.argmax(regime_probs)
            current_regime = MarketRegime(regime_classes[max_prob_idx])
            regime_confidence = regime_probs[max_prob_idx]
            
            # Calculate additional metrics
            volatility_percentile = self._calculate_volatility_percentile(features)
            trend_strength = self._calculate_trend_strength(features)
            liquidity_score = self._calculate_liquidity_score(features)
            
            # Estimate regime duration
            regime_duration = self._estimate_regime_duration(current_regime, features)
            
            # Calculate next regime probabilities
            next_regime_probs = self._calculate_regime_transition_probabilities(
                current_regime, features
            )
            
            regime_analysis = MarketRegimeAnalysis(
                current_regime=current_regime,
                regime_confidence=regime_confidence,
                volatility_percentile=volatility_percentile,
                trend_strength=trend_strength,
                liquidity_score=liquidity_score,
                regime_duration_minutes=regime_duration,
                next_regime_probability=next_regime_probs,
                timestamp=datetime.now()
            )
            
            # Update current regime
            self.current_regime = regime_analysis
            self.regime_history.append(regime_analysis)
            
            # Keep only recent history
            if len(self.regime_history) > 1000:
                self.regime_history = self.regime_history[-1000:]
            
            return regime_analysis
            
        except Exception as e:
            logger.error(f"[ERROR] Market regime detection failed: {e}")
            # Return neutral regime as fallback
            return MarketRegimeAnalysis(
                current_regime=MarketRegime.RANGING,
                regime_confidence=0.5,
                volatility_percentile=50.0,
                trend_strength=0.0,
                liquidity_score=0.5,
                regime_duration_minutes=30.0,
                next_regime_probability={regime: 1.0/len(MarketRegime) for regime in MarketRegime},
                timestamp=datetime.now()
            )
    
    async def adaptive_order_sizing(
        self, 
        base_quantity: int, 
        symbol: str,
        market_data: Dict[str, Any],
        risk_tolerance: float = 0.5
    ) -> Tuple[int, str]:
        """
        Adaptively adjust order size based on market conditions
        
        Args:
            base_quantity: Original intended quantity
            symbol: Trading symbol
            market_data: Real-time market data
            risk_tolerance: Risk tolerance (0-1, higher = more aggressive)
            
        Returns:
            Tuple of (adjusted_quantity, reasoning)
        """
        try:
            # Get market regime
            regime_analysis = await self.detect_market_regime(symbol, market_data)
            
            # Extract liquidity and volatility features
            features = await self._extract_sizing_features(symbol, market_data)
            
            # Base adjustment factors
            liquidity_factor = 1.0
            volatility_factor = 1.0
            regime_factor = 1.0
            
            # Adjust based on liquidity
            volume_ratio = features.get('volume_ratio', 1.0)
            if volume_ratio < 0.5:  # Low liquidity
                liquidity_factor = 0.7
            elif volume_ratio > 2.0:  # High liquidity
                liquidity_factor = 1.2
            
            # Adjust based on volatility
            volatility_percentile = regime_analysis.volatility_percentile
            if volatility_percentile > 80:  # High volatility
                volatility_factor = 0.8
            elif volatility_percentile < 20:  # Low volatility
                volatility_factor = 1.1
            
            # Adjust based on market regime
            if regime_analysis.current_regime == MarketRegime.HIGH_VOLATILITY:
                regime_factor = 0.7
            elif regime_analysis.current_regime in [MarketRegime.OPENING, MarketRegime.CLOSING]:
                regime_factor = 0.8
            elif regime_analysis.current_regime == MarketRegime.RANGING:
                regime_factor = 1.1
            
            # Apply risk tolerance
            risk_factor = 0.5 + (risk_tolerance * 0.5)  # Scale from 0.5 to 1.0
            
            # Calculate final adjustment
            total_factor = liquidity_factor * volatility_factor * regime_factor * risk_factor
            adjusted_quantity = max(1, int(base_quantity * total_factor))
            
            # Create reasoning
            reasoning = f"Adjusted by {total_factor:.2f}x: liquidity={liquidity_factor:.2f}, " \
                       f"volatility={volatility_factor:.2f}, regime={regime_factor:.2f}, " \
                       f"risk={risk_factor:.2f}"
            
            return adjusted_quantity, reasoning
            
        except Exception as e:
            logger.error(f"[ERROR] Adaptive order sizing failed: {e}")
            return base_quantity, "No adjustment due to error"

    async def _extract_slippage_features(
        self,
        symbol: str,
        quantity: int,
        side: str,
        market_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """Extract features for slippage prediction"""
        try:
            features = {}

            # Order characteristics
            features['quantity'] = float(quantity)
            features['side_numeric'] = 1.0 if side == 'BUY' else -1.0

            # Market data features
            features['bid_price'] = market_data.get('bid', 0.0)
            features['ask_price'] = market_data.get('ask', 0.0)
            features['last_price'] = market_data.get('ltp', 0.0)
            features['volume'] = market_data.get('volume', 0.0)
            features['avg_volume'] = market_data.get('avg_volume', features['volume'])

            # Spread and liquidity metrics
            if features['bid_price'] > 0 and features['ask_price'] > 0:
                features['bid_ask_spread'] = features['ask_price'] - features['bid_price']
                features['spread_bps'] = (features['bid_ask_spread'] / features['last_price']) * 10000
                features['mid_price'] = (features['bid_price'] + features['ask_price']) / 2
            else:
                features['bid_ask_spread'] = 0.0
                features['spread_bps'] = 0.0
                features['mid_price'] = features['last_price']

            # Volume metrics
            if features['avg_volume'] > 0:
                features['volume_ratio'] = features['volume'] / features['avg_volume']
            else:
                features['volume_ratio'] = 1.0

            # Order size relative to volume
            if features['volume'] > 0:
                features['quantity_volume_ratio'] = quantity / features['volume']
            else:
                features['quantity_volume_ratio'] = 0.1  # Conservative default

            # Time-based features
            now = datetime.now()
            features['hour_of_day'] = now.hour
            features['minute_of_hour'] = now.minute
            features['is_market_open'] = 1.0 if 9 <= now.hour <= 15 else 0.0
            features['is_opening_hour'] = 1.0 if now.hour == 9 else 0.0
            features['is_closing_hour'] = 1.0 if now.hour == 15 else 0.0

            # Volatility features
            features['volatility'] = market_data.get('volatility', 0.02)
            features['price_change_percent'] = market_data.get('price_change_percent', 0.0)

            return features

        except Exception as e:
            logger.error(f"[ERROR] Error extracting slippage features: {e}")
            return {'default_feature': 1.0}

    async def _heuristic_slippage_prediction(
        self,
        symbol: str,
        quantity: int,
        side: str,
        market_data: Dict[str, Any],
        features: Dict[str, float]
    ) -> SlippagePrediction:
        """Fallback heuristic slippage prediction when ML model unavailable"""
        try:
            # Base slippage estimate
            base_slippage_bps = 5.0

            # Adjust for spread
            spread_bps = features.get('spread_bps', 5.0)
            spread_adjustment = min(spread_bps * 0.5, 10.0)  # Max 10 bps from spread

            # Adjust for volume
            volume_ratio = features.get('volume_ratio', 1.0)
            if volume_ratio < 0.5:
                volume_adjustment = 5.0  # Low volume penalty
            elif volume_ratio > 2.0:
                volume_adjustment = -2.0  # High volume benefit
            else:
                volume_adjustment = 0.0

            # Adjust for order size
            quantity_volume_ratio = features.get('quantity_volume_ratio', 0.1)
            if quantity_volume_ratio > 0.1:
                size_adjustment = min(quantity_volume_ratio * 20, 15.0)  # Max 15 bps
            else:
                size_adjustment = 0.0

            # Time-based adjustment
            time_adjustment = 0.0
            if features.get('is_opening_hour', 0) or features.get('is_closing_hour', 0):
                time_adjustment = 3.0  # Higher slippage during market open/close

            # Calculate total predicted slippage
            predicted_slippage = base_slippage_bps + spread_adjustment + volume_adjustment + size_adjustment + time_adjustment
            predicted_slippage = max(1.0, predicted_slippage)  # Minimum 1 bps

            # Market impact estimate
            market_impact = min(predicted_slippage * 0.6, 8.0)

            # Optimal chunk size (conservative)
            daily_volume = features.get('avg_volume', 100000)
            optimal_chunk = min(quantity, max(10, int(daily_volume * 0.01)))  # Max 1% of daily volume

            return SlippagePrediction(
                predicted_slippage_bps=predicted_slippage,
                confidence=0.6,  # Lower confidence for heuristic
                market_impact_bps=market_impact,
                optimal_chunk_size=optimal_chunk,
                execution_urgency=0.5,
                features_used=features,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Heuristic slippage prediction failed: {e}")
            return SlippagePrediction(
                predicted_slippage_bps=10.0,
                confidence=0.3,
                market_impact_bps=5.0,
                optimal_chunk_size=min(quantity, 50),
                execution_urgency=0.5,
                features_used={},
                timestamp=datetime.now()
            )

    async def _select_optimal_algorithm(
        self,
        features: Dict[str, float],
        regime_analysis: MarketRegimeAnalysis,
        urgency: float
    ) -> Tuple[ExecutionAlgorithm, Dict[str, Any], str]:
        """Select optimal execution algorithm based on conditions"""
        try:
            # High urgency - use market orders
            if urgency > 0.8:
                return ExecutionAlgorithm.MARKET, {}, "High urgency requires immediate execution"

            # Low liquidity - use careful limit orders
            if regime_analysis.liquidity_score < 0.3:
                return ExecutionAlgorithm.LIMIT, {
                    'limit_price_offset_bps': 3,
                    'timeout_seconds': 300
                }, "Low liquidity requires patient limit orders"

            # High volatility - use adaptive algorithm
            if regime_analysis.volatility_percentile > 80:
                return ExecutionAlgorithm.ADAPTIVE, {
                    'volatility_threshold': 0.03,
                    'chunk_size_factor': 0.5
                }, "High volatility requires adaptive execution"

            # Large orders - use VWAP or TWAP
            quantity_volume_ratio = features.get('quantity_volume_ratio', 0.1)
            if quantity_volume_ratio > 0.05:  # Large relative to volume
                if regime_analysis.current_regime == MarketRegime.TRENDING_UP or \
                   regime_analysis.current_regime == MarketRegime.TRENDING_DOWN:
                    return ExecutionAlgorithm.VWAP, {
                        'participation_rate': 0.1,
                        'max_duration_minutes': 30
                    }, "Large order in trending market - use VWAP"
                else:
                    return ExecutionAlgorithm.TWAP, {
                        'duration_minutes': 20,
                        'chunk_count': 10
                    }, "Large order in ranging market - use TWAP"

            # Opening/closing periods - use POV
            if regime_analysis.current_regime in [MarketRegime.OPENING, MarketRegime.CLOSING]:
                return ExecutionAlgorithm.POV, {
                    'participation_rate': 0.15,
                    'max_rate': 0.25
                }, "Market open/close period - use POV"

            # Default to limit orders for normal conditions
            return ExecutionAlgorithm.LIMIT, {
                'limit_price_offset_bps': 2,
                'timeout_seconds': 180
            }, "Normal market conditions - use limit orders"

        except Exception as e:
            logger.error(f"[ERROR] Algorithm selection failed: {e}")
            return ExecutionAlgorithm.LIMIT, {'limit_price_offset_bps': 5}, "Fallback algorithm selection"

    async def _load_models(self):
        """Load pre-trained ML models"""
        try:
            # Load slippage prediction model
            slippage_model_path = os.path.join(self.model_dir, 'slippage_model.joblib')
            if os.path.exists(slippage_model_path):
                self.slippage_model = joblib.load(slippage_model_path)
                logger.info("[SUCCESS] Slippage prediction model loaded")

            # Load regime detection model
            regime_model_path = os.path.join(self.model_dir, 'regime_model.joblib')
            if os.path.exists(regime_model_path):
                self.regime_model = joblib.load(regime_model_path)
                logger.info("[SUCCESS] Market regime model loaded")

            # Load feature scalers
            scaler_path = os.path.join(self.model_dir, 'scalers.joblib')
            if os.path.exists(scaler_path):
                self.scalers = joblib.load(scaler_path)
                logger.info("[SUCCESS] Feature scalers loaded")

        except Exception as e:
            logger.error(f"[ERROR] Error loading models: {e}")

    async def _initialize_models(self):
        """Initialize ML models with default parameters"""
        try:
            # Initialize slippage prediction model
            self.slippage_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )

            # Initialize regime detection model
            self.regime_model = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            )

            # Initialize scalers
            self.scalers = {
                'slippage': StandardScaler(),
                'regime': StandardScaler()
            }

            logger.info("[SUCCESS] ML models initialized with default parameters")

        except Exception as e:
            logger.error(f"[ERROR] Error initializing models: {e}")

    def record_execution_result(
        self,
        symbol: str,
        quantity: int,
        side: str,
        predicted_slippage: float,
        actual_slippage: float,
        execution_time: float,
        market_data: Dict[str, Any]
    ):
        """Record execution result for model training"""
        try:
            execution_record = {
                'symbol': symbol,
                'quantity': quantity,
                'side': side,
                'predicted_slippage_bps': predicted_slippage,
                'actual_slippage_bps': actual_slippage,
                'execution_time_seconds': execution_time,
                'market_data': market_data,
                'timestamp': datetime.now()
            }

            self.execution_history.append(execution_record)

            # Keep only recent history
            if len(self.execution_history) > 10000:
                self.execution_history = self.execution_history[-10000:]

            # Update prediction accuracy
            error = abs(predicted_slippage - actual_slippage)
            if 'slippage_mae' not in self.prediction_accuracy:
                self.prediction_accuracy['slippage_mae'] = error
            else:
                # Exponential moving average
                alpha = 0.1
                self.prediction_accuracy['slippage_mae'] = (
                    alpha * error + (1 - alpha) * self.prediction_accuracy['slippage_mae']
                )

            logger.debug(f"[RECORD] Execution result recorded: {symbol} slippage {actual_slippage:.2f} bps")

        except Exception as e:
            logger.error(f"[ERROR] Error recording execution result: {e}")

    async def get_optimization_summary(self) -> Dict[str, Any]:
        """Get comprehensive optimization summary"""
        try:
            return {
                'models_loaded': {
                    'slippage_model': self.slippage_model is not None,
                    'regime_model': self.regime_model is not None,
                    'scalers': len(self.scalers) > 0
                },
                'data_statistics': {
                    'execution_history_count': len(self.execution_history),
                    'market_data_history_count': len(self.market_data_history),
                    'regime_history_count': len(self.regime_history)
                },
                'current_regime': {
                    'regime': self.current_regime.current_regime.value if self.current_regime else None,
                    'confidence': self.current_regime.regime_confidence if self.current_regime else 0.0,
                    'volatility_percentile': self.current_regime.volatility_percentile if self.current_regime else 50.0
                },
                'prediction_accuracy': self.prediction_accuracy,
                'model_performance': self.model_performance,
                'configuration': {
                    'min_training_samples': self.min_training_samples,
                    'retrain_frequency_hours': self.retrain_frequency_hours,
                    'feature_window_minutes': self.feature_window_minutes
                }
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting optimization summary: {e}")
            return {'error': str(e)}
