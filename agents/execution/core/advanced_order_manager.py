#!/usr/bin/env python3
"""
Advanced Order Management System

Comprehensive order management with advanced order types, partial fills handling,
order routing optimization, and intelligent order lifecycle management.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
from collections import defaultdict, deque
import numpy as np

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Advanced order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    STOP_LIMIT = "stop_limit"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"
    POV = "pov"  # Percentage of Volume
    BRACKET = "bracket"
    OCO = "oco"  # One Cancels Other
    TRAILING_STOP = "trailing_stop"
    ADAPTIVE = "adaptive"


class OrderStatus(Enum):
    """Order status states"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    SUSPENDED = "suspended"


class OrderPriority(Enum):
    """Order execution priority"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class OrderExecution:
    """Individual order execution record"""
    execution_id: str
    timestamp: datetime
    quantity: int
    price: float
    commission: float
    exchange_order_id: str
    execution_venue: str
    liquidity_flag: str  # 'maker' or 'taker'


@dataclass
class AdvancedOrder:
    """Advanced order with comprehensive tracking"""
    order_id: str
    parent_order_id: Optional[str]
    symbol: str
    side: str  # BUY or SELL
    order_type: OrderType
    quantity: int
    filled_quantity: int = 0
    remaining_quantity: int = field(init=False)
    price: Optional[float] = None
    stop_price: Optional[float] = None
    limit_price: Optional[float] = None
    
    # Advanced parameters
    time_in_force: str = "DAY"  # DAY, GTC, IOC, FOK
    priority: OrderPriority = OrderPriority.NORMAL
    max_show_quantity: Optional[int] = None  # For iceberg orders
    participation_rate: Optional[float] = None  # For POV orders
    duration_minutes: Optional[int] = None  # For TWAP orders
    
    # State tracking
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    submitted_at: Optional[datetime] = None
    last_updated: datetime = field(default_factory=datetime.now)
    
    # Execution tracking
    executions: List[OrderExecution] = field(default_factory=list)
    average_fill_price: float = 0.0
    total_commission: float = 0.0
    
    # Routing and venue
    preferred_venue: Optional[str] = None
    routing_strategy: str = "smart"
    
    # Risk and compliance
    risk_approved: bool = False
    compliance_checked: bool = False
    
    # Metadata
    strategy_name: Optional[str] = None
    signal_id: Optional[str] = None
    tags: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        self.remaining_quantity = self.quantity - self.filled_quantity
    
    def add_execution(self, execution: OrderExecution):
        """Add execution to order"""
        self.executions.append(execution)
        self.filled_quantity += execution.quantity
        self.remaining_quantity = self.quantity - self.filled_quantity
        self.total_commission += execution.commission
        
        # Update average fill price
        if self.filled_quantity > 0:
            total_value = sum(exec.quantity * exec.price for exec in self.executions)
            self.average_fill_price = total_value / self.filled_quantity
        
        # Update status
        if self.remaining_quantity == 0:
            self.status = OrderStatus.FILLED
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIALLY_FILLED
        
        self.last_updated = datetime.now()
    
    def get_fill_rate(self) -> float:
        """Get order fill rate as percentage"""
        return (self.filled_quantity / self.quantity) * 100 if self.quantity > 0 else 0.0


@dataclass
class OrderRoutingDecision:
    """Order routing decision result"""
    venue: str
    routing_reason: str
    expected_fill_probability: float
    expected_slippage_bps: float
    estimated_commission: float
    routing_confidence: float
    alternative_venues: List[str]
    timestamp: datetime


class AdvancedOrderManager:
    """
    Advanced Order Management System
    
    Features:
    - Advanced order types (TWAP, VWAP, Iceberg, etc.)
    - Intelligent order routing
    - Partial fill management
    - Order lifecycle tracking
    - Performance analytics
    - Risk integration
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Advanced Order Manager"""
        self.config = config
        self.order_config = config.get('order_management', {})
        
        # Order storage
        self.active_orders: Dict[str, AdvancedOrder] = {}
        self.completed_orders: Dict[str, AdvancedOrder] = {}
        self.order_history: deque = deque(maxlen=10000)
        
        # Order queues by priority
        self.order_queues: Dict[OrderPriority, deque] = {
            priority: deque() for priority in OrderPriority
        }
        
        # Execution tracking
        self.execution_stats: Dict[str, Any] = defaultdict(float)
        self.venue_performance: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        
        # Order routing
        self.available_venues = self.order_config.get('venues', ['NSE', 'BSE'])
        self.routing_rules: Dict[str, Any] = self.order_config.get('routing_rules', {})
        
        # Callbacks
        self.order_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Configuration
        self.max_order_age_hours = self.order_config.get('max_order_age_hours', 24)
        self.partial_fill_timeout_minutes = self.order_config.get('partial_fill_timeout_minutes', 30)
        self.max_retries = self.order_config.get('max_retries', 3)
        
        logger.info("[INIT] Advanced Order Manager initialized")
    
    async def create_order(
        self, 
        symbol: str, 
        side: str, 
        quantity: int,
        order_type: OrderType,
        **kwargs
    ) -> AdvancedOrder:
        """
        Create advanced order with comprehensive parameters
        
        Args:
            symbol: Trading symbol
            side: BUY or SELL
            quantity: Order quantity
            order_type: Type of order
            **kwargs: Additional order parameters
            
        Returns:
            AdvancedOrder object
        """
        try:
            order_id = kwargs.get('order_id', f"ORD_{uuid.uuid4().hex[:8]}")
            
            # Create order
            order = AdvancedOrder(
                order_id=order_id,
                parent_order_id=kwargs.get('parent_order_id'),
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=kwargs.get('price'),
                stop_price=kwargs.get('stop_price'),
                limit_price=kwargs.get('limit_price'),
                time_in_force=kwargs.get('time_in_force', 'DAY'),
                priority=kwargs.get('priority', OrderPriority.NORMAL),
                max_show_quantity=kwargs.get('max_show_quantity'),
                participation_rate=kwargs.get('participation_rate'),
                duration_minutes=kwargs.get('duration_minutes'),
                preferred_venue=kwargs.get('preferred_venue'),
                routing_strategy=kwargs.get('routing_strategy', 'smart'),
                strategy_name=kwargs.get('strategy_name'),
                signal_id=kwargs.get('signal_id'),
                tags=kwargs.get('tags', {})
            )
            
            # Add to active orders
            self.active_orders[order_id] = order
            
            # Add to priority queue
            self.order_queues[order.priority].append(order_id)
            
            # Log order creation
            logger.info(f"[CREATE] Order created: {order_id} {symbol} {side} {quantity} {order_type.value}")
            
            # Trigger callbacks
            await self._trigger_callbacks('order_created', order)
            
            return order
            
        except Exception as e:
            logger.error(f"[ERROR] Error creating order: {e}")
            raise
    
    async def submit_order(self, order_id: str, market_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Submit order for execution with intelligent routing
        
        Args:
            order_id: Order ID to submit
            market_data: Current market data
            
        Returns:
            True if submitted successfully
        """
        try:
            if order_id not in self.active_orders:
                logger.error(f"[ERROR] Order not found: {order_id}")
                return False
            
            order = self.active_orders[order_id]
            
            # Check if order is ready for submission
            if order.status != OrderStatus.PENDING:
                logger.warning(f"[WARN] Order {order_id} not in pending state: {order.status}")
                return False
            
            # Perform order routing decision
            routing_decision = await self._make_routing_decision(order, market_data)
            
            # Handle different order types
            if order.order_type == OrderType.ICEBERG:
                success = await self._submit_iceberg_order(order, routing_decision)
            elif order.order_type == OrderType.TWAP:
                success = await self._submit_twap_order(order, routing_decision)
            elif order.order_type == OrderType.VWAP:
                success = await self._submit_vwap_order(order, routing_decision)
            elif order.order_type == OrderType.POV:
                success = await self._submit_pov_order(order, routing_decision)
            elif order.order_type == OrderType.BRACKET:
                success = await self._submit_bracket_order(order, routing_decision)
            else:
                success = await self._submit_simple_order(order, routing_decision)
            
            if success:
                order.status = OrderStatus.SUBMITTED
                order.submitted_at = datetime.now()
                order.last_updated = datetime.now()
                
                logger.info(f"[SUBMIT] Order submitted: {order_id} to {routing_decision.venue}")
                await self._trigger_callbacks('order_submitted', order)
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error submitting order {order_id}: {e}")
            return False
    
    async def cancel_order(self, order_id: str, reason: str = "User requested") -> bool:
        """
        Cancel active order
        
        Args:
            order_id: Order ID to cancel
            reason: Cancellation reason
            
        Returns:
            True if cancelled successfully
        """
        try:
            if order_id not in self.active_orders:
                logger.error(f"[ERROR] Order not found: {order_id}")
                return False
            
            order = self.active_orders[order_id]
            
            # Check if order can be cancelled
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                logger.warning(f"[WARN] Cannot cancel order {order_id} in state {order.status}")
                return False
            
            # Cancel order (implementation would call broker API)
            success = await self._cancel_order_at_venue(order)
            
            if success:
                order.status = OrderStatus.CANCELLED
                order.last_updated = datetime.now()
                order.tags['cancellation_reason'] = reason
                
                # Move to completed orders
                self.completed_orders[order_id] = order
                del self.active_orders[order_id]
                
                logger.info(f"[CANCEL] Order cancelled: {order_id} - {reason}")
                await self._trigger_callbacks('order_cancelled', order)
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error cancelling order {order_id}: {e}")
            return False
    
    async def handle_execution(
        self, 
        order_id: str, 
        execution_data: Dict[str, Any]
    ) -> bool:
        """
        Handle order execution notification
        
        Args:
            order_id: Order ID
            execution_data: Execution details
            
        Returns:
            True if handled successfully
        """
        try:
            if order_id not in self.active_orders:
                logger.error(f"[ERROR] Order not found for execution: {order_id}")
                return False
            
            order = self.active_orders[order_id]
            
            # Create execution record
            execution = OrderExecution(
                execution_id=execution_data.get('execution_id', f"EXEC_{uuid.uuid4().hex[:8]}"),
                timestamp=execution_data.get('timestamp', datetime.now()),
                quantity=execution_data.get('quantity', 0),
                price=execution_data.get('price', 0.0),
                commission=execution_data.get('commission', 0.0),
                exchange_order_id=execution_data.get('exchange_order_id', ''),
                execution_venue=execution_data.get('venue', ''),
                liquidity_flag=execution_data.get('liquidity_flag', 'taker')
            )
            
            # Add execution to order
            order.add_execution(execution)
            
            # Update execution statistics
            self._update_execution_stats(order, execution)
            
            # Check if order is complete
            if order.status == OrderStatus.FILLED:
                # Move to completed orders
                self.completed_orders[order_id] = order
                del self.active_orders[order_id]
                
                logger.info(f"[FILLED] Order completed: {order_id} avg_price={order.average_fill_price:.2f}")
                await self._trigger_callbacks('order_filled', order)
            else:
                logger.info(f"[PARTIAL] Partial fill: {order_id} {execution.quantity}@{execution.price}")
                await self._trigger_callbacks('order_partially_filled', order)
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error handling execution for {order_id}: {e}")
            return False
    
    async def _make_routing_decision(
        self, 
        order: AdvancedOrder, 
        market_data: Optional[Dict[str, Any]]
    ) -> OrderRoutingDecision:
        """Make intelligent order routing decision"""
        try:
            # If preferred venue specified, use it
            if order.preferred_venue and order.preferred_venue in self.available_venues:
                return OrderRoutingDecision(
                    venue=order.preferred_venue,
                    routing_reason="Preferred venue specified",
                    expected_fill_probability=0.8,
                    expected_slippage_bps=5.0,
                    estimated_commission=10.0,
                    routing_confidence=0.9,
                    alternative_venues=[v for v in self.available_venues if v != order.preferred_venue],
                    timestamp=datetime.now()
                )
            
            # Smart routing based on order characteristics and market data
            venue_scores = {}
            
            for venue in self.available_venues:
                score = await self._calculate_venue_score(venue, order, market_data)
                venue_scores[venue] = score
            
            # Select best venue
            best_venue = max(venue_scores, key=venue_scores.get)
            best_score = venue_scores[best_venue]
            
            # Calculate routing metrics
            expected_fill_prob = min(0.95, 0.5 + best_score * 0.5)
            expected_slippage = max(2.0, 10.0 - best_score * 8.0)
            estimated_commission = self._estimate_commission(venue, order)
            
            return OrderRoutingDecision(
                venue=best_venue,
                routing_reason=f"Smart routing selected based on score {best_score:.2f}",
                expected_fill_probability=expected_fill_prob,
                expected_slippage_bps=expected_slippage,
                estimated_commission=estimated_commission,
                routing_confidence=best_score,
                alternative_venues=[v for v in self.available_venues if v != best_venue],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error making routing decision: {e}")
            # Fallback to first available venue
            return OrderRoutingDecision(
                venue=self.available_venues[0],
                routing_reason="Fallback routing due to error",
                expected_fill_probability=0.7,
                expected_slippage_bps=8.0,
                estimated_commission=15.0,
                routing_confidence=0.5,
                alternative_venues=self.available_venues[1:],
                timestamp=datetime.now()
            )

    async def _calculate_venue_score(
        self,
        venue: str,
        order: AdvancedOrder,
        market_data: Optional[Dict[str, Any]]
    ) -> float:
        """Calculate venue score for routing decision"""
        try:
            score = 0.5  # Base score

            # Historical performance factor
            if venue in self.venue_performance:
                perf = self.venue_performance[venue]
                fill_rate = perf.get('fill_rate', 0.8)
                avg_slippage = perf.get('avg_slippage_bps', 5.0)

                # Higher fill rate is better
                score += (fill_rate - 0.8) * 0.3

                # Lower slippage is better
                score += max(0, (8.0 - avg_slippage) / 8.0) * 0.2

            # Liquidity factor
            if market_data:
                volume = market_data.get('volume', 0)
                if volume > 100000:  # High volume
                    score += 0.1
                elif volume < 10000:  # Low volume
                    score -= 0.1

            # Order size factor
            if order.quantity > 1000:  # Large order
                if venue == 'NSE':  # Prefer NSE for large orders
                    score += 0.1

            # Time factor
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 15:  # Market hours
                score += 0.1

            return max(0.0, min(1.0, score))

        except Exception as e:
            logger.error(f"[ERROR] Error calculating venue score: {e}")
            return 0.5

    def _estimate_commission(self, venue: str, order: AdvancedOrder) -> float:
        """Estimate commission for order at venue"""
        try:
            # Basic commission calculation
            order_value = order.quantity * (order.price or 100.0)  # Default price if not set

            # Commission rates by venue (simplified)
            commission_rates = {
                'NSE': 0.0003,  # 0.03%
                'BSE': 0.0004   # 0.04%
            }

            rate = commission_rates.get(venue, 0.0005)
            commission = order_value * rate

            # Minimum commission
            min_commission = 1.0
            return max(min_commission, commission)

        except Exception as e:
            logger.error(f"[ERROR] Error estimating commission: {e}")
            return 10.0  # Default commission

    async def _submit_simple_order(
        self,
        order: AdvancedOrder,
        routing_decision: OrderRoutingDecision
    ) -> bool:
        """Submit simple market/limit order"""
        try:
            # This would integrate with actual broker API
            # For now, simulate successful submission

            logger.info(f"[SUBMIT] Simple order {order.order_id} to {routing_decision.venue}")

            # Simulate order submission delay
            await asyncio.sleep(0.1)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error submitting simple order: {e}")
            return False

    async def _cancel_order_at_venue(self, order: AdvancedOrder) -> bool:
        """Cancel order at execution venue"""
        try:
            # This would integrate with actual broker API
            logger.info(f"[CANCEL] Cancelling order {order.order_id} at venue")

            # Simulate cancellation delay
            await asyncio.sleep(0.1)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error cancelling order at venue: {e}")
            return False

    def _update_execution_stats(self, order: AdvancedOrder, execution: OrderExecution):
        """Update execution statistics"""
        try:
            # Update overall stats
            self.execution_stats['total_executions'] += 1
            self.execution_stats['total_quantity'] += execution.quantity
            self.execution_stats['total_commission'] += execution.commission

            # Update venue performance
            venue = execution.execution_venue
            if venue:
                venue_stats = self.venue_performance[venue]
                venue_stats['execution_count'] += 1
                venue_stats['total_quantity'] += execution.quantity
                venue_stats['total_commission'] += execution.commission

                # Calculate average metrics
                if venue_stats['execution_count'] > 0:
                    venue_stats['avg_commission'] = venue_stats['total_commission'] / venue_stats['execution_count']

            # Update order type performance
            order_type = order.order_type.value
            if order_type not in self.execution_stats:
                self.execution_stats[order_type] = {'count': 0, 'avg_fill_time': 0}

            self.execution_stats[order_type]['count'] += 1

        except Exception as e:
            logger.error(f"[ERROR] Error updating execution stats: {e}")

    async def _trigger_callbacks(self, event_type: str, order: AdvancedOrder):
        """Trigger registered callbacks for order events"""
        try:
            if event_type in self.order_callbacks:
                for callback in self.order_callbacks[event_type]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(order)
                        else:
                            callback(order)
                    except Exception as e:
                        logger.error(f"[ERROR] Callback error for {event_type}: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Error triggering callbacks: {e}")

    def register_callback(self, event_type: str, callback: Callable):
        """Register callback for order events"""
        self.order_callbacks[event_type].append(callback)
        logger.info(f"[CALLBACK] Registered callback for {event_type}")

    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive order status"""
        try:
            # Check active orders first
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
            elif order_id in self.completed_orders:
                order = self.completed_orders[order_id]
            else:
                return None

            return {
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side,
                'order_type': order.order_type.value,
                'status': order.status.value,
                'quantity': order.quantity,
                'filled_quantity': order.filled_quantity,
                'remaining_quantity': order.remaining_quantity,
                'fill_rate': order.get_fill_rate(),
                'average_fill_price': order.average_fill_price,
                'total_commission': order.total_commission,
                'created_at': order.created_at.isoformat(),
                'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                'last_updated': order.last_updated.isoformat(),
                'executions_count': len(order.executions),
                'tags': order.tags
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting order status: {e}")
            return None
