#!/usr/bin/env python3
"""
Enhanced Error Handling and Robustness Module

Comprehensive error handling system with circuit breaker patterns,
idempotency mechanisms, API error mapping, health checks, and recovery strategies.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import hashlib
import json
from collections import defaultdict, deque
import traceback

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories"""
    NETWORK = "network"
    API = "api"
    VALIDATION = "validation"
    EXECUTION = "execution"
    TIMEOUT = "timeout"
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    SYSTEM = "system"
    DATA = "data"
    BUSINESS_LOGIC = "business_logic"


class RecoveryAction(Enum):
    """Recovery actions"""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    ESCALATE = "escalate"
    IGNORE = "ignore"
    MANUAL_INTERVENTION = "manual_intervention"


@dataclass
class ErrorRecord:
    """Comprehensive error record"""
    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    category: ErrorCategory
    severity: ErrorSeverity
    context: Dict[str, Any]
    stack_trace: str
    recovery_action: Optional[RecoveryAction] = None
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    tags: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CircuitBreakerState:
    """Circuit breaker state tracking"""
    name: str
    state: str  # CLOSED, OPEN, HALF_OPEN
    failure_count: int
    success_count: int
    last_failure_time: Optional[datetime]
    last_success_time: Optional[datetime]
    failure_threshold: int
    recovery_timeout_seconds: int
    half_open_max_calls: int
    half_open_calls: int = 0


@dataclass
class HealthCheckResult:
    """Health check result"""
    component: str
    status: str  # HEALTHY, DEGRADED, UNHEALTHY
    response_time_ms: float
    error_message: Optional[str]
    details: Dict[str, Any]
    timestamp: datetime


class EnhancedErrorHandler:
    """
    Enhanced Error Handler with comprehensive error management
    
    Features:
    - Circuit breaker patterns for external dependencies
    - Idempotency mechanisms for critical operations
    - Comprehensive API error mapping and handling
    - Health checks and monitoring
    - Automatic recovery strategies
    - Error analytics and reporting
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Enhanced Error Handler"""
        self.config = config
        self.error_config = config.get('error_handling', {})
        
        # Error storage
        self.error_history: deque = deque(maxlen=10000)
        self.error_stats: Dict[str, Any] = defaultdict(int)
        self.error_patterns: Dict[str, List[ErrorRecord]] = defaultdict(list)
        
        # Circuit breakers
        self.circuit_breakers: Dict[str, CircuitBreakerState] = {}
        self.circuit_breaker_config = self.error_config.get('circuit_breakers', {})
        
        # Idempotency tracking
        self.idempotency_cache: Dict[str, Any] = {}
        self.idempotency_ttl_hours = self.error_config.get('idempotency_ttl_hours', 24)
        
        # Health checks
        self.health_checks: Dict[str, Callable] = {}
        self.health_status: Dict[str, HealthCheckResult] = {}
        
        # Recovery strategies
        self.recovery_strategies: Dict[ErrorCategory, List[RecoveryAction]] = {
            ErrorCategory.NETWORK: [RecoveryAction.RETRY, RecoveryAction.FALLBACK],
            ErrorCategory.API: [RecoveryAction.RETRY, RecoveryAction.CIRCUIT_BREAK],
            ErrorCategory.TIMEOUT: [RecoveryAction.RETRY, RecoveryAction.ESCALATE],
            ErrorCategory.RATE_LIMIT: [RecoveryAction.RETRY, RecoveryAction.CIRCUIT_BREAK],
            ErrorCategory.AUTHENTICATION: [RecoveryAction.ESCALATE, RecoveryAction.MANUAL_INTERVENTION],
            ErrorCategory.VALIDATION: [RecoveryAction.ESCALATE],
            ErrorCategory.SYSTEM: [RecoveryAction.ESCALATE, RecoveryAction.MANUAL_INTERVENTION]
        }
        
        # Configuration
        self.max_retry_attempts = self.error_config.get('max_retry_attempts', 3)
        self.retry_delay_seconds = self.error_config.get('retry_delay_seconds', 1.0)
        self.exponential_backoff = self.error_config.get('exponential_backoff', True)
        
        logger.info("[INIT] Enhanced Error Handler initialized")
    
    async def handle_error(
        self, 
        error: Exception, 
        context: Dict[str, Any],
        operation_name: str = "unknown"
    ) -> Tuple[bool, Any]:
        """
        Handle error with comprehensive error management
        
        Args:
            error: Exception that occurred
            context: Context information
            operation_name: Name of the operation that failed
            
        Returns:
            Tuple of (should_retry, recovery_result)
        """
        try:
            # Create error record
            error_record = self._create_error_record(error, context, operation_name)
            
            # Store error
            self.error_history.append(error_record)
            self._update_error_stats(error_record)
            
            # Determine recovery action
            recovery_action = self._determine_recovery_action(error_record)
            error_record.recovery_action = recovery_action
            
            # Execute recovery action
            should_retry, recovery_result = await self._execute_recovery_action(
                recovery_action, error_record, context
            )
            
            # Update circuit breakers if applicable
            if operation_name in self.circuit_breakers:
                await self._update_circuit_breaker(operation_name, success=False)
            
            logger.error(f"[ERROR] {operation_name}: {error_record.error_message} - Action: {recovery_action.value}")
            
            return should_retry, recovery_result
            
        except Exception as e:
            logger.critical(f"[CRITICAL] Error handler failed: {e}")
            return False, None
    
    async def execute_with_circuit_breaker(
        self, 
        operation_name: str, 
        operation: Callable,
        *args, 
        **kwargs
    ) -> Tuple[bool, Any]:
        """
        Execute operation with circuit breaker protection
        
        Args:
            operation_name: Name of the operation
            operation: Callable to execute
            *args, **kwargs: Arguments for the operation
            
        Returns:
            Tuple of (success, result)
        """
        try:
            # Initialize circuit breaker if not exists
            if operation_name not in self.circuit_breakers:
                self._initialize_circuit_breaker(operation_name)
            
            breaker = self.circuit_breakers[operation_name]
            
            # Check circuit breaker state
            if breaker.state == "OPEN":
                if self._should_attempt_reset(breaker):
                    breaker.state = "HALF_OPEN"
                    breaker.half_open_calls = 0
                    logger.info(f"[CIRCUIT] {operation_name} circuit breaker: OPEN -> HALF_OPEN")
                else:
                    logger.warning(f"[CIRCUIT] {operation_name} circuit breaker is OPEN, operation blocked")
                    return False, "Circuit breaker is OPEN"
            
            # Execute operation
            if breaker.state == "HALF_OPEN":
                if breaker.half_open_calls >= breaker.half_open_max_calls:
                    logger.warning(f"[CIRCUIT] {operation_name} half-open call limit reached")
                    return False, "Half-open call limit reached"
                breaker.half_open_calls += 1
            
            # Execute the operation
            if asyncio.iscoroutinefunction(operation):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)
            
            # Operation succeeded
            await self._update_circuit_breaker(operation_name, success=True)
            return True, result
            
        except Exception as e:
            # Operation failed
            await self._update_circuit_breaker(operation_name, success=False)
            context = {
                'operation_name': operation_name,
                'args': str(args)[:200],  # Limit context size
                'kwargs': str(kwargs)[:200]
            }
            should_retry, recovery_result = await self.handle_error(e, context, operation_name)
            return False, str(e)
    
    async def execute_with_idempotency(
        self, 
        idempotency_key: str, 
        operation: Callable,
        *args, 
        **kwargs
    ) -> Tuple[bool, Any]:
        """
        Execute operation with idempotency protection
        
        Args:
            idempotency_key: Unique key for the operation
            operation: Callable to execute
            *args, **kwargs: Arguments for the operation
            
        Returns:
            Tuple of (is_duplicate, result)
        """
        try:
            # Generate cache key
            cache_key = self._generate_idempotency_key(idempotency_key, args, kwargs)
            
            # Check if operation was already executed
            if cache_key in self.idempotency_cache:
                cached_result = self.idempotency_cache[cache_key]
                if self._is_cache_valid(cached_result['timestamp']):
                    logger.info(f"[IDEMPOTENT] Returning cached result for {idempotency_key}")
                    return True, cached_result['result']
                else:
                    # Cache expired, remove it
                    del self.idempotency_cache[cache_key]
            
            # Execute operation
            if asyncio.iscoroutinefunction(operation):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)
            
            # Cache the result
            self.idempotency_cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now(),
                'idempotency_key': idempotency_key
            }
            
            # Clean up old cache entries
            self._cleanup_idempotency_cache()
            
            return False, result
            
        except Exception as e:
            logger.error(f"[ERROR] Idempotent operation failed: {e}")
            raise
    
    async def perform_health_check(self, component: str) -> HealthCheckResult:
        """
        Perform health check for a component
        
        Args:
            component: Component name to check
            
        Returns:
            HealthCheckResult
        """
        try:
            if component not in self.health_checks:
                return HealthCheckResult(
                    component=component,
                    status="UNKNOWN",
                    response_time_ms=0.0,
                    error_message="No health check configured",
                    details={},
                    timestamp=datetime.now()
                )
            
            start_time = datetime.now()
            
            try:
                # Execute health check
                health_check_func = self.health_checks[component]
                if asyncio.iscoroutinefunction(health_check_func):
                    check_result = await health_check_func()
                else:
                    check_result = health_check_func()
                
                response_time = (datetime.now() - start_time).total_seconds() * 1000
                
                # Determine status based on response time and result
                if isinstance(check_result, bool):
                    status = "HEALTHY" if check_result else "UNHEALTHY"
                    details = {}
                    error_message = None if check_result else "Health check returned False"
                elif isinstance(check_result, dict):
                    status = check_result.get('status', 'HEALTHY')
                    details = check_result.get('details', {})
                    error_message = check_result.get('error')
                else:
                    status = "HEALTHY"
                    details = {'result': str(check_result)}
                    error_message = None
                
                # Apply response time thresholds
                if response_time > 5000:  # 5 seconds
                    status = "UNHEALTHY"
                    error_message = f"Health check timeout: {response_time:.0f}ms"
                elif response_time > 2000:  # 2 seconds
                    status = "DEGRADED"
                
                result = HealthCheckResult(
                    component=component,
                    status=status,
                    response_time_ms=response_time,
                    error_message=error_message,
                    details=details,
                    timestamp=datetime.now()
                )
                
            except Exception as e:
                response_time = (datetime.now() - start_time).total_seconds() * 1000
                result = HealthCheckResult(
                    component=component,
                    status="UNHEALTHY",
                    response_time_ms=response_time,
                    error_message=str(e),
                    details={'exception_type': type(e).__name__},
                    timestamp=datetime.now()
                )
            
            # Store health check result
            self.health_status[component] = result
            
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Health check failed for {component}: {e}")
            return HealthCheckResult(
                component=component,
                status="UNHEALTHY",
                response_time_ms=0.0,
                error_message=f"Health check system error: {str(e)}",
                details={},
                timestamp=datetime.now()
            )

    def _create_error_record(
        self,
        error: Exception,
        context: Dict[str, Any],
        operation_name: str
    ) -> ErrorRecord:
        """Create comprehensive error record"""
        try:
            # Determine error category and severity
            category = self._categorize_error(error)
            severity = self._determine_severity(error, category)

            return ErrorRecord(
                error_id=f"ERR_{uuid.uuid4().hex[:8]}",
                timestamp=datetime.now(),
                error_type=type(error).__name__,
                error_message=str(error),
                category=category,
                severity=severity,
                context=context,
                stack_trace=traceback.format_exc(),
                tags={'operation': operation_name}
            )

        except Exception as e:
            logger.error(f"[ERROR] Failed to create error record: {e}")
            return ErrorRecord(
                error_id=f"ERR_{uuid.uuid4().hex[:8]}",
                timestamp=datetime.now(),
                error_type="UnknownError",
                error_message="Failed to create error record",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                context={},
                stack_trace="",
                tags={}
            )

    def _categorize_error(self, error: Exception) -> ErrorCategory:
        """Categorize error based on type and message"""
        try:
            error_type = type(error).__name__
            error_message = str(error).lower()

            # Network-related errors
            if any(keyword in error_message for keyword in ['connection', 'network', 'socket', 'dns']):
                return ErrorCategory.NETWORK

            # API-related errors
            if any(keyword in error_message for keyword in ['api', 'http', 'status', 'response']):
                return ErrorCategory.API

            # Timeout errors
            if any(keyword in error_message for keyword in ['timeout', 'timed out']):
                return ErrorCategory.TIMEOUT

            # Authentication errors
            if any(keyword in error_message for keyword in ['auth', 'unauthorized', 'forbidden', 'token']):
                return ErrorCategory.AUTHENTICATION

            # Rate limit errors
            if any(keyword in error_message for keyword in ['rate limit', 'too many requests', 'throttle']):
                return ErrorCategory.RATE_LIMIT

            # Validation errors
            if error_type in ['ValueError', 'ValidationError', 'TypeError']:
                return ErrorCategory.VALIDATION

            # Data errors
            if error_type in ['KeyError', 'IndexError', 'AttributeError']:
                return ErrorCategory.DATA

            # Default to system error
            return ErrorCategory.SYSTEM

        except Exception:
            return ErrorCategory.SYSTEM

    def _determine_severity(self, error: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity"""
        try:
            # Critical errors
            if category in [ErrorCategory.AUTHENTICATION, ErrorCategory.SYSTEM]:
                return ErrorSeverity.CRITICAL

            # High severity errors
            if category in [ErrorCategory.EXECUTION, ErrorCategory.BUSINESS_LOGIC]:
                return ErrorSeverity.HIGH

            # Medium severity errors
            if category in [ErrorCategory.API, ErrorCategory.TIMEOUT]:
                return ErrorSeverity.MEDIUM

            # Low severity errors
            return ErrorSeverity.LOW

        except Exception:
            return ErrorSeverity.MEDIUM

    def _determine_recovery_action(self, error_record: ErrorRecord) -> RecoveryAction:
        """Determine appropriate recovery action"""
        try:
            category = error_record.category
            severity = error_record.severity

            # Critical errors require escalation
            if severity == ErrorSeverity.CRITICAL:
                return RecoveryAction.ESCALATE

            # Get recovery strategies for category
            strategies = self.recovery_strategies.get(category, [RecoveryAction.ESCALATE])

            # Return first strategy (most preferred)
            return strategies[0] if strategies else RecoveryAction.ESCALATE

        except Exception:
            return RecoveryAction.ESCALATE

    async def _execute_recovery_action(
        self,
        action: RecoveryAction,
        error_record: ErrorRecord,
        context: Dict[str, Any]
    ) -> Tuple[bool, Any]:
        """Execute recovery action"""
        try:
            if action == RecoveryAction.RETRY:
                # Check retry limits
                if error_record.retry_count >= self.max_retry_attempts:
                    return False, "Max retry attempts exceeded"

                # Calculate retry delay
                delay = self.retry_delay_seconds
                if self.exponential_backoff:
                    delay *= (2 ** error_record.retry_count)

                error_record.retry_count += 1

                # Wait before retry
                await asyncio.sleep(delay)

                return True, f"Retrying after {delay}s (attempt {error_record.retry_count})"

            elif action == RecoveryAction.CIRCUIT_BREAK:
                operation_name = context.get('operation_name', 'unknown')
                if operation_name in self.circuit_breakers:
                    breaker = self.circuit_breakers[operation_name]
                    breaker.state = "OPEN"
                    breaker.last_failure_time = datetime.now()

                return False, "Circuit breaker opened"

            elif action == RecoveryAction.ESCALATE:
                # Log for escalation
                logger.critical(f"[ESCALATE] Error requires escalation: {error_record.error_id}")
                return False, "Error escalated for manual intervention"

            elif action == RecoveryAction.FALLBACK:
                # Implement fallback logic
                return False, "Fallback mechanism activated"

            else:
                return False, f"Recovery action {action.value} not implemented"

        except Exception as e:
            logger.error(f"[ERROR] Recovery action execution failed: {e}")
            return False, "Recovery action failed"

    def _initialize_circuit_breaker(self, operation_name: str):
        """Initialize circuit breaker for operation"""
        try:
            config = self.circuit_breaker_config.get(operation_name, {})

            self.circuit_breakers[operation_name] = CircuitBreakerState(
                name=operation_name,
                state="CLOSED",
                failure_count=0,
                success_count=0,
                last_failure_time=None,
                last_success_time=None,
                failure_threshold=config.get('failure_threshold', 5),
                recovery_timeout_seconds=config.get('recovery_timeout_seconds', 60),
                half_open_max_calls=config.get('half_open_max_calls', 3)
            )

            logger.info(f"[CIRCUIT] Initialized circuit breaker for {operation_name}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize circuit breaker: {e}")

    async def _update_circuit_breaker(self, operation_name: str, success: bool):
        """Update circuit breaker state"""
        try:
            if operation_name not in self.circuit_breakers:
                return

            breaker = self.circuit_breakers[operation_name]

            if success:
                breaker.success_count += 1
                breaker.last_success_time = datetime.now()

                if breaker.state == "HALF_OPEN":
                    # Successful calls in half-open state
                    if breaker.success_count >= breaker.half_open_max_calls:
                        breaker.state = "CLOSED"
                        breaker.failure_count = 0
                        logger.info(f"[CIRCUIT] {operation_name} circuit breaker: HALF_OPEN -> CLOSED")

            else:
                breaker.failure_count += 1
                breaker.last_failure_time = datetime.now()

                if breaker.state == "CLOSED" and breaker.failure_count >= breaker.failure_threshold:
                    breaker.state = "OPEN"
                    logger.warning(f"[CIRCUIT] {operation_name} circuit breaker: CLOSED -> OPEN")
                elif breaker.state == "HALF_OPEN":
                    breaker.state = "OPEN"
                    logger.warning(f"[CIRCUIT] {operation_name} circuit breaker: HALF_OPEN -> OPEN")

        except Exception as e:
            logger.error(f"[ERROR] Failed to update circuit breaker: {e}")

    def _should_attempt_reset(self, breaker: CircuitBreakerState) -> bool:
        """Check if circuit breaker should attempt reset"""
        try:
            if not breaker.last_failure_time:
                return True

            time_since_failure = datetime.now() - breaker.last_failure_time
            return time_since_failure.total_seconds() >= breaker.recovery_timeout_seconds

        except Exception:
            return False

    def _generate_idempotency_key(
        self,
        base_key: str,
        args: tuple,
        kwargs: dict
    ) -> str:
        """Generate idempotency cache key"""
        try:
            # Create hash of arguments
            args_str = json.dumps(args, sort_keys=True, default=str)
            kwargs_str = json.dumps(kwargs, sort_keys=True, default=str)
            combined = f"{base_key}:{args_str}:{kwargs_str}"

            return hashlib.sha256(combined.encode()).hexdigest()[:16]

        except Exception:
            return hashlib.sha256(base_key.encode()).hexdigest()[:16]

    def _is_cache_valid(self, timestamp: datetime) -> bool:
        """Check if cache entry is still valid"""
        try:
            age_hours = (datetime.now() - timestamp).total_seconds() / 3600
            return age_hours < self.idempotency_ttl_hours

        except Exception:
            return False

    def _cleanup_idempotency_cache(self):
        """Clean up expired idempotency cache entries"""
        try:
            expired_keys = []
            for key, entry in self.idempotency_cache.items():
                if not self._is_cache_valid(entry['timestamp']):
                    expired_keys.append(key)

            for key in expired_keys:
                del self.idempotency_cache[key]

            if expired_keys:
                logger.debug(f"[CLEANUP] Removed {len(expired_keys)} expired idempotency cache entries")

        except Exception as e:
            logger.error(f"[ERROR] Idempotency cache cleanup failed: {e}")

    def _update_error_stats(self, error_record: ErrorRecord):
        """Update error statistics"""
        try:
            # Update overall stats
            self.error_stats['total_errors'] += 1
            self.error_stats[f'category_{error_record.category.value}'] += 1
            self.error_stats[f'severity_{error_record.severity.value}'] += 1
            self.error_stats[f'type_{error_record.error_type}'] += 1

            # Update error patterns
            pattern_key = f"{error_record.category.value}:{error_record.error_type}"
            self.error_patterns[pattern_key].append(error_record)

            # Keep only recent patterns
            if len(self.error_patterns[pattern_key]) > 100:
                self.error_patterns[pattern_key] = self.error_patterns[pattern_key][-100:]

        except Exception as e:
            logger.error(f"[ERROR] Failed to update error stats: {e}")

    def register_health_check(self, component: str, health_check_func: Callable):
        """Register health check function for component"""
        self.health_checks[component] = health_check_func
        logger.info(f"[HEALTH] Registered health check for {component}")

    def get_error_summary(self) -> Dict[str, Any]:
        """Get comprehensive error summary"""
        try:
            return {
                'error_statistics': dict(self.error_stats),
                'circuit_breakers': {
                    name: {
                        'state': breaker.state,
                        'failure_count': breaker.failure_count,
                        'success_count': breaker.success_count,
                        'last_failure': breaker.last_failure_time.isoformat() if breaker.last_failure_time else None
                    }
                    for name, breaker in self.circuit_breakers.items()
                },
                'health_status': {
                    component: {
                        'status': result.status,
                        'response_time_ms': result.response_time_ms,
                        'last_check': result.timestamp.isoformat()
                    }
                    for component, result in self.health_status.items()
                },
                'recent_errors': [
                    {
                        'error_id': error.error_id,
                        'timestamp': error.timestamp.isoformat(),
                        'category': error.category.value,
                        'severity': error.severity.value,
                        'message': error.error_message[:100]  # Truncate long messages
                    }
                    for error in list(self.error_history)[-10:]  # Last 10 errors
                ],
                'idempotency_cache_size': len(self.idempotency_cache),
                'configuration': {
                    'max_retry_attempts': self.max_retry_attempts,
                    'retry_delay_seconds': self.retry_delay_seconds,
                    'exponential_backoff': self.exponential_backoff,
                    'idempotency_ttl_hours': self.idempotency_ttl_hours
                }
            }

        except Exception as e:
            logger.error(f"[ERROR] Failed to get error summary: {e}")
            return {'error': str(e)}
