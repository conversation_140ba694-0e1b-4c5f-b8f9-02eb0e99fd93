#!/usr/bin/env python3
"""
Performance Monitoring and Analytics Module

Comprehensive performance tracking and analytics for execution operations:
- Real-time execution metrics and KPIs
- Slippage analysis and optimization insights
- Latency monitoring and bottleneck detection
- Fill rate and execution quality metrics
- Cost analysis and commission tracking
- Performance benchmarking and reporting
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import numpy as np
import statistics
import json

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of performance metrics"""
    LATENCY = "latency"
    SLIPPAGE = "slippage"
    FILL_RATE = "fill_rate"
    COMMISSION = "commission"
    MARKET_IMPACT = "market_impact"
    EXECUTION_QUALITY = "execution_quality"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"


class TimeWindow(Enum):
    """Time windows for metric aggregation"""
    MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    HOUR = "1h"
    DAY = "1d"
    WEEK = "1w"


@dataclass
class ExecutionMetric:
    """Individual execution metric record"""
    metric_id: str
    timestamp: datetime
    metric_type: MetricType
    value: float
    symbol: str
    order_id: str
    venue: str
    strategy: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SlippageAnalysis:
    """Comprehensive slippage analysis"""
    symbol: str
    time_window: TimeWindow
    avg_slippage_bps: float
    median_slippage_bps: float
    max_slippage_bps: float
    min_slippage_bps: float
    std_slippage_bps: float
    positive_slippage_rate: float
    sample_count: int
    confidence_interval_95: Tuple[float, float]
    slippage_by_venue: Dict[str, float]
    slippage_by_time: Dict[str, float]
    timestamp: datetime


@dataclass
class PerformanceBenchmark:
    """Performance benchmark comparison"""
    metric_name: str
    current_value: float
    benchmark_value: float
    performance_ratio: float
    percentile_rank: float
    improvement_suggestion: str
    timestamp: datetime


@dataclass
class ExecutionQualityScore:
    """Execution quality scoring"""
    order_id: str
    overall_score: float  # 0-100
    slippage_score: float
    speed_score: float
    fill_rate_score: float
    cost_score: float
    market_impact_score: float
    factors: Dict[str, float]
    grade: str  # A+, A, B+, B, C+, C, D, F
    timestamp: datetime


class PerformanceMonitor:
    """
    Performance Monitor - Comprehensive execution analytics
    
    Features:
    - Real-time metric collection and aggregation
    - Advanced slippage analysis and prediction
    - Execution quality scoring and benchmarking
    - Cost analysis and optimization insights
    - Performance reporting and alerting
    - Historical trend analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Performance Monitor"""
        self.config = config
        self.perf_config = config.get('performance_monitoring', {})
        
        # Metric storage
        self.metrics: Dict[MetricType, deque] = {
            metric_type: deque(maxlen=10000) for metric_type in MetricType
        }
        
        # Aggregated metrics by time window
        self.aggregated_metrics: Dict[TimeWindow, Dict[str, Any]] = {
            window: defaultdict(list) for window in TimeWindow
        }
        
        # Performance benchmarks
        self.benchmarks: Dict[str, float] = self.perf_config.get('benchmarks', {
            'avg_slippage_bps': 5.0,
            'avg_latency_ms': 100.0,
            'fill_rate_percent': 95.0,
            'commission_bps': 3.0
        })
        
        # Real-time tracking
        self.active_orders: Dict[str, Dict[str, Any]] = {}
        self.execution_sessions: Dict[str, List[ExecutionMetric]] = defaultdict(list)
        
        # Analytics cache
        self.slippage_analysis_cache: Dict[str, SlippageAnalysis] = {}
        self.quality_scores_cache: Dict[str, ExecutionQualityScore] = {}
        
        # Configuration
        self.metric_retention_hours = self.perf_config.get('metric_retention_hours', 168)  # 1 week
        self.alert_thresholds = self.perf_config.get('alert_thresholds', {})
        self.quality_weights = self.perf_config.get('quality_weights', {
            'slippage': 0.3,
            'speed': 0.2,
            'fill_rate': 0.25,
            'cost': 0.15,
            'market_impact': 0.1
        })
        
        logger.info("[INIT] Performance Monitor initialized")
    
    async def record_execution_start(
        self, 
        order_id: str, 
        symbol: str, 
        venue: str,
        strategy: str,
        order_details: Dict[str, Any]
    ):
        """Record execution start for latency tracking"""
        try:
            self.active_orders[order_id] = {
                'start_time': time.time(),
                'symbol': symbol,
                'venue': venue,
                'strategy': strategy,
                'order_details': order_details,
                'metrics': []
            }
            
            logger.debug(f"[TRACK] Started tracking execution for {order_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to record execution start: {e}")
    
    async def record_execution_complete(
        self, 
        order_id: str, 
        execution_details: Dict[str, Any]
    ):
        """Record execution completion and calculate metrics"""
        try:
            if order_id not in self.active_orders:
                logger.warning(f"[WARN] Order {order_id} not found in active tracking")
                return
            
            order_info = self.active_orders[order_id]
            end_time = time.time()
            
            # Calculate latency
            latency_ms = (end_time - order_info['start_time']) * 1000
            await self._record_metric(
                MetricType.LATENCY, latency_ms, order_id, 
                order_info['symbol'], order_info['venue'], order_info['strategy']
            )
            
            # Calculate slippage if price information available
            if 'expected_price' in execution_details and 'actual_price' in execution_details:
                slippage_bps = await self._calculate_slippage(
                    execution_details['expected_price'],
                    execution_details['actual_price'],
                    execution_details.get('side', 'BUY')
                )
                await self._record_metric(
                    MetricType.SLIPPAGE, slippage_bps, order_id,
                    order_info['symbol'], order_info['venue'], order_info['strategy']
                )
            
            # Calculate fill rate
            if 'requested_quantity' in execution_details and 'filled_quantity' in execution_details:
                fill_rate = (execution_details['filled_quantity'] / execution_details['requested_quantity']) * 100
                await self._record_metric(
                    MetricType.FILL_RATE, fill_rate, order_id,
                    order_info['symbol'], order_info['venue'], order_info['strategy']
                )
            
            # Record commission
            if 'commission' in execution_details:
                commission_bps = await self._calculate_commission_bps(
                    execution_details['commission'],
                    execution_details.get('trade_value', 0)
                )
                await self._record_metric(
                    MetricType.COMMISSION, commission_bps, order_id,
                    order_info['symbol'], order_info['venue'], order_info['strategy']
                )
            
            # Calculate execution quality score
            quality_score = await self._calculate_execution_quality(order_id, execution_details)
            
            # Remove from active tracking
            del self.active_orders[order_id]
            
            logger.info(f"[COMPLETE] Execution tracking completed for {order_id}: "
                       f"latency={latency_ms:.1f}ms, quality={quality_score.overall_score:.1f}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to record execution completion: {e}")
    
    async def analyze_slippage(
        self, 
        symbol: Optional[str] = None,
        time_window: TimeWindow = TimeWindow.HOUR,
        venue: Optional[str] = None
    ) -> SlippageAnalysis:
        """
        Perform comprehensive slippage analysis
        
        Args:
            symbol: Specific symbol to analyze (None for all)
            time_window: Time window for analysis
            venue: Specific venue to analyze (None for all)
            
        Returns:
            SlippageAnalysis with detailed insights
        """
        try:
            # Get slippage metrics for the time window
            cutoff_time = self._get_cutoff_time(time_window)
            slippage_metrics = [
                metric for metric in self.metrics[MetricType.SLIPPAGE]
                if metric.timestamp >= cutoff_time and
                (symbol is None or metric.symbol == symbol) and
                (venue is None or metric.venue == venue)
            ]
            
            if not slippage_metrics:
                return SlippageAnalysis(
                    symbol=symbol or "ALL",
                    time_window=time_window,
                    avg_slippage_bps=0.0,
                    median_slippage_bps=0.0,
                    max_slippage_bps=0.0,
                    min_slippage_bps=0.0,
                    std_slippage_bps=0.0,
                    positive_slippage_rate=0.0,
                    sample_count=0,
                    confidence_interval_95=(0.0, 0.0),
                    slippage_by_venue={},
                    slippage_by_time={},
                    timestamp=datetime.now()
                )
            
            # Extract slippage values
            slippage_values = [metric.value for metric in slippage_metrics]
            
            # Calculate statistics
            avg_slippage = statistics.mean(slippage_values)
            median_slippage = statistics.median(slippage_values)
            max_slippage = max(slippage_values)
            min_slippage = min(slippage_values)
            std_slippage = statistics.stdev(slippage_values) if len(slippage_values) > 1 else 0.0
            
            # Calculate positive slippage rate
            positive_slippage_count = sum(1 for value in slippage_values if value > 0)
            positive_slippage_rate = (positive_slippage_count / len(slippage_values)) * 100
            
            # Calculate confidence interval (95%)
            if len(slippage_values) > 1:
                std_error = std_slippage / np.sqrt(len(slippage_values))
                margin_error = 1.96 * std_error  # 95% confidence
                confidence_interval = (avg_slippage - margin_error, avg_slippage + margin_error)
            else:
                confidence_interval = (avg_slippage, avg_slippage)
            
            # Analyze by venue
            slippage_by_venue = {}
            venue_groups = defaultdict(list)
            for metric in slippage_metrics:
                venue_groups[metric.venue].append(metric.value)
            
            for venue_name, values in venue_groups.items():
                slippage_by_venue[venue_name] = statistics.mean(values)
            
            # Analyze by time (hourly buckets)
            slippage_by_time = {}
            time_groups = defaultdict(list)
            for metric in slippage_metrics:
                hour_key = metric.timestamp.strftime('%H:00')
                time_groups[hour_key].append(metric.value)
            
            for time_key, values in time_groups.items():
                slippage_by_time[time_key] = statistics.mean(values)
            
            analysis = SlippageAnalysis(
                symbol=symbol or "ALL",
                time_window=time_window,
                avg_slippage_bps=avg_slippage,
                median_slippage_bps=median_slippage,
                max_slippage_bps=max_slippage,
                min_slippage_bps=min_slippage,
                std_slippage_bps=std_slippage,
                positive_slippage_rate=positive_slippage_rate,
                sample_count=len(slippage_values),
                confidence_interval_95=confidence_interval,
                slippage_by_venue=slippage_by_venue,
                slippage_by_time=slippage_by_time,
                timestamp=datetime.now()
            )
            
            # Cache the analysis
            cache_key = f"{symbol or 'ALL'}_{time_window.value}_{venue or 'ALL'}"
            self.slippage_analysis_cache[cache_key] = analysis
            
            return analysis
            
        except Exception as e:
            logger.error(f"[ERROR] Slippage analysis failed: {e}")
            return SlippageAnalysis(
                symbol=symbol or "ALL",
                time_window=time_window,
                avg_slippage_bps=0.0,
                median_slippage_bps=0.0,
                max_slippage_bps=0.0,
                min_slippage_bps=0.0,
                std_slippage_bps=0.0,
                positive_slippage_rate=0.0,
                sample_count=0,
                confidence_interval_95=(0.0, 0.0),
                slippage_by_venue={},
                slippage_by_time={},
                timestamp=datetime.now()
            )

    async def _record_metric(
        self,
        metric_type: MetricType,
        value: float,
        order_id: str,
        symbol: str,
        venue: str,
        strategy: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record individual metric"""
        try:
            metric = ExecutionMetric(
                metric_id=f"{metric_type.value}_{order_id}_{int(time.time())}",
                timestamp=datetime.now(),
                metric_type=metric_type,
                value=value,
                symbol=symbol,
                order_id=order_id,
                venue=venue,
                strategy=strategy,
                metadata=metadata or {}
            )

            self.metrics[metric_type].append(metric)

            # Add to execution session
            self.execution_sessions[order_id].append(metric)

            # Check for alerts
            await self._check_metric_alerts(metric)

        except Exception as e:
            logger.error(f"[ERROR] Failed to record metric: {e}")

    async def _calculate_slippage(
        self,
        expected_price: float,
        actual_price: float,
        side: str
    ) -> float:
        """Calculate slippage in basis points"""
        try:
            if expected_price <= 0:
                return 0.0

            if side.upper() == 'BUY':
                # For buy orders, positive slippage means paying more than expected
                slippage = ((actual_price - expected_price) / expected_price) * 10000
            else:
                # For sell orders, positive slippage means receiving less than expected
                slippage = ((expected_price - actual_price) / expected_price) * 10000

            return slippage

        except Exception as e:
            logger.error(f"[ERROR] Slippage calculation failed: {e}")
            return 0.0

    async def _calculate_commission_bps(self, commission: float, trade_value: float) -> float:
        """Calculate commission in basis points"""
        try:
            if trade_value <= 0:
                return 0.0

            return (commission / trade_value) * 10000

        except Exception as e:
            logger.error(f"[ERROR] Commission calculation failed: {e}")
            return 0.0

    def get_performance_summary(self, time_window: TimeWindow = TimeWindow.HOUR) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            cutoff_time = self._get_cutoff_time(time_window)

            summary = {
                'time_window': time_window.value,
                'summary_timestamp': datetime.now().isoformat(),
                'metrics': {}
            }

            # Calculate summary for each metric type
            for metric_type in MetricType:
                recent_metrics = [
                    metric for metric in self.metrics[metric_type]
                    if metric.timestamp >= cutoff_time
                ]

                if recent_metrics:
                    values = [metric.value for metric in recent_metrics]
                    summary['metrics'][metric_type.value] = {
                        'count': len(values),
                        'average': statistics.mean(values),
                        'median': statistics.median(values),
                        'min': min(values),
                        'max': max(values),
                        'std_dev': statistics.stdev(values) if len(values) > 1 else 0.0
                    }
                else:
                    summary['metrics'][metric_type.value] = {
                        'count': 0,
                        'average': 0.0,
                        'median': 0.0,
                        'min': 0.0,
                        'max': 0.0,
                        'std_dev': 0.0
                    }

            return summary

        except Exception as e:
            logger.error(f"[ERROR] Performance summary generation failed: {e}")
            return {'error': str(e)}

    def _get_cutoff_time(self, time_window: TimeWindow) -> datetime:
        """Get cutoff time for time window"""
        now = datetime.now()

        if time_window == TimeWindow.MINUTE:
            return now - timedelta(minutes=1)
        elif time_window == TimeWindow.FIVE_MINUTES:
            return now - timedelta(minutes=5)
        elif time_window == TimeWindow.FIFTEEN_MINUTES:
            return now - timedelta(minutes=15)
        elif time_window == TimeWindow.HOUR:
            return now - timedelta(hours=1)
        elif time_window == TimeWindow.DAY:
            return now - timedelta(days=1)
        elif time_window == TimeWindow.WEEK:
            return now - timedelta(weeks=1)
        else:
            return now - timedelta(hours=1)  # Default to 1 hour

    async def _check_metric_alerts(self, metric: ExecutionMetric):
        """Check if metric triggers any alerts"""
        try:
            metric_name = f"{metric.metric_type.value}_{metric.symbol}"

            if metric_name in self.alert_thresholds:
                threshold = self.alert_thresholds[metric_name]

                if metric.value > threshold:
                    logger.warning(f"[ALERT] {metric_name} threshold exceeded: "
                                 f"{metric.value:.2f} > {threshold:.2f}")

        except Exception as e:
            logger.error(f"[ERROR] Alert check failed: {e}")
