# ═══════════════════════════════════════════════════════════════════════════════
# 🗂️ DATA RETENTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

# Data Retention Policies
retention_policies:
  
  # Historical Market Data
  historical_data:
    keep_days: 90              # Keep 3 months of historical data
    archive_after_days: 30     # Archive after 1 month
    compress_archives: true    # Compress archived files
    priority: "medium"         # low, medium, high
    description: "Raw market data from data providers"
  
  # Feature Engineering Data
  feature_data:
    keep_days: 60              # Keep 2 months of feature data
    archive_after_days: 21     # Archive after 3 weeks
    compress_archives: true
    priority: "high"           # Features are expensive to regenerate
    description: "Processed features for ML training"
  
  # Backtesting Results
  backtest_results:
    keep_days: 180             # Keep 6 months of backtest results
    archive_after_days: 60     # Archive after 2 months
    compress_archives: true
    priority: "medium"
    description: "Strategy backtesting results and performance data"
  
  # Model Data and Checkpoints
  model_data:
    keep_versions: 10          # Keep last 10 model versions
    archive_old_versions: true # Archive instead of deleting old versions
    compress_archives: true
    priority: "high"           # Models are critical
    description: "Trained models, checkpoints, and metadata"
  
  # Log Files
  logs:
    keep_days: 30              # Keep 1 month of logs
    archive_after_days: 7      # Archive after 1 week
    compress_archives: true
    priority: "low"
    description: "Application logs and debug information"
  
  # Execution Data
  execution_data:
    keep_days: 365             # Keep 1 year of execution data
    archive_after_days: 90     # Archive after 3 months
    compress_archives: true
    priority: "high"           # Important for compliance
    description: "Trade execution records and performance"

# Directory Mappings
directories:
  historical_data: "data/historical"
  feature_data: "data/features"
  backtest_results: "data/backtest"
  model_data: "data/models"
  logs: "logs"
  execution_data: "data/execution"
  archive_base: "data/archives"

# Automatic Cleanup Settings
auto_cleanup:
  enabled: true               # Enable automatic cleanup
  schedule: "weekly"          # daily, weekly, monthly
  day_of_week: "sunday"       # For weekly schedule
  time: "02:00"              # Time to run cleanup (HH:MM)
  dry_run: false             # Set to true to see what would be cleaned without doing it
  
  # Safety settings
  min_free_space_gb: 10      # Trigger cleanup if free space < 10GB
  max_cleanup_size_gb: 50    # Don't clean more than 50GB in one run
  
  # Notification settings
  notify_on_cleanup: true
  notify_on_errors: true

# Archive Settings
archive:
  # Compression settings
  compression:
    parquet_compression: "brotli"  # brotli, snappy, gzip
    csv_compression: "gzip"        # gzip, bz2
    log_compression: "gzip"
  
  # Archive structure
  structure: "year/month"          # year/month, year/quarter, flat
  
  # Archive validation
  verify_archives: true           # Verify archived files can be read
  checksum_validation: true       # Create and verify checksums

# Data Lifecycle Rules
lifecycle_rules:
  
  # Rule 1: Large file handling
  large_files:
    threshold_gb: 5             # Files larger than 5GB
    action: "archive_immediately" # archive_immediately, compress, split
    priority: "high"
  
  # Rule 2: Duplicate detection
  duplicates:
    enabled: true
    action: "keep_latest"       # keep_latest, keep_largest, manual_review
    scan_frequency: "weekly"
  
  # Rule 3: Unused data detection
  unused_data:
    enabled: true
    no_access_days: 60          # Files not accessed for 60 days
    action: "archive"           # archive, delete, flag
  
  # Rule 4: Critical data protection
  critical_data:
    patterns:
      - "*/models/production/*"
      - "*/execution/trades_*"
      - "*/backtest/final_*"
    never_delete: true
    always_backup: true

# Monitoring and Alerts
monitoring:
  # Disk space monitoring
  disk_space:
    warning_threshold_gb: 20    # Warn when free space < 20GB
    critical_threshold_gb: 10   # Critical alert when < 10GB
    check_frequency: "hourly"
  
  # Data growth monitoring
  data_growth:
    track_growth_rate: true
    alert_on_rapid_growth: true
    growth_threshold_gb_per_day: 5  # Alert if growing > 5GB/day
  
  # Retention compliance
  compliance:
    track_policy_violations: true
    alert_on_violations: true
    generate_reports: true

# Backup Integration
backup:
  # Before deletion backup
  backup_before_delete: true
  backup_location: "data/backups/pre_deletion"
  backup_retention_days: 30
  
  # Archive backup
  backup_archives: true
  archive_backup_location: "data/backups/archives"
  
  # Cloud backup integration
  cloud_backup:
    enabled: false
    provider: "aws_s3"          # aws_s3, azure_blob, gcp_storage
    bucket_name: "trading-data-backup"
    encryption: true

# Recovery Settings
recovery:
  # Archive recovery
  enable_recovery: true
  recovery_location: "data/recovery"
  
  # Recovery validation
  validate_recovered_data: true
  recovery_test_frequency: "monthly"

# Reporting
reporting:
  # Generate retention reports
  generate_reports: true
  report_frequency: "weekly"
  report_location: "data/reports/retention"
  
  # Report content
  include_metrics:
    - "storage_usage"
    - "cleanup_actions"
    - "space_savings"
    - "policy_compliance"
    - "data_growth_trends"
  
  # Export formats
  export_formats:
    - "json"
    - "csv"
    - "html"

# Performance Optimization
performance:
  # Parallel processing
  parallel_operations: true
  max_workers: 4
  
  # Batch processing
  batch_size: 1000            # Process files in batches
  
  # Memory management
  max_memory_usage_gb: 8      # Limit memory usage during operations
  
  # I/O optimization
  use_async_io: true
  buffer_size_mb: 64

# Integration Settings
integration:
  # AI Training Agent integration
  ai_training:
    auto_update_cutoff_date: true
    preserve_training_data: true
    backup_before_cleanup: true
  
  # Backtesting integration
  backtesting:
    preserve_recent_results: true
    archive_old_results: true
  
  # Monitoring integration
  monitoring:
    send_metrics: true
    metric_endpoint: "http://localhost:8080/metrics"
