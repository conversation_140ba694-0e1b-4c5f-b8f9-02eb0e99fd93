# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 PERFOR<PERSON><PERSON><PERSON> ANALYSIS GATEWAY CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# 📡 STREAM PROCESSING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
stream_config:
  buffer_size: 10000
  batch_size: 100
  flush_interval_seconds: 5
  max_out_of_order_delay_seconds: 30
  enable_deduplication: true
  enable_compression: true
  retry_attempts: 3
  retry_delay_seconds: 5

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 TRADE RECONCILIATION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
reconciliation_config:
  time_tolerance_seconds: 30
  price_tolerance_percent: 0.1
  quantity_tolerance_percent: 0.0
  min_confidence_score: 0.8
  enable_fuzzy_matching: true
  max_partial_fill_gap_minutes: 5
  duplicate_detection_window_minutes: 60

# ═══════════════════════════════════════════════════════════════════════════════
# 📚 HISTORICAL BACKFILL CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
backfill_config:
  batch_size: 1000
  max_concurrent_tasks: 3
  retry_attempts: 3
  retry_delay_seconds: 30
  rate_limit_requests_per_minute: 60
  data_validation_enabled: true
  checkpoint_interval_records: 10000
  temp_storage_path: "temp/backfill"

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 GATEWAY CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
gateway_config:
  # Financial parameters
  risk_free_rate: 0.06  # 6% annual risk-free rate
  benchmark_return: 0.12  # 12% annual benchmark return
  initial_capital: 100000  # ₹1,00,000 initial capital
  
  # API settings
  api_port: 8080
  websocket_port: 8081
  max_concurrent_requests: 100
  request_timeout_seconds: 30
  
  # Health monitoring
  health_check_interval_seconds: 60
  metrics_retention_days: 30

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 AGENT INTEGRATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
agent_integration:
  # Signal Generation Agent
  signal_generation_agent:
    enabled: true
    endpoint: "http://localhost:8082"
    websocket_endpoint: "ws://localhost:8083"
    timeout_seconds: 30
    retry_attempts: 3
    
  # Strategy Evolution Agent
  strategy_evolution_agent:
    enabled: true
    endpoint: "http://localhost:8084"
    timeout_seconds: 60
    retry_attempts: 3
    
  # Risk Management Agent
  risk_management_agent:
    enabled: true
    endpoint: "http://localhost:8085"
    timeout_seconds: 30
    retry_attempts: 3
    
  # AI Training Agent
  ai_training_agent:
    enabled: true
    endpoint: "http://localhost:8086"
    timeout_seconds: 120
    retry_attempts: 2
    
  # Execution Agent
  execution_agent:
    enabled: true
    endpoint: "http://localhost:8087"
    timeout_seconds: 30
    retry_attempts: 3

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 METRICS CALCULATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
metrics_config:
  # Core metrics settings
  core_metrics:
    min_trades_for_analysis: 5
    rolling_window_trades: 30
    enable_strategy_comparison: true
    enable_symbol_analysis: true
    enable_monthly_breakdown: true
    
  # Risk metrics settings
  risk_metrics:
    var_confidence_levels: [95, 99, 99.9]
    enable_parametric_var: true
    enable_monte_carlo_var: false
    drawdown_threshold_percent: 0.01
    
  # Drawdown analysis settings
  drawdown_analysis:
    min_drawdown_threshold: 0.01  # 0.01% minimum drawdown
    enable_clustering_analysis: true
    enable_seasonal_analysis: true
    rolling_window_days: 30

# ═══════════════════════════════════════════════════════════════════════════════
# 🚨 ALERTING AND NOTIFICATIONS
# ═══════════════════════════════════════════════════════════════════════════════
alerting:
  # Performance alerts
  performance_alerts:
    enabled: true
    max_drawdown_threshold: 10.0  # 10% max drawdown alert
    min_sharpe_ratio: 0.5
    min_win_rate: 40.0  # 40% minimum win rate
    max_consecutive_losses: 5
    
  # Risk alerts
  risk_alerts:
    enabled: true
    var_breach_threshold: 1.5  # 1.5x VaR breach
    volatility_spike_threshold: 2.0  # 2x normal volatility
    correlation_breakdown_threshold: 0.3
    
  # System alerts
  system_alerts:
    enabled: true
    data_delay_threshold_minutes: 15
    reconciliation_failure_threshold: 10  # 10% unmatched trades
    processing_delay_threshold_seconds: 60
    
  # Notification channels
  notification_channels:
    email:
      enabled: false
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      recipients: []
      
    slack:
      enabled: false
      webhook_url: ""
      channel: "#trading-alerts"
      
    telegram:
      enabled: false
      bot_token: ""
      chat_ids: []

# ═══════════════════════════════════════════════════════════════════════════════
# 💾 DATA STORAGE SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
storage:
  # Primary storage
  primary_storage:
    type: "parquet"  # parquet, csv, json
    base_path: "data/performance_analysis"
    compression: "snappy"
    partition_by: ["date", "strategy"]
    
  # Backup storage
  backup_storage:
    enabled: true
    type: "parquet"
    base_path: "backup/performance_analysis"
    retention_days: 90
    
  # Cache settings
  cache:
    enabled: true
    type: "memory"  # memory, redis
    max_size_mb: 512
    ttl_seconds: 3600
    
  # Database settings (optional)
  database:
    enabled: false
    type: "postgresql"  # postgresql, mysql, sqlite
    connection_string: ""
    table_prefix: "perf_"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 ADVANCED SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
advanced:
  # Performance optimization
  performance:
    enable_parallel_processing: true
    max_worker_threads: 4
    chunk_size: 1000
    enable_lazy_loading: true
    
  # Memory management
  memory:
    max_memory_usage_mb: 2048
    garbage_collection_interval_seconds: 300
    enable_memory_profiling: false
    
  # Logging
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: "logs/performance_analysis_gateway.log"
    max_file_size_mb: 100
    backup_count: 5
    enable_structured_logging: true
    
  # Security
  security:
    enable_authentication: false
    api_key_required: false
    rate_limiting:
      enabled: true
      requests_per_minute: 1000
      burst_limit: 100
    
  # Monitoring
  monitoring:
    enable_metrics_export: true
    metrics_export_interval_seconds: 60
    enable_health_endpoint: true
    enable_debug_endpoints: false

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING AND DEVELOPMENT
# ═══════════════════════════════════════════════════════════════════════════════
development:
  # Testing settings
  testing:
    enable_mock_data: false
    mock_data_path: "test_data/performance_analysis"
    enable_test_endpoints: false
    
  # Debug settings
  debug:
    enable_debug_mode: false
    verbose_logging: false
    enable_profiling: false
    profile_output_path: "profiles/"
    
  # Simulation settings
  simulation:
    enable_simulation_mode: false
    simulation_speed_multiplier: 1.0
    simulation_data_path: "simulation_data/"
