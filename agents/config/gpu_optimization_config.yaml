# GPU OPTIMIZATION CONFIGURATION (2024-2025)
# Optimized settings for RTX 3060Ti with 8GB VRAM
# Optimized settings for RTX 3060Ti with 8GB VRAM
# Implements latest performance recommendations

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 POLARS GPU ENGINE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
polars:
  # GPU Engine Settings (NEW 2024 Feature)
  gpu_engine: true           # Enable GPU acceleration (13x speedup)
  streaming: true            # Enable streaming for large datasets
  
  # Memory Management
  memory_pool_size: "6GB"    # Reserve 6GB for Polars (75% of 8GB VRAM)
  chunk_size: 500000         # Optimal chunk size for RTX 3060Ti
  
  # Performance Tuning
  thread_pool_size: 8        # Match CPU cores
  force_async: true          # Force async operations
  
  # Data Types Optimization
  string_cache: true         # Cache string data for faster operations
  categorical_cache: true    # Cache categorical data

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 PYTORCH GPU OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
pytorch:
  # CUDA Settings
  device: "cuda:0"
  memory_fraction: 0.8       # Use 80% of GPU memory
  
  # Performance Optimizations
  cudnn_benchmark: true      # Enable cuDNN benchmarking
  cudnn_deterministic: false # Disable for better performance
  
  # Mixed Precision Training
  mixed_precision: true      # Enable for 1.5-2x speedup
  amp_enabled: true          # Automatic Mixed Precision
  
  # Memory Management
  empty_cache_frequency: 100 # Clear cache every 100 iterations
  max_split_size_mb: 512     # Limit memory fragmentation

# ═══════════════════════════════════════════════════════════════════════════════
# 🌟 LIGHTGBM GPU OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
lightgbm:
  # GPU Configuration
  device: "gpu"
  gpu_platform_id: 0
  gpu_device_id: 0
  
  # Memory Optimization for RTX 3060Ti
  gpu_use_dp: false          # Single precision for speed
  max_bin: 255               # Optimal for GPU memory
  
  # Performance Tuning
  num_threads: 8             # Match CPU cores
  force_col_wise: true       # Better GPU utilization
  
  # Memory Management
  max_depth: 8               # Limit tree depth for GPU memory

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 CATBOOST GPU OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
catboost:
  # GPU Settings
  task_type: "GPU"
  devices: "0"
  
  # Memory Management
  gpu_ram_part: 0.8          # Use 80% of GPU memory
  pinned_memory_size: "2GB"  # Pin 2GB for faster transfers
  
  # Performance Settings
  thread_count: 8            # Match CPU cores
  used_ram_limit: "16GB"     # System RAM limit

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 SYSTEM MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
monitoring:
  # GPU Monitoring
  gpu_memory_threshold: 0.9  # Alert at 90% GPU memory usage
  gpu_utilization_target: 0.8 # Target 80% GPU utilization
  
  # Performance Metrics
  log_gpu_stats: true        # Log GPU statistics
  log_frequency: 60          # Log every 60 seconds
  
  # Alerts
  memory_alert_threshold: 0.95 # Alert at 95% memory usage
  temperature_alert: 80      # Alert at 80°C GPU temperature

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 AUTOMATIC OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
auto_optimization:
  # Dynamic Memory Management
  auto_memory_management: true
  memory_cleanup_frequency: 300 # Clean up every 5 minutes
  
  # Performance Tuning
  auto_batch_size: true      # Automatically adjust batch sizes
  auto_chunk_size: true      # Automatically adjust chunk sizes
  
  # Fallback Settings
  cpu_fallback: true         # Fall back to CPU if GPU fails
  fallback_threshold: 0.95   # Fall back at 95% GPU memory

# ═══════════════════════════════════════════════════════════════════════════════
# 🎮 RTX 3060Ti SPECIFIC OPTIMIZATIONS
# ═══════════════════════════════════════════════════════════════════════════════
rtx_3060ti:
  # Hardware Specifications
  vram_size: "8GB"
  cuda_cores: 3584
  memory_bandwidth: "448 GB/s"
  
  # Optimal Settings
  optimal_batch_size: 1024   # Sweet spot for this GPU
  optimal_chunk_size: 500000 # Optimal for 8GB VRAM
  
  # Memory Distribution
  memory_allocation:
    polars: "3GB"            # 37.5% for data processing
    pytorch: "2.5GB"         # 31.25% for deep learning
    lightgbm: "1.5GB"        # 18.75% for gradient boosting
    catboost: "1GB"          # 12.5% for additional models
  
  # Performance Targets
  target_utilization: 0.85   # Target 85% GPU utilization
  target_memory_usage: 0.8   # Target 80% memory usage

# ═══════════════════════════════════════════════════════════════════════════════
# 🛠️ TROUBLESHOOTING
# ═══════════════════════════════════════════════════════════════════════════════
troubleshooting:
  # Common Issues
  out_of_memory_action: "reduce_batch_size"
  low_utilization_action: "increase_batch_size"
  
  # Debug Settings
  debug_memory: false        # Enable for memory debugging
  profile_gpu: false         # Enable for performance profiling
  
  # Recovery Settings
  auto_recovery: true        # Auto-recover from GPU errors
  max_retries: 3             # Maximum retry attempts

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 PERFORMANCE BENCHMARKS (Expected on RTX 3060Ti)
# ═══════════════════════════════════════════════════════════════════════════════
# Polars GPU Engine: 13x speedup over CPU
# LightGBM GPU: 40-75% faster than CPU
# PyTorch Mixed Precision: 1.5-2x speedup
# CatBoost GPU: 10x faster training
# Overall Pipeline: 5-8x faster than CPU-only
