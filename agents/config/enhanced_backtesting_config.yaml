# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 ENHANCED BACKTESTING AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive configuration for multi-strategy, multi-timeframe, and regime-based backtesting

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ GENERAL CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
general:
  # Basic Settings
  agent_name: "Enhanced Backtesting Agent"
  version: "2.0.0"
  description: "Comprehensive backtesting system with multi-strategy, multi-timeframe, and regime-based testing"
  
  # Data Configuration
  data_directory: "data/features"
  output_directory: "data/backtest"
  strategies_config: "config/strategies.yaml"
  symbols_config: "config/symbols.json"
  
  # Processing Limits (None = unlimited)
  max_symbols: null
  max_strategies: null
  max_timeframes: null
  
  # Logging Configuration
  log_level: "INFO"
  log_file: "logs/enhanced_backtesting.log"
  enable_detailed_logging: true
  enable_progress_tracking: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🔁 MULTI-STRATEGY & MULTI-TIMEFRAME CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
multi_strategy:
  # Strategy Selection
  enable_individual_strategies: true
  enable_batch_processing: true
  enable_strategy_combinations: false  # Advanced feature for strategy ensembles
  
  # Strategy Filtering
  min_confidence_threshold: 0.0
  max_risk_per_strategy: 0.05  # 5% max risk per strategy
  enable_strategy_validation: true
  
  # Strategy Sources
  strategy_sources:
    - "config/strategies.yaml"
    - "config/enhanced_strategies.yaml"
    # - "data/evolved_strategies/*.yaml"  # Optional: evolved strategies

multi_timeframe:
  # Supported Timeframes (based on available historical data)
  timeframes:
    - "1min"
    - "3min"
    - "5min"
    - "15min"
    # - "30min"  # Not available in historical data
    # - "1hr"    # Not available in historical data
    # - "4hr"    # Optional: longer timeframes
    # - "1day"   # Optional: daily timeframes
  
  # Timeframe Processing
  process_timeframes_parallel: true
  timeframe_batch_size: 2
  enable_timeframe_correlation_analysis: false  # Advanced feature
  
  # Asset Types
  asset_types:
    underlying:
      - "NIFTY"
      - "BANKNIFTY"
      # - "FINNIFTY"  # Optional: additional indices
    
    options:
      expiry_types:
        - "weekly"    # NIFTY weekly expiry
        - "monthly"   # BANKNIFTY monthly expiry
      
      option_types:
        - "CE"  # Call options
        - "PE"  # Put options
      
      # Strike Selection (relative to spot)
      strike_range:
        otm_range: [-10, 10]  # Out-of-money strikes (% from spot)
        itm_range: [-5, 5]    # In-the-money strikes (% from spot)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 SMART BACKTESTING MODES
# ═══════════════════════════════════════════════════════════════════════════════
backtesting_modes:
  # Mode Selection (can enable multiple)
  enable_deterministic: true
  enable_probabilistic: false  # Monte Carlo
  enable_adaptive_ai: false    # AI-driven parameter adjustment
  
  # Deterministic Mode
  deterministic:
    description: "Historical conditions matched exactly (no randomness)"
    use_exact_historical_data: true
    enable_look_ahead_bias_protection: true
    enable_survivorship_bias_correction: false
  
  # Probabilistic Mode (Monte Carlo)
  probabilistic:
    description: "Uses Monte Carlo bootstrapping to estimate robustness"
    num_simulations: 1000
    confidence_intervals: [0.05, 0.95]  # 90% confidence interval
    bootstrap_method: "block_bootstrap"  # or "stationary_bootstrap"
    block_size: 252  # Trading days in a year
    enable_scenario_generation: true
  
  # Adaptive AI Mode
  adaptive_ai:
    description: "Adjusts parameter sets based on prior runs"
    learning_rate: 0.01
    adaptation_frequency: 100  # Adjust every N trades
    min_trades_before_adaptation: 50
    enable_online_learning: false
    model_type: "reinforcement_learning"  # or "bayesian_optimization"

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE METRICS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
performance_metrics:
  # Basic Metrics
  basic_metrics:
    - "roi"                    # Return on Investment (%)
    - "accuracy"               # % profitable trades
    - "expectancy"             # Average return per trade
    - "win_loss_ratio"         # Avg win / Avg loss
    - "total_trades"           # Number of trades
    - "avg_holding_period"     # Average trade duration
  
  # Risk-Adjusted Metrics
  risk_metrics:
    - "sharpe_ratio"           # Risk-adjusted return
    - "sortino_ratio"          # Downside risk-adjusted return
    - "calmar_ratio"           # Return / Max Drawdown
    - "max_drawdown"           # Maximum peak-to-trough decline
    - "var_95"                 # Value at Risk (95%)
    - "cvar_95"                # Conditional Value at Risk (95%)
  
  # Advanced Metrics
  advanced_metrics:
    - "profit_factor"          # Gross profit / Gross loss
    - "recovery_factor"        # Net profit / Max drawdown
    - "payoff_ratio"           # Avg win / Avg loss
    - "kelly_criterion"        # Optimal position size
    - "ulcer_index"            # Drawdown-based risk measure
    - "sterling_ratio"         # Return / Average drawdown
  
  # Capital Efficiency Metrics
  capital_metrics:
    - "capital_utilization"    # % of capital actively used
    - "return_on_margin"       # Return relative to margin used
    - "leverage_efficiency"    # Return per unit of leverage
    - "opportunity_cost"       # Cost of capital not deployed
  
  # Liquidity & Market Impact
  liquidity_metrics:
    - "avg_slippage"           # Average execution slippage
    - "market_impact"          # Price impact of trades
    - "liquidity_score"        # Overall liquidity assessment
    - "execution_quality"      # Trade execution efficiency

# ═══════════════════════════════════════════════════════════════════════════════
# 💰 CAPITAL & RISK MODELING
# ═══════════════════════════════════════════════════════════════════════════════
capital_risk_modeling:
  # Initial Capital
  initial_capital: 100000.0  # Rs. 1,00,000
  currency: "INR"
  
  # Position Sizing
  position_sizing:
    method: "fixed_risk"  # "fixed_risk", "fixed_amount", "kelly", "optimal_f"
    risk_per_trade_pct: 1.0  # 1% risk per trade
    max_position_size_pct: 10.0  # Max 10% of capital per position
    min_position_size: 1000.0  # Minimum position size in INR
    
    # Advanced Position Sizing
    enable_dynamic_sizing: false
    volatility_adjustment: false
    correlation_adjustment: false
  
  # Risk Parameters
  risk_management:
    max_concurrent_trades: 5
    max_daily_loss_pct: 3.0  # Max 3% daily loss
    max_drawdown_pct: 10.0   # Max 10% drawdown
    
    # Stop Loss & Take Profit
    default_stop_loss_pct: 2.0
    default_take_profit_pct: 4.0
    enable_trailing_stops: false
    
    # Risk-Reward Ratios
    risk_reward_ratios:
      - [1, 1.5]
      - [1, 2.0]
      - [1.5, 2.0]
      - [2, 3.0]
  
  # Transaction Costs (Indian Market)
  transaction_costs:
    # Brokerage
    brokerage_pct: 0.03      # 0.03% brokerage
    brokerage_flat: 20.0     # Rs. 20 flat brokerage (whichever is lower)
    
    # Taxes & Charges
    stt_pct: 0.025           # Securities Transaction Tax
    exchange_charges_pct: 0.00345  # NSE charges
    gst_pct: 18.0            # GST on brokerage
    sebi_charges_pct: 0.0001 # SEBI charges
    stamp_duty_pct: 0.003    # Stamp duty
    
    # Slippage
    slippage_pct: 0.02       # 0.02% average slippage
    slippage_model: "linear" # "linear", "sqrt", "impact"
  
  # Margin Requirements
  margin_modeling:
    # Intraday Margins
    intraday_margin_multiplier: 3.5  # 3.5x leverage for intraday
    
    # Options Margins
    options_margin_pct: 15.0  # 15% margin for options
    
    # Margin Utilization
    max_margin_utilization_pct: 80.0  # Max 80% margin usage
    margin_buffer_pct: 20.0           # 20% margin buffer

# ═══════════════════════════════════════════════════════════════════════════════
# 🗂️ SCENARIO-BASED & REGIME-BASED TESTING
# ═══════════════════════════════════════════════════════════════════════════════
scenario_regime_testing:
  # Enable Regime-Based Testing
  enable_regime_testing: true

  # Market Regimes
  market_regimes:
    trending_up:
      description: "Bull market / Strong uptrend"
      detection_method: "price_momentum"  # "price_momentum", "volatility", "ml_classifier"
      parameters:
        min_trend_strength: 0.7
        lookback_period: 20
        trend_threshold: 0.02  # 2% minimum trend

      preferred_strategies:
        - "momentum_strategies"
        - "breakout_strategies"
        - "trend_following"

      avoid_strategies:
        - "mean_reversion"
        - "contrarian_strategies"

    trending_down:
      description: "Bear market / Strong downtrend"
      detection_method: "price_momentum"
      parameters:
        min_trend_strength: -0.7
        lookback_period: 20
        trend_threshold: -0.02  # -2% minimum trend

      preferred_strategies:
        - "short_strategies"
        - "put_strategies"
        - "bear_spreads"

      avoid_strategies:
        - "long_only_strategies"
        - "call_strategies"

    sideways:
      description: "Range-bound / Sideways market"
      detection_method: "volatility"
      parameters:
        max_volatility: 0.15
        range_threshold: 0.05  # 5% range
        lookback_period: 20

      preferred_strategies:
        - "mean_reversion"
        - "range_trading"
        - "theta_strategies"

      avoid_strategies:
        - "momentum_strategies"
        - "breakout_strategies"

    volatile:
      description: "High volatility market"
      detection_method: "volatility"
      parameters:
        min_volatility: 0.25
        volatility_percentile: 0.8  # 80th percentile
        lookback_period: 20

      preferred_strategies:
        - "volatility_strategies"
        - "straddle_strategies"
        - "gamma_strategies"

      avoid_strategies:
        - "theta_strategies"
        - "low_volatility_strategies"

  # Event-Driven Scenarios
  event_scenarios:
    enable_event_testing: true

    events:
      expiry_day:
        description: "Options expiry day effects"
        detection_method: "calendar"
        impact_duration_days: 1
        volatility_multiplier: 1.5

      budget_day:
        description: "Budget announcement day"
        detection_method: "calendar"
        impact_duration_days: 3
        volatility_multiplier: 2.0

      fed_announcement:
        description: "Federal Reserve announcements"
        detection_method: "calendar"
        impact_duration_days: 2
        volatility_multiplier: 1.8

      earnings_season:
        description: "Corporate earnings season"
        detection_method: "calendar"
        impact_duration_days: 30
        volatility_multiplier: 1.3

  # Regime Detection Configuration
  regime_detection:
    method: "hmm"  # "hmm", "threshold", "ml_classifier"

    # Hidden Markov Model
    hmm_config:
      n_states: 4  # trending_up, trending_down, sideways, volatile
      covariance_type: "full"
      n_iter: 100

    # Threshold-based
    threshold_config:
      trend_threshold: 0.02
      volatility_threshold: 0.20
      volume_threshold: 1.5

    # ML Classifier
    ml_config:
      model_type: "random_forest"
      features: ["returns", "volatility", "volume", "rsi", "macd"]
      lookback_window: 20
      retrain_frequency: 252  # Trading days

# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 PARAMETER SWEEP & OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
parameter_optimization:
  # Enable Parameter Optimization
  enable_optimization: false  # Computationally intensive

  # Optimization Method
  optimization_method: "optuna"  # "optuna", "grid_search", "random_search", "genetic_algorithm"

  # Optuna Configuration
  optuna_config:
    n_trials: 100
    timeout: 3600  # 1 hour timeout
    sampler: "TPE"  # Tree-structured Parzen Estimator
    pruner: "MedianPruner"

    # Objective Function
    objective_metric: "sharpe_ratio"  # Metric to optimize
    direction: "maximize"  # "maximize" or "minimize"

    # Multi-objective optimization
    enable_multi_objective: false
    objectives:
      - "sharpe_ratio"
      - "max_drawdown"
      - "profit_factor"

  # Parameter Ranges
  parameter_ranges:
    # Technical Indicator Parameters
    rsi_period: [5, 50]
    ema_short: [5, 20]
    ema_long: [20, 100]
    macd_fast: [8, 15]
    macd_slow: [20, 30]
    bollinger_period: [10, 30]
    bollinger_std: [1.5, 2.5]

    # Risk Management Parameters
    stop_loss_pct: [0.5, 5.0]
    take_profit_pct: [1.0, 10.0]
    risk_reward_ratio: [1.0, 4.0]

    # Position Sizing Parameters
    position_size_pct: [0.5, 5.0]
    max_positions: [1, 10]

  # Optimization Constraints
  constraints:
    min_trades: 50  # Minimum trades for valid optimization
    max_drawdown_limit: 0.20  # 20% max drawdown constraint
    min_sharpe_ratio: 0.5  # Minimum Sharpe ratio

  # Walk-Forward Optimization
  walk_forward:
    enable: false
    training_period_days: 252  # 1 year training
    testing_period_days: 63   # 3 months testing
    step_size_days: 21        # 1 month steps
    min_training_trades: 100

# ═══════════════════════════════════════════════════════════════════════════════
# 🧾 RESULT LOGGING & VERSIONING
# ═══════════════════════════════════════════════════════════════════════════════
result_logging:
  # Output Configuration
  output_format: "parquet"  # "parquet", "csv", "json", "hdf5"
  compression: "brotli"     # "brotli", "snappy", "gzip", "lz4"

  # File Naming
  file_naming:
    pattern: "{strategy_id}_backtest_{timestamp}.{format}"
    timestamp_format: "%Y%m%d_%H%M%S"
    include_git_hash: false  # Include git commit hash in filename

  # Versioning
  versioning:
    enable_versioning: true
    version_directory: "data/backtest/versions"
    max_versions_per_strategy: 10
    auto_cleanup_old_versions: true

  # Content Configuration
  save_components:
    raw_trades: true          # Individual trade records
    pnl_curves: true          # Portfolio value over time
    metrics_summary: true     # Performance metrics
    strategy_parameters: true # Strategy configuration used
    market_conditions: true   # Market regime during backtest
    execution_details: true   # Slippage, costs, etc.

  # Database Integration
  database:
    enable_database_logging: false
    connection_string: "sqlite:///data/backtest/results.db"
    table_prefix: "backtest_"

  # Real-time Monitoring
  monitoring:
    enable_live_monitoring: false
    update_frequency_seconds: 60
    dashboard_port: 8080

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 VISUALIZATION & DEBUGGING
# ═══════════════════════════════════════════════════════════════════════════════
visualization:
  # Enable Visualization
  enable_visualization: false  # Optional feature

  # Chart Types
  chart_types:
    - "pnl_curve"           # Portfolio value over time
    - "drawdown_chart"      # Drawdown visualization
    - "trade_distribution"  # Win/loss distribution
    - "monthly_returns"     # Monthly return heatmap
    - "rolling_metrics"     # Rolling Sharpe, etc.

  # Interactive Features
  interactive:
    enable_plotly: true
    enable_bokeh: false
    save_html: true
    show_in_browser: false

  # Export Options
  export:
    formats: ["png", "pdf", "svg"]
    dpi: 300
    save_directory: "data/backtest/charts"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔍 SIGNAL DEBUGGING & REPLAY
# ═══════════════════════════════════════════════════════════════════════════════
debugging:
  # Enable Debugging Features
  enable_debugging: false

  # Trade Replay
  trade_replay:
    enable_replay: true
    replay_speed: "1x"  # "0.5x", "1x", "2x", "5x"
    save_replay_data: true

  # Signal Analysis
  signal_analysis:
    enable_signal_validation: true
    check_false_positives: true
    check_false_negatives: true
    signal_quality_threshold: 0.7

  # Execution Timing
  execution_timing:
    enable_timing_analysis: true
    check_latency_impact: true
    simulate_execution_delays: false
    max_execution_delay_ms: 100

# ═══════════════════════════════════════════════════════════════════════════════
# 🤖 LLM INTEGRATION
# ═══════════════════════════════════════════════════════════════════════════════
llm_integration:
  # Enable LLM Features
  enable_llm_summary: true

  # Summary Configuration
  summary_config:
    model: "local_ollama"  # "local_ollama", "openai", "anthropic"
    model_name: "mistral"  # For local models

    # Summary Content
    include_performance_summary: true
    include_risk_analysis: true
    include_regime_analysis: true
    include_recommendations: true

    # Output Format
    output_format: "markdown"  # "markdown", "json", "text"
    save_to_file: true
    file_path: "data/backtest/summaries/{strategy_id}_summary.md"

  # Integration with LLM Interface Agent
  agent_integration:
    enable_agent_communication: true
    agent_endpoint: "http://localhost:8000/llm"
    timeout_seconds: 30

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 ADVANCED FEATURES (OPTIONAL)
# ═══════════════════════════════════════════════════════════════════════════════
advanced_features:
  # Walk-Forward Testing
  walk_forward_testing:
    enable: false
    window_size_days: 252    # 1 year training window
    step_size_days: 21       # 1 month steps
    min_trades_per_window: 50

  # Reinforcement Learning Integration
  reinforcement_learning:
    enable: false
    reward_function: "sharpe_ratio"  # "sharpe_ratio", "profit_factor", "custom"
    learning_algorithm: "ppo"        # "ppo", "a3c", "dqn"
    training_episodes: 1000

  # Live vs Backtest Comparison
  live_comparison:
    enable: false
    comparison_metrics:
      - "roi"
      - "sharpe_ratio"
      - "max_drawdown"
      - "win_rate"

    alert_thresholds:
      roi_deviation_pct: 10.0      # Alert if live ROI deviates >10%
      sharpe_deviation: 0.5        # Alert if Sharpe deviates >0.5
      drawdown_excess_pct: 5.0     # Alert if live drawdown >5% worse

    notification_methods:
      - "email"
      - "telegram"
      - "slack"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE & OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Processing Configuration
  parallel_processing:
    enable_multiprocessing: true
    max_workers: null  # null = auto-detect
    chunk_size: 500000

  # GPU Acceleration
  gpu_acceleration:
    enable_gpu: true
    gpu_memory_limit_gb: 8
    prefer_cudf: true
    fallback_to_cpu: true

  # Memory Management
  memory_management:
    enable_aggressive_cleanup: true
    cleanup_frequency: 100  # Clean up every N strategies
    max_memory_usage_gb: 16

  # Caching
  caching:
    enable_result_caching: true
    cache_directory: "data/cache/backtest"
    cache_expiry_days: 7

  # Progress Tracking
  progress_tracking:
    enable_progress_bar: true
    log_frequency: 10  # Log progress every N strategies
    estimate_completion_time: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 INTEGRATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
integration:
  # Agent Communication
  agent_communication:
    strategy_evolution_agent:
      enabled: true
      endpoint: "http://localhost:8001/strategy_evolution"
      send_results: true

    ai_training_agent:
      enabled: true
      endpoint: "http://localhost:8002/ai_training"
      send_training_data: true

    performance_analysis_agent:
      enabled: true
      endpoint: "http://localhost:8003/performance_analysis"
      send_metrics: true

  # External Data Sources
  external_data:
    market_data_provider: "angel_one"
    fundamental_data: false
    news_sentiment: false
    economic_indicators: false

  # Notification Settings
  notifications:
    enable_notifications: true

    completion_notification:
      enabled: true
      methods: ["log", "file"]  # "log", "file", "email", "telegram"

    error_notification:
      enabled: true
      methods: ["log", "file"]

    progress_notification:
      enabled: false
      frequency_minutes: 30
