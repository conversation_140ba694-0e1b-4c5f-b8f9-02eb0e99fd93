evolution_config:
  base_templates:
  - RSI_Mean_Reversion_Base
  - EMA_Crossover_Base
  - Bollinger_Bounce_Base
  - VWAP_Breakout_Base
  max_strategies_per_stock_timeframe: 3
  min_performance_threshold: 0.05
  ranking_threshold: 80
  target_stocks:
  - 360ONE
  - ABB
  - RELIANCE
  - TCS
  - INFY
  - HDFC
  - ICICI
  - SBI
  target_timeframes:
  - 1min
  - 3min
  - 5min
  - 15min
  - 30min
  - 1h
strategies:
- description: RSI-based mean reversion strategy
  entry_long: RSI_14 < 30 & Volume > Volume.rolling(20).mean()
  entry_short: RSI_14 > 70 & Volume > Volume.rolling(20).mean()
  exit_long: RSI_14 > 50 | Close < (Entry * (1 - stop_loss_pct))
  exit_short: RSI_14 < 50 | Close > (Entry * (1 + stop_loss_pct))
  name: RSI_Mean_Reversion_Base
  parameters:
    rsi_period: 14
    volume_multiplier: 1.0
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 100
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.04
  type: base_template
- description: EMA crossover trend following strategy
  entry_long: EMA_5 > EMA_20 & EMA_20 > EMA_50 & Volume > Volume.rolling(20).mean()
  entry_short: EMA_5 < EMA_20 & EMA_20 < EMA_50 & Volume > Volume.rolling(20).mean()
  exit_long: EMA_5 < EMA_20 | Close < (Entry * (1 - stop_loss_pct))
  exit_short: EMA_5 > EMA_20 | Close > (Entry * (1 + stop_loss_pct))
  name: EMA_Crossover_Base
  parameters:
    ema_fast: 5
    ema_slow: 20
    ema_trend: 50
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.04
  ranking: 95
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.05
  type: base_template
- description: Bollinger Bands bounce strategy
  entry_long: Close < BB_Lower & RSI_14 < 40
  entry_short: Close > BB_Upper & RSI_14 > 60
  exit_long: Close > BB_Middle | Close < (Entry * (1 - stop_loss_pct))
  exit_short: Close < BB_Middle | Close > (Entry * (1 + stop_loss_pct))
  name: Bollinger_Bounce_Base
  parameters:
    bb_period: 20
    bb_std: 2.0
    rsi_period: 14
  position_sizing:
    method: fixed_percentage
    percentage: 0.06
  ranking: 90
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.03
    take_profit_pct: 0.06
  type: base_template
- description: VWAP breakout strategy
  entry_long: Close > VWAP * 1.002 & Volume > Volume.rolling(20).mean() * 1.5
  entry_short: Close < VWAP * 0.998 & Volume > Volume.rolling(20).mean() * 1.5
  exit_long: Close < VWAP | Close < (Entry * (1 - stop_loss_pct))
  exit_short: Close > VWAP | Close > (Entry * (1 + stop_loss_pct))
  name: VWAP_Breakout_Base
  parameters:
    volume_multiplier: 1.5
    volume_period: 20
    vwap_deviation: 0.002
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 85
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.04
  type: base_template
- best_risk_reward: 2.0
  entry_long: Close < BB_Lower & RSI_14 < 35 & Volume > Volume.rolling(15).mean()
    * 1.2
  entry_short: Close > BB_Upper & RSI_14 > 65 & Volume > Volume.rolling(15).mean()
    * 1.2
  exit_long: Close > BB_Middle | Close < (Entry * 0.975)
  exit_short: Close < BB_Middle | Close > (Entry * 1.025)
  name: Bollinger_Bounce_360ONE_1min
  parameters:
    bb_period: 18
    bb_std: 2.1
    rsi_period: 12
    volume_multiplier: 1.2
    volume_period: 15
  parent_template: Bollinger_Bounce_Base
  performance_summary:
    max_drawdown: 15.0
    profit_factor: 1.3
    sharpe_ratio: 1.2
    total_return: 8.5
    win_rate: 0.65
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 89
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.05
  target_stock: 360ONE
  target_timeframe: 1min
  type: evolved_strategy
- best_risk_reward: 2.25
  entry_long: RSI_14 < 28 & Volume > Volume.rolling(25).mean() * 1.5
  entry_short: RSI_14 > 72 & Volume > Volume.rolling(25).mean() * 1.5
  exit_long: RSI_14 > 55 | Close < (Entry * 0.98)
  exit_short: RSI_14 < 45 | Close > (Entry * 1.02)
  name: RSI_Mean_Reversion_ABB_5min
  parameters:
    rsi_period: 14
    volume_multiplier: 1.5
    volume_period: 25
  parent_template: RSI_Mean_Reversion_Base
  performance_summary:
    max_drawdown: 12.0
    profit_factor: 1.5
    sharpe_ratio: 1.4
    total_return: 10.2
    win_rate: 0.68
  position_sizing:
    method: fixed_percentage
    percentage: 0.04
  ranking: 92
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.045
  target_stock: ABB
  target_timeframe: 5min
  type: evolved_strategy
- best_risk_reward: 2.2
  entry_long: EMA_8 > EMA_21 & EMA_21 > EMA_55 & Volume > Volume.rolling(18).mean()
    * 1.3
  entry_short: EMA_8 < EMA_21 & EMA_21 < EMA_55 & Volume > Volume.rolling(18).mean()
    * 1.3
  exit_long: EMA_8 < EMA_21 | Close < (Entry * 0.975)
  exit_short: EMA_8 > EMA_21 | Close > (Entry * 1.025)
  name: EMA_Crossover_RELIANCE_3min
  parameters:
    ema_fast: 8
    ema_slow: 21
    ema_trend: 55
    volume_multiplier: 1.3
    volume_period: 18
  parent_template: EMA_Crossover_Base
  performance_summary:
    max_drawdown: 18.0
    profit_factor: 1.25
    sharpe_ratio: 1.1
    total_return: 7.8
    win_rate: 0.62
  position_sizing:
    method: fixed_percentage
    percentage: 0.045
  ranking: 87
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.055
  target_stock: RELIANCE
  target_timeframe: 3min
  type: evolved_strategy
