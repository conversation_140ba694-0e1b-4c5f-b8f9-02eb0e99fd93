#!/usr/bin/env python3
"""
Regime Detector for Market Monitoring Agent

Detects market regimes (Bull/Bear/Sideways) and volatility levels.
"""

import logging
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

from .data_structures import MarketRegime, MarketIndicators, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class RegimeDetector:
    """Detects market regimes and volatility levels"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.current_regime = None
        self.regime_change_handlers = []
    
    async def detect_regime_changes(self, symbols: List[str], get_indicators_func: Callable, get_market_data_func: Callable) -> Optional[MarketRegime]:
        """Detect market regime changes"""
        try:
            # Get market breadth data
            breadth_data = await self._calculate_market_breadth(symbols, get_indicators_func, get_market_data_func)

            # Get volatility data
            volatility_data = await self._calculate_market_volatility(symbols, get_indicators_func)

            # Determine market regime
            new_regime = self._determine_market_regime(breadth_data, volatility_data)

            # Check for regime change
            if self.current_regime is None or self.current_regime.regime != new_regime.regime:
                logger.info(f"[STATUS] Market regime changed: {self.current_regime.regime if self.current_regime else 'None'} -> {new_regime.regime}")

                # Store new regime
                old_regime = self.current_regime
                self.current_regime = new_regime

                # Notify regime change handlers
                await self._notify_regime_change(old_regime, new_regime)

            return new_regime

        except Exception as e:
            logger.error(f"[ERROR] Error detecting regime changes: {e}")
            return self.current_regime
    
    async def _calculate_market_breadth(self, symbols: List[str], get_indicators_func: Callable, get_market_data_func: Callable) -> Dict[str, float]:
        """Calculate market breadth metrics"""
        try:
            breadth_data = {}

            # Calculate percentage of stocks above EMA20
            above_ema20 = 0
            total_symbols = 0

            for symbol in symbols:
                # Use 15min timeframe for market breadth analysis (more stable)
                indicators = get_indicators_func(symbol, '15min')
                if indicators and hasattr(indicators, 'ema_20') and indicators.ema_20:
                    total_symbols += 1
                    # Get current price from 15min data
                    candles = get_market_data_func(symbol, '15min')
                    if candles:
                        current_price = candles[-1].close
                        if current_price > indicators.ema_20:
                            above_ema20 += 1

            if total_symbols > 0:
                breadth_data['above_ema20_percent'] = (above_ema20 / total_symbols) * 100
            else:
                breadth_data['above_ema20_percent'] = 50  # Neutral

            # Calculate additional breadth metrics
            breadth_data.update(await self._calculate_additional_breadth_metrics(symbols, get_indicators_func, get_market_data_func))

            return breadth_data

        except Exception as e:
            logger.error(f"[ERROR] Error calculating market breadth: {e}")
            return {'above_ema20_percent': 50}
    
    async def _calculate_additional_breadth_metrics(self, symbols: List[str], get_indicators_func: Callable, get_market_data_func: Callable) -> Dict[str, float]:
        """Calculate additional market breadth metrics"""
        try:
            metrics = {}
            
            # Calculate percentage above/below various EMAs
            ema_periods = [5, 10, 20, 50]
            for period in ema_periods:
                above_count = 0
                total_count = 0
                
                for symbol in symbols:
                    indicators = get_indicators_func(symbol, '15min')
                    ema_value = getattr(indicators, f'ema_{period}', None) if indicators else None
                    
                    if ema_value:
                        total_count += 1
                        candles = get_market_data_func(symbol, '15min')
                        if candles and candles[-1].close > ema_value:
                            above_count += 1
                
                if total_count > 0:
                    metrics[f'above_ema{period}_percent'] = (above_count / total_count) * 100
            
            # Calculate RSI breadth (percentage in oversold/overbought)
            oversold_count = 0
            overbought_count = 0
            rsi_total = 0
            
            for symbol in symbols:
                indicators = get_indicators_func(symbol, '15min')
                if indicators and hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                    rsi_total += 1
                    if indicators.rsi_14 < 30:
                        oversold_count += 1
                    elif indicators.rsi_14 > 70:
                        overbought_count += 1
            
            if rsi_total > 0:
                metrics['oversold_percent'] = (oversold_count / rsi_total) * 100
                metrics['overbought_percent'] = (overbought_count / rsi_total) * 100
            
            return metrics
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating additional breadth metrics: {e}")
            return {}
    
    async def _calculate_market_volatility(self, symbols: List[str], get_indicators_func: Callable) -> Dict[str, float]:
        """Calculate market volatility metrics using PyArrow"""
        try:
            volatility_data = {}

            # Calculate average ATR across symbols
            atr_values = []
            for symbol in symbols:
                indicators = get_indicators_func(symbol, '15min')
                if indicators and hasattr(indicators, 'atr') and indicators.atr:
                    atr_values.append(indicators.atr)

            if atr_values:
                # Use PyArrow for fast vectorized operations
                atr_array = pa.array(atr_values)
                volatility_data['avg_atr'] = pc.mean(atr_array).as_py()

                # Calculate percentile using PyArrow
                sorted_atr = pc.sort(atr_array)
                target_value = volatility_data['avg_atr']
                count_below = pc.sum(pc.less(sorted_atr, target_value)).as_py()
                volatility_data['volatility_percentile'] = (count_below / len(atr_values)) * 100
                
                # Calculate additional volatility metrics
                volatility_data['atr_std'] = pc.stddev(atr_array).as_py()
                volatility_data['atr_max'] = pc.max(atr_array).as_py()
                volatility_data['atr_min'] = pc.min(atr_array).as_py()

            return volatility_data

        except Exception as e:
            logger.error(f"[ERROR] Error calculating market volatility: {e}")
            return {}
    
    def _determine_market_regime(self, breadth_data: Dict[str, float], volatility_data: Dict[str, float]) -> MarketRegime:
        """Determine current market regime"""
        try:
            env_config = self.config.environment_config.get('regime_detection', {})

            # Get metrics
            breadth_percent = breadth_data.get('above_ema20_percent', 50)
            volatility_percentile = volatility_data.get('volatility_percentile', 50)

            # Determine regime using multiple breadth indicators
            regime, confidence = self._analyze_regime_from_breadth(breadth_data, env_config)
            
            # Adjust confidence based on volatility
            confidence = self._adjust_confidence_for_volatility(confidence, volatility_data)

            # Determine volatility level
            volatility_level = self._determine_volatility_level(volatility_data, env_config)
            
            # Calculate trend strength
            trend_strength = self._calculate_trend_strength(breadth_data)
            
            # Calculate correlation level (placeholder for now)
            correlation_level = 0.5

            return MarketRegime(
                regime=regime,
                confidence=confidence,
                volatility_level=volatility_level,
                trend_strength=trend_strength,
                market_breadth=breadth_percent,
                correlation_level=correlation_level,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Error determining market regime: {e}")
            return MarketRegime(
                regime='sideways',
                confidence=0.5,
                volatility_level='medium',
                trend_strength=0.0,
                market_breadth=50.0,
                correlation_level=0.5,
                timestamp=datetime.now()
            )
    
    def _analyze_regime_from_breadth(self, breadth_data: Dict[str, float], env_config: Dict) -> tuple:
        """Analyze market regime from breadth data"""
        try:
            breadth_percent = breadth_data.get('above_ema20_percent', 50)
            breadth_threshold = env_config.get('breadth_threshold', 60)
            
            # Get additional breadth metrics for more robust analysis
            above_ema5 = breadth_data.get('above_ema5_percent', 50)
            above_ema50 = breadth_data.get('above_ema50_percent', 50)
            
            # Multi-timeframe analysis
            bull_signals = 0
            bear_signals = 0
            
            # EMA20 analysis
            if breadth_percent > breadth_threshold:
                bull_signals += 1
            elif breadth_percent < (100 - breadth_threshold):
                bear_signals += 1
            
            # EMA5 analysis (short-term momentum)
            if above_ema5 > 65:
                bull_signals += 0.5
            elif above_ema5 < 35:
                bear_signals += 0.5
            
            # EMA50 analysis (long-term trend)
            if above_ema50 > 55:
                bull_signals += 0.5
            elif above_ema50 < 45:
                bear_signals += 0.5
            
            # Determine regime
            if bull_signals > bear_signals and bull_signals >= 1:
                regime = 'bull'
                confidence = min(bull_signals / 2.0, 1.0)
            elif bear_signals > bull_signals and bear_signals >= 1:
                regime = 'bear'
                confidence = min(bear_signals / 2.0, 1.0)
            else:
                regime = 'sideways'
                confidence = 1.0 - abs(breadth_percent - 50) / 50
            
            return regime, confidence
            
        except Exception as e:
            logger.error(f"[ERROR] Error analyzing regime from breadth: {e}")
            return 'sideways', 0.5
    
    def _adjust_confidence_for_volatility(self, confidence: float, volatility_data: Dict[str, float]) -> float:
        """Adjust confidence based on volatility levels"""
        try:
            volatility_percentile = volatility_data.get('volatility_percentile', 50)
            
            # High volatility reduces confidence in regime detection
            if volatility_percentile > 80:
                confidence *= 0.8
            elif volatility_percentile > 60:
                confidence *= 0.9
            elif volatility_percentile < 20:
                confidence *= 1.1  # Low volatility increases confidence
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"[ERROR] Error adjusting confidence for volatility: {e}")
            return confidence
    
    def _determine_volatility_level(self, volatility_data: Dict[str, float], env_config: Dict) -> str:
        """Determine volatility level"""
        try:
            volatility_percentile = volatility_data.get('volatility_percentile', 50)
            vol_config = env_config.get('volatility', {})
            
            if volatility_percentile > vol_config.get('high_vol_threshold', 75):
                return 'high'
            elif volatility_percentile < vol_config.get('low_vol_threshold', 25):
                return 'low'
            else:
                return 'medium'
                
        except Exception as e:
            logger.error(f"[ERROR] Error determining volatility level: {e}")
            return 'medium'
    
    def _calculate_trend_strength(self, breadth_data: Dict[str, float]) -> float:
        """Calculate trend strength from breadth data"""
        try:
            breadth_percent = breadth_data.get('above_ema20_percent', 50)
            return abs(breadth_percent - 50) / 50
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating trend strength: {e}")
            return 0.0
    
    async def _notify_regime_change(self, old_regime: Optional[MarketRegime], new_regime: MarketRegime):
        """Notify regime change handlers"""
        for handler in self.regime_change_handlers:
            try:
                await handler(old_regime, new_regime)
            except Exception as e:
                logger.error(f"[ERROR] Error in regime change handler: {e}")
    
    def add_regime_change_handler(self, handler: Callable):
        """Add regime change handler"""
        self.regime_change_handlers.append(handler)
    
    def get_current_regime(self) -> Optional[MarketRegime]:
        """Get current market regime"""
        return self.current_regime
    
    def is_bull_market(self) -> bool:
        """Check if current regime is bull market"""
        return self.current_regime and self.current_regime.regime == 'bull'
    
    def is_bear_market(self) -> bool:
        """Check if current regime is bear market"""
        return self.current_regime and self.current_regime.regime == 'bear'
    
    def is_sideways_market(self) -> bool:
        """Check if current regime is sideways market"""
        return self.current_regime and self.current_regime.regime == 'sideways'
    
    def get_volatility_level(self) -> str:
        """Get current volatility level"""
        return self.current_regime.volatility_level if self.current_regime else 'medium'
