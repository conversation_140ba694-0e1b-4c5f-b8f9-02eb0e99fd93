#!/usr/bin/env python3
"""
Enhanced Market Monitoring Runner

Production-ready runner with comprehensive error handling, monitoring, and orchestration.
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
from datetime import datetime
from typing import Optional, Dict, Any, List
import warnings
warnings.filterwarnings('ignore')

from .data_structures import MarketMonitoringConfig, TradingSignal, MarketRegime
from .config_manager import ConfigManager
from .performance_monitor import PerformanceMonitor
from .resource_manager import ResourceManager
from .health_monitor import HealthMonitor
from .graceful_degradation import GracefulDegradationManager

logger = logging.getLogger(__name__)


class EnhancedMarketMonitoringRunner:
    """
    Enhanced production runner for Market Monitoring Agent
    
    Features:
    - Graceful startup and shutdown
    - Advanced signal handling
    - Intelligent error recovery
    - Comprehensive performance monitoring
    - Resource management
    - Health checks with alerting
    - Graceful degradation
    """
    
    def __init__(self, config_path: str = "config/market_monitoring_config.yaml"):
        """Initialize enhanced runner"""
        self.config_path = config_path
        self.config = None
        self.agent = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        self.restart_event = asyncio.Event()
        
        # Initialize managers
        self.config_manager = ConfigManager()
        self.performance_monitor = None
        self.resource_manager = None
        self.health_monitor = None
        self.degradation_manager = None
        
        # Runtime state
        self.startup_time = None
        self.error_count = 0
        self.restart_count = 0
        self.last_health_check = None
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        logger.info("[INIT] Enhanced Market Monitoring Runner initialized")
    
    def _setup_signal_handlers(self):
        """Setup comprehensive signal handling"""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            logger.info(f"[SIGNAL] Received {signal_name} ({signum}), initiating graceful shutdown...")
            self.shutdown_event.set()
        
        def restart_handler(signum, frame):
            logger.info(f"[SIGNAL] Received SIGUSR1, initiating graceful restart...")
            self.restart_event.set()
        
        # Standard shutdown signals
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Custom restart signal (if available on platform)
        try:
            signal.signal(signal.SIGUSR1, restart_handler)
        except AttributeError:
            logger.debug("[SIGNAL] SIGUSR1 not available on this platform")
    
    async def initialize(self):
        """Initialize all components"""
        try:
            logger.info("[INIT] Initializing enhanced runner components...")
            
            # Load configuration
            self.config = self.config_manager.load_config(self.config_path)
            
            # Initialize managers
            self.performance_monitor = PerformanceMonitor(self.config)
            self.resource_manager = ResourceManager(self.config)
            self.health_monitor = HealthMonitor(self.config)
            self.degradation_manager = GracefulDegradationManager(self.config)
            
            # Setup logging
            self.config_manager.setup_logging(self.config)
            
            # Validate system requirements
            await self._validate_system_requirements()
            
            # Initialize the main agent
            await self._initialize_agent()
            
            logger.info("[SUCCESS] Enhanced runner initialization completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize enhanced runner: {e}")
            raise
    
    async def _validate_system_requirements(self):
        """Validate system requirements and dependencies"""
        logger.info("[VALIDATION] Validating system requirements...")
        
        # Check dependencies
        dependencies = self.config_manager.check_dependencies()
        missing_critical = []
        missing_optional = []
        
        critical_deps = ['polars', 'numpy', 'asyncio']
        optional_deps = ['smartapi', 'telegram', 'ai_training_agent']
        
        for dep, available in dependencies.items():
            if not available:
                if dep in critical_deps:
                    missing_critical.append(dep)
                else:
                    missing_optional.append(dep)
        
        if missing_critical:
            raise RuntimeError(f"Critical dependencies missing: {missing_critical}")
        
        if missing_optional:
            logger.warning(f"[WARN] Optional dependencies missing: {missing_optional}")
            # Configure graceful degradation
            await self.degradation_manager.configure_fallbacks(missing_optional)
        
        # Check system resources
        resource_status = await self.resource_manager.check_system_resources()
        if not resource_status['adequate']:
            logger.warning(f"[WARN] System resources may be insufficient: {resource_status}")
        
        logger.info("[SUCCESS] System requirements validation completed")
    
    async def _initialize_agent(self):
        """Initialize the main market monitoring agent"""
        try:
            # Import here to avoid circular imports
            from ..market_monitoring_agent import MarketMonitoringAgent
            
            self.agent = MarketMonitoringAgent(self.config_path)
            await self.agent.initialize()
            
            # Setup custom handlers
            await self._setup_enhanced_handlers()
            
            logger.info("[SUCCESS] Market monitoring agent initialized")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            raise
    
    async def _setup_enhanced_handlers(self):
        """Setup enhanced event handlers with error handling"""
        
        async def enhanced_signal_handler(signal: TradingSignal):
            """Enhanced signal handler with validation and logging"""
            try:
                # Validate signal
                if not self._validate_signal(signal):
                    logger.warning(f"[SIGNAL] Invalid signal rejected: {signal.symbol}")
                    return
                
                # Log signal with enhanced context
                logger.info(f"[SIGNAL] {signal.symbol} | {signal.strategy} | {signal.action} | "
                           f"Price: ₹{signal.price:.2f} | Target: ₹{signal.target:.2f} | "
                           f"SL: ₹{signal.stop_loss:.2f} | Confidence: {signal.confidence:.1%}")
                
                # Update performance metrics
                self.performance_monitor.record_signal_generated()
                
                # Process signal through degradation manager if needed
                await self.degradation_manager.process_signal(signal)
                
            except Exception as e:
                logger.error(f"[ERROR] Error in signal handler: {e}")
                self.error_count += 1
        
        async def enhanced_regime_handler(old_regime: Optional[MarketRegime], new_regime: MarketRegime):
            """Enhanced regime change handler"""
            try:
                old_name = old_regime.regime if old_regime else "None"
                
                logger.info(f"[REGIME] {old_name} → {new_regime.regime} | "
                           f"Confidence: {new_regime.confidence:.1%} | "
                           f"Volatility: {new_regime.volatility_level} | "
                           f"Breadth: {new_regime.market_breadth:.1f}%")
                
                # Update performance metrics
                self.performance_monitor.update_regime_change(old_regime, new_regime)
                
                # Adjust strategy parameters based on regime
                await self._adjust_strategy_for_regime(new_regime)
                
            except Exception as e:
                logger.error(f"[ERROR] Error in regime handler: {e}")
                self.error_count += 1
        
        # Register handlers
        if hasattr(self.agent, 'add_signal_handler'):
            self.agent.add_signal_handler(enhanced_signal_handler)
        if hasattr(self.agent, 'add_regime_change_handler'):
            self.agent.add_regime_change_handler(enhanced_regime_handler)
        
        logger.info("[SUCCESS] Enhanced handlers registered")
    
    def _validate_signal(self, signal: TradingSignal) -> bool:
        """Validate trading signal"""
        try:
            # Basic validation
            if not signal.symbol or not signal.action:
                return False
            
            if signal.price <= 0 or signal.confidence <= 0:
                return False
            
            # Risk validation
            if signal.action == 'BUY':
                if signal.stop_loss >= signal.price:
                    return False
                if signal.target <= signal.price:
                    return False
            elif signal.action == 'SELL':
                if signal.stop_loss <= signal.price:
                    return False
                if signal.target >= signal.price:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating signal: {e}")
            return False
    
    async def _adjust_strategy_for_regime(self, regime: MarketRegime):
        """Adjust strategy parameters based on market regime"""
        try:
            adjustments = {}
            
            if regime.regime == 'bull' and regime.confidence > 0.7:
                adjustments = {
                    'risk_multiplier': 1.2,
                    'confidence_threshold': 0.6,
                    'max_positions': 8
                }
            elif regime.regime == 'bear' and regime.confidence > 0.7:
                adjustments = {
                    'risk_multiplier': 0.8,
                    'confidence_threshold': 0.75,
                    'max_positions': 4
                }
            elif regime.volatility_level == 'high':
                adjustments = {
                    'risk_multiplier': 0.7,
                    'confidence_threshold': 0.8,
                    'max_positions': 3
                }
            
            if adjustments and hasattr(self.agent, 'update_strategy_parameters'):
                await self.agent.update_strategy_parameters(adjustments)
                logger.info(f"[STRATEGY] Adjusted parameters for {regime.regime} market: {adjustments}")
                
        except Exception as e:
            logger.error(f"[ERROR] Error adjusting strategy: {e}")
    
    async def start(self):
        """Start the enhanced runner"""
        try:
            logger.info("[START] Starting enhanced market monitoring runner...")
            
            self.startup_time = datetime.now()
            self.is_running = True
            
            # Start all monitoring services
            await self.performance_monitor.start_monitoring()
            await self.resource_manager.start_monitoring()
            await self.health_monitor.start_monitoring()
            
            # Start the main agent
            await self.agent.start()
            
            logger.info("[SUCCESS] Enhanced runner started successfully")
            
            # Run main loop
            await self._run_enhanced_main_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start enhanced runner: {e}")
            raise
    
    async def stop(self):
        """Stop the enhanced runner"""
        try:
            logger.info("[STOP] Stopping enhanced market monitoring runner...")
            
            self.is_running = False
            
            # Stop the agent first
            if self.agent:
                await self.agent.stop()
            
            # Stop all monitoring services
            if self.performance_monitor:
                await self.performance_monitor.stop_monitoring()
            if self.resource_manager:
                await self.resource_manager.stop_monitoring()
            if self.health_monitor:
                await self.health_monitor.stop_monitoring()
            
            # Log final statistics
            if self.startup_time:
                uptime = datetime.now() - self.startup_time
                logger.info(f"[STATS] Uptime: {uptime}, Errors: {self.error_count}, Restarts: {self.restart_count}")
            
            logger.info("[SUCCESS] Enhanced runner stopped successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping enhanced runner: {e}")
    
    async def _run_enhanced_main_loop(self):
        """Run enhanced main loop with restart capability"""
        while self.is_running:
            try:
                # Create monitoring tasks
                tasks = [
                    asyncio.create_task(self._enhanced_health_loop()),
                    asyncio.create_task(self._resource_monitoring_loop()),
                    asyncio.create_task(self.shutdown_event.wait()),
                    asyncio.create_task(self.restart_event.wait())
                ]
                
                # Wait for first completed task
                done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
                
                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # Handle completed task
                for task in done:
                    if task == tasks[2]:  # shutdown_event
                        logger.info("[STOP] Shutdown signal received")
                        return
                    elif task == tasks[3]:  # restart_event
                        logger.info("[RESTART] Restart signal received")
                        await self._perform_graceful_restart()
                        self.restart_event.clear()
                    else:
                        # Health or resource monitoring completed (error)
                        logger.warning("[WARN] Monitoring task completed unexpectedly")
                        await asyncio.sleep(5)  # Brief pause before continuing
                
            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced main loop: {e}")
                self.error_count += 1
                await asyncio.sleep(10)  # Longer pause on error
    
    async def _enhanced_health_loop(self):
        """Enhanced health monitoring loop"""
        while self.is_running:
            try:
                # Perform comprehensive health check
                health_status = await self.health_monitor.perform_health_check(self.agent)
                self.last_health_check = datetime.now()
                
                # Log health status
                if health_status['overall'] == 'healthy':
                    logger.debug(f"[HEALTH] System healthy - {health_status['summary']}")
                else:
                    logger.warning(f"[HEALTH] System issues detected - {health_status['summary']}")
                
                # Handle critical issues
                if health_status['overall'] == 'critical':
                    logger.error("[HEALTH] Critical system issues detected, initiating recovery")
                    await self._handle_critical_health_issues(health_status)
                
                # Wait for next check
                await asyncio.sleep(self.config.performance_config.get('health_check_interval', 30))
                
            except Exception as e:
                logger.error(f"[ERROR] Error in health monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _resource_monitoring_loop(self):
        """Resource monitoring loop"""
        while self.is_running:
            try:
                # Check resource usage
                resource_status = await self.resource_manager.check_resource_usage()
                
                # Handle resource issues
                if resource_status['memory_percent'] > 90:
                    logger.warning("[RESOURCE] High memory usage detected, triggering cleanup")
                    await self._trigger_memory_cleanup()
                
                if resource_status['cpu_percent'] > 95:
                    logger.warning("[RESOURCE] High CPU usage detected, reducing load")
                    await self._reduce_processing_load()
                
                # Wait for next check
                await asyncio.sleep(self.config.performance_config.get('resource_check_interval', 60))
                
            except Exception as e:
                logger.error(f"[ERROR] Error in resource monitoring: {e}")
                await asyncio.sleep(120)
    
    async def _perform_graceful_restart(self):
        """Perform graceful restart"""
        try:
            logger.info("[RESTART] Performing graceful restart...")
            
            # Stop current agent
            if self.agent:
                await self.agent.stop()
            
            # Brief pause
            await asyncio.sleep(2)
            
            # Reinitialize agent
            await self._initialize_agent()
            await self.agent.start()
            
            self.restart_count += 1
            logger.info(f"[SUCCESS] Graceful restart completed (restart #{self.restart_count})")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during graceful restart: {e}")
            self.error_count += 1
    
    async def _handle_critical_health_issues(self, health_status: Dict[str, Any]):
        """Handle critical health issues"""
        try:
            issues = health_status.get('issues', [])
            
            for issue in issues:
                if issue['type'] == 'websocket_disconnected':
                    logger.info("[RECOVERY] Attempting WebSocket reconnection...")
                    if hasattr(self.agent, 'reconnect_websocket'):
                        await self.agent.reconnect_websocket()
                
                elif issue['type'] == 'data_stale':
                    logger.info("[RECOVERY] Refreshing stale data...")
                    if hasattr(self.agent, 'refresh_data'):
                        await self.agent.refresh_data()
                
                elif issue['type'] == 'ai_model_error':
                    logger.info("[RECOVERY] Switching to fallback strategy...")
                    await self.degradation_manager.enable_fallback_mode()
            
        except Exception as e:
            logger.error(f"[ERROR] Error handling critical health issues: {e}")
    
    async def _trigger_memory_cleanup(self):
        """Trigger memory cleanup"""
        try:
            if hasattr(self.agent, 'cleanup_memory'):
                await self.agent.cleanup_memory()
            
            # Force garbage collection
            import gc
            gc.collect()
            
            logger.info("[CLEANUP] Memory cleanup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during memory cleanup: {e}")
    
    async def _reduce_processing_load(self):
        """Reduce processing load"""
        try:
            if hasattr(self.agent, 'reduce_processing_load'):
                await self.agent.reduce_processing_load()
            
            logger.info("[LOAD] Processing load reduced")
            
        except Exception as e:
            logger.error(f"[ERROR] Error reducing processing load: {e}")
    
    async def run(self):
        """Main run method"""
        try:
            await self.initialize()
            await self.start()
        except KeyboardInterrupt:
            logger.info("[STOP] Received keyboard interrupt")
        except Exception as e:
            logger.error(f"[ERROR] Runtime error: {e}")
            raise
        finally:
            await self.stop()
