#!/usr/bin/env python3
"""
Configuration Validator for Market Monitoring Agent

Provides basic validation for configuration files and settings.
"""

import logging
import os
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class ConfigValidator:
    """Basic configuration validator"""
    
    def __init__(self):
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """Validate configuration dictionary"""
        try:
            self.validation_errors.clear()
            self.validation_warnings.clear()
            
            # Basic validation
            if not isinstance(config, dict):
                self.validation_errors.append("Configuration must be a dictionary")
                return False, self.validation_errors, self.validation_warnings
            
            # Check for required sections
            required_sections = ['smartapi_config', 'market_data_config']
            for section in required_sections:
                if section not in config:
                    self.validation_errors.append(f"Required section missing: {section}")
            
            # Validate SmartAPI config
            if 'smartapi_config' in config:
                self._validate_smartapi_config(config['smartapi_config'])
            
            # Validate market data config
            if 'market_data_config' in config:
                self._validate_market_data_config(config['market_data_config'])
            
            is_valid = len(self.validation_errors) == 0
            
            if is_valid:
                logger.info("[VALIDATION] Configuration validation passed")
            else:
                logger.error(f"[VALIDATION] Configuration validation failed: {len(self.validation_errors)} errors")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating configuration: {e}")
            return False, [f"Validation error: {e}"], []
    
    def _validate_smartapi_config(self, config: Dict[str, Any]):
        """Validate SmartAPI configuration"""
        try:
            required_fields = ['api_key', 'client_id', 'password', 'totp_token']
            
            for field in required_fields:
                if field not in config:
                    self.validation_errors.append(f"smartapi_config: Required field missing: {field}")
                elif not config[field] or config[field] == '':
                    self.validation_errors.append(f"smartapi_config: Required field is empty: {field}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating SmartAPI config: {e}")
    
    def _validate_market_data_config(self, config: Dict[str, Any]):
        """Validate market data configuration"""
        try:
            # Validate symbols list
            if 'symbols' in config:
                symbols = config['symbols']
                if not symbols or not isinstance(symbols, list):
                    self.validation_errors.append("market_data_config.symbols: Must be a non-empty list")
                else:
                    for symbol in symbols:
                        if not isinstance(symbol, str) or not symbol.strip():
                            self.validation_errors.append(f"market_data_config.symbols: Invalid symbol: {symbol}")
            else:
                self.validation_errors.append("market_data_config: symbols field is required")
            
            # Validate timeframes
            if 'timeframes' in config:
                timeframes = config['timeframes']
                valid_timeframes = ['1min', '5min', '15min', '30min', '1hr', '1day']
                if isinstance(timeframes, list):
                    for timeframe in timeframes:
                        if timeframe not in valid_timeframes:
                            self.validation_errors.append(f"market_data_config.timeframes: Invalid timeframe: {timeframe}")
                else:
                    self.validation_errors.append("market_data_config.timeframes: Must be a list")
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating market data config: {e}")
    
    def validate_environment_variables(self) -> Tuple[bool, List[str], List[str]]:
        """Validate required environment variables"""
        try:
            errors = []
            warnings = []
            
            # Check for sensitive data in environment
            sensitive_vars = [
                'SMARTAPI_API_KEY',
                'SMARTAPI_CLIENT_ID',
                'SMARTAPI_PASSWORD',
                'SMARTAPI_TOTP_TOKEN',
                'TELEGRAM_BOT_TOKEN',
                'TELEGRAM_CHAT_ID'
            ]
            
            for var in sensitive_vars:
                value = os.getenv(var)
                if value:
                    if len(value.strip()) == 0:
                        errors.append(f"Environment variable {var} is empty")
                else:
                    warnings.append(f"Environment variable {var} not set")
            
            return len(errors) == 0, errors, warnings
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating environment variables: {e}")
            return False, [f"Environment validation error: {e}"], []
    
    def generate_validation_report(self, config_path: str) -> str:
        """Generate basic validation report"""
        try:
            import yaml
            from datetime import datetime
            
            # Load and validate config
            try:
                with open(config_path, 'r') as file:
                    config = yaml.safe_load(file)
                is_valid, errors, warnings = self.validate_config(config)
            except Exception as e:
                return f"Error loading configuration: {e}"
            
            # Validate environment
            env_valid, env_errors, env_warnings = self.validate_environment_variables()
            
            report = []
            report.append("=" * 60)
            report.append("CONFIGURATION VALIDATION REPORT")
            report.append("=" * 60)
            report.append(f"Configuration File: {config_path}")
            report.append(f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # Overall status
            overall_valid = is_valid and env_valid
            status = "PASSED" if overall_valid else "FAILED"
            report.append(f"Overall Status: {status}")
            report.append("")
            
            # Configuration errors
            if errors:
                report.append("CONFIGURATION ERRORS:")
                for error in errors:
                    report.append(f"  ❌ {error}")
                report.append("")
            
            # Configuration warnings
            if warnings:
                report.append("CONFIGURATION WARNINGS:")
                for warning in warnings:
                    report.append(f"  ⚠️  {warning}")
                report.append("")
            
            # Environment errors
            if env_errors:
                report.append("ENVIRONMENT ERRORS:")
                for error in env_errors:
                    report.append(f"  ❌ {error}")
                report.append("")
            
            # Environment warnings
            if env_warnings:
                report.append("ENVIRONMENT WARNINGS:")
                for warning in env_warnings:
                    report.append(f"  ⚠️  {warning}")
                report.append("")
            
            # Summary
            report.append("SUMMARY:")
            report.append(f"  Configuration Errors: {len(errors)}")
            report.append(f"  Configuration Warnings: {len(warnings)}")
            report.append(f"  Environment Errors: {len(env_errors)}")
            report.append(f"  Environment Warnings: {len(env_warnings)}")
            report.append("")
            
            if overall_valid:
                report.append("✅ Configuration is valid and ready for use!")
            else:
                report.append("❌ Configuration has errors that must be fixed before use.")
            
            report.append("=" * 60)
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating validation report: {e}")
            return f"Error generating validation report: {e}"
