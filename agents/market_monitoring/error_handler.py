# agents/market_monitoring/error_handler.py

import logging
import asyncio
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ErrorHandler:
    """
    Centralized error handling and alerting mechanism for the Market Monitoring Agent.
    Supports logging, Telegram, and Email alerts for critical errors.
    """

    def __init__(self, config: Dict[str, Any], telegram_bot: Optional[Any] = None):
        self.config = config.get('error_handling', {})
        self.notifications_config = config.get('notifications', {})
        self.telegram_bot = telegram_bot
        self.error_count = 0
        self.last_error_time: Optional[datetime] = None
        self.critical_error_threshold = self.config.get('alerting', {}).get('critical_error_threshold', 5)
        self.alert_cooldown_minutes = self.config.get('alerting', {}).get('cooldown_minutes', 30)

    async def handle_exception(self, e: Exception, context: str = "General Error", level: str = "ERROR"):
        """
        Handles an exception by logging it and potentially triggering alerts.
        """
        log_message = f"[{context}] An exception occurred: {e}"
        if level == "ERROR":
            logger.error(log_message, exc_info=True)
            self._increment_error_count()
            await self._check_and_trigger_critical_alert(log_message)
        elif level == "WARNING":
            logger.warning(log_message, exc_info=True)
        elif level == "CRITICAL":
            logger.critical(log_message, exc_info=True)
            self._increment_error_count()
            await self._trigger_critical_alert(log_message)
        else:
            logger.info(log_message, exc_info=True)

    def _increment_error_count(self):
        self.error_count += 1
        self.last_error_time = datetime.now()

    async def _check_and_trigger_critical_alert(self, message: str):
        """
        Checks if the critical error threshold has been reached and triggers an alert.
        Applies a cooldown period to prevent alert spamming.
        """
        if self.error_count >= self.critical_error_threshold:
            if self.last_error_time and (datetime.now() - self.last_error_time) < timedelta(minutes=self.alert_cooldown_minutes):
                logger.warning(f"[ALERT_COOLDOWN] Critical alert suppressed due to cooldown. Errors: {self.error_count}")
                return

            await self._trigger_critical_alert(f"Multiple errors detected ({self.error_count} in last {self.alert_cooldown_minutes} mins). Last error: {message}")
            self.error_count = 0 # Reset count after triggering alert
            self.last_error_time = datetime.now() # Reset cooldown timer

    async def _trigger_critical_alert(self, message: str):
        """
        Triggers critical alerts via configured channels.
        """
        logger.critical(f"[CRITICAL_ALERT] {message}")

        if self.config.get('alerting', {}).get('enable_telegram_alerts', False):
            await self._send_telegram_alert(message)
        if self.config.get('alerting', {}).get('enable_email_alerts', False):
            await self._send_email_alert(message)

    async def _send_telegram_alert(self, message: str):
        """Sends a critical alert via Telegram."""
        if self.telegram_bot:
            telegram_config = self.notifications_config.get('telegram', {})
            chat_id = telegram_config.get('chat_id')
            if chat_id and chat_id != "YOUR_CHAT_ID":
                try:
                    await self.telegram_bot.send_message(chat_id=chat_id, text=f"🚨 CRITICAL ALERT: {message}")
                    logger.info("[ALERT] Telegram critical alert sent.")
                except Exception as e:
                    logger.error(f"[ALERT_ERROR] Failed to send Telegram alert: {e}")
            else:
                logger.warning("[ALERT_CONFIG] Telegram chat_id not configured for alerts.")
        else:
            logger.warning("[ALERT_CONFIG] Telegram bot not initialized for alerts.")

    async def _send_email_alert(self, message: str):
        """Sends a critical alert via Email."""
        email_config = self.notifications_config.get('email', {})
        if not email_config.get('enable', False):
            return

        sender_email = email_config.get('sender_email')
        sender_password = email_config.get('sender_password')
        receiver_email = email_config.get('receiver_email')
        smtp_server = email_config.get('smtp_server')
        smtp_port = email_config.get('smtp_port')

        if not all([sender_email, sender_password, receiver_email, smtp_server, smtp_port]):
            logger.warning("[ALERT_CONFIG] Email configuration incomplete for alerts.")
            return

        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = receiver_email
        msg['Subject'] = "CRITICAL ALERT: Market Monitoring Agent"

        body = f"Dear User,\n\nThis is a critical alert from your Market Monitoring Agent.\n\nDetails:\n{message}\n\nPlease investigate immediately.\n\nSincerely,\nMarket Monitoring Agent"
        msg.attach(MIMEText(body, 'plain'))

        try:
            # Run blocking SMTP operation in a separate thread
            await asyncio.to_thread(self._send_smtp_email, smtp_server, smtp_port, sender_email, sender_password, receiver_email, msg)
            logger.info("[ALERT] Email critical alert sent.")
        except Exception as e:
            logger.error(f"[ALERT_ERROR] Failed to send email alert: {e}")

    def _send_smtp_email(self, smtp_server, smtp_port, sender_email, sender_password, receiver_email, msg):
        """Blocking function to send email via SMTP."""
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()  # Secure the connection
            server.login(sender_email, sender_password)
            server.send_message(msg)
