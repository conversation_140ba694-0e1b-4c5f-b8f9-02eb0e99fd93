#!/usr/bin/env python3
"""
WebSocket Manager for Market Monitoring Agent

Handles WebSocket connections, subscriptions, and real-time data processing.
"""

import asyncio
import logging
import json
import pyotp
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

from .data_structures import MarketTick, WebSocketConfig, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manages WebSocket connections and real-time data streaming"""
    
    def __init__(self, config: MarketMonitoringConfig, smartapi_client=None):
        self.config = config
        self.smartapi_client = smartapi_client
        self.websocket_client = None
        self.enhanced_websocket = None
        self.websocket_manager = None
        
        # Connection state
        self.is_connected = False
        self.subscribed_symbols = set()
        self.auth_token = None
        self.feed_token = None
        
        # Thread-safe operation flags
        self._subscription_pending = False
        self._reconnection_pending = False
        self._pending_messages = []
        self._pending_symbols = set()
        
        # Token mappings
        self.nse_500_token_map = {}
        self.token_to_symbol_map = {}
        
        # Callbacks
        self.tick_callbacks = []
        self.candle_callbacks = []
        self.connection_callbacks = []
    
    async def setup_websocket_connections(self):
        """Setup WebSocket connections with SmartAPI"""
        if not self._check_smartapi_availability():
            logger.warning("[WARN] SmartAPI not available, using mock data")
            return

        try:
            smartapi_config = self.config.smartapi_config
            
            # Initialize SmartAPI client if not provided
            if not self.smartapi_client:
                await self._initialize_smartapi_client(smartapi_config)
            
            # Generate session and get tokens
            await self._authenticate_smartapi(smartapi_config)
            
            # Setup Enhanced WebSocket Service (preferred)
            if self._check_enhanced_websocket_service():
                await self._setup_enhanced_websocket_service(smartapi_config)
            
            # Setup Enhanced WebSocket Manager (fallback)
            elif self._check_robust_websocket_manager():
                await self._setup_robust_websocket_manager(smartapi_config)
            
            # Setup basic WebSocket (last resort)
            elif self._check_basic_websocket():
                await self._setup_basic_websocket(smartapi_config)
            
            logger.info("[SUCCESS] WebSocket setup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket setup failed: {e}")
            raise
    
    def _check_smartapi_availability(self) -> bool:
        """Check if SmartAPI is available"""
        try:
            from SmartApi import SmartConnect
            return True
        except ImportError:
            return False
    
    def _check_enhanced_websocket_service(self) -> bool:
        """Check if Enhanced WebSocket Service is available"""
        try:
            from utils.enhanced_websocket_service import EnhancedWebSocketService
            return True
        except ImportError:
            return False
    
    def _check_robust_websocket_manager(self) -> bool:
        """Check if Robust WebSocket Manager is available"""
        try:
            from utils.robust_websocket_manager import RobustWebSocketManager
            return True
        except ImportError:
            return False
    
    def _check_basic_websocket(self) -> bool:
        """Check if basic WebSocket is available"""
        try:
            from SmartApi.smartWebSocketV2 import SmartWebSocketV2
            return True
        except ImportError:
            return False
    
    async def _initialize_smartapi_client(self, smartapi_config: Dict[str, Any]):
        """Initialize SmartAPI client"""
        from SmartApi import SmartConnect
        self.smartapi_client = SmartConnect(api_key=smartapi_config['api_key'])
    
    async def _authenticate_smartapi(self, smartapi_config: Dict[str, Any]):
        """Authenticate with SmartAPI and get tokens"""
        totp = pyotp.TOTP(smartapi_config['totp_token']).now()
        data = self.smartapi_client.generateSession(
            smartapi_config['username'],
            smartapi_config['password'],
            totp
        )

        if not data['status']:
            raise Exception(f"SmartAPI login failed: {data}")

        self.auth_token = data['data']['jwtToken']
        refresh_token = data['data']['refreshToken']
        self.feed_token = self.smartapi_client.getfeedToken()
        
        logger.info("[SUCCESS] SmartAPI authentication completed")
    
    async def _setup_enhanced_websocket_service(self, smartapi_config: Dict[str, Any]):
        """Setup Enhanced WebSocket Service"""
        from utils.enhanced_websocket_service import EnhancedWebSocketService
        
        self.enhanced_websocket = EnhancedWebSocketService(
            auth_token=self.auth_token,
            api_key=smartapi_config['api_key'],
            username=smartapi_config['username'],
            feed_token=self.feed_token,
            selected_symbols=None
        )

        # Setup callbacks
        self.enhanced_websocket.add_tick_callback(self._on_enhanced_tick)
        self.enhanced_websocket.add_candle_callback(self._on_enhanced_candle)

        logger.info("[SUCCESS] Enhanced WebSocket Service initialized")
    
    async def _setup_robust_websocket_manager(self, smartapi_config: Dict[str, Any]):
        """Setup Robust WebSocket Manager"""
        from utils.robust_websocket_manager import RobustWebSocketManager
        
        ws_config = {
            'max_retry_attempts': smartapi_config.get('websocket', {}).get('reconnect_attempts', 5),
            'retry_delay_base': smartapi_config.get('websocket', {}).get('reconnect_delay', 5),
            'connection_timeout': smartapi_config.get('websocket', {}).get('connection_timeout', 15),
            'heartbeat_interval': smartapi_config.get('websocket', {}).get('heartbeat_interval', 30)
        }

        self.websocket_manager = RobustWebSocketManager(ws_config)

        # Set authentication tokens
        self.websocket_manager.auth_token = self.auth_token
        self.websocket_manager.feed_token = self.feed_token
        self.websocket_manager.api_key = smartapi_config['api_key']
        self.websocket_manager.username = smartapi_config['username']

        # Setup callbacks
        self.websocket_manager.set_callbacks(
            on_connected=self._on_websocket_connected,
            on_data=self._on_websocket_data_enhanced,
            on_error=self._on_websocket_error_enhanced,
            on_disconnected=self._on_websocket_disconnected
        )

        logger.info("[SUCCESS] Enhanced WebSocket Manager initialized")
    
    async def _setup_basic_websocket(self, smartapi_config: Dict[str, Any]):
        """Setup basic WebSocket"""
        from SmartApi.smartWebSocketV2 import SmartWebSocketV2
        
        self.websocket_client = SmartWebSocketV2(
            self.auth_token,
            smartapi_config['api_key'],
            smartapi_config['username'],
            self.feed_token
        )

        # Setup WebSocket callbacks
        self.websocket_client.on_open = self._on_websocket_open
        self.websocket_client.on_data = self._on_websocket_data
        self.websocket_client.on_error = self._on_websocket_error
        self.websocket_client.on_close = self._on_websocket_close

        logger.info("[SUCCESS] Basic WebSocket initialized")
    
    async def start_websocket_connection(self):
        """Start WebSocket connection"""
        try:
            if self.enhanced_websocket:
                logger.info("[CONNECT] Starting Enhanced WebSocket Service...")
                success = await self.enhanced_websocket.initialize()
                if success:
                    await self.enhanced_websocket.start()
                    logger.info("[SUCCESS] Enhanced WebSocket Service started")
                else:
                    logger.error("[ERROR] Enhanced WebSocket Service initialization failed")
            
            elif self.websocket_manager:
                logger.info("[CONNECT] Starting Enhanced WebSocket connection...")
                connection_success = await self.websocket_manager.connect()
                if connection_success:
                    logger.info("[SUCCESS] Enhanced WebSocket connected")
                else:
                    logger.error("[ERROR] Enhanced WebSocket connection failed")
            
            elif self.websocket_client:
                logger.info("[CONNECT] Starting basic WebSocket connection...")
                self._websocket_task = asyncio.create_task(
                    asyncio.to_thread(self.websocket_client.connect)
                )
                await asyncio.sleep(0.5)  # Give it a moment to connect
            
            # Process any pending operations
            self._process_pending_operations()
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting WebSocket connection: {e}")
            raise
    
    async def stop_websocket_connection(self):
        """Stop WebSocket connection"""
        try:
            if self.enhanced_websocket:
                logger.info("[STOP] Stopping Enhanced WebSocket Service...")
                await self.enhanced_websocket.stop()
                logger.info("[STOP] Enhanced WebSocket Service stopped")
            
            elif self.websocket_manager:
                logger.info("[STOP] Closing Enhanced WebSocket connection...")
                await self.websocket_manager.disconnect()
                logger.info("[STOP] Enhanced WebSocket connection closed")
            
            elif self.websocket_client:
                logger.info("[STOP] Closing basic WebSocket connection...")
                await asyncio.wait_for(
                    asyncio.to_thread(self.websocket_client.close_connection),
                    timeout=1.0
                )
                logger.info("[STOP] Basic WebSocket connection closed")
                
        except Exception as e:
            logger.warning(f"[STOP] WebSocket close error (ignoring): {e}")
    
    async def subscribe_to_symbols(self, symbols: List[str]) -> bool:
        """Subscribe to market data for symbols"""
        try:
            if not symbols:
                logger.warning("[WARN] No symbols provided for subscription")
                return False

            # Check connection status
            if not self._is_websocket_connected():
                logger.info("[INFO] WebSocket not connected, storing symbols for later subscription")
                self._pending_symbols.update(symbols)
                return True

            # Subscribe to the provided symbols
            return await self._subscribe_to_symbol_list(symbols)

        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
            return False
    
    def _is_websocket_connected(self) -> bool:
        """Check if WebSocket is connected"""
        if self.websocket_manager:
            status = self.websocket_manager.get_connection_status()
            return status['is_connected']
        elif self.websocket_client:
            return self.is_connected
        return False
    
    async def _subscribe_to_symbol_list(self, symbols: List[str]) -> bool:
        """Subscribe to a list of symbols"""
        try:
            # Prepare token list for subscription
            token_list = []
            for symbol in symbols:
                token = self._get_token_for_symbol(symbol)
                if token:
                    token_list.append(token)

            if token_list:
                subscription_config = self.config.smartapi_config.get('subscription', {})
                mode = subscription_config.get('mode', 1)
                exchange_type = subscription_config.get('exchange_type', 1)

                subscription_data = [{
                    "exchangeType": exchange_type,
                    "tokens": token_list
                }]

                if self.websocket_manager:
                    success = await self.websocket_manager.subscribe_symbols(symbols)
                    if success:
                        self.subscribed_symbols.update(symbols)
                        logger.info(f"[SIGNAL] Enhanced subscription to {len(symbols)} symbols: {', '.join(symbols)}")
                        return True
                    else:
                        logger.warning(f"[WARN] Enhanced subscription failed for symbols: {', '.join(symbols)}")
                        return False
                        
                elif self.websocket_client:
                    self.websocket_client.subscribe("correlation_id", mode, subscription_data)
                    self.subscribed_symbols.update(symbols)

                logger.info(f"[SIGNAL] Subscribed to {len(token_list)} symbols: {', '.join(symbols)}")
                return True
            else:
                logger.warning(f"[WARN] No valid tokens found for symbols: {', '.join(symbols)}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Symbol subscription failed: {e}")
            return False

    def _get_token_for_symbol(self, symbol: str) -> Optional[str]:
        """Get token for symbol"""
        try:
            # First try NSE 500 token map
            if symbol in self.nse_500_token_map:
                return self.nse_500_token_map[symbol]

            # Load NSE 500 token mapping if not loaded
            if not self.nse_500_token_map:
                self._load_nse_500_token_mapping()
                if symbol in self.nse_500_token_map:
                    return self.nse_500_token_map[symbol]

            # Try stock universe
            try:
                from utils.stock_universe import StockUniverse
                stock_universe = StockUniverse()
                if stock_universe.load_stock_universe():
                    stock_info = stock_universe.get_stock_info(symbol)
                    if stock_info and hasattr(stock_info, 'token'):
                        return stock_info.token
            except ImportError:
                pass

            logger.warning(f"[WARN] Could not find token for symbol: {symbol}")
            return None

        except Exception as e:
            logger.error(f"[ERROR] Error getting token for symbol {symbol}: {e}")
            return None

    def _load_nse_500_token_mapping(self):
        """Load token mapping from NSE 500 universe file"""
        try:
            import json
            from pathlib import Path

            nse_500_file = Path("config/nse_500_universe.json")
            if nse_500_file.exists():
                with open(nse_500_file, 'r') as f:
                    data = json.load(f)

                self.nse_500_token_map = {}
                for stock in data.get('stocks', []):
                    symbol = stock.get('symbol')
                    token = stock.get('token')
                    if symbol and token:
                        self.nse_500_token_map[symbol] = str(token)
                        self.token_to_symbol_map[str(token)] = symbol

                logger.info(f"[TOKEN_MAP] Loaded {len(self.nse_500_token_map)} symbol-token mappings")
            else:
                logger.warning("[TOKEN_MAP] NSE 500 universe file not found")
                self.nse_500_token_map = {}

        except Exception as e:
            logger.error(f"[ERROR] Failed to load NSE 500 token mapping: {e}")
            self.nse_500_token_map = {}

    # WebSocket event handlers
    def _on_websocket_open(self, wsapp):
        """WebSocket connection opened"""
        logger.info("[CONNECT] WebSocket connection opened")
        self.is_connected = True
        self._schedule_subscription()

    def _on_websocket_data(self, wsapp, message):
        """Handle incoming WebSocket data"""
        try:
            self._schedule_tick_processing(message)
        except Exception as e:
            logger.error(f"[ERROR] Error processing WebSocket data: {e}")

    def _on_websocket_error(self, wsapp, error):
        """WebSocket error occurred"""
        logger.error(f"[CONNECT] WebSocket error: {error}")
        self.is_connected = False

    def _on_websocket_close(self, wsapp):
        """WebSocket connection closed"""
        logger.warning("[CONNECT] WebSocket connection closed")
        self.is_connected = False
        if hasattr(self, 'is_running') and self.is_running:
            self._schedule_reconnection()

    # Enhanced WebSocket event handlers
    def _on_websocket_connected(self):
        """Enhanced WebSocket connection established"""
        logger.info("[CONNECT] Enhanced WebSocket connection established")
        self.is_connected = True
        self._schedule_subscription()

    def _on_websocket_data_enhanced(self, message):
        """Enhanced WebSocket data handler"""
        try:
            logger.debug(f"[WEBSOCKET] Received message: {type(message)}")
            self._schedule_tick_processing(message)
        except Exception as e:
            logger.error(f"[ERROR] Enhanced data processing error: {e}")

    def _on_websocket_error_enhanced(self, error):
        """Enhanced WebSocket error handler"""
        logger.error(f"[CONNECT] Enhanced WebSocket error: {error}")
        self.is_connected = False

    def _on_websocket_disconnected(self):
        """Enhanced WebSocket disconnection handler"""
        logger.warning("[CONNECT] Enhanced WebSocket disconnected")
        self.is_connected = False

    def _on_enhanced_tick(self, tick_data):
        """Handle tick data from enhanced WebSocket service"""
        try:
            # Notify tick callbacks
            for callback in self.tick_callbacks:
                try:
                    callback(tick_data)
                except Exception as e:
                    logger.error(f"[ERROR] Error in tick callback: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling enhanced tick: {e}")

    async def _on_enhanced_candle(self, candle_data):
        """Handle completed candle from enhanced WebSocket service"""
        try:
            # Notify candle callbacks
            for callback in self.candle_callbacks:
                try:
                    await callback(candle_data)
                except Exception as e:
                    logger.error(f"[ERROR] Error in candle callback: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling enhanced candle: {e}")

    # Thread-safe scheduling helpers
    def _schedule_subscription(self):
        """Schedule symbol subscription"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._subscribe_pending_symbols())
            else:
                self._subscription_pending = True
        except RuntimeError:
            self._subscription_pending = True

    def _schedule_tick_processing(self, message):
        """Schedule tick processing"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._process_tick_message(message))
            else:
                self._pending_messages.append(message)
        except RuntimeError:
            self._pending_messages.append(message)

    def _schedule_reconnection(self):
        """Schedule reconnection"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._reconnect_websocket())
            else:
                self._reconnection_pending = True
        except RuntimeError:
            self._reconnection_pending = True

    async def _subscribe_pending_symbols(self):
        """Subscribe to pending symbols"""
        if self._pending_symbols:
            symbols = list(self._pending_symbols)
            self._pending_symbols.clear()
            await self.subscribe_to_symbols(symbols)

    async def _process_tick_message(self, message):
        """Process tick message"""
        try:
            tick_data = self._parse_websocket_message(message)
            if tick_data:
                # Create MarketTick object
                tick = MarketTick(
                    symbol=tick_data.get('symbol'),
                    token=tick_data.get('token'),
                    timestamp=datetime.now(),
                    ltp=tick_data.get('ltp'),
                    volume=tick_data.get('volume', 0),
                    open_price=tick_data.get('open'),
                    high_price=tick_data.get('high'),
                    low_price=tick_data.get('low'),
                    close_price=tick_data.get('close')
                )

                # Notify tick callbacks
                for callback in self.tick_callbacks:
                    try:
                        callback(tick)
                    except Exception as e:
                        logger.error(f"[ERROR] Error in tick callback: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Error processing tick message: {e}")

    def _parse_websocket_message(self, message) -> Optional[Dict[str, Any]]:
        """Parse WebSocket message to extract tick data"""
        try:
            if isinstance(message, dict):
                # Extract symbol from token if needed
                if 'tk' in message and 'symbol' not in message:
                    token = message.get('tk')
                    symbol = self.token_to_symbol_map.get(str(token))
                    if symbol:
                        message['symbol'] = symbol

                # Map SmartAPI fields
                token = message.get('token', message.get('tk'))
                ltp = message.get('last_traded_price', message.get('ltp', message.get('lp', 0)))

                # Convert price from paise to rupees if needed
                if ltp > 10000:
                    ltp = ltp / 100

                return {
                    'symbol': message.get('symbol'),
                    'token': str(token) if token else None,
                    'ltp': float(ltp),
                    'volume': int(message.get('volume_traded_today', message.get('volume', message.get('v', 0)))),
                    'open': float(message.get('open_price_of_day', message.get('open', message.get('o', 0)))),
                    'high': float(message.get('high_price_of_day', message.get('high', message.get('h', 0)))),
                    'low': float(message.get('low_price_of_day', message.get('low', message.get('l', 0)))),
                    'close': float(ltp)
                }

            elif isinstance(message, str):
                try:
                    parsed = json.loads(message)
                    return self._parse_websocket_message(parsed)
                except json.JSONDecodeError:
                    logger.warning(f"[PARSE] Failed to parse WebSocket message as JSON")
                    return None
            else:
                logger.warning(f"[PARSE] Unsupported message type: {type(message)}")
                return None

        except Exception as e:
            logger.error(f"[ERROR] Error parsing WebSocket message: {e}")
            return None

    async def _reconnect_websocket(self):
        """Reconnect WebSocket with retry logic"""
        try:
            ws_config = self.config.smartapi_config.get('websocket', {})
            max_attempts = ws_config.get('reconnect_attempts', 5)
            delay = ws_config.get('reconnect_delay', 5)

            for attempt in range(max_attempts):
                try:
                    logger.info(f"[CONNECT] Reconnection attempt {attempt + 1}/{max_attempts}")

                    if self.websocket_client:
                        self.websocket_client.connect()
                        await asyncio.sleep(2)

                        if self.is_connected:
                            logger.info("[SUCCESS] WebSocket reconnected successfully")
                            return

                except Exception as e:
                    logger.error(f"[ERROR] Reconnection attempt {attempt + 1} failed: {e}")

                if attempt < max_attempts - 1:
                    await asyncio.sleep(delay * (2 ** attempt))

            logger.error("[ERROR] All reconnection attempts failed")

        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket reconnection: {e}")

    def _process_pending_operations(self):
        """Process any pending operations"""
        if self._subscription_pending:
            self._schedule_subscription()

        if self._reconnection_pending:
            self._schedule_reconnection()

        if self._pending_messages:
            for message in self._pending_messages:
                self._schedule_tick_processing(message)
            self._pending_messages = []

    # Callback management
    def add_tick_callback(self, callback: Callable):
        """Add tick data callback"""
        self.tick_callbacks.append(callback)

    def add_candle_callback(self, callback: Callable):
        """Add candle data callback"""
        self.candle_callbacks.append(callback)

    def add_connection_callback(self, callback: Callable):
        """Add connection status callback"""
        self.connection_callbacks.append(callback)

    # Status methods
    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection status"""
        return {
            'is_connected': self.is_connected,
            'subscribed_symbols': len(self.subscribed_symbols),
            'pending_symbols': len(self._pending_symbols),
            'websocket_type': self._get_websocket_type()
        }

    def _get_websocket_type(self) -> str:
        """Get the type of WebSocket being used"""
        if self.enhanced_websocket:
            return "enhanced_service"
        elif self.websocket_manager:
            return "robust_manager"
        elif self.websocket_client:
            return "basic_client"
        else:
            return "none"
