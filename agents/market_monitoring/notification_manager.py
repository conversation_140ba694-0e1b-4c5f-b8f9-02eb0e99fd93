#!/usr/bin/env python3
"""
Notification Manager for Market Monitoring Agent

Handles sending notifications via Telegram, email, and other channels.
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from .data_structures import TradingSignal, MarketRegime, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class NotificationManager:
    """Manages notifications for trading signals and market events"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.telegram_bot = None
        self.notification_queue = asyncio.Queue()
        self.is_running = False
        self._initialize_notification_services()
    
    def _initialize_notification_services(self):
        """Initialize notification services"""
        try:
            # Initialize Telegram bot
            self._initialize_telegram()
            
            # Initialize email service
            self._initialize_email()
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing notification services: {e}")
    
    def _initialize_telegram(self):
        """Initialize Telegram bot"""
        try:
            telegram_config = self.config.notifications_config.get('telegram', {})
            if not telegram_config.get('enable', False):
                logger.info("[TELEGRAM] Telegram notifications disabled")
                return
            
            bot_token = telegram_config.get('bot_token')
            if not bot_token:
                logger.warning("[TELEGRAM] No bot token provided")
                return
            
            import telegram
            self.telegram_bot = telegram.Bot(token=bot_token)
            logger.info("[TELEGRAM] Telegram bot initialized")
            
        except ImportError:
            logger.warning("[TELEGRAM] Telegram library not available")
        except Exception as e:
            logger.error(f"[ERROR] Error initializing Telegram: {e}")
    
    def _initialize_email(self):
        """Initialize email service"""
        try:
            email_config = self.config.notifications_config.get('email', {})
            if not email_config.get('enable', False):
                logger.info("[EMAIL] Email notifications disabled")
                return
            
            # Email configuration validation
            required_fields = ['smtp_server', 'smtp_port', 'username', 'password', 'from_email']
            missing_fields = [field for field in required_fields if not email_config.get(field)]
            
            if missing_fields:
                logger.warning(f"[EMAIL] Missing email configuration: {missing_fields}")
                return
            
            logger.info("[EMAIL] Email service configured")
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing email: {e}")
    
    async def start_notification_service(self):
        """Start the notification service"""
        try:
            self.is_running = True
            logger.info("[NOTIFICATIONS] Starting notification service")
            
            # Start notification processor
            asyncio.create_task(self._process_notification_queue())
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting notification service: {e}")
    
    async def stop_notification_service(self):
        """Stop the notification service"""
        try:
            self.is_running = False
            logger.info("[NOTIFICATIONS] Stopping notification service")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping notification service: {e}")
    
    async def _process_notification_queue(self):
        """Process notification queue"""
        while self.is_running:
            try:
                # Get notification from queue with timeout
                notification = await asyncio.wait_for(
                    self.notification_queue.get(), 
                    timeout=1.0
                )
                
                # Process notification
                await self._send_notification(notification)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"[ERROR] Error processing notification queue: {e}")
                await asyncio.sleep(1)
    
    async def send_signal_notification(self, signal: TradingSignal):
        """Send notification for trading signal"""
        try:
            notification = {
                'type': 'signal',
                'data': signal,
                'timestamp': datetime.now()
            }
            
            await self.notification_queue.put(notification)
            
        except Exception as e:
            logger.error(f"[ERROR] Error queuing signal notification: {e}")
    
    async def send_regime_change_notification(self, old_regime: Optional[MarketRegime], new_regime: MarketRegime):
        """Send notification for market regime change"""
        try:
            notification = {
                'type': 'regime_change',
                'data': {
                    'old_regime': old_regime,
                    'new_regime': new_regime
                },
                'timestamp': datetime.now()
            }
            
            await self.notification_queue.put(notification)
            
        except Exception as e:
            logger.error(f"[ERROR] Error queuing regime change notification: {e}")
    
    async def send_error_notification(self, error_message: str, error_details: Optional[Dict[str, Any]] = None):
        """Send notification for errors"""
        try:
            notification = {
                'type': 'error',
                'data': {
                    'message': error_message,
                    'details': error_details or {}
                },
                'timestamp': datetime.now()
            }
            
            await self.notification_queue.put(notification)
            
        except Exception as e:
            logger.error(f"[ERROR] Error queuing error notification: {e}")
    
    async def send_performance_notification(self, performance_data: Dict[str, Any]):
        """Send notification for performance updates"""
        try:
            notification = {
                'type': 'performance',
                'data': performance_data,
                'timestamp': datetime.now()
            }
            
            await self.notification_queue.put(notification)
            
        except Exception as e:
            logger.error(f"[ERROR] Error queuing performance notification: {e}")
    
    async def _send_notification(self, notification: Dict[str, Any]):
        """Send notification via configured channels"""
        try:
            notification_type = notification['type']
            
            # Generate message based on type
            message = await self._generate_message(notification)
            if not message:
                return
            
            # Send via Telegram
            if self._should_send_telegram(notification_type):
                await self._send_telegram_message(message)
            
            # Send via email
            if self._should_send_email(notification_type):
                await self._send_email_message(message, notification)
            
        except Exception as e:
            logger.error(f"[ERROR] Error sending notification: {e}")
    
    async def _generate_message(self, notification: Dict[str, Any]) -> Optional[str]:
        """Generate message text based on notification type"""
        try:
            notification_type = notification['type']
            data = notification['data']
            timestamp = notification['timestamp']
            
            if notification_type == 'signal':
                return self._generate_signal_message(data, timestamp)
            elif notification_type == 'regime_change':
                return self._generate_regime_change_message(data, timestamp)
            elif notification_type == 'error':
                return self._generate_error_message(data, timestamp)
            elif notification_type == 'performance':
                return self._generate_performance_message(data, timestamp)
            else:
                logger.warning(f"[WARN] Unknown notification type: {notification_type}")
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Error generating message: {e}")
            return None
    
    def _generate_signal_message(self, signal: TradingSignal, timestamp: datetime) -> str:
        """Generate message for trading signal"""
        try:
            # Calculate R:R ratio
            risk = abs(signal.price - signal.stop_loss)
            reward = abs(signal.target - signal.price)
            rr_ratio = reward / risk if risk > 0 else 0
            
            message = f"""🚨 **TRADING SIGNAL** 🚨

📈 **Symbol:** {signal.symbol}
🎯 **Action:** {signal.action}
💰 **Price:** ₹{signal.price:.2f}
🎯 **Target:** ₹{signal.target:.2f}
🛑 **Stop Loss:** ₹{signal.stop_loss:.2f}
📊 **Quantity:** {signal.quantity}
🎲 **Confidence:** {signal.confidence:.1%}
⚖️ **R:R Ratio:** {rr_ratio:.2f}

📋 **Strategy:** {signal.strategy}
🌍 **Market Regime:** {signal.market_regime}
⏰ **Time:** {timestamp.strftime('%Y-%m-%d %H:%M:%S')}

💡 **Context:** {signal.context.get('signal_reason', 'N/A')}"""

            return message
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating signal message: {e}")
            return f"Trading Signal: {signal.symbol} {signal.action} at ₹{signal.price:.2f}"
    
    def _generate_regime_change_message(self, data: Dict[str, Any], timestamp: datetime) -> str:
        """Generate message for regime change"""
        try:
            old_regime = data['old_regime']
            new_regime = data['new_regime']
            
            old_regime_str = old_regime.regime.upper() if old_regime else "UNKNOWN"
            
            message = f"""🔄 **MARKET REGIME CHANGE** 🔄

📊 **Change:** {old_regime_str} → {new_regime.regime.upper()}
🎯 **Confidence:** {new_regime.confidence:.1%}
📈 **Volatility:** {new_regime.volatility_level.upper()}
💪 **Trend Strength:** {new_regime.trend_strength:.1%}
📊 **Market Breadth:** {new_regime.market_breadth:.1f}%
⏰ **Time:** {timestamp.strftime('%Y-%m-%d %H:%M:%S')}

💡 **Impact:** Adjust trading strategies accordingly"""

            return message
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating regime change message: {e}")
            return f"Market regime changed to {new_regime.regime}"
    
    def _generate_error_message(self, data: Dict[str, Any], timestamp: datetime) -> str:
        """Generate message for error notification"""
        try:
            message = f"""❌ **ERROR ALERT** ❌

🚨 **Message:** {data['message']}
⏰ **Time:** {timestamp.strftime('%Y-%m-%d %H:%M:%S')}

🔧 **Action Required:** Check system logs for details"""

            return message
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating error message: {e}")
            return f"Error: {data.get('message', 'Unknown error')}"
    
    def _generate_performance_message(self, data: Dict[str, Any], timestamp: datetime) -> str:
        """Generate message for performance notification"""
        try:
            message = f"""📊 **PERFORMANCE UPDATE** 📊

📈 **Signals Generated:** {data.get('signals_generated', 0)}
📊 **Symbols Analyzed:** {data.get('symbols_analyzed', 0)}
⏱️ **Cycle Duration:** {data.get('cycle_duration', 0):.2f}s
💾 **Memory Usage:** {data.get('memory_usage', 0):.1f}%
🔌 **WebSocket Status:** {data.get('websocket_status', 'Unknown')}
⏰ **Time:** {timestamp.strftime('%Y-%m-%d %H:%M:%S')}"""

            return message
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating performance message: {e}")
            return f"Performance update at {timestamp.strftime('%H:%M:%S')}"
    
    def _should_send_telegram(self, notification_type: str) -> bool:
        """Check if should send Telegram notification"""
        try:
            if not self.telegram_bot:
                return False
            
            telegram_config = self.config.notifications_config.get('telegram', {})
            if not telegram_config.get('enable', False):
                return False
            
            # Check type-specific settings
            type_settings = telegram_config.get('notification_types', {})
            return type_settings.get(notification_type, True)
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking Telegram settings: {e}")
            return False
    
    def _should_send_email(self, notification_type: str) -> bool:
        """Check if should send email notification"""
        try:
            email_config = self.config.notifications_config.get('email', {})
            if not email_config.get('enable', False):
                return False
            
            # Check type-specific settings
            type_settings = email_config.get('notification_types', {})
            return type_settings.get(notification_type, False)  # Email disabled by default
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking email settings: {e}")
            return False
    
    async def _send_telegram_message(self, message: str):
        """Send message via Telegram"""
        try:
            if not self.telegram_bot:
                return
            
            telegram_config = self.config.notifications_config.get('telegram', {})
            chat_id = telegram_config.get('chat_id')
            
            if not chat_id:
                logger.warning("[TELEGRAM] No chat ID configured")
                return
            
            await self.telegram_bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
            logger.debug("[TELEGRAM] Message sent successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error sending Telegram message: {e}")
    
    async def _send_email_message(self, message: str, notification: Dict[str, Any]):
        """Send message via email"""
        try:
            email_config = self.config.notifications_config.get('email', {})
            
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config['from_email']
            msg['To'] = email_config.get('to_email', email_config['from_email'])
            msg['Subject'] = f"Market Monitoring Alert - {notification['type'].title()}"
            
            msg.attach(MIMEText(message, 'plain'))
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            
            text = msg.as_string()
            server.sendmail(email_config['from_email'], msg['To'], text)
            server.quit()
            
            logger.debug("[EMAIL] Message sent successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error sending email message: {e}")
    
    def get_notification_status(self) -> Dict[str, Any]:
        """Get notification service status"""
        return {
            'is_running': self.is_running,
            'queue_size': self.notification_queue.qsize(),
            'telegram_available': self.telegram_bot is not None,
            'email_configured': self._is_email_configured()
        }
    
    def _is_email_configured(self) -> bool:
        """Check if email is properly configured"""
        try:
            email_config = self.config.notifications_config.get('email', {})
            required_fields = ['smtp_server', 'smtp_port', 'username', 'password', 'from_email']
            return all(email_config.get(field) for field in required_fields)
        except Exception:
            return False
