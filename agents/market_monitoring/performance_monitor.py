#!/usr/bin/env python3
"""
Performance Monitor for Market Monitoring Agent

Monitors system performance, tracks metrics, and provides health checks.
"""

import logging
import asyncio
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import deque

from .data_structures import PerformanceMetrics, SystemMetrics, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Monitors system performance and health metrics"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.is_monitoring = False
        self.monitoring_task = None
        
        # Performance counters
        self.signals_generated = 0
        self.symbols_analyzed = 0
        self.cycle_count = 0
        self.error_count = 0
        self.last_cycle_start = None
        
        # Health status
        self.health_status = {
            'overall': 'healthy',
            'websocket': 'unknown',
            'data_processing': 'healthy',
            'memory': 'healthy',
            'cpu': 'healthy'
        }
    
    async def start_monitoring(self):
        """Start performance monitoring"""
        try:
            if self.is_monitoring:
                logger.warning("[PERF] Performance monitoring already running")
                return
            
            self.is_monitoring = True
            
            # Start monitoring task
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("[PERF] Performance monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting performance monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop performance monitoring"""
        try:
            self.is_monitoring = False
            
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("[PERF] Performance monitoring stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping performance monitoring: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect metrics
                metrics = await self._collect_metrics()
                
                # Store metrics
                self.metrics_history.append(metrics)
                
                # Update health status
                await self._update_health_status(metrics)
                
                # Check for alerts
                await self._check_performance_alerts(metrics)
                
                # Wait for next cycle
                monitoring_interval = self.config.performance_config.get('monitoring_interval', 30)
                await asyncio.sleep(monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in monitoring loop: {e}")
                await asyncio.sleep(5)
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Calculate cycle duration
            cycle_duration = 0.0
            if self.last_cycle_start:
                cycle_duration = (datetime.now() - self.last_cycle_start).total_seconds()
            
            # WebSocket status
            websocket_status = self._get_websocket_status()
            
            # Data quality score (placeholder)
            data_quality_score = self._calculate_data_quality_score()
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                signals_generated=self.signals_generated,
                symbols_analyzed=self.symbols_analyzed,
                cycle_duration=cycle_duration,
                memory_usage=memory_percent,
                cpu_usage=cpu_percent,
                websocket_status=websocket_status,
                data_quality_score=data_quality_score
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"[ERROR] Error collecting metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                signals_generated=0,
                symbols_analyzed=0,
                cycle_duration=0.0,
                memory_usage=0.0,
                cpu_usage=0.0,
                websocket_status='error',
                data_quality_score=0.0
            )
    
    def _get_websocket_status(self) -> str:
        """Get WebSocket connection status"""
        # This would be updated by the WebSocket manager
        return self.health_status.get('websocket', 'unknown')
    
    def _calculate_data_quality_score(self) -> float:
        """Calculate data quality score based on various factors"""
        try:
            score = 1.0
            
            # Reduce score based on error count
            if self.error_count > 0:
                error_penalty = min(self.error_count * 0.1, 0.5)
                score -= error_penalty
            
            # Reduce score if no symbols are being analyzed
            if self.symbols_analyzed == 0:
                score -= 0.3
            
            # Reduce score based on WebSocket status
            websocket_status = self.health_status.get('websocket', 'unknown')
            if websocket_status == 'disconnected':
                score -= 0.4
            elif websocket_status == 'error':
                score -= 0.6
            
            return max(score, 0.0)
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating data quality score: {e}")
            return 0.5
    
    async def _update_health_status(self, metrics: PerformanceMetrics):
        """Update overall health status based on metrics"""
        try:
            # CPU health
            if metrics.cpu_usage > 90:
                self.health_status['cpu'] = 'critical'
            elif metrics.cpu_usage > 70:
                self.health_status['cpu'] = 'warning'
            else:
                self.health_status['cpu'] = 'healthy'
            
            # Memory health
            if metrics.memory_usage > 90:
                self.health_status['memory'] = 'critical'
            elif metrics.memory_usage > 80:
                self.health_status['memory'] = 'warning'
            else:
                self.health_status['memory'] = 'healthy'
            
            # Data processing health
            if metrics.symbols_analyzed == 0:
                self.health_status['data_processing'] = 'warning'
            elif metrics.data_quality_score < 0.5:
                self.health_status['data_processing'] = 'critical'
            else:
                self.health_status['data_processing'] = 'healthy'
            
            # Overall health
            critical_components = [status for status in self.health_status.values() if status == 'critical']
            warning_components = [status for status in self.health_status.values() if status == 'warning']
            
            if critical_components:
                self.health_status['overall'] = 'critical'
            elif warning_components:
                self.health_status['overall'] = 'warning'
            else:
                self.health_status['overall'] = 'healthy'
                
        except Exception as e:
            logger.error(f"[ERROR] Error updating health status: {e}")
    
    async def _check_performance_alerts(self, metrics: PerformanceMetrics):
        """Check for performance alerts and trigger notifications"""
        try:
            alerts = []
            
            # High CPU usage alert
            cpu_threshold = self.config.performance_config.get('cpu_alert_threshold', 85)
            if metrics.cpu_usage > cpu_threshold:
                alerts.append(f"High CPU usage: {metrics.cpu_usage:.1f}%")
            
            # High memory usage alert
            memory_threshold = self.config.performance_config.get('memory_alert_threshold', 85)
            if metrics.memory_usage > memory_threshold:
                alerts.append(f"High memory usage: {metrics.memory_usage:.1f}%")
            
            # Long cycle duration alert
            cycle_threshold = self.config.performance_config.get('cycle_duration_threshold', 60)
            if metrics.cycle_duration > cycle_threshold:
                alerts.append(f"Long cycle duration: {metrics.cycle_duration:.1f}s")
            
            # Low data quality alert
            quality_threshold = self.config.performance_config.get('data_quality_threshold', 0.7)
            if metrics.data_quality_score < quality_threshold:
                alerts.append(f"Low data quality: {metrics.data_quality_score:.1%}")
            
            # WebSocket disconnection alert
            if metrics.websocket_status in ['disconnected', 'error']:
                alerts.append(f"WebSocket issue: {metrics.websocket_status}")
            
            # Send alerts if any
            if alerts:
                await self._send_performance_alerts(alerts, metrics)
                
        except Exception as e:
            logger.error(f"[ERROR] Error checking performance alerts: {e}")
    
    async def _send_performance_alerts(self, alerts: List[str], metrics: PerformanceMetrics):
        """Send performance alerts"""
        try:
            # This would integrate with the notification manager
            alert_message = f"Performance Alert: {', '.join(alerts)}"
            logger.warning(f"[PERF_ALERT] {alert_message}")
            
            # TODO: Send to notification manager
            # await notification_manager.send_performance_notification({
            #     'alerts': alerts,
            #     'metrics': metrics
            # })
            
        except Exception as e:
            logger.error(f"[ERROR] Error sending performance alerts: {e}")
    
    def record_signal_generated(self):
        """Record that a signal was generated"""
        self.signals_generated += 1
    
    def record_symbol_analyzed(self):
        """Record that a symbol was analyzed"""
        self.symbols_analyzed += 1
    
    def record_cycle_start(self):
        """Record the start of a processing cycle"""
        self.last_cycle_start = datetime.now()
        self.cycle_count += 1
    
    def record_error(self):
        """Record an error occurrence"""
        self.error_count += 1
    
    def update_websocket_status(self, status: str):
        """Update WebSocket status"""
        self.health_status['websocket'] = status
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """Get the most recent metrics"""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self, hours_back: int = 1) -> List[PerformanceMetrics]:
        """Get metrics history for the specified time period"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            return [m for m in self.metrics_history if m.timestamp > cutoff_time]
        except Exception as e:
            logger.error(f"[ERROR] Error getting metrics history: {e}")
            return []
    
    def get_health_status(self) -> Dict[str, str]:
        """Get current health status"""
        return self.health_status.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        try:
            current_metrics = self.get_current_metrics()
            recent_metrics = self.get_metrics_history(hours_back=1)
            
            summary = {
                'current_status': self.health_status['overall'],
                'total_signals': self.signals_generated,
                'total_symbols': self.symbols_analyzed,
                'total_cycles': self.cycle_count,
                'total_errors': self.error_count,
                'uptime_hours': self._calculate_uptime_hours()
            }
            
            if current_metrics:
                summary.update({
                    'current_cpu': current_metrics.cpu_usage,
                    'current_memory': current_metrics.memory_usage,
                    'current_cycle_duration': current_metrics.cycle_duration,
                    'websocket_status': current_metrics.websocket_status,
                    'data_quality': current_metrics.data_quality_score
                })
            
            if recent_metrics:
                avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
                avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
                avg_cycle = sum(m.cycle_duration for m in recent_metrics) / len(recent_metrics)
                
                summary.update({
                    'avg_cpu_1h': avg_cpu,
                    'avg_memory_1h': avg_memory,
                    'avg_cycle_duration_1h': avg_cycle
                })
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting performance summary: {e}")
            return {'current_status': 'error'}
    
    def _calculate_uptime_hours(self) -> float:
        """Calculate uptime in hours"""
        try:
            if not self.metrics_history:
                return 0.0
            
            first_metric = self.metrics_history[0]
            uptime_delta = datetime.now() - first_metric.timestamp
            return uptime_delta.total_seconds() / 3600
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating uptime: {e}")
            return 0.0
    
    def reset_counters(self):
        """Reset performance counters"""
        self.signals_generated = 0
        self.symbols_analyzed = 0
        self.cycle_count = 0
        self.error_count = 0
        logger.info("[PERF] Performance counters reset")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get detailed system information"""
        try:
            # CPU information
            cpu_info = {
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            }
            
            # Memory information
            memory = psutil.virtual_memory()
            memory_info = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free
            }
            
            # Disk information
            disk = psutil.disk_usage('/')
            disk_info = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }
            
            # Network information
            network = psutil.net_io_counters()
            network_info = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            return {
                'cpu': cpu_info,
                'memory': memory_info,
                'disk': disk_info,
                'network': network_info,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting system info: {e}")
            return {'error': str(e)}
