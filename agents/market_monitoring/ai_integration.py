#!/usr/bin/env python3
"""
AI Integration for Market Monitoring Agent

Handles AI model integration for strategy recommendations and predictions.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from .data_structures import MarketIndicators, MarketRegime, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class AIIntegration:
    """Handles AI model integration for market monitoring"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.ai_training_agent = None
        self.enhanced_model_integration = None
        self._initialize_ai_components()
    
    def _initialize_ai_components(self):
        """Initialize AI components if available"""
        try:
            # Try to initialize AI Training Agent
            try:
                from agents.ai_training_agent import AITrainingAgent
                self.ai_training_agent = AITrainingAgent()
                logger.info("[AI] AI Training Agent initialized")
            except ImportError:
                logger.warning("[AI] AI Training Agent not available")
            
            # Try to initialize Enhanced Model Integration
            try:
                from utils.enhanced_model_integration import get_enhanced_model_integration
                self.enhanced_model_integration = get_enhanced_model_integration()
                logger.info("[AI] Enhanced Model Integration initialized")
            except ImportError:
                logger.warning("[AI] Enhanced Model Integration not available")
                
        except Exception as e:
            logger.error(f"[ERROR] Error initializing AI components: {e}")
    
    async def get_ai_strategy_recommendation(self, symbol: str, indicators: MarketIndicators, market_regime: Optional[MarketRegime] = None) -> Optional[Dict[str, Any]]:
        """Get AI strategy recommendation for a symbol"""
        try:
            if not self._is_ai_available():
                logger.debug(f"[AI] No AI components available for {symbol}")
                return None

            # Prepare feature data for AI model
            features = await self._prepare_ai_features(symbol, indicators, market_regime)
            if not features:
                logger.debug(f"[AI] Could not prepare features for {symbol}")
                return None

            # Get recommendation from available AI components
            recommendation = None
            
            if self.enhanced_model_integration:
                recommendation = await self._get_enhanced_model_recommendation(symbol, features)
            elif self.ai_training_agent:
                recommendation = await self._get_training_agent_recommendation(symbol, features)

            if recommendation:
                logger.debug(f"[AI] AI recommendation for {symbol}: {recommendation.get('action', 'HOLD')} (confidence: {recommendation.get('confidence', 0):.2%})")

            return recommendation

        except Exception as e:
            logger.error(f"[ERROR] Error getting AI strategy recommendation for {symbol}: {e}")
            return None
    
    def _is_ai_available(self) -> bool:
        """Check if any AI components are available"""
        return self.ai_training_agent is not None or self.enhanced_model_integration is not None
    
    async def _prepare_ai_features(self, symbol: str, indicators: MarketIndicators, market_regime: Optional[MarketRegime] = None) -> Optional[Dict[str, Any]]:
        """Prepare features for AI model"""
        try:
            features = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'timeframe': indicators.timeframe
            }

            # Technical indicators
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                features['rsi_14'] = indicators.rsi_14
            if hasattr(indicators, 'rsi_5') and indicators.rsi_5:
                features['rsi_5'] = indicators.rsi_5

            # Moving averages
            for period in [5, 10, 13, 20, 21, 30, 50, 100]:
                ema_attr = f'ema_{period}'
                if hasattr(indicators, ema_attr):
                    ema_value = getattr(indicators, ema_attr)
                    if ema_value:
                        features[ema_attr] = ema_value

            # MACD
            if hasattr(indicators, 'macd') and indicators.macd:
                features['macd'] = indicators.macd
            if hasattr(indicators, 'macd_signal') and indicators.macd_signal:
                features['macd_signal'] = indicators.macd_signal
            if hasattr(indicators, 'macd_histogram') and indicators.macd_histogram:
                features['macd_histogram'] = indicators.macd_histogram

            # Other indicators
            if hasattr(indicators, 'atr') and indicators.atr:
                features['atr'] = indicators.atr
            if hasattr(indicators, 'vwap') and indicators.vwap:
                features['vwap'] = indicators.vwap
            if hasattr(indicators, 'bb_position') and indicators.bb_position is not None:
                features['bb_position'] = indicators.bb_position
            if hasattr(indicators, 'volume_ratio') and indicators.volume_ratio:
                features['volume_ratio'] = indicators.volume_ratio

            # Market regime features
            if market_regime:
                features['market_regime'] = market_regime.regime
                features['regime_confidence'] = market_regime.confidence
                features['volatility_level'] = market_regime.volatility_level
                features['trend_strength'] = market_regime.trend_strength
                features['market_breadth'] = market_regime.market_breadth

            # Calculate derived features
            features.update(self._calculate_derived_features(features))

            return features

        except Exception as e:
            logger.error(f"[ERROR] Error preparing AI features: {e}")
            return None
    
    def _calculate_derived_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate derived features for AI model"""
        derived = {}
        
        try:
            # EMA relationships
            if 'ema_5' in features and 'ema_20' in features:
                derived['ema_5_20_ratio'] = features['ema_5'] / features['ema_20']
                derived['ema_5_above_20'] = 1 if features['ema_5'] > features['ema_20'] else 0

            if 'ema_20' in features and 'ema_50' in features:
                derived['ema_20_50_ratio'] = features['ema_20'] / features['ema_50']
                derived['ema_20_above_50'] = 1 if features['ema_20'] > features['ema_50'] else 0

            # RSI momentum
            if 'rsi_14' in features:
                derived['rsi_oversold'] = 1 if features['rsi_14'] < 30 else 0
                derived['rsi_overbought'] = 1 if features['rsi_14'] > 70 else 0
                derived['rsi_neutral'] = 1 if 40 <= features['rsi_14'] <= 60 else 0

            # MACD momentum
            if 'macd' in features and 'macd_signal' in features:
                derived['macd_bullish'] = 1 if features['macd'] > features['macd_signal'] else 0
                derived['macd_bearish'] = 1 if features['macd'] < features['macd_signal'] else 0

            # Volatility features
            if 'volatility_level' in features:
                derived['high_volatility'] = 1 if features['volatility_level'] == 'high' else 0
                derived['low_volatility'] = 1 if features['volatility_level'] == 'low' else 0

            # Market regime features
            if 'market_regime' in features:
                derived['bull_market'] = 1 if features['market_regime'] == 'bull' else 0
                derived['bear_market'] = 1 if features['market_regime'] == 'bear' else 0
                derived['sideways_market'] = 1 if features['market_regime'] == 'sideways' else 0

        except Exception as e:
            logger.error(f"[ERROR] Error calculating derived features: {e}")

        return derived
    
    async def _get_enhanced_model_recommendation(self, symbol: str, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get recommendation from Enhanced Model Integration"""
        try:
            if not self.enhanced_model_integration:
                return None

            # Get prediction from enhanced model
            prediction = await self.enhanced_model_integration.predict_strategy(
                symbol=symbol,
                features=features,
                timeframe=features.get('timeframe', '5min')
            )

            if prediction:
                return {
                    'action': prediction.get('action', 'HOLD'),
                    'confidence': prediction.get('confidence', 0.0),
                    'strategy': prediction.get('strategy', 'ai_enhanced'),
                    'reasoning': prediction.get('reasoning', 'AI model prediction'),
                    'model_type': 'enhanced_integration'
                }

        except Exception as e:
            logger.error(f"[ERROR] Error getting enhanced model recommendation: {e}")

        return None
    
    async def _get_training_agent_recommendation(self, symbol: str, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get recommendation from AI Training Agent"""
        try:
            if not self.ai_training_agent:
                return None

            # Prepare data for AI Training Agent
            market_data = {
                'symbol': symbol,
                'features': features,
                'timestamp': features.get('timestamp')
            }

            # Get strategy classification
            strategy_result = await self.ai_training_agent.classify_strategy(market_data)

            if strategy_result:
                return {
                    'action': strategy_result.get('recommended_action', 'HOLD'),
                    'confidence': strategy_result.get('confidence', 0.0),
                    'strategy': strategy_result.get('strategy_name', 'ai_training'),
                    'reasoning': strategy_result.get('reasoning', 'AI training agent classification'),
                    'model_type': 'training_agent'
                }

        except Exception as e:
            logger.error(f"[ERROR] Error getting training agent recommendation: {e}")

        return None
    
    async def update_ai_models(self, market_data: Dict[str, Any], performance_feedback: Optional[Dict[str, Any]] = None):
        """Update AI models with new market data and performance feedback"""
        try:
            if self.enhanced_model_integration:
                await self._update_enhanced_model(market_data, performance_feedback)
            
            if self.ai_training_agent:
                await self._update_training_agent(market_data, performance_feedback)

        except Exception as e:
            logger.error(f"[ERROR] Error updating AI models: {e}")
    
    async def _update_enhanced_model(self, market_data: Dict[str, Any], performance_feedback: Optional[Dict[str, Any]] = None):
        """Update enhanced model with new data"""
        try:
            if performance_feedback:
                await self.enhanced_model_integration.update_model_performance(
                    market_data, performance_feedback
                )
            else:
                await self.enhanced_model_integration.update_market_data(market_data)

        except Exception as e:
            logger.error(f"[ERROR] Error updating enhanced model: {e}")
    
    async def _update_training_agent(self, market_data: Dict[str, Any], performance_feedback: Optional[Dict[str, Any]] = None):
        """Update training agent with new data"""
        try:
            if performance_feedback:
                await self.ai_training_agent.update_performance_feedback(
                    market_data, performance_feedback
                )
            else:
                await self.ai_training_agent.process_market_data(market_data)

        except Exception as e:
            logger.error(f"[ERROR] Error updating training agent: {e}")
    
    def get_ai_model_status(self) -> Dict[str, Any]:
        """Get status of AI models"""
        status = {
            'ai_training_agent': {
                'available': self.ai_training_agent is not None,
                'status': 'active' if self.ai_training_agent else 'unavailable'
            },
            'enhanced_model_integration': {
                'available': self.enhanced_model_integration is not None,
                'status': 'active' if self.enhanced_model_integration else 'unavailable'
            }
        }

        # Get detailed status if available
        try:
            if self.enhanced_model_integration:
                enhanced_status = self.enhanced_model_integration.get_model_status()
                status['enhanced_model_integration'].update(enhanced_status)
        except Exception as e:
            logger.debug(f"[DEBUG] Could not get enhanced model status: {e}")

        try:
            if self.ai_training_agent:
                training_status = self.ai_training_agent.get_agent_status()
                status['ai_training_agent'].update(training_status)
        except Exception as e:
            logger.debug(f"[DEBUG] Could not get training agent status: {e}")

        return status
    
    async def validate_ai_recommendation(self, recommendation: Dict[str, Any], market_context: Dict[str, Any]) -> bool:
        """Validate AI recommendation against market context"""
        try:
            if not recommendation:
                return False

            # Check confidence threshold
            ai_config = self.config.strategy_config.get('ai_model', {})
            min_confidence = ai_config.get('confidence_threshold', 0.6)
            
            if recommendation.get('confidence', 0) < min_confidence:
                logger.debug(f"[AI_FILTER] Recommendation rejected: confidence {recommendation.get('confidence', 0):.2%} < {min_confidence:.2%}")
                return False

            # Check market regime compatibility
            market_regime = market_context.get('market_regime')
            if market_regime:
                if not self._is_recommendation_compatible_with_regime(recommendation, market_regime):
                    logger.debug(f"[AI_FILTER] Recommendation rejected: incompatible with market regime {market_regime}")
                    return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error validating AI recommendation: {e}")
            return False
    
    def _is_recommendation_compatible_with_regime(self, recommendation: Dict[str, Any], market_regime: str) -> bool:
        """Check if recommendation is compatible with market regime"""
        try:
            action = recommendation.get('action', 'HOLD')
            
            # In strong bear markets, be cautious about buy signals
            if market_regime == 'bear' and action == 'BUY':
                confidence = recommendation.get('confidence', 0)
                return confidence > 0.8  # Require high confidence for buy in bear market
            
            # In strong bull markets, be cautious about sell signals
            if market_regime == 'bull' and action == 'SELL':
                confidence = recommendation.get('confidence', 0)
                return confidence > 0.8  # Require high confidence for sell in bull market
            
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error checking regime compatibility: {e}")
            return True
