#!/usr/bin/env python3
"""
Health Monitor for Market Monitoring Agent

Comprehensive health monitoring with alerting and recovery mechanisms.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import deque

from .data_structures import MarketMonitoringConfig

logger = logging.getLogger(__name__)


class HealthMonitor:
    """Comprehensive health monitoring system"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.is_monitoring = False
        self.health_history = deque(maxlen=1000)
        self.alert_handlers = []
        
        # Health check configuration
        self.checks = {
            'websocket_connection': True,
            'data_freshness': True,
            'signal_generation': True,
            'ai_model_health': True,
            'memory_usage': True,
            'error_rate': True,
            'processing_latency': True
        }
        
        # Health thresholds
        self.thresholds = {
            'data_staleness_minutes': config.performance_config.get('data_staleness_threshold', 5),
            'max_error_rate_percent': config.performance_config.get('max_error_rate', 10),
            'max_processing_latency_seconds': config.performance_config.get('max_processing_latency', 30),
            'min_signals_per_hour': config.strategy_config.get('min_signals_per_hour', 1),
            'websocket_timeout_seconds': config.smartapi_config.get('websocket', {}).get('timeout', 30)
        }
        
        # Health state tracking
        self.last_websocket_message = None
        self.last_signal_time = None
        self.error_count = 0
        self.total_operations = 0
        self.processing_times = deque(maxlen=100)
    
    async def start_monitoring(self):
        """Start health monitoring"""
        try:
            if self.is_monitoring:
                return
            
            self.is_monitoring = True
            logger.info("[HEALTH] Health monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting health monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        try:
            self.is_monitoring = False
            logger.info("[HEALTH] Health monitoring stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping health monitoring: {e}")
    
    async def perform_health_check(self, agent) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        try:
            health_status = {
                'timestamp': datetime.now(),
                'overall': 'healthy',
                'checks': {},
                'issues': [],
                'summary': '',
                'score': 100
            }
            
            # Perform individual health checks
            if self.checks['websocket_connection']:
                await self._check_websocket_health(agent, health_status)
            
            if self.checks['data_freshness']:
                await self._check_data_freshness(agent, health_status)
            
            if self.checks['signal_generation']:
                await self._check_signal_generation(agent, health_status)
            
            if self.checks['ai_model_health']:
                await self._check_ai_model_health(agent, health_status)
            
            if self.checks['memory_usage']:
                await self._check_memory_usage(agent, health_status)
            
            if self.checks['error_rate']:
                await self._check_error_rate(health_status)
            
            if self.checks['processing_latency']:
                await self._check_processing_latency(health_status)
            
            # Determine overall health
            self._determine_overall_health(health_status)
            
            # Store in history
            self.health_history.append(health_status)
            
            # Send alerts if needed
            await self._send_health_alerts(health_status)
            
            return health_status
            
        except Exception as e:
            logger.error(f"[ERROR] Error performing health check: {e}")
            return {
                'timestamp': datetime.now(),
                'overall': 'error',
                'error': str(e),
                'score': 0
            }
    
    async def _check_websocket_health(self, agent, health_status: Dict[str, Any]):
        """Check WebSocket connection health"""
        try:
            is_connected = False
            connection_status = "unknown"
            
            if hasattr(agent, 'websocket_manager'):
                ws_status = agent.websocket_manager.get_connection_status()
                is_connected = ws_status.get('is_connected', False)
                connection_status = "connected" if is_connected else "disconnected"
            
            health_status['checks']['websocket'] = {
                'status': 'healthy' if is_connected else 'critical',
                'connected': is_connected,
                'connection_status': connection_status
            }
            
            if not is_connected:
                health_status['issues'].append({
                    'type': 'websocket_disconnected',
                    'severity': 'critical',
                    'message': 'WebSocket connection is down'
                })
                health_status['score'] -= 30
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking WebSocket health: {e}")
            health_status['checks']['websocket'] = {'status': 'error', 'error': str(e)}
    
    async def _check_data_freshness(self, agent, health_status: Dict[str, Any]):
        """Check data freshness"""
        try:
            data_fresh = True
            staleness_minutes = 0
            
            if hasattr(agent, 'data_processor'):
                # Check if we have recent market data
                symbols = agent.data_processor.get_symbols_with_data()
                if symbols:
                    # Check latest candle for first symbol
                    latest_candle = agent.data_processor.get_latest_candle(symbols[0])
                    if latest_candle:
                        staleness = datetime.now() - latest_candle.timestamp
                        staleness_minutes = staleness.total_seconds() / 60
                        data_fresh = staleness_minutes < self.thresholds['data_staleness_minutes']
            
            health_status['checks']['data_freshness'] = {
                'status': 'healthy' if data_fresh else 'warning',
                'staleness_minutes': staleness_minutes,
                'threshold_minutes': self.thresholds['data_staleness_minutes']
            }
            
            if not data_fresh:
                health_status['issues'].append({
                    'type': 'data_stale',
                    'severity': 'warning',
                    'message': f'Data is {staleness_minutes:.1f} minutes old'
                })
                health_status['score'] -= 15
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking data freshness: {e}")
            health_status['checks']['data_freshness'] = {'status': 'error', 'error': str(e)}
    
    async def _check_signal_generation(self, agent, health_status: Dict[str, Any]):
        """Check signal generation health"""
        try:
            signals_healthy = True
            recent_signals = 0
            
            if hasattr(agent, 'signal_generator'):
                active_signals = agent.signal_generator.get_active_signals()
                
                # Count signals from last hour
                one_hour_ago = datetime.now() - timedelta(hours=1)
                recent_signals = len([
                    s for s in active_signals 
                    if s.timestamp > one_hour_ago
                ])
                
                signals_healthy = recent_signals >= self.thresholds['min_signals_per_hour']
            
            health_status['checks']['signal_generation'] = {
                'status': 'healthy' if signals_healthy else 'warning',
                'recent_signals_1h': recent_signals,
                'min_expected': self.thresholds['min_signals_per_hour']
            }
            
            if not signals_healthy:
                health_status['issues'].append({
                    'type': 'low_signal_generation',
                    'severity': 'warning',
                    'message': f'Only {recent_signals} signals in last hour'
                })
                health_status['score'] -= 10
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking signal generation: {e}")
            health_status['checks']['signal_generation'] = {'status': 'error', 'error': str(e)}
    
    async def _check_ai_model_health(self, agent, health_status: Dict[str, Any]):
        """Check AI model health"""
        try:
            ai_healthy = True
            model_status = "unknown"
            
            if hasattr(agent, 'ai_integration'):
                ai_status = agent.ai_integration.get_ai_model_status()
                
                # Check if any AI models are available and working
                available_models = [
                    name for name, status in ai_status.items() 
                    if status.get('available', False)
                ]
                
                ai_healthy = len(available_models) > 0
                model_status = f"{len(available_models)} models available"
            
            health_status['checks']['ai_models'] = {
                'status': 'healthy' if ai_healthy else 'warning',
                'model_status': model_status
            }
            
            if not ai_healthy:
                health_status['issues'].append({
                    'type': 'ai_model_error',
                    'severity': 'warning',
                    'message': 'No AI models available'
                })
                health_status['score'] -= 5
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking AI model health: {e}")
            health_status['checks']['ai_models'] = {'status': 'error', 'error': str(e)}
    
    async def _check_memory_usage(self, agent, health_status: Dict[str, Any]):
        """Check memory usage"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            memory_healthy = memory.percent < 90
            
            health_status['checks']['memory'] = {
                'status': 'healthy' if memory_healthy else 'critical',
                'usage_percent': memory.percent,
                'available_gb': memory.available / (1024**3)
            }
            
            if not memory_healthy:
                health_status['issues'].append({
                    'type': 'high_memory_usage',
                    'severity': 'critical',
                    'message': f'Memory usage at {memory.percent:.1f}%'
                })
                health_status['score'] -= 25
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking memory usage: {e}")
            health_status['checks']['memory'] = {'status': 'error', 'error': str(e)}
    
    async def _check_error_rate(self, health_status: Dict[str, Any]):
        """Check error rate"""
        try:
            error_rate = 0
            if self.total_operations > 0:
                error_rate = (self.error_count / self.total_operations) * 100
            
            error_rate_healthy = error_rate < self.thresholds['max_error_rate_percent']
            
            health_status['checks']['error_rate'] = {
                'status': 'healthy' if error_rate_healthy else 'warning',
                'error_rate_percent': error_rate,
                'error_count': self.error_count,
                'total_operations': self.total_operations
            }
            
            if not error_rate_healthy:
                health_status['issues'].append({
                    'type': 'high_error_rate',
                    'severity': 'warning',
                    'message': f'Error rate at {error_rate:.1f}%'
                })
                health_status['score'] -= 15
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking error rate: {e}")
            health_status['checks']['error_rate'] = {'status': 'error', 'error': str(e)}
    
    async def _check_processing_latency(self, health_status: Dict[str, Any]):
        """Check processing latency"""
        try:
            avg_latency = 0
            if self.processing_times:
                avg_latency = sum(self.processing_times) / len(self.processing_times)
            
            latency_healthy = avg_latency < self.thresholds['max_processing_latency_seconds']
            
            health_status['checks']['processing_latency'] = {
                'status': 'healthy' if latency_healthy else 'warning',
                'avg_latency_seconds': avg_latency,
                'max_threshold': self.thresholds['max_processing_latency_seconds']
            }
            
            if not latency_healthy:
                health_status['issues'].append({
                    'type': 'high_processing_latency',
                    'severity': 'warning',
                    'message': f'Average processing latency: {avg_latency:.1f}s'
                })
                health_status['score'] -= 10
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking processing latency: {e}")
            health_status['checks']['processing_latency'] = {'status': 'error', 'error': str(e)}
    
    def _determine_overall_health(self, health_status: Dict[str, Any]):
        """Determine overall health status"""
        try:
            score = health_status['score']
            critical_issues = [i for i in health_status['issues'] if i['severity'] == 'critical']
            
            if critical_issues or score < 50:
                health_status['overall'] = 'critical'
            elif score < 80:
                health_status['overall'] = 'warning'
            else:
                health_status['overall'] = 'healthy'
            
            # Generate summary
            issue_count = len(health_status['issues'])
            if issue_count == 0:
                health_status['summary'] = f"All systems healthy (score: {score})"
            else:
                health_status['summary'] = f"{issue_count} issues detected (score: {score})"
            
        except Exception as e:
            logger.error(f"[ERROR] Error determining overall health: {e}")
            health_status['overall'] = 'error'
            health_status['summary'] = f"Health check error: {e}"
    
    async def _send_health_alerts(self, health_status: Dict[str, Any]):
        """Send health alerts if needed"""
        try:
            if health_status['overall'] in ['critical', 'warning']:
                for handler in self.alert_handlers:
                    try:
                        await handler(health_status)
                    except Exception as e:
                        logger.error(f"[ERROR] Error in health alert handler: {e}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error sending health alerts: {e}")
    
    def record_operation(self, success: bool = True, processing_time: Optional[float] = None):
        """Record operation for health tracking"""
        self.total_operations += 1
        if not success:
            self.error_count += 1
        
        if processing_time is not None:
            self.processing_times.append(processing_time)
    
    def record_websocket_message(self):
        """Record WebSocket message receipt"""
        self.last_websocket_message = datetime.now()
    
    def record_signal_generation(self):
        """Record signal generation"""
        self.last_signal_time = datetime.now()
    
    def add_alert_handler(self, handler: Callable):
        """Add health alert handler"""
        self.alert_handlers.append(handler)
    
    def get_health_history(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get health check history"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            return [
                h for h in self.health_history 
                if h.get('timestamp', datetime.min) > cutoff_time
            ]
        except Exception as e:
            logger.error(f"[ERROR] Error getting health history: {e}")
            return []
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary"""
        try:
            if not self.health_history:
                return {"error": "No health data available"}
            
            latest = self.health_history[-1]
            
            # Calculate health trends
            recent_checks = self.get_health_history(hours_back=1)
            if recent_checks:
                avg_score = sum(h.get('score', 0) for h in recent_checks) / len(recent_checks)
                healthy_count = len([h for h in recent_checks if h.get('overall') == 'healthy'])
                health_percentage = (healthy_count / len(recent_checks)) * 100
            else:
                avg_score = latest.get('score', 0)
                health_percentage = 100 if latest.get('overall') == 'healthy' else 0
            
            return {
                'current_status': latest.get('overall', 'unknown'),
                'current_score': latest.get('score', 0),
                'current_issues': len(latest.get('issues', [])),
                'avg_score_1h': avg_score,
                'health_percentage_1h': health_percentage,
                'total_operations': self.total_operations,
                'error_count': self.error_count,
                'error_rate_percent': (self.error_count / max(self.total_operations, 1)) * 100,
                'last_check': latest.get('timestamp'),
                'checks_enabled': sum(1 for enabled in self.checks.values() if enabled),
                'thresholds': self.thresholds
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting health summary: {e}")
            return {"error": str(e)}
    
    def reset_counters(self):
        """Reset health counters"""
        self.error_count = 0
        self.total_operations = 0
        self.processing_times.clear()
        logger.info("[HEALTH] Health counters reset")
    
    def configure_checks(self, check_config: Dict[str, bool]):
        """Configure which health checks to perform"""
        self.checks.update(check_config)
        enabled_checks = [name for name, enabled in self.checks.items() if enabled]
        logger.info(f"[HEALTH] Health checks configured: {enabled_checks}")
    
    def update_thresholds(self, new_thresholds: Dict[str, Any]):
        """Update health check thresholds"""
        self.thresholds.update(new_thresholds)
        logger.info(f"[HEALTH] Health thresholds updated: {new_thresholds}")
