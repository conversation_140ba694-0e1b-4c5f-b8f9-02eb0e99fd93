#!/usr/bin/env python3
"""
Strategy Manager for Market Monitoring Agent

Handles dynamic strategy selection, AI integration, and adaptive parameters.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from .data_structures import MarketMonitoringConfig, TradingSignal, MarketIndicators, MarketRegime

logger = logging.getLogger(__name__)


class StrategyType(Enum):
    """Available strategy types"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    TREND_FOLLOWING = "trend_following"
    SCALPING = "scalping"
    SWING = "swing"
    AI_HYBRID = "ai_hybrid"


@dataclass
class StrategyParameters:
    """Strategy parameters configuration"""
    strategy_type: StrategyType
    confidence_threshold: float
    risk_reward_ratio: float
    max_position_size: float
    stop_loss_multiplier: float
    target_multiplier: float
    indicators_config: Dict[str, Any]
    market_regime_filter: Optional[str] = None
    volatility_filter: Optional[str] = None
    time_filter: Optional[Dict[str, Any]] = None


class StrategyManager:
    """Manages trading strategies and dynamic selection"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.active_strategies = {}
        self.strategy_performance = {}
        self.ai_integration = None
        
        # Initialize default strategies
        self._initialize_default_strategies()
        
        # Strategy selection history
        self.selection_history = []
        
        # Performance tracking
        self.strategy_stats = {
            strategy.value: {
                'signals_generated': 0,
                'successful_signals': 0,
                'failed_signals': 0,
                'avg_confidence': 0.0,
                'last_used': None
            }
            for strategy in StrategyType
        }
    
    def _initialize_default_strategies(self):
        """Initialize default strategy configurations"""
        try:
            # Momentum Strategy
            self.active_strategies[StrategyType.MOMENTUM] = StrategyParameters(
                strategy_type=StrategyType.MOMENTUM,
                confidence_threshold=0.7,
                risk_reward_ratio=1.5,
                max_position_size=2.0,
                stop_loss_multiplier=2.0,
                target_multiplier=3.0,
                indicators_config={
                    'rsi_periods': [14],
                    'ema_periods': [5, 20],
                    'volume_threshold': 1.5,
                    'momentum_threshold': 0.02
                },
                market_regime_filter='bull',
                volatility_filter='medium'
            )
            
            # Mean Reversion Strategy
            self.active_strategies[StrategyType.MEAN_REVERSION] = StrategyParameters(
                strategy_type=StrategyType.MEAN_REVERSION,
                confidence_threshold=0.65,
                risk_reward_ratio=1.2,
                max_position_size=1.5,
                stop_loss_multiplier=1.5,
                target_multiplier=1.8,
                indicators_config={
                    'rsi_periods': [14],
                    'bb_period': 20,
                    'bb_std': 2,
                    'oversold_threshold': 30,
                    'overbought_threshold': 70
                },
                market_regime_filter='sideways',
                volatility_filter='low'
            )
            
            # Breakout Strategy
            self.active_strategies[StrategyType.BREAKOUT] = StrategyParameters(
                strategy_type=StrategyType.BREAKOUT,
                confidence_threshold=0.75,
                risk_reward_ratio=2.0,
                max_position_size=2.5,
                stop_loss_multiplier=2.5,
                target_multiplier=5.0,
                indicators_config={
                    'bb_period': 20,
                    'volume_threshold': 2.0,
                    'breakout_threshold': 0.95,
                    'atr_period': 14
                },
                volatility_filter='high'
            )
            
            # Trend Following Strategy
            self.active_strategies[StrategyType.TREND_FOLLOWING] = StrategyParameters(
                strategy_type=StrategyType.TREND_FOLLOWING,
                confidence_threshold=0.6,
                risk_reward_ratio=2.5,
                max_position_size=3.0,
                stop_loss_multiplier=3.0,
                target_multiplier=7.5,
                indicators_config={
                    'ema_periods': [10, 20, 50],
                    'macd_config': {'fast': 12, 'slow': 26, 'signal': 9},
                    'adx_period': 14,
                    'trend_strength_threshold': 25
                },
                market_regime_filter='bull'
            )
            
            # AI Hybrid Strategy
            self.active_strategies[StrategyType.AI_HYBRID] = StrategyParameters(
                strategy_type=StrategyType.AI_HYBRID,
                confidence_threshold=0.8,
                risk_reward_ratio=1.8,
                max_position_size=2.0,
                stop_loss_multiplier=2.0,
                target_multiplier=3.6,
                indicators_config={
                    'use_all_indicators': True,
                    'ai_weight': 0.7,
                    'technical_weight': 0.3
                }
            )
            
            logger.info(f"[STRATEGY] Initialized {len(self.active_strategies)} default strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing default strategies: {e}")
    
    async def select_optimal_strategy(self, symbol: str, indicators: MarketIndicators, 
                                    market_regime: Optional[MarketRegime] = None,
                                    ai_recommendation: Optional[Dict[str, Any]] = None) -> StrategyParameters:
        """Select optimal strategy based on market conditions and AI input"""
        try:
            # Get strategy scores
            strategy_scores = await self._calculate_strategy_scores(
                symbol, indicators, market_regime, ai_recommendation
            )
            
            # Select best strategy
            best_strategy_type = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy_type]
            
            # Get strategy parameters
            strategy_params = self.active_strategies[best_strategy_type]
            
            # Apply dynamic adjustments
            adjusted_params = await self._apply_dynamic_adjustments(
                strategy_params, indicators, market_regime, best_score
            )
            
            # Record selection
            selection_record = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'selected_strategy': best_strategy_type.value,
                'score': best_score,
                'all_scores': {k.value: v for k, v in strategy_scores.items()},
                'market_regime': market_regime.regime if market_regime else None
            }
            self.selection_history.append(selection_record)
            
            # Update usage stats
            self.strategy_stats[best_strategy_type.value]['last_used'] = datetime.now()
            
            logger.debug(f"[STRATEGY] Selected {best_strategy_type.value} for {symbol} (score: {best_score:.2f})")
            
            return adjusted_params
            
        except Exception as e:
            logger.error(f"[ERROR] Error selecting optimal strategy: {e}")
            # Return default strategy
            return self.active_strategies[StrategyType.MOMENTUM]
    
    async def _calculate_strategy_scores(self, symbol: str, indicators: MarketIndicators,
                                       market_regime: Optional[MarketRegime],
                                       ai_recommendation: Optional[Dict[str, Any]]) -> Dict[StrategyType, float]:
        """Calculate scores for each strategy"""
        try:
            scores = {}
            
            for strategy_type, params in self.active_strategies.items():
                score = 0.0
                
                # Base score from historical performance
                strategy_stats = self.strategy_stats[strategy_type.value]
                if strategy_stats['signals_generated'] > 0:
                    success_rate = strategy_stats['successful_signals'] / strategy_stats['signals_generated']
                    score += success_rate * 0.3
                else:
                    score += 0.5  # Neutral score for untested strategies
                
                # Market regime compatibility
                if market_regime and params.market_regime_filter:
                    if market_regime.regime == params.market_regime_filter:
                        score += 0.3 * market_regime.confidence
                    else:
                        score -= 0.2
                
                # Volatility compatibility
                if market_regime and params.volatility_filter:
                    if market_regime.volatility_level == params.volatility_filter:
                        score += 0.2
                    elif market_regime.volatility_level == 'high' and params.volatility_filter == 'medium':
                        score += 0.1  # Partial compatibility
                
                # Indicator-based scoring
                indicator_score = await self._score_strategy_indicators(strategy_type, indicators)
                score += indicator_score * 0.3
                
                # AI recommendation integration
                if ai_recommendation and strategy_type == StrategyType.AI_HYBRID:
                    ai_confidence = ai_recommendation.get('confidence', 0.5)
                    score += ai_confidence * 0.4
                
                # Time-based filters
                if params.time_filter:
                    time_score = self._evaluate_time_filter(params.time_filter)
                    score *= time_score
                
                scores[strategy_type] = max(0.0, min(1.0, score))  # Clamp between 0 and 1
            
            return scores
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating strategy scores: {e}")
            # Return equal scores as fallback
            return {strategy: 0.5 for strategy in self.active_strategies.keys()}
    
    async def _score_strategy_indicators(self, strategy_type: StrategyType, indicators: MarketIndicators) -> float:
        """Score strategy based on current indicator values"""
        try:
            score = 0.0
            
            if strategy_type == StrategyType.MOMENTUM:
                # Score based on momentum indicators
                if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                    if 40 <= indicators.rsi_14 <= 60:
                        score += 0.3  # Good momentum range
                    elif indicators.rsi_14 > 70:
                        score += 0.5  # Strong momentum
                
                if hasattr(indicators, 'macd_histogram') and indicators.macd_histogram:
                    if indicators.macd_histogram > 0:
                        score += 0.3
            
            elif strategy_type == StrategyType.MEAN_REVERSION:
                # Score based on mean reversion indicators
                if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                    if indicators.rsi_14 < 30 or indicators.rsi_14 > 70:
                        score += 0.5  # Good for mean reversion
                
                if hasattr(indicators, 'bb_position') and indicators.bb_position is not None:
                    if indicators.bb_position < 0.2 or indicators.bb_position > 0.8:
                        score += 0.3  # Near bands
            
            elif strategy_type == StrategyType.BREAKOUT:
                # Score based on breakout indicators
                if hasattr(indicators, 'bb_position') and indicators.bb_position is not None:
                    if indicators.bb_position > 0.9 or indicators.bb_position < 0.1:
                        score += 0.4  # Near breakout levels
                
                if hasattr(indicators, 'volume_ratio') and indicators.volume_ratio:
                    if indicators.volume_ratio > 1.5:
                        score += 0.4  # High volume supports breakout
            
            elif strategy_type == StrategyType.TREND_FOLLOWING:
                # Score based on trend indicators
                if (hasattr(indicators, 'ema_10') and hasattr(indicators, 'ema_20') and
                    indicators.ema_10 and indicators.ema_20):
                    if indicators.ema_10 > indicators.ema_20:
                        score += 0.3  # Uptrend
                
                if hasattr(indicators, 'macd') and hasattr(indicators, 'macd_signal'):
                    if indicators.macd and indicators.macd_signal:
                        if indicators.macd > indicators.macd_signal:
                            score += 0.3  # Bullish MACD
            
            return min(1.0, score)
            
        except Exception as e:
            logger.error(f"[ERROR] Error scoring strategy indicators: {e}")
            return 0.5
    
    def _evaluate_time_filter(self, time_filter: Dict[str, Any]) -> float:
        """Evaluate time-based filters"""
        try:
            current_time = datetime.now().time()
            
            # Market hours filter
            if 'market_hours_only' in time_filter and time_filter['market_hours_only']:
                market_open = datetime.strptime('09:15', '%H:%M').time()
                market_close = datetime.strptime('15:30', '%H:%M').time()
                
                if not (market_open <= current_time <= market_close):
                    return 0.5  # Reduce score outside market hours
            
            # Specific time ranges
            if 'preferred_hours' in time_filter:
                preferred_start = datetime.strptime(time_filter['preferred_hours']['start'], '%H:%M').time()
                preferred_end = datetime.strptime(time_filter['preferred_hours']['end'], '%H:%M').time()
                
                if preferred_start <= current_time <= preferred_end:
                    return 1.2  # Boost score during preferred hours
            
            return 1.0  # Neutral score
            
        except Exception as e:
            logger.error(f"[ERROR] Error evaluating time filter: {e}")
            return 1.0
    
    async def _apply_dynamic_adjustments(self, strategy_params: StrategyParameters,
                                       indicators: MarketIndicators,
                                       market_regime: Optional[MarketRegime],
                                       strategy_score: float) -> StrategyParameters:
        """Apply dynamic adjustments to strategy parameters"""
        try:
            # Create a copy to avoid modifying the original
            adjusted_params = StrategyParameters(
                strategy_type=strategy_params.strategy_type,
                confidence_threshold=strategy_params.confidence_threshold,
                risk_reward_ratio=strategy_params.risk_reward_ratio,
                max_position_size=strategy_params.max_position_size,
                stop_loss_multiplier=strategy_params.stop_loss_multiplier,
                target_multiplier=strategy_params.target_multiplier,
                indicators_config=strategy_params.indicators_config.copy(),
                market_regime_filter=strategy_params.market_regime_filter,
                volatility_filter=strategy_params.volatility_filter,
                time_filter=strategy_params.time_filter
            )
            
            # Adjust confidence threshold based on market regime
            if market_regime:
                if market_regime.confidence > 0.8:
                    adjusted_params.confidence_threshold *= 0.9  # Lower threshold for high confidence regime
                elif market_regime.confidence < 0.5:
                    adjusted_params.confidence_threshold *= 1.1  # Higher threshold for uncertain regime
                
                # Adjust risk parameters based on volatility
                if market_regime.volatility_level == 'high':
                    adjusted_params.stop_loss_multiplier *= 1.2  # Wider stops in high volatility
                    adjusted_params.max_position_size *= 0.8  # Smaller positions
                elif market_regime.volatility_level == 'low':
                    adjusted_params.stop_loss_multiplier *= 0.9  # Tighter stops in low volatility
                    adjusted_params.max_position_size *= 1.1  # Larger positions
            
            # Adjust based on strategy performance score
            if strategy_score > 0.8:
                adjusted_params.max_position_size *= 1.1  # Increase size for high-scoring strategies
            elif strategy_score < 0.4:
                adjusted_params.max_position_size *= 0.8  # Reduce size for low-scoring strategies
                adjusted_params.confidence_threshold *= 1.2  # Higher confidence required
            
            return adjusted_params
            
        except Exception as e:
            logger.error(f"[ERROR] Error applying dynamic adjustments: {e}")
            return strategy_params
    
    async def update_strategy_performance(self, signal: TradingSignal, outcome: str, pnl: Optional[float] = None):
        """Update strategy performance based on signal outcome"""
        try:
            strategy_name = signal.strategy
            
            # Map strategy name to enum if needed
            strategy_type = None
            for st in StrategyType:
                if st.value in strategy_name.lower():
                    strategy_type = st.value
                    break
            
            if not strategy_type:
                strategy_type = StrategyType.AI_HYBRID.value  # Default fallback
            
            # Update statistics
            stats = self.strategy_stats[strategy_type]
            stats['signals_generated'] += 1
            
            if outcome == 'success':
                stats['successful_signals'] += 1
            elif outcome == 'failure':
                stats['failed_signals'] += 1
            
            # Update average confidence
            total_signals = stats['signals_generated']
            current_avg = stats['avg_confidence']
            stats['avg_confidence'] = ((current_avg * (total_signals - 1)) + signal.confidence) / total_signals
            
            logger.debug(f"[STRATEGY] Updated performance for {strategy_type}: {outcome}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy performance: {e}")
    
    def get_strategy_performance(self) -> Dict[str, Any]:
        """Get comprehensive strategy performance metrics"""
        try:
            performance = {
                'strategy_stats': self.strategy_stats.copy(),
                'total_selections': len(self.selection_history),
                'strategy_distribution': {},
                'recent_selections': []
            }
            
            # Calculate strategy distribution
            if self.selection_history:
                recent_selections = self.selection_history[-100:]  # Last 100 selections
                strategy_counts = {}
                
                for selection in recent_selections:
                    strategy = selection['selected_strategy']
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
                
                total_recent = len(recent_selections)
                performance['strategy_distribution'] = {
                    strategy: (count / total_recent) * 100
                    for strategy, count in strategy_counts.items()
                }
                
                performance['recent_selections'] = recent_selections[-10:]  # Last 10
            
            return performance
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting strategy performance: {e}")
            return {}
    
    def get_active_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Get active strategy configurations"""
        return {
            strategy_type.value: asdict(params)
            for strategy_type, params in self.active_strategies.items()
        }
    
    def update_strategy_config(self, strategy_type: str, config_updates: Dict[str, Any]):
        """Update strategy configuration"""
        try:
            # Find strategy type enum
            strategy_enum = None
            for st in StrategyType:
                if st.value == strategy_type:
                    strategy_enum = st
                    break
            
            if not strategy_enum or strategy_enum not in self.active_strategies:
                logger.error(f"[STRATEGY] Unknown strategy type: {strategy_type}")
                return
            
            # Update configuration
            current_params = self.active_strategies[strategy_enum]
            
            for key, value in config_updates.items():
                if hasattr(current_params, key):
                    setattr(current_params, key, value)
                elif key == 'indicators_config':
                    current_params.indicators_config.update(value)
            
            logger.info(f"[STRATEGY] Updated configuration for {strategy_type}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy config: {e}")
    
    def reset_performance_stats(self):
        """Reset strategy performance statistics"""
        for strategy in self.strategy_stats:
            self.strategy_stats[strategy] = {
                'signals_generated': 0,
                'successful_signals': 0,
                'failed_signals': 0,
                'avg_confidence': 0.0,
                'last_used': None
            }
        
        self.selection_history.clear()
        logger.info("[STRATEGY] Performance statistics reset")
