#!/usr/bin/env python3
"""
Resource Manager for Market Monitoring Agent

Manages system resources, monitors usage, and implements scaling strategies.
"""

import logging
import asyncio
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import deque

from .data_structures import MarketMonitoringConfig

logger = logging.getLogger(__name__)


class ResourceManager:
    """Manages system resources and implements intelligent scaling"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.is_monitoring = False
        self.resource_history = deque(maxlen=1000)
        self.scaling_actions = []
        
        # Resource thresholds
        self.thresholds = {
            'memory_warning': config.performance_config.get('memory_warning_threshold', 80),
            'memory_critical': config.performance_config.get('memory_critical_threshold', 90),
            'cpu_warning': config.performance_config.get('cpu_warning_threshold', 80),
            'cpu_critical': config.performance_config.get('cpu_critical_threshold', 95),
            'disk_warning': config.performance_config.get('disk_warning_threshold', 85),
            'disk_critical': config.performance_config.get('disk_critical_threshold', 95)
        }
        
        # Scaling parameters
        self.max_symbols = config.market_data_config.get('max_symbols', 500)
        self.current_symbol_limit = self.max_symbols
        self.processing_interval = config.market_data_config.get('processing_interval', 30)
        self.current_processing_interval = self.processing_interval
    
    async def start_monitoring(self):
        """Start resource monitoring"""
        try:
            if self.is_monitoring:
                return
            
            self.is_monitoring = True
            logger.info("[RESOURCE] Resource monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting resource monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop resource monitoring"""
        try:
            self.is_monitoring = False
            logger.info("[RESOURCE] Resource monitoring stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping resource monitoring: {e}")
    
    async def check_system_resources(self) -> Dict[str, Any]:
        """Check current system resource status"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)
            
            # Network I/O
            network = psutil.net_io_counters()
            
            # Process information
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            resource_status = {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'cpu_freq': cpu_freq.current if cpu_freq else None,
                'memory_percent': memory_percent,
                'memory_available_gb': memory_available_gb,
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk_percent,
                'disk_free_gb': disk_free_gb,
                'disk_total_gb': disk.total / (1024**3),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'process_memory_mb': process_memory.rss / (1024**2),
                'process_cpu_percent': process_cpu,
                'adequate': self._assess_resource_adequacy(cpu_percent, memory_percent, disk_percent)
            }
            
            # Store in history
            self.resource_history.append(resource_status)
            
            return resource_status
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking system resources: {e}")
            return {
                'timestamp': datetime.now(),
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'adequate': False,
                'error': str(e)
            }
    
    def _assess_resource_adequacy(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> bool:
        """Assess if current resources are adequate"""
        return (
            cpu_percent < self.thresholds['cpu_warning'] and
            memory_percent < self.thresholds['memory_warning'] and
            disk_percent < self.thresholds['disk_warning']
        )
    
    async def check_resource_usage(self) -> Dict[str, Any]:
        """Check resource usage and trigger scaling if needed"""
        try:
            resource_status = await self.check_system_resources()
            
            # Check for scaling needs
            scaling_needed = await self._check_scaling_needs(resource_status)
            
            if scaling_needed:
                await self._apply_scaling_actions(resource_status)
            
            return resource_status
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking resource usage: {e}")
            return {}
    
    async def _check_scaling_needs(self, resource_status: Dict[str, Any]) -> bool:
        """Check if scaling actions are needed"""
        try:
            cpu_percent = resource_status.get('cpu_percent', 0)
            memory_percent = resource_status.get('memory_percent', 0)
            disk_percent = resource_status.get('disk_percent', 0)
            
            # Check for critical thresholds
            if (cpu_percent > self.thresholds['cpu_critical'] or
                memory_percent > self.thresholds['memory_critical'] or
                disk_percent > self.thresholds['disk_critical']):
                return True
            
            # Check for sustained high usage
            if len(self.resource_history) >= 5:
                recent_resources = list(self.resource_history)[-5:]
                avg_cpu = sum(r.get('cpu_percent', 0) for r in recent_resources) / 5
                avg_memory = sum(r.get('memory_percent', 0) for r in recent_resources) / 5
                
                if (avg_cpu > self.thresholds['cpu_warning'] or
                    avg_memory > self.thresholds['memory_warning']):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking scaling needs: {e}")
            return False
    
    async def _apply_scaling_actions(self, resource_status: Dict[str, Any]):
        """Apply scaling actions based on resource status"""
        try:
            cpu_percent = resource_status.get('cpu_percent', 0)
            memory_percent = resource_status.get('memory_percent', 0)
            
            actions_taken = []
            
            # Reduce symbol monitoring if high resource usage
            if (cpu_percent > self.thresholds['cpu_warning'] or 
                memory_percent > self.thresholds['memory_warning']):
                
                new_limit = max(50, int(self.current_symbol_limit * 0.8))
                if new_limit < self.current_symbol_limit:
                    self.current_symbol_limit = new_limit
                    actions_taken.append(f"Reduced symbol limit to {new_limit}")
            
            # Increase processing interval if high CPU usage
            if cpu_percent > self.thresholds['cpu_warning']:
                new_interval = min(120, int(self.current_processing_interval * 1.5))
                if new_interval > self.current_processing_interval:
                    self.current_processing_interval = new_interval
                    actions_taken.append(f"Increased processing interval to {new_interval}s")
            
            # Scale back up if resources are available
            elif (cpu_percent < self.thresholds['cpu_warning'] * 0.7 and
                  memory_percent < self.thresholds['memory_warning'] * 0.7):
                
                # Increase symbol limit
                if self.current_symbol_limit < self.max_symbols:
                    new_limit = min(self.max_symbols, int(self.current_symbol_limit * 1.2))
                    self.current_symbol_limit = new_limit
                    actions_taken.append(f"Increased symbol limit to {new_limit}")
                
                # Decrease processing interval
                if self.current_processing_interval > self.processing_interval:
                    new_interval = max(self.processing_interval, int(self.current_processing_interval * 0.8))
                    self.current_processing_interval = new_interval
                    actions_taken.append(f"Decreased processing interval to {new_interval}s")
            
            if actions_taken:
                action_record = {
                    'timestamp': datetime.now(),
                    'resource_status': resource_status,
                    'actions': actions_taken
                }
                self.scaling_actions.append(action_record)
                logger.info(f"[SCALING] Applied scaling actions: {', '.join(actions_taken)}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error applying scaling actions: {e}")
    
    def get_current_limits(self) -> Dict[str, Any]:
        """Get current resource limits"""
        return {
            'symbol_limit': self.current_symbol_limit,
            'processing_interval': self.current_processing_interval,
            'max_symbols': self.max_symbols,
            'base_processing_interval': self.processing_interval
        }
    
    def get_resource_history(self, hours_back: int = 1) -> List[Dict[str, Any]]:
        """Get resource usage history"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            return [
                r for r in self.resource_history 
                if r.get('timestamp', datetime.min) > cutoff_time
            ]
        except Exception as e:
            logger.error(f"[ERROR] Error getting resource history: {e}")
            return []
    
    def get_scaling_history(self) -> List[Dict[str, Any]]:
        """Get scaling action history"""
        return self.scaling_actions.copy()
    
    async def optimize_for_symbol_count(self, symbol_count: int) -> Dict[str, Any]:
        """Optimize resource allocation for specific symbol count"""
        try:
            # Get current resource status
            resource_status = await self.check_system_resources()
            
            # Calculate optimal parameters
            memory_gb = resource_status.get('memory_total_gb', 8)
            cpu_count = resource_status.get('cpu_count', 4)
            
            # Estimate resource requirements per symbol
            memory_per_symbol_mb = 2  # Estimated 2MB per symbol
            cpu_per_symbol_percent = 0.1  # Estimated 0.1% CPU per symbol
            
            # Calculate limits
            memory_limit = int((memory_gb * 1024 * 0.7) / memory_per_symbol_mb)  # 70% of available memory
            cpu_limit = int((cpu_count * 100 * 0.8) / cpu_per_symbol_percent)  # 80% of available CPU
            
            recommended_limit = min(symbol_count, memory_limit, cpu_limit, self.max_symbols)
            
            # Calculate optimal processing interval
            base_interval = 30
            if symbol_count > 200:
                optimal_interval = base_interval + (symbol_count - 200) * 0.1
            else:
                optimal_interval = base_interval
            
            optimization = {
                'requested_symbols': symbol_count,
                'recommended_limit': recommended_limit,
                'optimal_processing_interval': int(optimal_interval),
                'memory_limit': memory_limit,
                'cpu_limit': cpu_limit,
                'resource_status': resource_status
            }
            
            logger.info(f"[OPTIMIZATION] For {symbol_count} symbols: limit={recommended_limit}, interval={optimal_interval}s")
            
            return optimization
            
        except Exception as e:
            logger.error(f"[ERROR] Error optimizing for symbol count: {e}")
            return {
                'requested_symbols': symbol_count,
                'recommended_limit': min(symbol_count, 100),
                'optimal_processing_interval': 60
            }
    
    async def get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        try:
            recommendations = []
            
            if len(self.resource_history) < 10:
                return ["Insufficient data for recommendations"]
            
            # Analyze recent resource usage
            recent_resources = list(self.resource_history)[-10:]
            avg_cpu = sum(r.get('cpu_percent', 0) for r in recent_resources) / 10
            avg_memory = sum(r.get('memory_percent', 0) for r in recent_resources) / 10
            
            # CPU recommendations
            if avg_cpu > 80:
                recommendations.append("Consider reducing symbol count or increasing processing interval")
                recommendations.append("Enable parallel processing if not already active")
            elif avg_cpu < 30:
                recommendations.append("System can handle more symbols or faster processing")
            
            # Memory recommendations
            if avg_memory > 80:
                recommendations.append("Consider implementing more aggressive data cleanup")
                recommendations.append("Reduce historical data retention period")
            elif avg_memory < 40:
                recommendations.append("System can handle larger datasets or more indicators")
            
            # Scaling recommendations
            if len(self.scaling_actions) > 5:
                recommendations.append("Frequent scaling detected - consider adjusting base limits")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting performance recommendations: {e}")
            return ["Error generating recommendations"]
    
    def reset_scaling_parameters(self):
        """Reset scaling parameters to defaults"""
        self.current_symbol_limit = self.max_symbols
        self.current_processing_interval = self.processing_interval
        logger.info("[SCALING] Reset scaling parameters to defaults")
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get comprehensive resource summary"""
        try:
            if not self.resource_history:
                return {"error": "No resource data available"}
            
            latest = self.resource_history[-1]
            
            # Calculate averages over last hour
            recent = self.get_resource_history(hours_back=1)
            if recent:
                avg_cpu = sum(r.get('cpu_percent', 0) for r in recent) / len(recent)
                avg_memory = sum(r.get('memory_percent', 0) for r in recent) / len(recent)
                max_cpu = max(r.get('cpu_percent', 0) for r in recent)
                max_memory = max(r.get('memory_percent', 0) for r in recent)
            else:
                avg_cpu = avg_memory = max_cpu = max_memory = 0
            
            return {
                'current': {
                    'cpu_percent': latest.get('cpu_percent', 0),
                    'memory_percent': latest.get('memory_percent', 0),
                    'disk_percent': latest.get('disk_percent', 0),
                    'process_memory_mb': latest.get('process_memory_mb', 0)
                },
                'averages_1h': {
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory
                },
                'peaks_1h': {
                    'cpu_percent': max_cpu,
                    'memory_percent': max_memory
                },
                'limits': self.get_current_limits(),
                'scaling_actions_count': len(self.scaling_actions),
                'thresholds': self.thresholds,
                'adequate': latest.get('adequate', False)
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting resource summary: {e}")
            return {"error": str(e)}
