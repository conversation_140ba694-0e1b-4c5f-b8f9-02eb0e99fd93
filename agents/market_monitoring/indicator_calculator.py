#!/usr/bin/env python3
"""
Indicator Calculator for Market Monitoring Agent

Handles calculation of technical indicators using Polars and PyArrow for performance.
"""

import logging
import polars as pl
from datetime import datetime
from typing import Dict, List, Any, Optional

from .data_structures import MarketIndicators, OHLCV, MarketMonitoringConfig

logger = logging.getLogger(__name__)

# Check for polars-talib availability
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    logger.warning("[WARN] polars-talib not available, using fallback calculations")


class IndicatorCalculator:
    """Calculates technical indicators for market data"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.indicators = {}  # symbol -> timeframe -> indicators
    
    async def calculate_indicators(self, symbol: str, candles: List[OHLCV], timeframe: str = '5min') -> Optional[MarketIndicators]:
        """Calculate technical indicators for a symbol"""
        try:
            # Determine minimum candles needed
            min_candles_needed = self._get_min_candles_for_timeframe(timeframe)
            
            if len(candles) < min_candles_needed:
                logger.debug(f"[DEBUG] Insufficient {timeframe} data for {symbol}: {len(candles)} candles (need {min_candles_needed}+)")
                return None

            logger.debug(f"[INDICATOR_DEBUG] Calculating indicators for {symbol} {timeframe} with {len(candles)} candles")

            # Convert to Polars DataFrame for fast vectorized operations
            df = pl.DataFrame({
                'timestamp': [c.timestamp for c in candles],
                'open': [c.open for c in candles],
                'high': [c.high for c in candles],
                'low': [c.low for c in candles],
                'close': [c.close for c in candles],
                'volume': [c.volume for c in candles]
            })

            # Calculate indicators
            indicators = MarketIndicators(
                symbol=symbol,
                timestamp=datetime.now(),
                timeframe=timeframe
            )

            indicator_config = self.config.market_data_config.get('indicators', {})

            # Calculate all indicators in one pass using Polars lazy evaluation
            df_with_indicators = df.lazy()

            # Moving Averages (EMA and SMA)
            df_with_indicators = await self._calculate_moving_averages(df_with_indicators, indicator_config)
            
            # RSI calculation
            df_with_indicators = await self._calculate_rsi(df_with_indicators, indicator_config)
            
            # MACD calculation
            df_with_indicators = await self._calculate_macd(df_with_indicators, indicator_config)
            
            # ATR calculation
            df_with_indicators = await self._calculate_atr(df_with_indicators, indicator_config)
            
            # VWAP calculation
            df_with_indicators = await self._calculate_vwap(df_with_indicators)
            
            # Bollinger Bands calculation
            df_with_indicators = await self._calculate_bollinger_bands(df_with_indicators, indicator_config)

            # Execute lazy evaluation and get the result
            result_df = df_with_indicators.collect()

            # Extract the latest values for each indicator
            if len(result_df) > 0:
                latest_row = result_df.row(-1, named=True)
                self._populate_indicators(indicators, latest_row, indicator_config)

            # Store indicators
            if symbol not in self.indicators:
                self.indicators[symbol] = {}
            self.indicators[symbol][timeframe] = indicators
            
            logger.debug(f"[INDICATORS] Calculated {timeframe} indicators for {symbol}: RSI={getattr(indicators, 'rsi_14', 'N/A')}, EMA20={getattr(indicators, 'ema_20', 'N/A')}")
            
            return indicators

        except Exception as e:
            logger.error(f"Error calculating indicators for {symbol} {timeframe}: {e}")
            return None
    
    async def _calculate_moving_averages(self, df_lazy, indicator_config):
        """Calculate EMA and SMA indicators"""
        # EMA calculations
        for period in indicator_config.get('ema_periods', [5, 10, 13, 20, 21, 30, 50, 100]):
            if POLARS_TALIB_AVAILABLE:
                df_lazy = df_lazy.with_columns(
                    ta.ema(pl.col('close'), period).alias(f'ema_{period}')
                )
            else:
                # Fallback: Manual EMA calculation
                alpha = 2.0 / (period + 1)
                df_lazy = df_lazy.with_columns(
                    pl.col('close').ewm_mean(alpha=alpha).alias(f'ema_{period}')
                )

        # SMA calculations
        for period in indicator_config.get('sma_periods', [20]):
            if POLARS_TALIB_AVAILABLE:
                df_lazy = df_lazy.with_columns(
                    ta.sma(pl.col('close'), period).alias(f'sma_{period}')
                )
            else:
                # Fallback: Manual SMA calculation
                df_lazy = df_lazy.with_columns(
                    pl.col('close').rolling_mean(window_size=period).alias(f'sma_{period}')
                )
        
        return df_lazy
    
    async def _calculate_rsi(self, df_lazy, indicator_config):
        """Calculate RSI indicators"""
        for period in indicator_config.get('rsi_periods', [5, 14]):
            if POLARS_TALIB_AVAILABLE:
                df_lazy = df_lazy.with_columns(
                    ta.rsi(pl.col('close'), period).alias(f'rsi_{period}')
                )
            else:
                # Fallback: Manual RSI calculation
                df_lazy = df_lazy.with_columns([
                    (pl.col('close') - pl.col('close').shift(1)).alias('price_change'),
                ]).with_columns([
                    pl.when(pl.col('price_change') > 0)
                    .then(pl.col('price_change'))
                    .otherwise(0).alias('gain'),
                    pl.when(pl.col('price_change') < 0)
                    .then(-pl.col('price_change'))
                    .otherwise(0).alias('loss')
                ]).with_columns([
                    pl.col('gain').ewm_mean(alpha=1.0/period).alias('avg_gain'),
                    pl.col('loss').ewm_mean(alpha=1.0/period).alias('avg_loss')
                ]).with_columns([
                    (100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss'))))).alias(f'rsi_{period}')
                ])
        
        return df_lazy
    
    async def _calculate_macd(self, df_lazy, indicator_config):
        """Calculate MACD indicators"""
        macd_config = indicator_config.get('macd_config', {})
        fast_period = macd_config.get('fast', 12)
        slow_period = macd_config.get('slow', 26)
        signal_period = macd_config.get('signal', 9)

        if POLARS_TALIB_AVAILABLE:
            df_lazy = df_lazy.with_columns([
                ta.macd(pl.col('close'), fast_period, slow_period, signal_period).alias('macd'),
                ta.macd_signal(pl.col('close'), fast_period, slow_period, signal_period).alias('macd_signal'),
                ta.macd_histogram(pl.col('close'), fast_period, slow_period, signal_period).alias('macd_histogram')
            ])
        else:
            # Fallback: Manual MACD calculation
            fast_alpha = 2.0 / (fast_period + 1)
            slow_alpha = 2.0 / (slow_period + 1)
            signal_alpha = 2.0 / (signal_period + 1)

            df_lazy = df_lazy.with_columns([
                pl.col('close').ewm_mean(alpha=fast_alpha).alias('ema_fast'),
                pl.col('close').ewm_mean(alpha=slow_alpha).alias('ema_slow')
            ]).with_columns([
                (pl.col('ema_fast') - pl.col('ema_slow')).alias('macd')
            ]).with_columns([
                pl.col('macd').ewm_mean(alpha=signal_alpha).alias('macd_signal')
            ]).with_columns([
                (pl.col('macd') - pl.col('macd_signal')).alias('macd_histogram')
            ])
        
        return df_lazy
    
    async def _calculate_atr(self, df_lazy, indicator_config):
        """Calculate ATR (Average True Range)"""
        atr_period = indicator_config.get('atr_period', 14)
        
        if POLARS_TALIB_AVAILABLE:
            df_lazy = df_lazy.with_columns(
                ta.atr(pl.col('high'), pl.col('low'), pl.col('close'), atr_period).alias('atr')
            )
        else:
            # Manual ATR calculation
            df_lazy = df_lazy.with_columns([
                pl.max_horizontal([
                    pl.col('high') - pl.col('low'),
                    (pl.col('high') - pl.col('close').shift(1)).abs(),
                    (pl.col('low') - pl.col('close').shift(1)).abs()
                ]).alias('true_range')
            ]).with_columns([
                pl.col('true_range').rolling_mean(window_size=atr_period).alias('atr')
            ])
        
        return df_lazy
    
    async def _calculate_vwap(self, df_lazy):
        """Calculate VWAP (Volume Weighted Average Price)"""
        df_lazy = df_lazy.with_columns([
            (pl.col('close') * pl.col('volume')).alias('price_volume'),
            pl.col('volume').cum_sum().alias('cumulative_volume'),
            (pl.col('close') * pl.col('volume')).cum_sum().alias('cumulative_price_volume')
        ]).with_columns([
            (pl.col('cumulative_price_volume') / pl.col('cumulative_volume')).alias('vwap')
        ])
        
        return df_lazy
    
    async def _calculate_bollinger_bands(self, df_lazy, indicator_config):
        """Calculate Bollinger Bands"""
        bb_config = indicator_config.get('bollinger_bands', {})
        period = bb_config.get('period', 20)
        std_dev = bb_config.get('std_dev', 2)
        
        if POLARS_TALIB_AVAILABLE:
            df_lazy = df_lazy.with_columns([
                ta.bbands_upper(pl.col('close'), period, std_dev).alias('bb_upper'),
                ta.bbands_middle(pl.col('close'), period).alias('bb_middle'),
                ta.bbands_lower(pl.col('close'), period, std_dev).alias('bb_lower')
            ])
        else:
            # Manual Bollinger Bands calculation
            df_lazy = df_lazy.with_columns([
                pl.col('close').rolling_mean(window_size=period).alias('bb_middle'),
                pl.col('close').rolling_std(window_size=period).alias('bb_std')
            ]).with_columns([
                (pl.col('bb_middle') + (pl.col('bb_std') * std_dev)).alias('bb_upper'),
                (pl.col('bb_middle') - (pl.col('bb_std') * std_dev)).alias('bb_lower')
            ])
        
        # Calculate Bollinger Band position (0 = lower band, 1 = upper band)
        df_lazy = df_lazy.with_columns([
            ((pl.col('close') - pl.col('bb_lower')) / (pl.col('bb_upper') - pl.col('bb_lower'))).alias('bb_position')
        ])
        
        return df_lazy
    
    def _populate_indicators(self, indicators: MarketIndicators, latest_row: Dict, indicator_config: Dict):
        """Populate indicator object with calculated values"""
        # Set EMA values
        for period in indicator_config.get('ema_periods', [5, 10, 13, 20, 21, 30, 50, 100]):
            if f'ema_{period}' in latest_row:
                setattr(indicators, f'ema_{period}', latest_row[f'ema_{period}'])

        # Set SMA values
        for period in indicator_config.get('sma_periods', [20]):
            if f'sma_{period}' in latest_row:
                setattr(indicators, f'sma_{period}', latest_row[f'sma_{period}'])

        # Set RSI values
        for period in indicator_config.get('rsi_periods', [5, 14]):
            if f'rsi_{period}' in latest_row:
                setattr(indicators, f'rsi_{period}', latest_row[f'rsi_{period}'])

        # Set MACD values
        if 'macd' in latest_row:
            indicators.macd = latest_row['macd']
        if 'macd_signal' in latest_row:
            indicators.macd_signal = latest_row['macd_signal']
        if 'macd_histogram' in latest_row:
            indicators.macd_histogram = latest_row['macd_histogram']

        # Set other indicators
        if 'atr' in latest_row:
            indicators.atr = latest_row['atr']
        if 'vwap' in latest_row:
            indicators.vwap = latest_row['vwap']
        if 'bb_upper' in latest_row:
            indicators.bb_upper = latest_row['bb_upper']
        if 'bb_middle' in latest_row:
            indicators.bb_middle = latest_row['bb_middle']
        if 'bb_lower' in latest_row:
            indicators.bb_lower = latest_row['bb_lower']
        if 'bb_position' in latest_row:
            indicators.bb_position = latest_row['bb_position']
        
        # Calculate volume ratio (current volume vs average volume)
        # This would need historical volume data - placeholder for now
        indicators.volume_ratio = 1.0
    
    def _get_min_candles_for_timeframe(self, timeframe: str) -> int:
        """Get minimum candles needed for indicator calculation based on timeframe"""
        timeframe_requirements = {
            '1min': 50,   # 50 minutes of data
            '5min': 20,   # 100 minutes of data
            '15min': 10,  # 150 minutes of data
            '30min': 8,   # 240 minutes of data
            '1hr': 6      # 360 minutes of data
        }
        return timeframe_requirements.get(timeframe, 20)
    
    def get_indicators(self, symbol: str, timeframe: str = '5min') -> Optional[MarketIndicators]:
        """Get indicators for a symbol and timeframe"""
        symbol_indicators = self.indicators.get(symbol, {})
        if isinstance(symbol_indicators, dict):
            return symbol_indicators.get(timeframe)
        else:
            # Backward compatibility
            return symbol_indicators if timeframe == '5min' else None
    
    def get_all_indicators(self) -> Dict[str, Dict[str, MarketIndicators]]:
        """Get all calculated indicators"""
        return self.indicators.copy()
    
    def clear_old_indicators(self, symbols_to_keep: List[str] = None):
        """Clear indicators for symbols not in the keep list"""
        if symbols_to_keep is None:
            return
        
        symbols_to_remove = [symbol for symbol in self.indicators.keys() if symbol not in symbols_to_keep]
        for symbol in symbols_to_remove:
            del self.indicators[symbol]
        
        if symbols_to_remove:
            logger.info(f"[CLEANUP] Cleared indicators for {len(symbols_to_remove)} symbols")
