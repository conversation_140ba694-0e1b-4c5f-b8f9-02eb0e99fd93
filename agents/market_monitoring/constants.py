# agents/market_monitoring/constants.py

# Timeframe definitions
TIMEFRAME_MAP = {
    "1min": "1m",
    "5min": "5m",
    "15min": "15m",
    "30min": "30m",
    "1hr": "1h"
}

TIMEFRAME_MULTIPLIERS = {
    "15min": 3,   # 3 x 5min = 15min
    "30min": 6,   # 6 x 5min = 30min
    "1hr": 12     # 12 x 5min = 1hr
}

# Default periods for indicators
DEFAULT_EMA_PERIODS = [5, 10, 13, 20, 21, 30, 50, 100]
DEFAULT_SMA_PERIODS = [20]
DEFAULT_RSI_PERIODS = [5, 14]
DEFAULT_ATR_PERIOD = 14
DEFAULT_MACD_FAST_PERIOD = 12
DEFAULT_MACD_SLOW_PERIOD = 26
DEFAULT_MACD_SIGNAL_PERIOD = 9

# Data storage paths
LIVE_DATA_DIR = "data/live"
HISTORICAL_DATA_DIR = "data/historical"
LIVE_5MIN_PARQUET = f"{LIVE_DATA_DIR}/live_5min.parquet"
HISTORICAL_5MIN_PARQUET = f"{HISTORICAL_DATA_DIR}/historical_5min.parquet"

# SmartAPI configuration
SMARTAPI_INTERVAL_FIVE_MINUTE = "FIVE_MINUTE"
SMARTAPI_MAX_RETRIES = 5
SMARTAPI_BASE_DELAY = 2
SMARTAPI_RATE_LIMIT_DELAY = 0.75

# Market hours
MARKET_OPEN_HOUR = 9
MARKET_OPEN_MINUTE = 15
MARKET_CLOSE_HOUR = 15
MARKET_CLOSE_MINUTE = 30
MARKET_START_BUFFER_HOUR = 9
MARKET_START_BUFFER_MINUTE = 45

# Data retention
LIVE_DATA_RETENTION_DAYS = 35 # Keep 35 days of live data for analysis

# Logging
LOG_DIR = "logs"
SIGNAL_LOG_FILE = f"{LOG_DIR}/signals.log"
PERFORMANCE_METRICS_FILE = f"{LOG_DIR}/performance_metrics.log"

# Default values for configuration validation
DEFAULT_CONFIG_VALUES = {
    "market_data": {
        "timeframes": ["5min", "15min", "30min", "1hr"],
        "max_candles_per_timeframe": 1000,
        "indicators": {
            "ema_periods": DEFAULT_EMA_PERIODS,
            "sma_periods": DEFAULT_SMA_PERIODS,
            "rsi_periods": DEFAULT_RSI_PERIODS,
            "atr_period": DEFAULT_ATR_PERIOD,
            "macd_config": {
                "fast": DEFAULT_MACD_FAST_PERIOD,
                "slow": DEFAULT_MACD_SLOW_PERIOD,
                "signal": DEFAULT_MACD_SIGNAL_PERIOD
            }
        }
    },
    "environment": {
        "regime_detection": {
            "breadth_threshold": 60
        },
        "volatility": {
            "high_vol_threshold": 75,
            "low_vol_threshold": 25
        }
    },
    "strategy_triggering": {
        "ai_model": {
            "enable": True,
            "confidence_threshold": 0.7
        },
        "risk_management": {
            "max_position_size_percent": 1.0,
            "max_daily_trades": 10
        }
    },
    "notifications": {
        "telegram": {
            "enable": False,
            "bot_token": "YOUR_BOT_TOKEN",
            "chat_id": "YOUR_CHAT_ID",
            "templates": {
                "signal": "[SIGNAL] {symbol} | {strategy} | {action} | Price: {price} | Target: {target} | SL: {stop_loss}",
                "regime_change": "[STATUS] REGIME CHANGE: Market shifted to {regime} | Breadth: {breadth}%"
            }
        },
        "email": {
            "enable": False,
            "sender_email": "<EMAIL>",
            "sender_password": "your_email_password",
            "receiver_email": "<EMAIL>",
            "smtp_server": "smtp.example.com",
            "smtp_port": 587
        }
    },
    "logging": {
        "level": "INFO",
        "file_logging": {
            "enable": True,
            "log_dir": LOG_DIR
        },
        "signal_logging": {
            "enable": True,
            "signal_log_file": SIGNAL_LOG_FILE,
            "include_market_context": True
        },
        "performance_logging": {
            "enable": True,
            "metrics_file": PERFORMANCE_METRICS_FILE,
            "log_interval": 300
        }
    },
    "storage": {
        "realtime_data": {
            "storage_path": "data/realtime"
        },
        "signals": {
            "storage_path": "data/signals"
        },
        "market_context": {
            "storage_path": "data/market_context"
        }
    },
    "performance": {
        "memory": {
            "cleanup_interval": 3600
        }
    },
    "error_handling": {
        "shutdown": {
            "save_state": True
        },
        "alerting": {
            "enable_email_alerts": False,
            "enable_telegram_alerts": False,
            "critical_error_threshold": 5 # Number of errors before triggering critical alert
        }
    }
}
