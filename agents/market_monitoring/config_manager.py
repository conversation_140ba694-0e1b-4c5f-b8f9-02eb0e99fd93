#!/usr/bin/env python3
"""
Configuration Manager for Market Monitoring Agent

Handles loading, validation, and management of all configuration settings.
"""

import os
import sys
import logging
import io
from typing import Dict, Any

from .data_structures import MarketMonitoringConfig
from .config_validator import ConfigValidator

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and validation for Market Monitoring Agent"""
    
    def __init__(self):
        self.config = None
        self.config_path = None
    
    def load_config(self, config_path: str = "config/market_monitoring_config.yaml") -> MarketMonitoringConfig:
        """Load configuration from YAML file with environment variable resolution"""
        try:
            self.config_path = config_path
            
            # Use ConfigurationLoader for proper environment variable resolution
            from utils.config_loader import ConfigurationLoader
            config_loader = ConfigurationLoader()
            config_data = config_loader.load_agent_config(config_path)
            
            # Validate configuration
            validator = ConfigValidator(config_data)
            if not validator.validate():
                raise ValueError("Configuration validation failed. Please check logs for details.")

            self.config = MarketMonitoringConfig(
                smartapi_config=config_data.get('smartapi', {}),
                market_data_config=config_data.get('market_data', {}),
                environment_config=config_data.get('environment', {}),
                strategy_config=config_data.get('strategy_triggering', {}),
                notifications_config=config_data.get('notifications', {}),
                logging_config=config_data.get('logging', {}),
                storage_config=config_data.get('storage', {}),
                performance_config=config_data.get('performance', {}),
                error_handling_config=config_data.get('error_handling', {})
            )
            
            return self.config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def setup_logging(self, config: MarketMonitoringConfig):
        """Setup logging configuration with Unicode support for Windows"""
        log_config = config.logging_config

        # Force stdout and stderr to UTF-8 for Windows compatibility
        if sys.stdout.encoding != 'utf-8':
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        if sys.stderr.encoding != 'utf-8':
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        # Create logs directory
        log_dir = log_config.get('file_logging', {}).get('log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        # Configure logging
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # Clear existing handlers to prevent duplicates
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Console handler with UTF-8 encoding for Windows compatibility
        try:
            from rich.console import Console
            from rich.logging import RichHandler
            # Only add if Rich is available
            console_handler = RichHandler(console=Console(stderr=True))
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        except ImportError:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)

        # File handler with UTF-8 encoding
        if log_config.get('file_logging', {}).get('enable', True):
            from logging.handlers import RotatingFileHandler

            file_handler = RotatingFileHandler(
                filename=os.path.join(log_dir, 'market_monitoring.log'),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)

        # Reduce websocket ping noise
        logging.getLogger('websocket').setLevel(logging.WARNING)

        logger.info("[SUCCESS] Logging configured successfully with Unicode support")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for monitoring"""
        try:
            import psutil
            
            return {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "timestamp": datetime.now().isoformat()
            }
        except ImportError:
            logger.warning("psutil not available, returning basic system info")
            return {
                "cpu_percent": 0.0,
                "memory_percent": 0.0,
                "disk_usage": 0.0,
                "timestamp": datetime.now().isoformat()
            }
    
    def check_dependencies(self) -> Dict[str, bool]:
        """Check if required dependencies are available"""
        dependencies = {
            "smartapi": self._check_smartapi(),
            "talib": self._check_talib(),
            "telegram": self._check_telegram(),
            "websocket": self._check_websocket(),
            "polars": self._check_polars(),
            "numpy": self._check_numpy(),
            "rich": self._check_rich()
        }

        return dependencies
    
    def _check_smartapi(self) -> bool:
        """Check SmartAPI availability"""
        try:
            from SmartApi import SmartConnect
            return True
        except ImportError:
            return False
    
    def _check_talib(self) -> bool:
        """Check TA-Lib availability"""
        try:
            import polars_talib
            return True
        except ImportError:
            return False
    
    def _check_telegram(self) -> bool:
        """Check Telegram bot availability"""
        try:
            import telegram
            return True
        except ImportError:
            return False
    
    def _check_websocket(self) -> bool:
        """Check WebSocket availability"""
        try:
            import websocket
            return True
        except ImportError:
            return False
    
    def _check_polars(self) -> bool:
        """Check Polars availability"""
        try:
            import polars
            return True
        except ImportError:
            return False
    
    def _check_numpy(self) -> bool:
        """Check NumPy availability"""
        try:
            import numpy
            return True
        except ImportError:
            return False
    
    def _check_rich(self) -> bool:
        """Check Rich availability"""
        try:
            from rich.console import Console
            return True
        except ImportError:
            return False
    
    def create_storage_directories(self, config: MarketMonitoringConfig):
        """Create necessary storage directories"""
        storage_config = config.storage_config

        directories = [
            storage_config.get('realtime_data', {}).get('storage_path', 'data/realtime'),
            storage_config.get('signals', {}).get('storage_path', 'data/signals'),
            storage_config.get('market_context', {}).get('storage_path', 'data/market_context'),
            config.logging_config.get('file_logging', {}).get('log_dir', 'logs')
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info("[FOLDER] Storage directories created")
    
    def get_config(self) -> MarketMonitoringConfig:
        """Get current configuration"""
        if self.config is None:
            raise ValueError("Configuration not loaded. Call load_config() first.")
        return self.config
    
    def reload_config(self) -> MarketMonitoringConfig:
        """Reload configuration from file"""
        if self.config_path is None:
            raise ValueError("No config path available for reload")
        return self.load_config(self.config_path)
