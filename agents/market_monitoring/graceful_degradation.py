#!/usr/bin/env python3
"""
Graceful Degradation Manager for Market Monitoring Agent

Handles fallback mechanisms when external services or components fail.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from enum import Enum

from .data_structures import MarketMonitoringConfig, TradingSignal, MarketIndicators

logger = logging.getLogger(__name__)


class DegradationLevel(Enum):
    """Degradation levels"""
    NORMAL = "normal"
    PARTIAL = "partial"
    FALLBACK = "fallback"
    MINIMAL = "minimal"


class GracefulDegradationManager:
    """Manages graceful degradation of services"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.current_level = DegradationLevel.NORMAL
        self.failed_services: Set[str] = set()
        self.fallback_strategies = {}
        self.degradation_history = []
        
        # Fallback configurations
        self.fallback_config = {
            'smartapi': {
                'use_cached_data': True,
                'disable_live_trading': True,
                'use_historical_patterns': True
            },
            'ai_models': {
                'use_rule_based_strategy': True,
                'disable_ml_predictions': True,
                'use_simple_indicators': True
            },
            'telegram': {
                'use_file_logging': True,
                'disable_notifications': False,
                'use_email_fallback': True
            },
            'websocket': {
                'use_polling_mode': True,
                'increase_intervals': True,
                'reduce_symbol_count': True
            }
        }
        
        # Rule-based strategy parameters
        self.rule_based_params = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'ema_fast': 5,
            'ema_slow': 20,
            'volume_threshold': 1.5,
            'confidence_base': 0.6
        }
    
    async def configure_fallbacks(self, missing_services: List[str]):
        """Configure fallback mechanisms for missing services"""
        try:
            logger.info(f"[DEGRADATION] Configuring fallbacks for missing services: {missing_services}")
            
            for service in missing_services:
                self.failed_services.add(service)
                
                if service in self.fallback_config:
                    fallback = self.fallback_config[service]
                    self.fallback_strategies[service] = fallback
                    logger.info(f"[FALLBACK] Configured fallback for {service}: {fallback}")
            
            # Determine degradation level
            await self._update_degradation_level()
            
        except Exception as e:
            logger.error(f"[ERROR] Error configuring fallbacks: {e}")
    
    async def handle_service_failure(self, service: str, error: Exception):
        """Handle service failure and activate fallback"""
        try:
            logger.warning(f"[DEGRADATION] Service failure detected: {service} - {error}")
            
            if service not in self.failed_services:
                self.failed_services.add(service)
                
                # Record failure
                failure_record = {
                    'timestamp': datetime.now(),
                    'service': service,
                    'error': str(error),
                    'action': 'fallback_activated'
                }
                self.degradation_history.append(failure_record)
                
                # Activate fallback
                await self._activate_fallback(service)
                
                # Update degradation level
                await self._update_degradation_level()
            
        except Exception as e:
            logger.error(f"[ERROR] Error handling service failure: {e}")
    
    async def _activate_fallback(self, service: str):
        """Activate fallback for specific service"""
        try:
            if service == 'smartapi':
                await self._activate_smartapi_fallback()
            elif service == 'ai_models':
                await self._activate_ai_fallback()
            elif service == 'telegram':
                await self._activate_notification_fallback()
            elif service == 'websocket':
                await self._activate_websocket_fallback()
            
            logger.info(f"[FALLBACK] Activated fallback for {service}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error activating fallback for {service}: {e}")
    
    async def _activate_smartapi_fallback(self):
        """Activate SmartAPI fallback mechanisms"""
        try:
            # Switch to cached data mode
            logger.info("[FALLBACK] SmartAPI: Switching to cached data mode")
            
            # Disable live trading signals
            logger.info("[FALLBACK] SmartAPI: Disabling live trading signals")
            
            # Use historical pattern analysis
            logger.info("[FALLBACK] SmartAPI: Enabling historical pattern analysis")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in SmartAPI fallback: {e}")
    
    async def _activate_ai_fallback(self):
        """Activate AI model fallback mechanisms"""
        try:
            # Switch to rule-based strategy
            logger.info("[FALLBACK] AI: Switching to rule-based strategy generation")
            
            # Disable ML predictions
            logger.info("[FALLBACK] AI: Disabling ML predictions")
            
            # Use simple technical indicators
            logger.info("[FALLBACK] AI: Using simple technical indicators only")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in AI fallback: {e}")
    
    async def _activate_notification_fallback(self):
        """Activate notification fallback mechanisms"""
        try:
            # Switch to file logging
            logger.info("[FALLBACK] Notifications: Switching to enhanced file logging")
            
            # Try email fallback if available
            logger.info("[FALLBACK] Notifications: Attempting email fallback")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in notification fallback: {e}")
    
    async def _activate_websocket_fallback(self):
        """Activate WebSocket fallback mechanisms"""
        try:
            # Switch to polling mode
            logger.info("[FALLBACK] WebSocket: Switching to polling mode")
            
            # Increase update intervals
            logger.info("[FALLBACK] WebSocket: Increasing update intervals")
            
            # Reduce symbol count
            logger.info("[FALLBACK] WebSocket: Reducing monitored symbol count")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket fallback: {e}")
    
    async def _update_degradation_level(self):
        """Update current degradation level"""
        try:
            failed_count = len(self.failed_services)
            critical_services = {'smartapi', 'websocket'}
            critical_failed = len(self.failed_services.intersection(critical_services))
            
            old_level = self.current_level
            
            if failed_count == 0:
                self.current_level = DegradationLevel.NORMAL
            elif critical_failed > 0 or failed_count >= 3:
                self.current_level = DegradationLevel.MINIMAL
            elif failed_count >= 2:
                self.current_level = DegradationLevel.FALLBACK
            else:
                self.current_level = DegradationLevel.PARTIAL
            
            if old_level != self.current_level:
                logger.info(f"[DEGRADATION] Level changed: {old_level.value} → {self.current_level.value}")
                
                # Record level change
                level_change = {
                    'timestamp': datetime.now(),
                    'old_level': old_level.value,
                    'new_level': self.current_level.value,
                    'failed_services': list(self.failed_services)
                }
                self.degradation_history.append(level_change)
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating degradation level: {e}")
    
    async def process_signal(self, signal: TradingSignal) -> TradingSignal:
        """Process signal through degradation filters"""
        try:
            if self.current_level == DegradationLevel.NORMAL:
                return signal
            
            # Apply degradation adjustments
            adjusted_signal = signal
            
            if self.current_level in [DegradationLevel.FALLBACK, DegradationLevel.MINIMAL]:
                # Reduce confidence in degraded mode
                adjusted_signal.confidence *= 0.8
                
                # Increase stop loss (more conservative)
                if signal.action == 'BUY':
                    risk = signal.price - signal.stop_loss
                    adjusted_signal.stop_loss = signal.price - (risk * 1.2)
                else:
                    risk = signal.stop_loss - signal.price
                    adjusted_signal.stop_loss = signal.price + (risk * 1.2)
                
                logger.debug(f"[DEGRADATION] Adjusted signal for {self.current_level.value} mode")
            
            return adjusted_signal
            
        except Exception as e:
            logger.error(f"[ERROR] Error processing signal through degradation: {e}")
            return signal
    
    async def generate_fallback_signal(self, symbol: str, indicators: MarketIndicators) -> Optional[TradingSignal]:
        """Generate signal using rule-based fallback strategy"""
        try:
            if 'ai_models' not in self.failed_services:
                return None  # Only use fallback if AI is unavailable
            
            # Rule-based signal generation
            signals = []
            
            # RSI-based signals
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                if indicators.rsi_14 < self.rule_based_params['rsi_oversold']:
                    signals.append({
                        'action': 'BUY',
                        'confidence': self.rule_based_params['confidence_base'],
                        'reason': f'RSI oversold: {indicators.rsi_14:.1f}'
                    })
                elif indicators.rsi_14 > self.rule_based_params['rsi_overbought']:
                    signals.append({
                        'action': 'SELL',
                        'confidence': self.rule_based_params['confidence_base'],
                        'reason': f'RSI overbought: {indicators.rsi_14:.1f}'
                    })
            
            # EMA crossover signals
            if (hasattr(indicators, 'ema_5') and hasattr(indicators, 'ema_20') and
                indicators.ema_5 and indicators.ema_20):
                
                if indicators.ema_5 > indicators.ema_20 * 1.01:  # 1% above
                    signals.append({
                        'action': 'BUY',
                        'confidence': self.rule_based_params['confidence_base'] * 0.9,
                        'reason': 'EMA bullish crossover'
                    })
                elif indicators.ema_5 < indicators.ema_20 * 0.99:  # 1% below
                    signals.append({
                        'action': 'SELL',
                        'confidence': self.rule_based_params['confidence_base'] * 0.9,
                        'reason': 'EMA bearish crossover'
                    })
            
            # Select best signal
            if signals:
                best_signal = max(signals, key=lambda x: x['confidence'])
                
                # Create trading signal (simplified)
                fallback_signal = TradingSignal(
                    symbol=symbol,
                    strategy='rule_based_fallback',
                    action=best_signal['action'],
                    price=100.0,  # Placeholder - would need current price
                    target=105.0 if best_signal['action'] == 'BUY' else 95.0,
                    stop_loss=98.0 if best_signal['action'] == 'BUY' else 102.0,
                    quantity=100,
                    confidence=best_signal['confidence'],
                    market_regime='unknown',
                    timestamp=datetime.now(),
                    context={'fallback_reason': best_signal['reason']}
                )
                
                logger.info(f"[FALLBACK] Generated rule-based signal for {symbol}: {best_signal['reason']}")
                return fallback_signal
            
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating fallback signal: {e}")
            return None
    
    async def enable_fallback_mode(self):
        """Manually enable fallback mode"""
        try:
            logger.info("[DEGRADATION] Manually enabling fallback mode")
            
            # Add AI models to failed services to trigger rule-based mode
            self.failed_services.add('ai_models')
            await self._activate_ai_fallback()
            await self._update_degradation_level()
            
        except Exception as e:
            logger.error(f"[ERROR] Error enabling fallback mode: {e}")
    
    async def attempt_service_recovery(self, service: str) -> bool:
        """Attempt to recover a failed service"""
        try:
            logger.info(f"[RECOVERY] Attempting to recover service: {service}")
            
            # Service-specific recovery attempts
            recovery_success = False
            
            if service == 'smartapi':
                recovery_success = await self._recover_smartapi()
            elif service == 'websocket':
                recovery_success = await self._recover_websocket()
            elif service == 'telegram':
                recovery_success = await self._recover_telegram()
            elif service == 'ai_models':
                recovery_success = await self._recover_ai_models()
            
            if recovery_success:
                self.failed_services.discard(service)
                if service in self.fallback_strategies:
                    del self.fallback_strategies[service]
                
                await self._update_degradation_level()
                
                recovery_record = {
                    'timestamp': datetime.now(),
                    'service': service,
                    'action': 'recovery_successful'
                }
                self.degradation_history.append(recovery_record)
                
                logger.info(f"[RECOVERY] Successfully recovered service: {service}")
            else:
                logger.warning(f"[RECOVERY] Failed to recover service: {service}")
            
            return recovery_success
            
        except Exception as e:
            logger.error(f"[ERROR] Error attempting service recovery: {e}")
            return False
    
    async def _recover_smartapi(self) -> bool:
        """Attempt SmartAPI recovery"""
        try:
            # Placeholder for SmartAPI recovery logic
            # Would involve reconnection attempts, credential validation, etc.
            await asyncio.sleep(1)  # Simulate recovery attempt
            return False  # Placeholder - would return actual recovery status
        except Exception:
            return False
    
    async def _recover_websocket(self) -> bool:
        """Attempt WebSocket recovery"""
        try:
            # Placeholder for WebSocket recovery logic
            await asyncio.sleep(1)  # Simulate recovery attempt
            return False  # Placeholder - would return actual recovery status
        except Exception:
            return False
    
    async def _recover_telegram(self) -> bool:
        """Attempt Telegram recovery"""
        try:
            # Placeholder for Telegram recovery logic
            await asyncio.sleep(1)  # Simulate recovery attempt
            return False  # Placeholder - would return actual recovery status
        except Exception:
            return False
    
    async def _recover_ai_models(self) -> bool:
        """Attempt AI models recovery"""
        try:
            # Placeholder for AI models recovery logic
            await asyncio.sleep(1)  # Simulate recovery attempt
            return False  # Placeholder - would return actual recovery status
        except Exception:
            return False
    
    def get_degradation_status(self) -> Dict[str, Any]:
        """Get current degradation status"""
        return {
            'current_level': self.current_level.value,
            'failed_services': list(self.failed_services),
            'active_fallbacks': list(self.fallback_strategies.keys()),
            'degradation_events': len(self.degradation_history),
            'last_event': self.degradation_history[-1] if self.degradation_history else None
        }
    
    def get_degradation_history(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get degradation history"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            return [
                event for event in self.degradation_history
                if event.get('timestamp', datetime.min) > cutoff_time
            ]
        except Exception as e:
            logger.error(f"[ERROR] Error getting degradation history: {e}")
            return []
    
    def is_service_degraded(self, service: str) -> bool:
        """Check if a service is currently degraded"""
        return service in self.failed_services
    
    def get_fallback_strategy(self, service: str) -> Optional[Dict[str, Any]]:
        """Get fallback strategy for a service"""
        return self.fallback_strategies.get(service)
    
    def reset_degradation(self):
        """Reset degradation state (for testing/recovery)"""
        self.failed_services.clear()
        self.fallback_strategies.clear()
        self.current_level = DegradationLevel.NORMAL
        logger.info("[DEGRADATION] Degradation state reset to normal")
