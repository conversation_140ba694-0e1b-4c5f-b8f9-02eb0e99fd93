#!/usr/bin/env python3
"""
Data Downloader for Market Monitoring Agent

Handles downloading historical and live market data from various sources.
"""

import os
import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import polars as pl

from .data_structures import MarketMonitoringConfig

logger = logging.getLogger(__name__)


class DataDownloader:
    """Handles market data downloading from SmartAPI and other sources"""
    
    def __init__(self, config: MarketMonitoringConfig, smartapi_client=None):
        self.config = config
        self.smartapi_client = smartapi_client
        self.symbol_token_map = {}
        self.nse_500_token_map = {}
    
    async def download_live_historical_data(self, days_back: int = 35, max_symbols: int = 500, testing_mode: bool = False) -> bool:
        """
        Download live historical data from SmartAPI for live market monitoring with robust retry logic
        """
        try:
            # Check for testing mode
            if testing_mode or os.getenv('TESTING_MODE', 'false').lower() == 'true':
                max_symbols = min(max_symbols, int(os.getenv('MAX_SYMBOLS', '20')))
                logger.info(f"[TESTING] Testing mode enabled - limiting to {max_symbols} symbols")

            logger.info(f"[INIT] Starting live historical data download for {max_symbols} symbols, {days_back} days back")

            if not self.smartapi_client:
                logger.error("[ERROR] SmartAPI client not initialized")
                return False

            # Load symbols
            symbols = await self._get_symbol_list(max_symbols)
            if not symbols:
                logger.error("[ERROR] No symbols loaded")
                return False

            logger.info(f"[CONFIG] Loaded {len(symbols)} symbols for live data processing")

            # Calculate date range
            end_date, start_date = self._calculate_date_range(days_back)
            
            logger.info(f"[CONFIG] Downloading LIVE data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            # Download data with retry logic
            all_data, successful_downloads, failed_symbols = await self._download_symbols_with_retry(
                symbols, start_date, end_date
            )

            # Log final statistics
            self._log_download_statistics(symbols, successful_downloads, failed_symbols)

            # Save and process data
            if all_data:
                await self._save_live_data_parquet(all_data, start_date, end_date)
                await self._generate_and_load_timeframes()
                
                logger.info(f"[SUCCESS] Downloaded live data for {len(all_data)} symbols, {len(failed_symbols)} failed")
                return True
            else:
                logger.error("[ERROR] No live data downloaded")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Live historical data download failed: {e}")
            return False
    
    def _calculate_date_range(self, days_back: int) -> tuple:
        """Calculate start and end dates for data download"""
        import pytz
        
        # Get current time in IST
        ist = pytz.timezone('Asia/Kolkata')
        end_date = datetime.now(ist).replace(tzinfo=None)

        # Check if we should include today's data
        include_today = self._should_download_today_data()
        if include_today:
            logger.info(f"[MARKET HOURS] Including today's data (current time: {end_date.strftime('%H:%M')})")
        else:
            # Set to previous market close
            if end_date.weekday() < 5:  # Monday to Friday
                end_date = end_date.replace(hour=15, minute=30)
            else:
                # If weekend, go to Friday's close
                days_to_friday = (end_date.weekday() - 4) % 7
                end_date = end_date - timedelta(days=days_to_friday)
                end_date = end_date.replace(hour=15, minute=30)

        start_date = end_date - timedelta(days=days_back)
        if start_date.weekday() < 5:  # Monday to Friday
            start_date = start_date.replace(hour=9, minute=15)  # Market open
            
        return end_date, start_date
    
    def _should_download_today_data(self) -> bool:
        """Check if we should download today's data"""
        try:
            if not self._is_market_hours():
                return False

            import pytz
            from datetime import time

            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()

            # Only download if we're at least 30 minutes into the market
            market_start_buffer = time(9, 45)
            return current_time >= market_start_buffer
            
        except Exception as e:
            logger.warning(f"Error checking if should download today's data: {e}")
            return False
    
    def _is_market_hours(self) -> bool:
        """Check if current time is during market hours"""
        try:
            import pytz
            from datetime import time

            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()

            # Market hours: 9:15 AM to 3:30 PM
            market_open = time(9, 15)
            market_close = time(15, 30)

            # Check if it's a weekday
            is_weekday = now.weekday() < 5

            return is_weekday and market_open <= current_time <= market_close
            
        except Exception as e:
            logger.warning(f"Error checking market hours: {e}")
            return False
    
    async def _get_symbol_list(self, max_symbols: int) -> List[Dict]:
        """Get symbol list with tokens for historical data download"""
        try:
            # Check if we're in testing mode
            testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
            if testing_mode and max_symbols <= 20:
                top_20_symbols = await self._get_top_20_stocks()
                if top_20_symbols:
                    logger.info(f"[TESTING] Using predefined top 20 stocks for testing mode")
                    return top_20_symbols[:max_symbols] if max_symbols > 0 else top_20_symbols

            # Try to load from historical data first
            historical_symbols = await self._load_symbols_from_historical_data()
            if historical_symbols:
                logger.info(f"[SUCCESS] Loaded {len(historical_symbols)} symbols from historical data")
                return historical_symbols[:max_symbols] if max_symbols > 0 else historical_symbols

            # Try symbols.json as fallback
            symbols_file = "config/symbols.json"
            if os.path.exists(symbols_file):
                with open(symbols_file, 'r') as f:
                    symbols = json.load(f)
                logger.info(f"[SUCCESS] Loaded {len(symbols)} symbols from {symbols_file}")
                return symbols[:max_symbols] if max_symbols > 0 else symbols

            # Load from NSE 500 universe
            nse_500_universe_file = "config/nse_500_universe.json"
            if os.path.exists(nse_500_universe_file):
                with open(nse_500_universe_file, 'r') as f:
                    universe_data = json.load(f)
                
                all_symbols = [
                    {"symbol": stock["symbol"], "token": stock["token"], "exchange": stock["exchange"]}
                    for stock in universe_data["stocks"]
                    if stock.get("is_active", False) and stock.get("nifty_500", False)
                ]
                logger.info(f"[SUCCESS] Loaded {len(all_symbols)} active Nifty 500 symbols")
                return all_symbols[:max_symbols] if max_symbols > 0 else all_symbols
            else:
                logger.warning(f"[WARN] NSE 500 universe file not found: {nse_500_universe_file}")
                return []

        except Exception as e:
            logger.error(f"[ERROR] Error loading symbol list: {e}")
            return []
    
    async def _get_top_20_stocks(self) -> List[Dict]:
        """Get top 20 stocks from NSE 500 universe"""
        try:
            nse_500_universe_file = "config/nse_500_universe.json"
            if os.path.exists(nse_500_universe_file):
                with open(nse_500_universe_file, 'r') as f:
                    universe_data = json.load(f)
                
                top_stocks = sorted(
                    [
                        {"symbol": stock["symbol"], "token": stock["token"], "exchange": stock["exchange"]}
                        for stock in universe_data["stocks"]
                        if stock.get("is_active", False) and stock.get("nifty_500", False)
                    ],
                    key=lambda x: x.get("market_cap_value", 0),
                    reverse=True
                )
                
                logger.info(f"[TOP_STOCKS] Selected {min(20, len(top_stocks))} top stocks from NSE 500 universe")
                return top_stocks[:20]
            else:
                logger.warning(f"[WARN] NSE 500 universe file not found")
                return []

        except Exception as e:
            logger.error(f"[ERROR] Error loading top stocks: {e}")
            return []
    
    async def _load_symbols_from_historical_data(self) -> List[Dict]:
        """Load symbols from historical data files"""
        try:
            historical_files = [
                "data/historical/historical_1hr.parquet",
                "data/historical/backup/historical_5min.parquet",
                "data/historical/historical_30min.parquet",
                "data/historical/historical_15min.parquet",
                "data/historical/historical_5min.parquet"
            ]

            for file_path in historical_files:
                if os.path.exists(file_path):
                    logger.info(f"[LOAD] Loading symbols from {file_path}")
                    df = pl.read_parquet(file_path)

                    symbols_df = df.select([
                        pl.col(" Stock_Name").alias("symbol"),
                        pl.col(" SymbolToken").alias("token")
                    ]).unique().sort("symbol")

                    symbols_list = []
                    for row in symbols_df.iter_rows(named=True):
                        symbol = row["symbol"].replace("-EQ", "")
                        symbols_list.append({
                            "symbol": symbol,
                            "token": str(row["token"]),
                            "exchange": "NSE"
                        })

                    logger.info(f"[SUCCESS] Extracted {len(symbols_list)} symbols from historical data")
                    return symbols_list

            logger.warning("[WARN] No historical data files found")
            return []

        except Exception as e:
            logger.error(f"[ERROR] Error loading symbols from historical data: {e}")
            return []

    async def _download_symbols_with_retry(self, symbols: List[Dict], start_date: datetime, end_date: datetime) -> tuple:
        """Download symbols with retry logic"""
        all_data = []
        failed_symbols = {}
        successful_downloads = 0

        logger.info(f"[DOWNLOAD] Starting initial download for {len(symbols)} symbols...")

        for i, symbol_info in enumerate(symbols):
            symbol = symbol_info['symbol']
            token = symbol_info['token']
            exchange = symbol_info.get('exchange', 'NSE')

            logger.debug(f"[PROGRESS] Processing {symbol} ({i+1}/{len(symbols)})")

            # Download with retry logic
            df = await self._download_from_smartapi_with_retry(
                symbol, token, exchange, start_date, end_date
            )

            if df is not None and len(df) > 0:
                all_data.append(df)
                successful_downloads += 1
                logger.debug(f"[SUCCESS] Downloaded {symbol}: {len(df)} records")
            else:
                failed_symbols[symbol] = {"token": token, "exchange": exchange}
                logger.warning(f"[FAILED] Failed to download {symbol}")

            # Rate limiting
            await asyncio.sleep(0.75)

        # Retry failed symbols
        if failed_symbols:
            logger.warning(f"[RETRY] Retrying {len(failed_symbols)} failed symbols...")
            retry_success = 0

            for symbol, info in failed_symbols.copy().items():
                logger.info(f"[RETRY] Retrying: {symbol}")

                df = await self._download_from_smartapi_with_retry(
                    symbol, info['token'], info['exchange'], start_date, end_date
                )

                if df is not None and len(df) > 0:
                    all_data.append(df)
                    retry_success += 1
                    successful_downloads += 1
                    del failed_symbols[symbol]
                    logger.info(f"[RETRY_SUCCESS] Successfully downloaded {symbol}: {len(df)} records")

                await asyncio.sleep(1.0)

            logger.info(f"[RETRY_SUMMARY] Retry success for {retry_success} symbols")

        return all_data, successful_downloads, failed_symbols

    async def _download_from_smartapi_with_retry(self, symbol: str, token: str, exchange: str, start_date: datetime, end_date: datetime):
        """Download from SmartAPI with retry logic"""
        try:
            hist_params = {
                "exchange": exchange,
                "symboltoken": token,
                "interval": "FIVE_MINUTE",
                "fromdate": start_date.strftime("%Y-%m-%d %H:%M"),
                "todate": end_date.strftime("%Y-%m-%d %H:%M")
            }

            logger.debug(f"[SMARTAPI] Requesting {symbol} with params: {hist_params}")

            max_retries = 5
            base_delay = 2

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        delay = base_delay * (2 ** attempt) + (attempt * 0.5)
                        logger.debug(f"[RATE_LIMIT] Waiting {delay:.1f}s before retry {attempt + 1} for {symbol}")
                        await asyncio.sleep(delay)

                    response = await asyncio.to_thread(self.smartapi_client.getCandleData, hist_params)

                    if response.get('status'):
                        break
                    else:
                        error_msg = response.get('message', 'Unknown error')
                        error_code = response.get('errorcode', '')

                        if error_code == 'AB1004' or 'Something Went Wrong, Please Try After Sometime' in error_msg:
                            if attempt < max_retries - 1:
                                wait_time = base_delay * (2 ** (attempt + 1))
                                logger.warning(f"[RATE_LIMIT] AB1004 error for {symbol}, waiting {wait_time}s")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                logger.error(f"[RATE_LIMIT] AB1004 error for {symbol} after {max_retries} attempts")
                                return None

                        if attempt < max_retries - 1:
                            logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {error_msg}. Retrying...")
                            await asyncio.sleep(base_delay)
                        else:
                            logger.error(f"Failed to download {symbol} after {max_retries} attempts: {error_msg}")
                            return None

                except Exception as e:
                    if attempt < max_retries - 1:
                        wait_time = base_delay * (attempt + 1)
                        logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}. Retrying in {wait_time}s...")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"Failed to download {symbol} after {max_retries} attempts: {e}")
                        return None

            data = response.get('data', [])
            if not data:
                logger.warning(f"[SMARTAPI] No data for {symbol}")
                return None

            # Convert to Polars DataFrame
            timestamps = [datetime.fromisoformat(row[0]) for row in data]
            df = pl.DataFrame({
                'timestamp': [row[0] for row in data],
                'date': [ts.strftime('%d-%m-%Y') for ts in timestamps],
                'time': [ts.strftime('%H:%M:%S') for ts in timestamps],
                'open': [float(row[1]) for row in data],
                'high': [float(row[2]) for row in data],
                'low': [float(row[3]) for row in data],
                'close': [float(row[4]) for row in data],
                'volume': [int(row[5]) for row in data],
                'symbol': [symbol] * len(data),
                'exchange': [exchange] * len(data)
            })

            if len(df) > 0:
                min_ts = df['timestamp'].min()
                max_ts = df['timestamp'].max()
                logger.info(f"[SMARTAPI_DATA] Downloaded {symbol} data range: {min_ts} to {max_ts} ({len(df)} records)")

            await asyncio.sleep(0.75)
            logger.debug(f"[SUCCESS] Downloaded {symbol} from SmartAPI: {len(df)} records")
            return df

        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading {symbol}: {e}")
            return None

    def _log_download_statistics(self, symbols: List[Dict], successful_downloads: int, failed_symbols: Dict):
        """Log download statistics"""
        logger.info(f"[DOWNLOAD_SUMMARY] Total symbols attempted: {len(symbols)}")
        logger.info(f"[DOWNLOAD_SUMMARY] Successfully downloaded: {successful_downloads}")
        logger.info(f"[DOWNLOAD_SUMMARY] Failed after retry: {len(failed_symbols)}")

        if failed_symbols:
            logger.warning("[FAILED_SYMBOLS] List of symbols still failed after retry:")
            for symbol in failed_symbols:
                logger.warning(f" - {symbol}")

    async def _save_live_data_parquet(self, data_frames: List, start_date: datetime, end_date: datetime):
        """Save live data to parquet file"""
        try:
            logger.info(f"[SAVE_LIVE] Combining {len(data_frames)} live dataframes...")
            new_df = pl.concat(data_frames, how="vertical")
            new_df = new_df.sort(['symbol', 'timestamp'])

            output_dir = Path("data/live")
            output_dir.mkdir(parents=True, exist_ok=True)

            filename = "live_5min.parquet"
            output_path = output_dir / filename

            final_df = await self._merge_with_existing_data(new_df, output_path)

            final_df.write_parquet(
                output_path,
                compression="zstd",
                compression_level=15,
                use_pyarrow=True
            )

            total_records = len(final_df)
            unique_symbols = final_df['symbol'].n_unique()
            file_size_mb = output_path.stat().st_size / (1024 * 1024)

            logger.info(f"[SUCCESS] Live 5min data saved to {output_path}")
            logger.info(f"[SUMMARY] Records: {total_records:,}, Symbols: {unique_symbols}, Size: {file_size_mb:.2f} MB")

        except Exception as e:
            logger.error(f"[ERROR] Error saving live data: {e}")

    async def _merge_with_existing_data(self, new_df: pl.DataFrame, output_path: Path) -> pl.DataFrame:
        """Merge new data with existing data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=35)
            cutoff_str = cutoff_date.strftime("%Y-%m-%dT%H:%M:%S")

            if output_path.exists():
                logger.info("[MERGE] Loading existing live data...")
                existing_df = pl.read_parquet(output_path)

                logger.info(f"[CLEANUP] Removing data older than {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
                existing_df = existing_df.filter(pl.col('timestamp') >= cutoff_str)

                if len(existing_df) > 0:
                    latest_timestamp = existing_df['timestamp'].max()
                    logger.info(f"[INFO] Latest existing data: {latest_timestamp}")

                    logger.info(f"[APPEND] Adding {len(new_df)} new records")
                    if len(new_df) > 0:
                        combined_df = pl.concat([existing_df, new_df], how="vertical")
                    else:
                        logger.info("[INFO] No new data to append")
                        combined_df = existing_df
                else:
                    logger.info("[INFO] Existing file is empty, using all new data")
                    combined_df = new_df
            else:
                logger.info("[NEW] Creating new live data file")
                combined_df = new_df

            combined_df = combined_df.sort(['symbol', 'timestamp'])
            combined_df = combined_df.unique(['symbol', 'timestamp'], keep='last')

            if len(combined_df) > 0:
                min_date = combined_df['timestamp'].min()
                max_date = combined_df['timestamp'].max()
                logger.info(f"[RESULT] Final dataset: {len(combined_df)} records, {combined_df['symbol'].n_unique()} symbols")
                logger.info(f"[DATE_RANGE] Data spans from {min_date} to {max_date}")
            else:
                logger.warning("[WARNING] No data in final dataset")

            return combined_df

        except Exception as e:
            logger.error(f"[ERROR] Error merging data: {e}")
            return new_df

    async def _generate_and_load_timeframes(self):
        """Generate higher timeframes from 5min data"""
        try:
            live_5min_path = Path("data/live/live_5min.parquet")
            if not live_5min_path.exists():
                logger.warning("[TIMEFRAMES] No 5min live data found for timeframe generation")
                return

            logger.info("[TIMEFRAMES] Generating higher timeframes from 5min live data...")
            df_5min = pl.read_parquet(live_5min_path)

            timeframes_to_generate = {
                '15min': 3,   # 3 x 5min = 15min
                '30min': 6,   # 6 x 5min = 30min
                '1hr': 12     # 12 x 5min = 1hr
            }

            for timeframe, multiplier in timeframes_to_generate.items():
                logger.info(f"[TIMEFRAMES] Generating {timeframe} data...")

                timestamp_dtype = df_5min.select(pl.col('timestamp')).dtypes[0]
                if timestamp_dtype == pl.Utf8:
                    df_5min = df_5min.with_columns(pl.col('timestamp').str.to_datetime())

                df_resampled = (
                    df_5min
                    .sort(['symbol', 'timestamp'])
                    .with_row_index()
                    .with_columns((pl.col('index') // multiplier).alias('group_id'))
                    .group_by(['symbol', 'group_id'])
                    .agg([
                        pl.col('timestamp').first().alias('timestamp'),
                        pl.col('open').first().alias('open'),
                        pl.col('high').max().alias('high'),
                        pl.col('low').min().alias('low'),
                        pl.col('close').last().alias('close'),
                        pl.col('volume').sum().alias('volume')
                    ])
                    .drop('group_id')
                    .sort(['symbol', 'timestamp'])
                )

                logger.info(f"[TIMEFRAMES] Generated {timeframe}: {len(df_resampled)} records")

            logger.info("[TIMEFRAMES] All timeframes generated and loaded to memory")

        except Exception as e:
            logger.error(f"[ERROR] Error generating timeframes: {e}")
