#!/usr/bin/env python3
"""
Performance Optimizer for Market Monitoring Agent

Handles incremental data loading, parallel processing, and performance optimizations.
"""

import logging
import asyncio
import polars as pl
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import concurrent.futures
from functools import partial

from .data_structures import MarketMonitoringConfig, OHLCV

logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """Optimizes performance through incremental loading and parallel processing"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.last_update_times = {}  # symbol -> last_update_time
        self.data_cache = {}  # symbol -> timeframe -> data
        self.processing_pool = None
        self.max_workers = config.performance_config.get('max_workers', 4)
        
        # Performance tracking
        self.processing_times = {}
        self.cache_hit_rate = 0.0
        self.cache_hits = 0
        self.cache_misses = 0
    
    async def initialize(self):
        """Initialize performance optimizer"""
        try:
            # Initialize thread pool for CPU-intensive tasks
            self.processing_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=self.max_workers,
                thread_name_prefix="market_optimizer"
            )
            
            logger.info(f"[OPTIMIZER] Performance optimizer initialized with {self.max_workers} workers")
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing performance optimizer: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown performance optimizer"""
        try:
            if self.processing_pool:
                self.processing_pool.shutdown(wait=True)
            
            logger.info("[OPTIMIZER] Performance optimizer shutdown completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Error shutting down performance optimizer: {e}")
    
    async def incremental_data_load(self, symbols: List[str], timeframe: str = '5min') -> Dict[str, pl.DataFrame]:
        """Load data incrementally, only fetching new/updated data"""
        try:
            start_time = datetime.now()
            
            # Determine which symbols need updates
            symbols_to_update = await self._get_symbols_needing_update(symbols, timeframe)
            
            if not symbols_to_update:
                logger.debug(f"[OPTIMIZER] No symbols need updates for {timeframe}")
                return self._get_cached_data(symbols, timeframe)
            
            logger.info(f"[OPTIMIZER] Incremental update needed for {len(symbols_to_update)} symbols")
            
            # Load only new data for symbols that need updates
            updated_data = await self._load_incremental_data(symbols_to_update, timeframe)
            
            # Merge with existing cached data
            merged_data = await self._merge_with_cached_data(updated_data, timeframe)
            
            # Update cache and timestamps
            await self._update_cache(merged_data, timeframe)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.processing_times[f'incremental_load_{timeframe}'] = processing_time
            
            logger.info(f"[OPTIMIZER] Incremental load completed in {processing_time:.2f}s")
            
            return merged_data
            
        except Exception as e:
            logger.error(f"[ERROR] Error in incremental data load: {e}")
            # Fallback to full load
            return await self._fallback_full_load(symbols, timeframe)
    
    async def _get_symbols_needing_update(self, symbols: List[str], timeframe: str) -> List[str]:
        """Determine which symbols need data updates"""
        try:
            symbols_to_update = []
            current_time = datetime.now()
            
            # Check update intervals based on timeframe
            update_intervals = {
                '1min': timedelta(minutes=1),
                '5min': timedelta(minutes=5),
                '15min': timedelta(minutes=15),
                '30min': timedelta(minutes=30),
                '1hr': timedelta(hours=1)
            }
            
            update_interval = update_intervals.get(timeframe, timedelta(minutes=5))
            
            for symbol in symbols:
                cache_key = f"{symbol}_{timeframe}"
                last_update = self.last_update_times.get(cache_key)
                
                if (last_update is None or 
                    current_time - last_update > update_interval):
                    symbols_to_update.append(symbol)
            
            return symbols_to_update
            
        except Exception as e:
            logger.error(f"[ERROR] Error determining symbols needing update: {e}")
            return symbols  # Return all symbols as fallback
    
    async def _load_incremental_data(self, symbols: List[str], timeframe: str) -> Dict[str, pl.DataFrame]:
        """Load incremental data for specified symbols"""
        try:
            data_path = Path(f"data/live/live_{timeframe}.parquet")
            
            if not data_path.exists():
                logger.warning(f"[OPTIMIZER] Data file not found: {data_path}")
                return {}
            
            # Load full data file (Polars is efficient with this)
            df = pl.read_parquet(data_path)
            
            # Filter for required symbols and recent data only
            cutoff_time = datetime.now() - timedelta(hours=24)  # Last 24 hours
            cutoff_str = cutoff_time.strftime("%Y-%m-%dT%H:%M:%S")
            
            filtered_df = df.filter(
                (pl.col('symbol').is_in(symbols)) &
                (pl.col('timestamp') >= cutoff_str)
            )
            
            # Split by symbol
            symbol_data = {}
            for symbol in symbols:
                symbol_df = filtered_df.filter(pl.col('symbol') == symbol)
                if len(symbol_df) > 0:
                    symbol_data[symbol] = symbol_df
            
            return symbol_data
            
        except Exception as e:
            logger.error(f"[ERROR] Error loading incremental data: {e}")
            return {}
    
    async def _merge_with_cached_data(self, new_data: Dict[str, pl.DataFrame], timeframe: str) -> Dict[str, pl.DataFrame]:
        """Merge new data with cached data"""
        try:
            merged_data = {}
            
            for symbol, new_df in new_data.items():
                cache_key = f"{symbol}_{timeframe}"
                cached_df = self.data_cache.get(cache_key)
                
                if cached_df is not None:
                    # Merge with existing data
                    combined_df = pl.concat([cached_df, new_df], how="vertical")
                    
                    # Remove duplicates and sort
                    merged_df = combined_df.unique(['symbol', 'timestamp'], keep='last').sort(['symbol', 'timestamp'])
                    
                    # Keep only recent data to manage memory
                    cutoff_time = datetime.now() - timedelta(days=7)  # Keep 7 days
                    cutoff_str = cutoff_time.strftime("%Y-%m-%dT%H:%M:%S")
                    
                    merged_df = merged_df.filter(pl.col('timestamp') >= cutoff_str)
                    
                    merged_data[symbol] = merged_df
                    self.cache_hits += 1
                else:
                    # No cached data, use new data as-is
                    merged_data[symbol] = new_df
                    self.cache_misses += 1
            
            # Update cache hit rate
            total_requests = self.cache_hits + self.cache_misses
            if total_requests > 0:
                self.cache_hit_rate = self.cache_hits / total_requests
            
            return merged_data
            
        except Exception as e:
            logger.error(f"[ERROR] Error merging with cached data: {e}")
            return new_data
    
    async def _update_cache(self, data: Dict[str, pl.DataFrame], timeframe: str):
        """Update data cache and timestamps"""
        try:
            current_time = datetime.now()
            
            for symbol, df in data.items():
                cache_key = f"{symbol}_{timeframe}"
                self.data_cache[cache_key] = df
                self.last_update_times[cache_key] = current_time
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating cache: {e}")
    
    def _get_cached_data(self, symbols: List[str], timeframe: str) -> Dict[str, pl.DataFrame]:
        """Get cached data for symbols"""
        cached_data = {}
        
        for symbol in symbols:
            cache_key = f"{symbol}_{timeframe}"
            if cache_key in self.data_cache:
                cached_data[symbol] = self.data_cache[cache_key]
                self.cache_hits += 1
            else:
                self.cache_misses += 1
        
        return cached_data
    
    async def _fallback_full_load(self, symbols: List[str], timeframe: str) -> Dict[str, pl.DataFrame]:
        """Fallback to full data load"""
        try:
            logger.warning(f"[OPTIMIZER] Falling back to full data load for {timeframe}")
            
            data_path = Path(f"data/live/live_{timeframe}.parquet")
            if not data_path.exists():
                return {}
            
            df = pl.read_parquet(data_path)
            
            # Split by symbol
            symbol_data = {}
            for symbol in symbols:
                symbol_df = df.filter(pl.col('symbol') == symbol)
                if len(symbol_df) > 0:
                    symbol_data[symbol] = symbol_df
            
            return symbol_data
            
        except Exception as e:
            logger.error(f"[ERROR] Error in fallback full load: {e}")
            return {}
    
    async def parallel_timeframe_generation(self, df_5min: pl.DataFrame, target_timeframes: List[str]) -> Dict[str, pl.DataFrame]:
        """Generate multiple timeframes in parallel"""
        try:
            start_time = datetime.now()
            
            if not self.processing_pool:
                logger.warning("[OPTIMIZER] Processing pool not initialized, using sequential processing")
                return await self._sequential_timeframe_generation(df_5min, target_timeframes)
            
            # Prepare tasks for parallel execution
            tasks = []
            for timeframe in target_timeframes:
                if timeframe != '5min':  # Skip 5min as it's the source
                    task = self.processing_pool.submit(
                        self._generate_single_timeframe,
                        df_5min,
                        timeframe
                    )
                    tasks.append((timeframe, task))
            
            # Collect results
            results = {'5min': df_5min}  # Include source data
            
            for timeframe, task in tasks:
                try:
                    result_df = task.result(timeout=60)  # 60 second timeout
                    if result_df is not None:
                        results[timeframe] = result_df
                except concurrent.futures.TimeoutError:
                    logger.error(f"[OPTIMIZER] Timeout generating {timeframe} timeframe")
                except Exception as e:
                    logger.error(f"[OPTIMIZER] Error generating {timeframe} timeframe: {e}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.processing_times['parallel_timeframe_generation'] = processing_time
            
            logger.info(f"[OPTIMIZER] Parallel timeframe generation completed in {processing_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error in parallel timeframe generation: {e}")
            return await self._sequential_timeframe_generation(df_5min, target_timeframes)
    
    def _generate_single_timeframe(self, df_5min: pl.DataFrame, timeframe: str) -> Optional[pl.DataFrame]:
        """Generate a single timeframe (runs in thread pool)"""
        try:
            # Timeframe mapping
            timeframe_map = {
                "15min": "15m",
                "30min": "30m",
                "1hr": "1h"
            }
            
            if timeframe not in timeframe_map:
                logger.error(f"[OPTIMIZER] Unsupported timeframe: {timeframe}")
                return None
            
            resample_rule = timeframe_map[timeframe]
            
            # Group by symbol and resample
            result_dfs = []
            
            for symbol in df_5min['symbol'].unique():
                symbol_df = df_5min.filter(pl.col('symbol') == symbol)
                
                # Convert timestamp to datetime for resampling
                symbol_df = symbol_df.with_columns([
                    pl.col('timestamp').str.strptime(pl.Datetime, "%Y-%m-%dT%H:%M:%S%z").alias('datetime')
                ]).sort('datetime')
                
                if symbol_df.height == 0:
                    continue
                
                # Resample OHLCV data
                resampled = symbol_df.group_by_dynamic(
                    'datetime',
                    every=resample_rule,
                    closed='left'
                ).agg([
                    pl.col('open').first().alias('open'),
                    pl.col('high').max().alias('high'),
                    pl.col('low').min().alias('low'),
                    pl.col('close').last().alias('close'),
                    pl.col('volume').sum().alias('volume'),
                    pl.col('symbol').first().alias('symbol'),
                    pl.col('exchange').first().alias('exchange')
                ]).with_columns([
                    pl.col('datetime').dt.strftime("%Y-%m-%dT%H:%M:%S+05:30").alias('timestamp')
                ]).drop('datetime')
                
                if resampled.height > 0:
                    result_dfs.append(resampled)
            
            # Combine all symbols
            if result_dfs:
                return pl.concat(result_dfs)
            else:
                logger.warning(f"[OPTIMIZER] No data available for {timeframe} conversion")
                return None
                
        except Exception as e:
            logger.error(f"[OPTIMIZER] Error generating {timeframe} timeframe: {e}")
            return None
    
    async def _sequential_timeframe_generation(self, df_5min: pl.DataFrame, target_timeframes: List[str]) -> Dict[str, pl.DataFrame]:
        """Generate timeframes sequentially (fallback)"""
        try:
            results = {'5min': df_5min}
            
            for timeframe in target_timeframes:
                if timeframe != '5min':
                    result_df = self._generate_single_timeframe(df_5min, timeframe)
                    if result_df is not None:
                        results[timeframe] = result_df
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error in sequential timeframe generation: {e}")
            return {'5min': df_5min}
    
    async def optimize_indicator_calculation(self, symbol_data: Dict[str, pl.DataFrame], indicators_config: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Optimize indicator calculations using parallel processing"""
        try:
            start_time = datetime.now()
            
            if not self.processing_pool:
                logger.warning("[OPTIMIZER] Processing pool not initialized for indicator calculation")
                return {}
            
            # Prepare tasks for parallel indicator calculation
            tasks = []
            for symbol, df in symbol_data.items():
                task = self.processing_pool.submit(
                    self._calculate_indicators_for_symbol,
                    symbol,
                    df,
                    indicators_config
                )
                tasks.append((symbol, task))
            
            # Collect results
            results = {}
            for symbol, task in tasks:
                try:
                    indicators = task.result(timeout=30)  # 30 second timeout
                    if indicators:
                        results[symbol] = indicators
                except concurrent.futures.TimeoutError:
                    logger.error(f"[OPTIMIZER] Timeout calculating indicators for {symbol}")
                except Exception as e:
                    logger.error(f"[OPTIMIZER] Error calculating indicators for {symbol}: {e}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.processing_times['parallel_indicator_calculation'] = processing_time
            
            logger.info(f"[OPTIMIZER] Parallel indicator calculation completed in {processing_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error in optimized indicator calculation: {e}")
            return {}
    
    def _calculate_indicators_for_symbol(self, symbol: str, df: pl.DataFrame, indicators_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate indicators for a single symbol (runs in thread pool)"""
        try:
            if len(df) < 50:  # Need minimum data for indicators
                return None
            
            # Use polars-talib if available
            try:
                import polars_talib as ta
                
                # Calculate common indicators
                indicators = {}
                
                # EMAs
                for period in indicators_config.get('ema_periods', [5, 10, 20, 50]):
                    ema_col = f'ema_{period}'
                    df = df.with_columns(ta.ema(pl.col('close'), period).alias(ema_col))
                    indicators[ema_col] = df[ema_col].tail(1).item()
                
                # RSI
                for period in indicators_config.get('rsi_periods', [14]):
                    rsi_col = f'rsi_{period}'
                    df = df.with_columns(ta.rsi(pl.col('close'), period).alias(rsi_col))
                    indicators[rsi_col] = df[rsi_col].tail(1).item()
                
                # MACD
                df = df.with_columns([
                    ta.macd(pl.col('close')).alias('macd'),
                    ta.macd_signal(pl.col('close')).alias('macd_signal'),
                    ta.macd_histogram(pl.col('close')).alias('macd_histogram')
                ])
                
                indicators.update({
                    'macd': df['macd'].tail(1).item(),
                    'macd_signal': df['macd_signal'].tail(1).item(),
                    'macd_histogram': df['macd_histogram'].tail(1).item()
                })
                
                return indicators
                
            except ImportError:
                # Fallback to manual calculations
                return self._calculate_indicators_manual(df, indicators_config)
                
        except Exception as e:
            logger.error(f"[OPTIMIZER] Error calculating indicators for {symbol}: {e}")
            return None
    
    def _calculate_indicators_manual(self, df: pl.DataFrame, indicators_config: Dict[str, Any]) -> Dict[str, Any]:
        """Manual indicator calculations (fallback)"""
        try:
            indicators = {}
            
            # Simple EMA calculation
            for period in indicators_config.get('ema_periods', [20]):
                alpha = 2.0 / (period + 1)
                ema = df.select(pl.col('close').ewm_mean(alpha=alpha).alias(f'ema_{period}'))
                indicators[f'ema_{period}'] = ema[f'ema_{period}'].tail(1).item()
            
            return indicators
            
        except Exception as e:
            logger.error(f"[OPTIMIZER] Error in manual indicator calculation: {e}")
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance optimization metrics"""
        return {
            'cache_hit_rate': self.cache_hit_rate,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'processing_times': self.processing_times.copy(),
            'cached_symbols': len(self.data_cache),
            'max_workers': self.max_workers,
            'pool_active': self.processing_pool is not None
        }
    
    def clear_cache(self, older_than_hours: int = 24):
        """Clear old cache entries"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            
            keys_to_remove = []
            for key, timestamp in self.last_update_times.items():
                if timestamp < cutoff_time:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in self.data_cache:
                    del self.data_cache[key]
                del self.last_update_times[key]
            
            logger.info(f"[OPTIMIZER] Cleared {len(keys_to_remove)} old cache entries")
            
        except Exception as e:
            logger.error(f"[ERROR] Error clearing cache: {e}")
    
    def reset_performance_counters(self):
        """Reset performance counters"""
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_hit_rate = 0.0
        self.processing_times.clear()
        logger.info("[OPTIMIZER] Performance counters reset")
