#!/usr/bin/env python3
"""
Signal Generator for Market Monitoring Agent

Generates trading signals based on technical indicators and market conditions.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import asdict

from .data_structures import TradingSignal, MarketIndicators, OHLCV, MarketRegime, MarketMonitoringConfig

logger = logging.getLogger(__name__)


class SignalGenerator:
    """Generates and processes trading signals"""
    
    def __init__(self, config: MarketMonitoringConfig):
        self.config = config
        self.active_signals = []
        self.signal_handlers = []
    
    async def check_trading_signals(self, symbol: str, get_indicators_func: Callable, get_market_data_func: Callable, market_regime: Optional[MarketRegime] = None):
        """Check for trading signals for a symbol"""
        try:
            # Check signals on multiple timeframes
            timeframes = ['5min', '15min', '30min']

            for timeframe in timeframes:
                await self._check_trading_signals_for_timeframe(
                    symbol, timeframe, get_indicators_func, get_market_data_func, market_regime
                )

        except Exception as e:
            logger.error(f"[ERROR] Error checking trading signals for {symbol}: {e}")
    
    async def _check_trading_signals_for_timeframe(self, symbol: str, timeframe: str, get_indicators_func: Callable, get_market_data_func: Callable, market_regime: Optional[MarketRegime] = None):
        """Check for trading signals for a symbol on a specific timeframe"""
        try:
            logger.debug(f"[SIGNAL_CHECK] Checking signals for {symbol} {timeframe}")
            
            indicators = get_indicators_func(symbol, timeframe)
            if not indicators:
                logger.debug(f"[SIGNAL_CHECK] No indicators available for {symbol} {timeframe}")
                return

            candles = get_market_data_func(symbol, timeframe)
            if not candles or len(candles) < 20:
                logger.debug(f"[SIGNAL_CHECK] Insufficient candles for {symbol} {timeframe}: {len(candles) if candles else 0}")
                return

            logger.debug(f"[SIGNAL_CHECK] Processing {symbol} {timeframe} with {len(candles)} candles")

            current_candle = candles[-1]

            # Check entry conditions
            entry_signals = await self._check_entry_conditions(symbol, indicators, current_candle, market_regime)

            # Generate signals
            for signal_data in entry_signals:
                signal = await self._create_trading_signal(symbol, signal_data, indicators, current_candle, market_regime)
                if signal:
                    await self._process_trading_signal(signal)

        except Exception as e:
            logger.error(f"[ERROR] Error checking trading signals for {symbol} {timeframe}: {e}")
    
    async def _check_entry_conditions(self, symbol: str, indicators: MarketIndicators, current_candle: OHLCV, market_regime: Optional[MarketRegime] = None) -> List[Dict[str, Any]]:
        """Check entry conditions for various strategies"""
        signals = []

        try:
            logger.debug(f"[SIGNAL_DEBUG] Checking entry conditions for {symbol}")

            # RSI oversold/overbought conditions
            signals.extend(self._check_rsi_signals(symbol, indicators))
            
            # EMA crossover signals
            signals.extend(self._check_ema_crossover_signals(symbol, indicators))
            
            # MACD signals
            signals.extend(self._check_macd_signals(symbol, indicators))
            
            # Bollinger Bands signals
            signals.extend(self._check_bollinger_signals(symbol, indicators))
            
            # Volume-based signals
            signals.extend(self._check_volume_signals(symbol, indicators, current_candle))
            
            # Market regime specific signals
            if market_regime:
                signals.extend(self._check_regime_specific_signals(symbol, indicators, market_regime))

            logger.debug(f"[SIGNAL_DEBUG] {symbol} Generated {len(signals)} signals")

        except Exception as e:
            logger.error(f"[ERROR] Error checking entry conditions: {e}")

        return signals
    
    def _check_rsi_signals(self, symbol: str, indicators: MarketIndicators) -> List[Dict[str, Any]]:
        """Check RSI-based signals"""
        signals = []
        
        if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
            logger.debug(f"[SIGNAL_DEBUG] {symbol} RSI(14): {indicators.rsi_14}")
            
            if indicators.rsi_14 < 30:  # Oversold
                logger.info(f"[SIGNAL_FOUND] {symbol} RSI oversold signal: RSI={indicators.rsi_14}")
                signals.append({
                    'strategy': 'rsi_oversold',
                    'action': 'BUY',
                    'confidence': (30 - indicators.rsi_14) / 30,
                    'reason': f'RSI oversold at {indicators.rsi_14:.2f}'
                })
            elif indicators.rsi_14 > 70:  # Overbought
                logger.info(f"[SIGNAL_FOUND] {symbol} RSI overbought signal: RSI={indicators.rsi_14}")
                signals.append({
                    'strategy': 'rsi_overbought',
                    'action': 'SELL',
                    'confidence': (indicators.rsi_14 - 70) / 30,
                    'reason': f'RSI overbought at {indicators.rsi_14:.2f}'
                })
        
        return signals
    
    def _check_ema_crossover_signals(self, symbol: str, indicators: MarketIndicators) -> List[Dict[str, Any]]:
        """Check EMA crossover signals"""
        signals = []
        
        if (hasattr(indicators, 'ema_5') and hasattr(indicators, 'ema_20') and
            indicators.ema_5 and indicators.ema_20):

            logger.debug(f"[SIGNAL_DEBUG] {symbol} EMA(5): {indicators.ema_5}, EMA(20): {indicators.ema_20}")
            
            crossover_strength = (indicators.ema_5 - indicators.ema_20) / indicators.ema_20
            
            if crossover_strength > 0.005:  # Bullish crossover (0.5% above)
                logger.info(f"[SIGNAL_FOUND] {symbol} EMA bullish crossover: EMA5={indicators.ema_5}, EMA20={indicators.ema_20}")
                signals.append({
                    'strategy': 'ema_crossover_bull',
                    'action': 'BUY',
                    'confidence': min(abs(crossover_strength) * 10, 1.0),
                    'reason': f'EMA5 above EMA20 by {crossover_strength*100:.2f}%'
                })
            elif crossover_strength < -0.005:  # Bearish crossover (0.5% below)
                logger.info(f"[SIGNAL_FOUND] {symbol} EMA bearish crossover: EMA5={indicators.ema_5}, EMA20={indicators.ema_20}")
                signals.append({
                    'strategy': 'ema_crossover_bear',
                    'action': 'SELL',
                    'confidence': min(abs(crossover_strength) * 10, 1.0),
                    'reason': f'EMA5 below EMA20 by {abs(crossover_strength)*100:.2f}%'
                })
        
        return signals
    
    def _check_macd_signals(self, symbol: str, indicators: MarketIndicators) -> List[Dict[str, Any]]:
        """Check MACD signals"""
        signals = []
        
        if (hasattr(indicators, 'macd') and hasattr(indicators, 'macd_signal') and
            indicators.macd and indicators.macd_signal):
            
            macd_diff = indicators.macd - indicators.macd_signal
            
            if macd_diff > 0 and abs(macd_diff) > 0.1:  # MACD above signal line
                signals.append({
                    'strategy': 'macd_bullish',
                    'action': 'BUY',
                    'confidence': min(abs(macd_diff) / 2.0, 1.0),
                    'reason': f'MACD above signal line by {macd_diff:.3f}'
                })
            elif macd_diff < 0 and abs(macd_diff) > 0.1:  # MACD below signal line
                signals.append({
                    'strategy': 'macd_bearish',
                    'action': 'SELL',
                    'confidence': min(abs(macd_diff) / 2.0, 1.0),
                    'reason': f'MACD below signal line by {abs(macd_diff):.3f}'
                })
        
        return signals
    
    def _check_bollinger_signals(self, symbol: str, indicators: MarketIndicators) -> List[Dict[str, Any]]:
        """Check Bollinger Bands signals"""
        signals = []
        
        if (hasattr(indicators, 'bb_position') and indicators.bb_position is not None):
            
            if indicators.bb_position < 0.1:  # Near lower band
                signals.append({
                    'strategy': 'bollinger_oversold',
                    'action': 'BUY',
                    'confidence': (0.1 - indicators.bb_position) / 0.1,
                    'reason': f'Price near lower Bollinger Band (position: {indicators.bb_position:.2f})'
                })
            elif indicators.bb_position > 0.9:  # Near upper band
                signals.append({
                    'strategy': 'bollinger_overbought',
                    'action': 'SELL',
                    'confidence': (indicators.bb_position - 0.9) / 0.1,
                    'reason': f'Price near upper Bollinger Band (position: {indicators.bb_position:.2f})'
                })
        
        return signals
    
    def _check_volume_signals(self, symbol: str, indicators: MarketIndicators, current_candle: OHLCV) -> List[Dict[str, Any]]:
        """Check volume-based signals"""
        signals = []
        
        # Volume ratio analysis (if available)
        if hasattr(indicators, 'volume_ratio') and indicators.volume_ratio and indicators.volume_ratio > 1.5:
            # High volume could indicate breakout
            if hasattr(indicators, 'bb_position') and indicators.bb_position:
                if indicators.bb_position > 0.8:  # High volume + upper BB
                    signals.append({
                        'strategy': 'volume_breakout_bull',
                        'action': 'BUY',
                        'confidence': min(indicators.volume_ratio / 3.0, 1.0),
                        'reason': f'High volume breakout (volume ratio: {indicators.volume_ratio:.2f})'
                    })
                elif indicators.bb_position < 0.2:  # High volume + lower BB
                    signals.append({
                        'strategy': 'volume_breakdown_bear',
                        'action': 'SELL',
                        'confidence': min(indicators.volume_ratio / 3.0, 1.0),
                        'reason': f'High volume breakdown (volume ratio: {indicators.volume_ratio:.2f})'
                    })
        
        return signals
    
    def _check_regime_specific_signals(self, symbol: str, indicators: MarketIndicators, market_regime: MarketRegime) -> List[Dict[str, Any]]:
        """Check market regime specific signals"""
        signals = []
        
        # In bull markets, favor buy signals
        if market_regime.regime == 'bull' and market_regime.confidence > 0.7:
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14 and 35 < indicators.rsi_14 < 50:
                signals.append({
                    'strategy': 'bull_market_dip',
                    'action': 'BUY',
                    'confidence': market_regime.confidence * 0.8,
                    'reason': f'Bull market dip buying opportunity (RSI: {indicators.rsi_14:.2f})'
                })
        
        # In bear markets, favor sell signals
        elif market_regime.regime == 'bear' and market_regime.confidence > 0.7:
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14 and 50 < indicators.rsi_14 < 65:
                signals.append({
                    'strategy': 'bear_market_rally',
                    'action': 'SELL',
                    'confidence': market_regime.confidence * 0.8,
                    'reason': f'Bear market rally selling opportunity (RSI: {indicators.rsi_14:.2f})'
                })
        
        return signals
    
    async def _create_trading_signal(self, symbol: str, signal_data: Dict[str, Any], indicators: MarketIndicators, current_candle: OHLCV, market_regime: Optional[MarketRegime] = None) -> Optional[TradingSignal]:
        """Create a trading signal"""
        try:
            current_price = current_candle.close

            # Calculate position size
            risk_config = self.config.strategy_config.get('risk_management', {})
            position_size_percent = risk_config.get('max_position_size_percent', 1.0)

            # Calculate stop loss and target based on ATR or fixed percentage
            atr = getattr(indicators, 'atr', None) if indicators else None
            atr = atr or current_price * 0.02  # 2% fallback

            if signal_data['action'] == 'BUY':
                stop_loss = current_price - (2 * atr)
                target = current_price + (3 * atr)  # 1.5 R:R ratio
            else:
                stop_loss = current_price + (2 * atr)
                target = current_price - (3 * atr)

            # Calculate quantity (placeholder - needs actual capital amount)
            capital = 100000  # Rs 1,00,000 default
            risk_amount = capital * (position_size_percent / 100)
            quantity = int(risk_amount / abs(current_price - stop_loss))

            signal = TradingSignal(
                symbol=symbol,
                strategy=signal_data['strategy'],
                action=signal_data['action'],
                price=current_price,
                target=target,
                stop_loss=stop_loss,
                quantity=quantity,
                confidence=signal_data['confidence'],
                market_regime=market_regime.regime if market_regime else 'unknown',
                timestamp=datetime.now(),
                context={
                    'indicators': asdict(indicators) if indicators else {},
                    'market_regime': asdict(market_regime) if market_regime else {},
                    'signal_reason': signal_data.get('reason', ''),
                    'timeframe': indicators.timeframe if indicators else '5min'
                }
            )

            return signal

        except Exception as e:
            logger.error(f"[ERROR] Error creating trading signal: {e}")
            return None
    
    async def _process_trading_signal(self, signal: TradingSignal):
        """Process and validate trading signal"""
        try:
            # Validate signal
            if not await self._validate_signal(signal):
                return

            # Add to active signals
            self.active_signals.append(signal)

            # Log signal
            await self._log_signal(signal)

            # Call signal handlers
            for handler in self.signal_handlers:
                try:
                    await handler(signal)
                except Exception as e:
                    logger.error(f"[ERROR] Error in signal handler: {e}")

            logger.info(f"[SIGNAL] Signal generated: {signal.symbol} | {signal.strategy} | {signal.action} | Confidence: {signal.confidence:.2%}")

        except Exception as e:
            logger.error(f"[ERROR] Error processing trading signal: {e}")
    
    async def _validate_signal(self, signal: TradingSignal) -> bool:
        """Validate trading signal against filters"""
        try:
            # Check confidence threshold
            ai_config = self.config.strategy_config.get('ai_model', {})
            min_confidence = ai_config.get('confidence_threshold', 0.6)
            if signal.confidence < min_confidence:
                logger.debug(f"[FILTER] Signal rejected: confidence {signal.confidence:.2%} < {min_confidence:.2%}")
                return False

            # Check maximum daily trades
            risk_config = self.config.strategy_config.get('risk_management', {})
            max_daily_trades = risk_config.get('max_daily_trades', 10)

            today_signals = [s for s in self.active_signals
                           if s.timestamp.date() == datetime.now().date()]
            if len(today_signals) >= max_daily_trades:
                logger.debug(f"[FILTER] Signal rejected: daily limit reached ({len(today_signals)}/{max_daily_trades})")
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error validating signal: {e}")
            return False
    
    async def _log_signal(self, signal: TradingSignal):
        """Log trading signal to file"""
        try:
            signal_config = self.config.logging_config.get('signal_logging', {})
            if not signal_config.get('enable', True):
                return

            log_file = signal_config.get('signal_log_file', 'logs/signals.log')

            # Prepare log entry
            log_entry = {
                'timestamp': signal.timestamp.isoformat(),
                'symbol': signal.symbol,
                'strategy': signal.strategy,
                'action': signal.action,
                'price': signal.price,
                'target': signal.target,
                'stop_loss': signal.stop_loss,
                'quantity': signal.quantity,
                'confidence': signal.confidence,
                'market_regime': signal.market_regime
            }

            if signal_config.get('include_market_context', True):
                log_entry['context'] = signal.context

            # Write to file
            with open(log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

        except Exception as e:
            logger.error(f"[ERROR] Error logging signal: {e}")
    
    def add_signal_handler(self, handler: Callable):
        """Add signal handler"""
        self.signal_handlers.append(handler)
    
    def get_active_signals(self) -> List[TradingSignal]:
        """Get active trading signals"""
        return self.active_signals.copy()
    
    def clear_old_signals(self, hours_back: int = 24):
        """Clear old signals"""
        from datetime import timedelta
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        self.active_signals = [s for s in self.active_signals if s.timestamp > cutoff_time]
