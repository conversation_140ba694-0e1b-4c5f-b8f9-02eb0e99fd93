#!/usr/bin/env python3
"""
Data Structures for Market Monitoring Agent

Contains all data classes and structures used across the market monitoring system.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Any, Optional


@dataclass
class MarketTick:
    """Real-time market tick data"""
    symbol: str
    token: str
    timestamp: datetime
    ltp: float
    volume: int
    open_price: float = None
    high_price: float = None
    low_price: float = None
    close_price: float = None


@dataclass
class OHLCV:
    """OHLC candle data"""
    symbol: str
    timestamp: datetime
    timeframe: str
    open: float
    high: float
    low: float
    close: float
    volume: int


@dataclass
class MarketIndicators:
    """Technical indicators for a symbol"""
    symbol: str
    timestamp: datetime
    timeframe: str = '5min'
    
    # Moving Averages
    ema_5: float = None
    ema_10: float = None
    ema_13: float = None
    ema_20: float = None
    ema_21: float = None
    ema_30: float = None
    ema_50: float = None
    ema_100: float = None
    sma_20: float = None
    
    # Momentum Indicators
    rsi_5: float = None
    rsi_14: float = None
    macd: float = None
    macd_signal: float = None
    macd_histogram: float = None
    stoch_k: float = None
    stoch_d: float = None
    
    # Other Indicators
    cci: float = None
    adx: float = None
    mfi: float = None
    bb_upper: float = None
    bb_lower: float = None
    bb_middle: float = None
    bb_position: float = None
    atr: float = None
    vwap: float = None
    volume_ratio: float = None
    supertrend: float = None
    supertrend_direction: int = None
    donchian_high: float = None
    donchian_low: float = None


@dataclass
class MarketRegime:
    """Market regime information"""
    regime: str  # 'bull', 'bear', 'sideways'
    confidence: float
    volatility_level: str  # 'low', 'medium', 'high'
    trend_strength: float
    market_breadth: float
    correlation_level: float
    timestamp: datetime


@dataclass
class TradingSignal:
    """Trading signal generated by the agent"""
    symbol: str
    strategy: str
    action: str  # 'BUY', 'SELL'
    price: float
    target: float
    stop_loss: float
    quantity: int
    confidence: float
    market_regime: str
    timestamp: datetime
    context: Dict[str, Any]


@dataclass
class MarketMonitoringConfig:
    """Configuration for Market Monitoring Agent"""
    
    # SmartAPI Configuration
    smartapi_config: Dict[str, Any]
    
    # Market Data Configuration
    market_data_config: Dict[str, Any]
    
    # Environment Detection Configuration
    environment_config: Dict[str, Any]
    
    # Strategy Triggering Configuration
    strategy_config: Dict[str, Any]
    
    # Notifications Configuration
    notifications_config: Dict[str, Any]
    
    # Logging Configuration
    logging_config: Dict[str, Any]
    
    # Storage Configuration
    storage_config: Dict[str, Any]
    
    # Performance Configuration
    performance_config: Dict[str, Any]
    
    # Error Handling Configuration
    error_handling_config: Dict[str, Any]


@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    active_signals: int
    subscribed_symbols: int
    market_regime: str
    connection_status: bool


@dataclass
class SignalContext:
    """Context information for trading signals"""
    indicators: Dict[str, Any]
    market_regime: Dict[str, Any]
    ai_recommendation: Optional[Dict[str, Any]]
    volume_analysis: Dict[str, Any]
    price_action: Dict[str, Any]


@dataclass
class WebSocketConfig:
    """WebSocket connection configuration"""
    auth_token: str
    api_key: str
    username: str
    feed_token: str
    max_retry_attempts: int = 5
    retry_delay_base: int = 5
    connection_timeout: int = 15
    heartbeat_interval: int = 30


@dataclass
class PerformanceMetrics:
    """Performance tracking metrics"""
    timestamp: datetime
    signals_generated: int
    symbols_analyzed: int
    cycle_duration: float
    memory_usage: float
    cpu_usage: float
    websocket_status: str
    data_quality_score: float


@dataclass
class RiskParameters:
    """Risk management parameters"""
    max_position_size_percent: float
    max_daily_trades: int
    min_confidence_threshold: float
    max_drawdown_percent: float
    stop_loss_multiplier: float
    target_multiplier: float
    min_liquidity: int


@dataclass
class NotificationTemplate:
    """Notification message templates"""
    signal_template: str
    regime_change_template: str
    error_template: str
    performance_template: str
