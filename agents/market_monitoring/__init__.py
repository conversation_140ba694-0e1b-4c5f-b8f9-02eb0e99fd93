#!/usr/bin/env python3
"""
Market Monitoring Module

This module contains all the components for the Market Monitoring Agent,
organized into separate modules for better maintainability and modularity.

Components:
- data_structures: Core data classes and structures
- config_manager: Configuration loading and management
- data_downloader: Historical and live data downloading
- websocket_manager: WebSocket connections and real-time data
- data_processor: Market data processing and OHLC generation
- indicator_calculator: Technical indicator calculations
- regime_detector: Market regime detection
- signal_generator: Trading signal generation
- ai_integration: AI model integration
- notification_manager: Notification services
- performance_monitor: System performance monitoring
"""

from .data_structures import (
    MarketTick,
    OHLCV,
    MarketIndicators,
    MarketRegime,
    TradingSignal,
    MarketMonitoringConfig,
    SystemMetrics,
    SignalContext,
    WebSocketConfig,
    PerformanceMetrics,
    RiskParameters,
    NotificationTemplate
)

from .config_manager import ConfigManager
from .data_downloader import DataDownloader
from .websocket_manager import WebSocketManager
from .data_processor import DataProcessor
from .indicator_calculator import IndicatorCalculator
from .regime_detector import RegimeDetector
from .signal_generator import SignalGenerator
from .ai_integration import AIIntegration
from .notification_manager import NotificationManager
from .performance_monitor import PerformanceMonitor

__all__ = [
    # Data structures
    'MarketTick',
    'OHLCV',
    'MarketIndicators',
    'MarketRegime',
    'TradingSignal',
    'MarketMonitoringConfig',
    'SystemMetrics',
    'SignalContext',
    'WebSocketConfig',
    'PerformanceMetrics',
    'RiskParameters',
    'NotificationTemplate',

    # Core components
    'ConfigManager',
    'DataDownloader',
    'WebSocketManager',
    'DataProcessor',
    'IndicatorCalculator',
    'RegimeDetector',
    'SignalGenerator',
    'AIIntegration',
    'NotificationManager',
    'PerformanceMonitor'
]

__version__ = "1.0.0"
__author__ = "Market Monitoring Team"
__description__ = "Modular Market Monitoring Agent Components"
