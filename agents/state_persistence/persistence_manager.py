#!/usr/bin/env python3
"""
State Persistence Manager - Comprehensive State Management for Multi-Agent Trading System

This module implements the state persistence layer that handles:
- Worker state persistence and recovery
- Active position tracking and reconciliation
- System state backup and restore
- Trade history and performance data storage

Features:
💾 Multiple Storage Backends
- File-based persistence (JSON/Parquet)
- SQLite database support
- Redis cache integration
- Extensible for other databases

🔄 State Recovery & Reconciliation
- System restart recovery
- Position reconciliation with broker
- State consistency validation
- Automatic backup and restore

📊 Performance Data Management
- Trade history storage
- Worker performance metrics
- System analytics and reporting
- Data compression and archival
"""

import os
import json
import sqlite3
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import polars as pl
import aiofiles
import pickle
import gzip

logger = logging.getLogger(__name__)


class PersistenceBackend(Enum):
    """Supported persistence backends"""
    FILE = "file"
    SQLITE = "sqlite"
    REDIS = "redis"
    POSTGRESQL = "postgresql"


@dataclass
class PersistenceConfig:
    """Configuration for persistence manager"""
    backend: PersistenceBackend
    base_path: str
    backup_interval_minutes: int = 5
    max_backup_files: int = 100
    compression_enabled: bool = True
    encryption_enabled: bool = False
    
    # Database specific settings
    db_host: Optional[str] = None
    db_port: Optional[int] = None
    db_name: Optional[str] = None
    db_username: Optional[str] = None
    db_password: Optional[str] = None
    
    # Redis specific settings
    redis_host: Optional[str] = None
    redis_port: Optional[int] = None
    redis_password: Optional[str] = None


@dataclass
class StateSnapshot:
    """Complete system state snapshot"""
    timestamp: datetime
    system_state: Dict[str, Any]
    worker_states: Dict[str, Any]
    active_positions: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    trade_history: List[Dict[str, Any]]
    system_metrics: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'system_state': self.system_state,
            'worker_states': self.worker_states,
            'active_positions': self.active_positions,
            'performance_metrics': self.performance_metrics,
            'trade_history': self.trade_history,
            'system_metrics': self.system_metrics
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StateSnapshot':
        """Create from dictionary"""
        return cls(
            timestamp=datetime.fromisoformat(data['timestamp']),
            system_state=data.get('system_state', {}),
            worker_states=data.get('worker_states', {}),
            active_positions=data.get('active_positions', {}),
            performance_metrics=data.get('performance_metrics', {}),
            trade_history=data.get('trade_history', []),
            system_metrics=data.get('system_metrics', {})
        )


class PersistenceManager:
    """
    Manages state persistence for the multi-agent trading system
    """
    
    def __init__(self, config: PersistenceConfig = None):
        """Initialize Persistence Manager"""
        self.config = config or self._load_default_config()
        
        # Storage backends
        self.file_backend: Optional['FileBackend'] = None
        self.sqlite_backend: Optional['SQLiteBackend'] = None
        self.redis_backend: Optional['RedisBackend'] = None
        
        # State management
        self.current_snapshot: Optional[StateSnapshot] = None
        self.backup_history: List[str] = []
        
        # Background tasks
        self.backup_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info(f"[INIT] Persistence Manager initialized - Backend: {self.config.backend.value}")
    
    def _load_default_config(self) -> PersistenceConfig:
        """Load default configuration from environment"""
        backend_type = os.getenv('STATE_PERSISTENCE_TYPE', 'file')
        base_path = os.getenv('STATE_PERSISTENCE_PATH', 'data/system_state')
        
        return PersistenceConfig(
            backend=PersistenceBackend(backend_type),
            base_path=base_path,
            backup_interval_minutes=int(os.getenv('STATE_BACKUP_INTERVAL_MINUTES', '5')),
            compression_enabled=os.getenv('STATE_COMPRESSION_ENABLED', 'true').lower() == 'true'
        )
    
    async def initialize(self) -> bool:
        """Initialize the persistence manager"""
        try:
            logger.info("[INIT] Initializing Persistence Manager...")
            
            # Ensure base directory exists
            Path(self.config.base_path).mkdir(parents=True, exist_ok=True)
            
            # Initialize appropriate backend
            if self.config.backend == PersistenceBackend.FILE:
                self.file_backend = FileBackend(self.config)
                await self.file_backend.initialize()
            elif self.config.backend == PersistenceBackend.SQLITE:
                self.sqlite_backend = SQLiteBackend(self.config)
                await self.sqlite_backend.initialize()
            elif self.config.backend == PersistenceBackend.REDIS:
                self.redis_backend = RedisBackend(self.config)
                await self.redis_backend.initialize()
            
            # Load backup history
            await self._load_backup_history()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.running = True
            logger.info("[SUCCESS] Persistence Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Persistence Manager: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the persistence manager"""
        try:
            logger.info("[SHUTDOWN] Shutting down Persistence Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.backup_task:
                self.backup_task.cancel()
            if self.cleanup_task:
                self.cleanup_task.cancel()
            
            # Final backup
            if self.current_snapshot:
                await self.save_state_snapshot(self.current_snapshot)
            
            # Shutdown backends
            if self.file_backend:
                await self.file_backend.shutdown()
            if self.sqlite_backend:
                await self.sqlite_backend.shutdown()
            if self.redis_backend:
                await self.redis_backend.shutdown()
            
            logger.info("[SUCCESS] Persistence Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during Persistence Manager shutdown: {e}")
    
    async def save_state_snapshot(self, snapshot: StateSnapshot) -> bool:
        """Save a complete state snapshot"""
        try:
            logger.debug(f"[SAVE] Saving state snapshot: {snapshot.timestamp}")
            
            # Update current snapshot
            self.current_snapshot = snapshot
            
            # Save using appropriate backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                success = await self.file_backend.save_snapshot(snapshot)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                success = await self.sqlite_backend.save_snapshot(snapshot)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                success = await self.redis_backend.save_snapshot(snapshot)
            else:
                logger.error("[ERROR] No valid backend available for saving")
                return False
            
            if success:
                # Update backup history
                backup_id = f"snapshot_{snapshot.timestamp.strftime('%Y%m%d_%H%M%S')}"
                self.backup_history.append(backup_id)
                
                # Cleanup old backups
                await self._cleanup_old_backups()
                
                logger.debug(f"[SUCCESS] State snapshot saved: {backup_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save state snapshot: {e}")
            return False
    
    async def load_latest_snapshot(self) -> Optional[StateSnapshot]:
        """Load the latest state snapshot"""
        try:
            logger.info("[LOAD] Loading latest state snapshot...")
            
            # Load using appropriate backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                snapshot = await self.file_backend.load_latest_snapshot()
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                snapshot = await self.sqlite_backend.load_latest_snapshot()
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                snapshot = await self.redis_backend.load_latest_snapshot()
            else:
                logger.error("[ERROR] No valid backend available for loading")
                return None
            
            if snapshot:
                self.current_snapshot = snapshot
                logger.info(f"[SUCCESS] Loaded state snapshot from: {snapshot.timestamp}")
            else:
                logger.info("[INFO] No existing state snapshot found")
            
            return snapshot
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load state snapshot: {e}")
            return None
    
    async def load_snapshot_by_timestamp(self, timestamp: datetime) -> Optional[StateSnapshot]:
        """Load a specific state snapshot by timestamp"""
        try:
            logger.info(f"[LOAD] Loading state snapshot from: {timestamp}")
            
            # Load using appropriate backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                snapshot = await self.file_backend.load_snapshot_by_timestamp(timestamp)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                snapshot = await self.sqlite_backend.load_snapshot_by_timestamp(timestamp)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                snapshot = await self.redis_backend.load_snapshot_by_timestamp(timestamp)
            else:
                logger.error("[ERROR] No valid backend available for loading")
                return None
            
            if snapshot:
                logger.info(f"[SUCCESS] Loaded specific state snapshot from: {snapshot.timestamp}")
            else:
                logger.warning(f"[WARNING] No state snapshot found for timestamp: {timestamp}")
            
            return snapshot
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load state snapshot by timestamp: {e}")
            return None
    
    async def save_worker_state(self, worker_id: str, worker_state: Dict[str, Any]) -> bool:
        """Save individual worker state"""
        try:
            # Update current snapshot if exists
            if self.current_snapshot:
                self.current_snapshot.worker_states[worker_id] = worker_state
                self.current_snapshot.timestamp = datetime.now()
            
            # Save using backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                return await self.file_backend.save_worker_state(worker_id, worker_state)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                return await self.sqlite_backend.save_worker_state(worker_id, worker_state)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                return await self.redis_backend.save_worker_state(worker_id, worker_state)
            
            return False
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save worker state for {worker_id}: {e}")
            return False
    
    async def load_worker_state(self, worker_id: str) -> Optional[Dict[str, Any]]:
        """Load individual worker state"""
        try:
            # Check current snapshot first
            if self.current_snapshot and worker_id in self.current_snapshot.worker_states:
                return self.current_snapshot.worker_states[worker_id]
            
            # Load from backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                return await self.file_backend.load_worker_state(worker_id)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                return await self.sqlite_backend.load_worker_state(worker_id)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                return await self.redis_backend.load_worker_state(worker_id)
            
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load worker state for {worker_id}: {e}")
            return None

    async def save_trade_record(self, trade_record: Dict[str, Any]) -> bool:
        """Save individual trade record"""
        try:
            # Update current snapshot if exists
            if self.current_snapshot:
                self.current_snapshot.trade_history.append(trade_record)
                self.current_snapshot.timestamp = datetime.now()

            # Save using backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                return await self.file_backend.save_trade_record(trade_record)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                return await self.sqlite_backend.save_trade_record(trade_record)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                return await self.redis_backend.save_trade_record(trade_record)

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to save trade record: {e}")
            return False

    async def get_trade_history(self, start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get trade history within date range"""
        try:
            # Load from backend
            if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                return await self.file_backend.get_trade_history(start_date, end_date)
            elif self.config.backend == PersistenceBackend.SQLITE and self.sqlite_backend:
                return await self.sqlite_backend.get_trade_history(start_date, end_date)
            elif self.config.backend == PersistenceBackend.REDIS and self.redis_backend:
                return await self.redis_backend.get_trade_history(start_date, end_date)

            return []

        except Exception as e:
            logger.error(f"[ERROR] Failed to get trade history: {e}")
            return []

    async def _start_background_tasks(self):
        """Start background maintenance tasks"""
        try:
            # Start periodic backup task
            self.backup_task = asyncio.create_task(self._periodic_backup_loop())

            # Start cleanup task
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())

            logger.info("[TASKS] Background tasks started")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start background tasks: {e}")

    async def _periodic_backup_loop(self):
        """Periodic backup loop"""
        while self.running:
            try:
                await asyncio.sleep(self.config.backup_interval_minutes * 60)

                if self.current_snapshot:
                    # Update timestamp and save
                    self.current_snapshot.timestamp = datetime.now()
                    await self.save_state_snapshot(self.current_snapshot)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Periodic backup error: {e}")
                await asyncio.sleep(60)

    async def _cleanup_loop(self):
        """Cleanup old backups loop"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_old_backups()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Cleanup loop error: {e}")
                await asyncio.sleep(3600)

    async def _load_backup_history(self):
        """Load backup history"""
        try:
            history_file = Path(self.config.base_path) / "backup_history.json"

            if history_file.exists():
                async with aiofiles.open(history_file, 'r') as f:
                    content = await f.read()
                    self.backup_history = json.loads(content)

                logger.info(f"[HISTORY] Loaded {len(self.backup_history)} backup entries")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load backup history: {e}")

    async def _save_backup_history(self):
        """Save backup history"""
        try:
            history_file = Path(self.config.base_path) / "backup_history.json"

            async with aiofiles.open(history_file, 'w') as f:
                await f.write(json.dumps(self.backup_history, indent=2))

        except Exception as e:
            logger.error(f"[ERROR] Failed to save backup history: {e}")

    async def _cleanup_old_backups(self):
        """Cleanup old backup files"""
        try:
            if len(self.backup_history) > self.config.max_backup_files:
                # Remove oldest backups
                to_remove = self.backup_history[:-self.config.max_backup_files]

                for backup_id in to_remove:
                    # Remove from backend
                    if self.config.backend == PersistenceBackend.FILE and self.file_backend:
                        await self.file_backend.remove_backup(backup_id)

                    # Remove from history
                    self.backup_history.remove(backup_id)

                # Save updated history
                await self._save_backup_history()

                logger.info(f"[CLEANUP] Removed {len(to_remove)} old backups")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old backups: {e}")

    def get_persistence_stats(self) -> Dict[str, Any]:
        """Get persistence statistics"""
        try:
            return {
                'backend': self.config.backend.value,
                'base_path': self.config.base_path,
                'backup_count': len(self.backup_history),
                'current_snapshot_time': self.current_snapshot.timestamp.isoformat() if self.current_snapshot else None,
                'backup_interval_minutes': self.config.backup_interval_minutes,
                'max_backup_files': self.config.max_backup_files,
                'compression_enabled': self.config.compression_enabled,
                'running': self.running
            }

        except Exception as e:
            logger.error(f"[ERROR] Failed to get persistence stats: {e}")
            return {'error': str(e)}


# ═══════════════════════════════════════════════════════════════════════════════
# BACKEND IMPLEMENTATIONS
# ═══════════════════════════════════════════════════════════════════════════════

class FileBackend:
    """File-based persistence backend"""

    def __init__(self, config: PersistenceConfig):
        self.config = config
        self.base_path = Path(config.base_path)

    async def initialize(self):
        """Initialize file backend"""
        self.base_path.mkdir(parents=True, exist_ok=True)

        # Create subdirectories
        (self.base_path / "snapshots").mkdir(exist_ok=True)
        (self.base_path / "workers").mkdir(exist_ok=True)
        (self.base_path / "trades").mkdir(exist_ok=True)

        logger.info(f"[FILE] File backend initialized: {self.base_path}")

    async def shutdown(self):
        """Shutdown file backend"""
        pass

    async def save_snapshot(self, snapshot: StateSnapshot) -> bool:
        """Save state snapshot to file"""
        try:
            timestamp_str = snapshot.timestamp.strftime('%Y%m%d_%H%M%S')
            filename = f"snapshot_{timestamp_str}.json"
            filepath = self.base_path / "snapshots" / filename

            # Prepare data
            data = snapshot.to_dict()

            # Compress if enabled
            if self.config.compression_enabled:
                content = json.dumps(data, indent=2).encode('utf-8')
                compressed_content = gzip.compress(content)

                async with aiofiles.open(f"{filepath}.gz", 'wb') as f:
                    await f.write(compressed_content)
            else:
                async with aiofiles.open(filepath, 'w') as f:
                    await f.write(json.dumps(data, indent=2))

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save snapshot to file: {e}")
            return False

    async def load_latest_snapshot(self) -> Optional[StateSnapshot]:
        """Load latest snapshot from file"""
        try:
            snapshots_dir = self.base_path / "snapshots"

            if not snapshots_dir.exists():
                return None

            # Find latest snapshot file
            snapshot_files = list(snapshots_dir.glob("snapshot_*.json*"))

            if not snapshot_files:
                return None

            # Sort by modification time (latest first)
            latest_file = max(snapshot_files, key=lambda f: f.stat().st_mtime)

            # Load the file
            if latest_file.suffix == '.gz':
                async with aiofiles.open(latest_file, 'rb') as f:
                    compressed_content = await f.read()
                    content = gzip.decompress(compressed_content).decode('utf-8')
                    data = json.loads(content)
            else:
                async with aiofiles.open(latest_file, 'r') as f:
                    content = await f.read()
                    data = json.loads(content)

            return StateSnapshot.from_dict(data)

        except Exception as e:
            logger.error(f"[ERROR] Failed to load latest snapshot from file: {e}")
            return None

    async def load_snapshot_by_timestamp(self, timestamp: datetime) -> Optional[StateSnapshot]:
        """Load snapshot by timestamp from file"""
        try:
            timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S')
            filename = f"snapshot_{timestamp_str}.json"
            filepath = self.base_path / "snapshots" / filename

            # Try compressed version first
            compressed_filepath = Path(f"{filepath}.gz")

            if compressed_filepath.exists():
                async with aiofiles.open(compressed_filepath, 'rb') as f:
                    compressed_content = await f.read()
                    content = gzip.decompress(compressed_content).decode('utf-8')
                    data = json.loads(content)
            elif filepath.exists():
                async with aiofiles.open(filepath, 'r') as f:
                    content = await f.read()
                    data = json.loads(content)
            else:
                return None

            return StateSnapshot.from_dict(data)

        except Exception as e:
            logger.error(f"[ERROR] Failed to load snapshot by timestamp from file: {e}")
            return None

    async def save_worker_state(self, worker_id: str, worker_state: Dict[str, Any]) -> bool:
        """Save worker state to file"""
        try:
            filepath = self.base_path / "workers" / f"{worker_id}.json"

            async with aiofiles.open(filepath, 'w') as f:
                await f.write(json.dumps(worker_state, indent=2, default=str))

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save worker state to file: {e}")
            return False

    async def load_worker_state(self, worker_id: str) -> Optional[Dict[str, Any]]:
        """Load worker state from file"""
        try:
            filepath = self.base_path / "workers" / f"{worker_id}.json"

            if not filepath.exists():
                return None

            async with aiofiles.open(filepath, 'r') as f:
                content = await f.read()
                return json.loads(content)

        except Exception as e:
            logger.error(f"[ERROR] Failed to load worker state from file: {e}")
            return None

    async def save_trade_record(self, trade_record: Dict[str, Any]) -> bool:
        """Save trade record to file"""
        try:
            # Create daily trade file
            trade_date = datetime.now().strftime('%Y%m%d')
            filepath = self.base_path / "trades" / f"trades_{trade_date}.jsonl"

            # Append to file (JSONL format)
            async with aiofiles.open(filepath, 'a') as f:
                await f.write(json.dumps(trade_record, default=str) + '\n')

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save trade record to file: {e}")
            return False

    async def get_trade_history(self, start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get trade history from files"""
        try:
            trades = []
            trades_dir = self.base_path / "trades"

            if not trades_dir.exists():
                return trades

            # Get date range
            if not start_date:
                start_date = datetime.now() - timedelta(days=30)  # Default to last 30 days
            if not end_date:
                end_date = datetime.now()

            # Iterate through date range
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y%m%d')
                filepath = trades_dir / f"trades_{date_str}.jsonl"

                if filepath.exists():
                    async with aiofiles.open(filepath, 'r') as f:
                        async for line in f:
                            if line.strip():
                                trade_record = json.loads(line.strip())
                                trades.append(trade_record)

                current_date += timedelta(days=1)

            return trades

        except Exception as e:
            logger.error(f"[ERROR] Failed to get trade history from files: {e}")
            return []

    async def remove_backup(self, backup_id: str) -> bool:
        """Remove backup file"""
        try:
            # Extract timestamp from backup_id
            timestamp_str = backup_id.replace('snapshot_', '')
            filename = f"snapshot_{timestamp_str}.json"

            filepath = self.base_path / "snapshots" / filename
            compressed_filepath = Path(f"{filepath}.gz")

            # Remove both compressed and uncompressed versions
            if compressed_filepath.exists():
                compressed_filepath.unlink()
            if filepath.exists():
                filepath.unlink()

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to remove backup file: {e}")
            return False


class SQLiteBackend:
    """SQLite database persistence backend"""

    def __init__(self, config: PersistenceConfig):
        self.config = config
        self.db_path = Path(config.base_path) / "trading_system.db"
        self.connection: Optional[sqlite3.Connection] = None

    async def initialize(self):
        """Initialize SQLite backend"""
        try:
            # Ensure directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            # Create connection
            self.connection = sqlite3.connect(str(self.db_path))
            self.connection.row_factory = sqlite3.Row

            # Create tables
            await self._create_tables()

            logger.info(f"[SQLITE] SQLite backend initialized: {self.db_path}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize SQLite backend: {e}")
            raise

    async def shutdown(self):
        """Shutdown SQLite backend"""
        if self.connection:
            self.connection.close()

    async def _create_tables(self):
        """Create database tables"""
        try:
            cursor = self.connection.cursor()

            # State snapshots table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS state_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    system_state TEXT,
                    worker_states TEXT,
                    active_positions TEXT,
                    performance_metrics TEXT,
                    trade_history TEXT,
                    system_metrics TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Worker states table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS worker_states (
                    worker_id TEXT PRIMARY KEY,
                    state_data TEXT NOT NULL,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Trade records table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE,
                    worker_id TEXT,
                    symbol TEXT,
                    trade_data TEXT NOT NULL,
                    timestamp TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_snapshots_timestamp ON state_snapshots(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trade_records(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trade_records(symbol)')

            self.connection.commit()

        except Exception as e:
            logger.error(f"[ERROR] Failed to create SQLite tables: {e}")
            raise

    async def save_snapshot(self, snapshot: StateSnapshot) -> bool:
        """Save state snapshot to SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT INTO state_snapshots
                (timestamp, system_state, worker_states, active_positions,
                 performance_metrics, trade_history, system_metrics)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                snapshot.timestamp.isoformat(),
                json.dumps(snapshot.system_state),
                json.dumps(snapshot.worker_states),
                json.dumps(snapshot.active_positions),
                json.dumps(snapshot.performance_metrics),
                json.dumps(snapshot.trade_history),
                json.dumps(snapshot.system_metrics)
            ))

            self.connection.commit()
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save snapshot to SQLite: {e}")
            return False

    async def load_latest_snapshot(self) -> Optional[StateSnapshot]:
        """Load latest snapshot from SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT * FROM state_snapshots
                ORDER BY timestamp DESC
                LIMIT 1
            ''')

            row = cursor.fetchone()
            if not row:
                return None

            return StateSnapshot(
                timestamp=datetime.fromisoformat(row['timestamp']),
                system_state=json.loads(row['system_state'] or '{}'),
                worker_states=json.loads(row['worker_states'] or '{}'),
                active_positions=json.loads(row['active_positions'] or '{}'),
                performance_metrics=json.loads(row['performance_metrics'] or '{}'),
                trade_history=json.loads(row['trade_history'] or '[]'),
                system_metrics=json.loads(row['system_metrics'] or '{}')
            )

        except Exception as e:
            logger.error(f"[ERROR] Failed to load latest snapshot from SQLite: {e}")
            return None

    async def load_snapshot_by_timestamp(self, timestamp: datetime) -> Optional[StateSnapshot]:
        """Load snapshot by timestamp from SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT * FROM state_snapshots
                WHERE timestamp = ?
            ''', (timestamp.isoformat(),))

            row = cursor.fetchone()
            if not row:
                return None

            return StateSnapshot(
                timestamp=datetime.fromisoformat(row['timestamp']),
                system_state=json.loads(row['system_state'] or '{}'),
                worker_states=json.loads(row['worker_states'] or '{}'),
                active_positions=json.loads(row['active_positions'] or '{}'),
                performance_metrics=json.loads(row['performance_metrics'] or '{}'),
                trade_history=json.loads(row['trade_history'] or '[]'),
                system_metrics=json.loads(row['system_metrics'] or '{}')
            )

        except Exception as e:
            logger.error(f"[ERROR] Failed to load snapshot by timestamp from SQLite: {e}")
            return None

    async def save_worker_state(self, worker_id: str, worker_state: Dict[str, Any]) -> bool:
        """Save worker state to SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO worker_states (worker_id, state_data)
                VALUES (?, ?)
            ''', (worker_id, json.dumps(worker_state, default=str)))

            self.connection.commit()
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save worker state to SQLite: {e}")
            return False

    async def load_worker_state(self, worker_id: str) -> Optional[Dict[str, Any]]:
        """Load worker state from SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('SELECT state_data FROM worker_states WHERE worker_id = ?', (worker_id,))
            row = cursor.fetchone()

            if row:
                return json.loads(row['state_data'])
            return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load worker state from SQLite: {e}")
            return None

    async def save_trade_record(self, trade_record: Dict[str, Any]) -> bool:
        """Save trade record to SQLite"""
        try:
            cursor = self.connection.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO trade_records
                (trade_id, worker_id, symbol, trade_data, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                trade_record.get('trade_id'),
                trade_record.get('worker_id'),
                trade_record.get('symbol'),
                json.dumps(trade_record, default=str),
                trade_record.get('timestamp', datetime.now().isoformat())
            ))

            self.connection.commit()
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to save trade record to SQLite: {e}")
            return False

    async def get_trade_history(self, start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get trade history from SQLite"""
        try:
            cursor = self.connection.cursor()

            if start_date and end_date:
                cursor.execute('''
                    SELECT trade_data FROM trade_records
                    WHERE timestamp BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                ''', (start_date.isoformat(), end_date.isoformat()))
            else:
                cursor.execute('''
                    SELECT trade_data FROM trade_records
                    ORDER BY timestamp DESC
                    LIMIT 1000
                ''')

            rows = cursor.fetchall()
            return [json.loads(row['trade_data']) for row in rows]

        except Exception as e:
            logger.error(f"[ERROR] Failed to get trade history from SQLite: {e}")
            return []


class RedisBackend:
    """Redis cache persistence backend (placeholder implementation)"""

    def __init__(self, config: PersistenceConfig):
        self.config = config
        self.redis_client = None

    async def initialize(self):
        """Initialize Redis backend"""
        try:
            # This would initialize Redis connection
            # For now, just log that it's not implemented
            logger.warning("[REDIS] Redis backend not fully implemented - using placeholder")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Redis backend: {e}")
            raise

    async def shutdown(self):
        """Shutdown Redis backend"""
        if self.redis_client:
            # Close Redis connection
            pass

    async def save_snapshot(self, snapshot: StateSnapshot) -> bool:
        """Save state snapshot to Redis"""
        logger.warning("[REDIS] save_snapshot not implemented")
        return False

    async def load_latest_snapshot(self) -> Optional[StateSnapshot]:
        """Load latest snapshot from Redis"""
        logger.warning("[REDIS] load_latest_snapshot not implemented")
        return None

    async def load_snapshot_by_timestamp(self, timestamp: datetime) -> Optional[StateSnapshot]:
        """Load snapshot by timestamp from Redis"""
        logger.warning("[REDIS] load_snapshot_by_timestamp not implemented")
        return None

    async def save_worker_state(self, worker_id: str, worker_state: Dict[str, Any]) -> bool:
        """Save worker state to Redis"""
        logger.warning("[REDIS] save_worker_state not implemented")
        return False

    async def load_worker_state(self, worker_id: str) -> Optional[Dict[str, Any]]:
        """Load worker state from Redis"""
        logger.warning("[REDIS] load_worker_state not implemented")
        return None

    async def save_trade_record(self, trade_record: Dict[str, Any]) -> bool:
        """Save trade record to Redis"""
        logger.warning("[REDIS] save_trade_record not implemented")
        return False

    async def get_trade_history(self, start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get trade history from Redis"""
        logger.warning("[REDIS] get_trade_history not implemented")
        return []


# ═══════════════════════════════════════════════════════════════════════════════
# UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def create_persistence_manager(config: PersistenceConfig = None) -> PersistenceManager:
    """Factory function to create PersistenceManager instance"""
    return PersistenceManager(config)


async def create_state_snapshot(
    system_state: Dict[str, Any],
    worker_states: Dict[str, Any],
    active_positions: Dict[str, Any] = None,
    performance_metrics: Dict[str, Any] = None,
    trade_history: List[Dict[str, Any]] = None,
    system_metrics: Dict[str, Any] = None
) -> StateSnapshot:
    """Helper function to create state snapshot"""
    return StateSnapshot(
        timestamp=datetime.now(),
        system_state=system_state,
        worker_states=worker_states,
        active_positions=active_positions or {},
        performance_metrics=performance_metrics or {},
        trade_history=trade_history or [],
        system_metrics=system_metrics or {}
    )


# ═══════════════════════════════════════════════════════════════════════════════
# MAIN FUNCTION FOR TESTING
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Test function for PersistenceManager"""
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create persistence manager
    config = PersistenceConfig(
        backend=PersistenceBackend.FILE,
        base_path="test_persistence",
        backup_interval_minutes=1
    )

    manager = PersistenceManager(config)

    try:
        # Initialize
        await manager.initialize()

        # Create test snapshot
        test_snapshot = await create_state_snapshot(
            system_state={'max_trades': 5, 'running': True},
            worker_states={
                'worker_001': {'state': 'IDLE', 'symbols': ['RELIANCE-EQ']},
                'worker_002': {'state': 'ACTIVE', 'symbols': ['TCS-EQ']}
            },
            system_metrics={'daily_pnl': 150.0, 'trades_today': 3}
        )

        # Save snapshot
        success = await manager.save_state_snapshot(test_snapshot)
        print(f"Save snapshot success: {success}")

        # Load snapshot
        loaded_snapshot = await manager.load_latest_snapshot()
        if loaded_snapshot:
            print(f"Loaded snapshot from: {loaded_snapshot.timestamp}")
            print(f"System state: {loaded_snapshot.system_state}")

        # Test worker state
        await manager.save_worker_state('worker_003', {'state': 'COOLDOWN', 'symbols': ['INFY-EQ']})
        worker_state = await manager.load_worker_state('worker_003')
        print(f"Worker state: {worker_state}")

        # Test trade record
        trade_record = {
            'trade_id': 'test_trade_001',
            'worker_id': 'worker_001',
            'symbol': 'RELIANCE-EQ',
            'pnl': 75.0,
            'timestamp': datetime.now().isoformat()
        }
        await manager.save_trade_record(trade_record)

        # Get stats
        stats = manager.get_persistence_stats()
        print(f"Persistence stats: {stats}")

        # Wait a bit then shutdown
        await asyncio.sleep(2)
        await manager.shutdown()

        print("Test completed successfully")

    except Exception as e:
        print(f"Test failed: {e}")
        await manager.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
