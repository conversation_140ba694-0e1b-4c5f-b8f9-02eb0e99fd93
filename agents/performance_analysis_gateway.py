#!/usr/bin/env python3
"""
Performance Analysis Gateway

Centralized gateway for all performance analysis functionality.
Acts as the main interface for other agents to communicate with performance analysis modules.

Features:
- Unified API for all performance analysis operations
- Real-time data ingestion and processing
- Advanced metrics calculations
- ML-powered performance predictions
- Inter-agent communication hub
- Event-driven architecture
- Health monitoring and diagnostics
"""

import asyncio
import logging
import json
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import polars as pl

# Import performance analysis modules
from performance_analysis.data_ingestion import (
    StreamProcessor, TradeReconciler, HistoricalBackfiller, 
    DataValidator, OrderMatcher, StreamConfig, ReconciliationConfig, 
    BackfillConfig, ValidationRule, ValidationSeverity
)
from performance_analysis.advanced_metrics import (
    CoreMetricsCalculator, RiskMetricsCalculator, DrawdownAnalyzer
)

logger = logging.getLogger(__name__)

@dataclass
class PerformanceAnalysisConfig:
    """Configuration for performance analysis gateway"""
    # Data ingestion settings
    stream_config: StreamConfig
    reconciliation_config: ReconciliationConfig
    backfill_config: BackfillConfig
    
    # Analysis settings
    risk_free_rate: float = 0.06
    benchmark_return: float = 0.12
    initial_capital: float = 100000
    
    # Gateway settings
    api_port: int = 8080
    websocket_port: int = 8081
    max_concurrent_requests: int = 100
    request_timeout_seconds: int = 30
    
    # Health monitoring
    health_check_interval_seconds: int = 60
    metrics_retention_days: int = 30

class PerformanceAnalysisGateway:
    """
    Main gateway for performance analysis operations
    """
    
    def __init__(self, config_path: str = "config/performance_analysis_gateway_config.yaml"):
        """Initialize the performance analysis gateway"""
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Initialize core components
        self.stream_processor = StreamProcessor(self.config.stream_config)
        self.trade_reconciler = TradeReconciler(self.config.reconciliation_config)
        self.historical_backfiller = HistoricalBackfiller(self.config.backfill_config)
        self.data_validator = DataValidator()
        self.order_matcher = OrderMatcher()
        
        # Initialize metrics calculators
        self.core_metrics_calculator = CoreMetricsCalculator(self.config.risk_free_rate)
        self.risk_metrics_calculator = RiskMetricsCalculator(
            self.config.risk_free_rate, 
            self.config.benchmark_return
        )
        self.drawdown_analyzer = DrawdownAnalyzer()
        
        # Gateway state
        self.is_running = False
        self.registered_agents = {}
        self.event_handlers = {}
        
        # Performance tracking
        self.gateway_metrics = {
            'start_time': None,
            'requests_processed': 0,
            'events_processed': 0,
            'errors_count': 0,
            'last_health_check': None,
            'connected_agents': 0
        }
        
        # Setup event handlers
        self._setup_event_handlers()
        
        logger.info("PerformanceAnalysisGateway initialized")

    def _load_config(self, config_path: str) -> PerformanceAnalysisConfig:
        """Load configuration from file"""
        try:
            if Path(config_path).exists():
                with open(config_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                
                # Create configuration objects
                stream_config = StreamConfig(**config_data.get('stream_config', {}))
                reconciliation_config = ReconciliationConfig(**config_data.get('reconciliation_config', {}))
                backfill_config = BackfillConfig(**config_data.get('backfill_config', {}))
                
                return PerformanceAnalysisConfig(
                    stream_config=stream_config,
                    reconciliation_config=reconciliation_config,
                    backfill_config=backfill_config,
                    **config_data.get('gateway_config', {})
                )
            else:
                logger.warning(f"Config file not found: {config_path}, using defaults")
                return PerformanceAnalysisConfig(
                    stream_config=StreamConfig(),
                    reconciliation_config=ReconciliationConfig(),
                    backfill_config=BackfillConfig()
                )
                
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return PerformanceAnalysisConfig(
                stream_config=StreamConfig(),
                reconciliation_config=ReconciliationConfig(),
                backfill_config=BackfillConfig()
            )

    def _setup_event_handlers(self):
        """Setup event handlers for different data types"""
        # Register handlers with stream processor
        self.stream_processor.register_handler('trade', self._handle_trade_event)
        self.stream_processor.register_handler('signal', self._handle_signal_event)
        self.stream_processor.register_handler('order', self._handle_order_event)
        self.stream_processor.register_handler('market_data', self._handle_market_data_event)

    async def start(self):
        """Start the performance analysis gateway"""
        if self.is_running:
            logger.warning("Gateway already running")
            return
        
        self.is_running = True
        self.gateway_metrics['start_time'] = datetime.now()
        
        logger.info("Starting Performance Analysis Gateway...")
        
        try:
            # Start core components
            await self.stream_processor.start()
            
            # Start background tasks
            tasks = [
                asyncio.create_task(self._health_monitoring_loop()),
                asyncio.create_task(self._metrics_cleanup_loop()),
                asyncio.create_task(self._periodic_reconciliation_loop())
            ]
            
            logger.info("Performance Analysis Gateway started successfully")
            
            # Wait for tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error starting gateway: {e}")
            await self.stop()

    async def stop(self):
        """Stop the performance analysis gateway"""
        logger.info("Stopping Performance Analysis Gateway...")
        
        self.is_running = False
        
        # Stop components
        await self.stream_processor.stop()
        
        logger.info("Performance Analysis Gateway stopped")

    # ═══════════════════════════════════════════════════════════════════════════════
    # DATA INGESTION API
    # ═══════════════════════════════════════════════════════════════════════════════

    async def ingest_trade_data(self, trade_data: Dict[str, Any], source: str = "unknown") -> Dict[str, Any]:
        """
        Ingest trade data for analysis
        
        Args:
            trade_data: Trade data dictionary
            source: Data source identifier
            
        Returns:
            Response with ingestion status
        """
        try:
            # Validate trade data
            validation_report = self.data_validator.validate_record(trade_data, "trade")
            
            if validation_report.overall_result.value in ['failed']:
                return {
                    'status': 'error',
                    'message': 'Trade data validation failed',
                    'validation_report': asdict(validation_report)
                }
            
            # Create stream event
            from performance_analysis.data_ingestion.stream_processor import StreamEvent
            event = StreamEvent(
                event_id=f"trade_{datetime.now().timestamp()}",
                event_type="trade",
                source=source,
                timestamp=datetime.now(),
                data=trade_data
            )
            
            # Ingest through stream processor
            success = await self.stream_processor.ingest_event(event)
            
            if success:
                self.gateway_metrics['events_processed'] += 1
                return {
                    'status': 'success',
                    'message': 'Trade data ingested successfully',
                    'event_id': event.event_id
                }
            else:
                return {
                    'status': 'error',
                    'message': 'Failed to ingest trade data'
                }
                
        except Exception as e:
            logger.error(f"Error ingesting trade data: {e}")
            self.gateway_metrics['errors_count'] += 1
            return {
                'status': 'error',
                'message': f'Ingestion error: {str(e)}'
            }

    async def ingest_signal_data(self, signal_data: Dict[str, Any], source: str = "unknown") -> Dict[str, Any]:
        """Ingest signal data for analysis"""
        try:
            # Validate signal data
            validation_report = self.data_validator.validate_record(signal_data, "signal")
            
            # Create and ingest stream event
            from performance_analysis.data_ingestion.stream_processor import StreamEvent
            event = StreamEvent(
                event_id=f"signal_{datetime.now().timestamp()}",
                event_type="signal",
                source=source,
                timestamp=datetime.now(),
                data=signal_data
            )
            
            success = await self.stream_processor.ingest_event(event)
            
            if success:
                self.gateway_metrics['events_processed'] += 1
                return {'status': 'success', 'event_id': event.event_id}
            else:
                return {'status': 'error', 'message': 'Failed to ingest signal data'}
                
        except Exception as e:
            logger.error(f"Error ingesting signal data: {e}")
            self.gateway_metrics['errors_count'] += 1
            return {'status': 'error', 'message': str(e)}

    async def start_historical_backfill(self, start_date: str, end_date: str, 
                                      sources: List[str], symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """Start historical data backfill"""
        try:
            from datetime import date
            from performance_analysis.data_ingestion.historical_backfiller import BackfillTask, DataSource
            
            start_dt = datetime.fromisoformat(start_date).date()
            end_dt = datetime.fromisoformat(end_date).date()
            
            tasks = []
            for source in sources:
                try:
                    data_source = DataSource(source)
                    task = BackfillTask(
                        task_id=f"backfill_{source}_{start_date}_{end_date}",
                        source=data_source,
                        start_date=start_dt,
                        end_date=end_dt,
                        symbols=symbols
                    )
                    
                    success = await self.historical_backfiller.start_backfill_task(task)
                    if success:
                        tasks.append(task.task_id)
                        
                except ValueError as e:
                    logger.error(f"Invalid source: {source}")
                    continue
            
            if tasks:
                # Start backfill process
                asyncio.create_task(self.historical_backfiller.run_backfill_tasks())
                
                return {
                    'status': 'success',
                    'message': f'Started {len(tasks)} backfill tasks',
                    'task_ids': tasks
                }
            else:
                return {
                    'status': 'error',
                    'message': 'No valid backfill tasks created'
                }
                
        except Exception as e:
            logger.error(f"Error starting historical backfill: {e}")
            return {'status': 'error', 'message': str(e)}

    # ═══════════════════════════════════════════════════════════════════════════════
    # METRICS CALCULATION API
    # ═══════════════════════════════════════════════════════════════════════════════

    async def calculate_performance_metrics(self, trades_data: Optional[List[Dict]] = None, 
                                          strategy: Optional[str] = None,
                                          symbol: Optional[str] = None,
                                          start_date: Optional[str] = None,
                                          end_date: Optional[str] = None) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        try:
            # Get trades data (from parameter or internal storage)
            if trades_data:
                trades_df = pl.DataFrame(trades_data)
            else:
                # This would fetch from internal storage based on filters
                trades_df = await self._get_trades_data(strategy, symbol, start_date, end_date)
            
            if trades_df.is_empty():
                return {
                    'status': 'error',
                    'message': 'No trades data available for analysis'
                }
            
            # Calculate core metrics
            core_metrics = self.core_metrics_calculator.calculate_metrics(trades_df, self.config.initial_capital)
            
            # Calculate risk metrics
            risk_metrics = self.risk_metrics_calculator.calculate_risk_metrics(trades_df)
            
            # Calculate drawdown metrics
            equity_curve = await self._generate_equity_curve(trades_df)
            drawdown_metrics, drawdown_periods = self.drawdown_analyzer.analyze_drawdowns(equity_curve)
            
            return {
                'status': 'success',
                'core_metrics': asdict(core_metrics),
                'risk_metrics': asdict(risk_metrics),
                'drawdown_metrics': asdict(drawdown_metrics),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {'status': 'error', 'message': str(e)}

    async def get_strategy_comparison(self, strategies: Optional[List[str]] = None) -> Dict[str, Any]:
        """Get performance comparison across strategies"""
        try:
            trades_df = await self._get_trades_data()
            
            if trades_df.is_empty():
                return {'status': 'error', 'message': 'No trades data available'}
            
            # Filter by strategies if specified
            if strategies:
                trades_df = trades_df.filter(pl.col('strategy').is_in(strategies))
            
            # Calculate strategy comparison
            strategy_comparison = self.core_metrics_calculator.calculate_strategy_comparison(trades_df)
            
            return {
                'status': 'success',
                'strategy_comparison': strategy_comparison.to_dicts(),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting strategy comparison: {e}")
            return {'status': 'error', 'message': str(e)}

    # ═══════════════════════════════════════════════════════════════════════════════
    # EVENT HANDLERS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _handle_trade_event(self, event):
        """Handle incoming trade events"""
        try:
            # Add trade to reconciler
            from performance_analysis.data_ingestion.trade_reconciler import TradeRecord, DataSource
            
            trade_record = TradeRecord(
                source=DataSource(event.source) if event.source in [ds.value for ds in DataSource] else DataSource.EXECUTION_AGENT,
                trade_id=event.data.get('trade_id', ''),
                symbol=event.data.get('symbol', ''),
                side=event.data.get('side', ''),
                quantity=event.data.get('quantity', 0),
                price=event.data.get('price', 0.0),
                timestamp=event.timestamp,
                strategy=event.data.get('strategy'),
                order_id=event.data.get('order_id'),
                signal_id=event.data.get('signal_id'),
                commission=event.data.get('commission', 0.0),
                raw_data=event.data
            )
            
            self.trade_reconciler.add_trade(trade_record)
            
        except Exception as e:
            logger.error(f"Error handling trade event: {e}")

    async def _handle_signal_event(self, event):
        """Handle incoming signal events"""
        try:
            # Process signal for execution quality analysis
            logger.debug(f"Processing signal event: {event.event_id}")
            
        except Exception as e:
            logger.error(f"Error handling signal event: {e}")

    async def _handle_order_event(self, event):
        """Handle incoming order events"""
        try:
            # Add order to order matcher
            from performance_analysis.data_ingestion.order_matcher import OrderRecord, OrderType, OrderStatus
            
            order_record = OrderRecord(
                order_id=event.data.get('order_id', ''),
                parent_order_id=event.data.get('parent_order_id'),
                symbol=event.data.get('symbol', ''),
                side=event.data.get('side', ''),
                order_type=OrderType(event.data.get('order_type', 'market')),
                quantity=event.data.get('quantity', 0),
                price=event.data.get('price'),
                stop_price=event.data.get('stop_price'),
                status=OrderStatus(event.data.get('status', 'pending')),
                timestamp=event.timestamp,
                strategy=event.data.get('strategy'),
                signal_id=event.data.get('signal_id'),
                source=event.source,
                raw_data=event.data
            )
            
            self.order_matcher.add_order(order_record)
            
        except Exception as e:
            logger.error(f"Error handling order event: {e}")

    async def _handle_market_data_event(self, event):
        """Handle incoming market data events"""
        try:
            # Process market data for regime detection and analysis
            logger.debug(f"Processing market data event: {event.event_id}")
            
        except Exception as e:
            logger.error(f"Error handling market data event: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # BACKGROUND TASKS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _health_monitoring_loop(self):
        """Background health monitoring"""
        while self.is_running:
            try:
                # Update health metrics
                self.gateway_metrics['last_health_check'] = datetime.now()
                self.gateway_metrics['connected_agents'] = len(self.registered_agents)
                
                # Log health status
                logger.debug(f"Gateway health check: {self.gateway_metrics}")
                
                await asyncio.sleep(self.config.health_check_interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(60)

    async def _metrics_cleanup_loop(self):
        """Background metrics cleanup"""
        while self.is_running:
            try:
                # Clean up old data
                cutoff_date = datetime.now() - timedelta(days=self.config.metrics_retention_days)
                
                # Clean up reconciler data
                self.trade_reconciler.clear_old_data(self.config.metrics_retention_days)
                
                # Clean up order matcher data
                self.order_matcher.clear_old_data(self.config.metrics_retention_days)
                
                logger.info("Completed metrics cleanup")
                
                # Run cleanup daily
                await asyncio.sleep(24 * 3600)
                
            except Exception as e:
                logger.error(f"Error in metrics cleanup: {e}")
                await asyncio.sleep(3600)

    async def _periodic_reconciliation_loop(self):
        """Background trade reconciliation"""
        while self.is_running:
            try:
                # Run trade reconciliation
                match_results = self.trade_reconciler.reconcile_trades()
                
                if match_results:
                    logger.info(f"Reconciled {len(match_results)} trade matches")
                
                # Run order matching
                order_matches = self.order_matcher.match_orders_and_fills()
                
                if order_matches:
                    logger.info(f"Matched {len(order_matches)} orders with fills")
                
                # Run every 15 minutes
                await asyncio.sleep(15 * 60)
                
            except Exception as e:
                logger.error(f"Error in periodic reconciliation: {e}")
                await asyncio.sleep(5 * 60)

    # ═══════════════════════════════════════════════════════════════════════════════
    # UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _get_trades_data(self, strategy: Optional[str] = None, symbol: Optional[str] = None,
                             start_date: Optional[str] = None, end_date: Optional[str] = None) -> pl.DataFrame:
        """Get trades data with optional filters"""
        # This would fetch from internal storage/database
        # For now, return empty DataFrame
        return pl.DataFrame()

    async def _generate_equity_curve(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """Generate equity curve from trades data"""
        try:
            if trades_df.is_empty():
                return pl.DataFrame()
            
            # Sort by entry time
            sorted_trades = trades_df.sort('entry_time')
            
            # Calculate cumulative PnL
            pnl_values = sorted_trades.select(pl.col('pnl')).to_series().to_list()
            timestamps = sorted_trades.select(pl.col('entry_time')).to_series().to_list()
            
            cumulative_pnl = []
            running_total = self.config.initial_capital
            
            for pnl in pnl_values:
                running_total += pnl if pnl is not None else 0
                cumulative_pnl.append(running_total)
            
            return pl.DataFrame({
                'timestamp': timestamps,
                'equity': cumulative_pnl
            })
            
        except Exception as e:
            logger.error(f"Error generating equity curve: {e}")
            return pl.DataFrame()

    def get_gateway_status(self) -> Dict[str, Any]:
        """Get current gateway status"""
        return {
            'is_running': self.is_running,
            'metrics': self.gateway_metrics,
            'stream_processor_metrics': self.stream_processor.get_metrics(),
            'reconciler_summary': self.trade_reconciler.get_reconciliation_summary(),
            'order_matcher_metrics': self.order_matcher.get_matching_metrics()
        }

# ═══════════════════════════════════════════════════════════════════════════════
# MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main function for testing the gateway"""
    gateway = PerformanceAnalysisGateway()
    
    try:
        await gateway.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await gateway.stop()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    asyncio.run(main())
