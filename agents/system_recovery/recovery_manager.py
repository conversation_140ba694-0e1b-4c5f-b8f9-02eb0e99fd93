#!/usr/bin/env python3
"""
System Recovery Manager - Comprehensive Recovery Logic for Multi-Agent Trading System

This module implements the system recovery logic that handles:
- System startup recovery after crashes or restarts
- Active position reconciliation with broker
- Worker state consistency validation
- Trade execution continuity
- Data integrity verification

Features:
🔄 Startup Recovery
- Detect system restart scenarios
- Load and validate persisted state
- Reconcile with broker positions
- Resume interrupted operations

⚖️ Position Reconciliation
- Compare system state with broker positions
- Identify orphaned or missing positions
- Handle position discrepancies
- Update worker assignments accordingly

🛡️ Data Integrity
- Validate state consistency
- Detect and repair corrupted data
- Backup critical information
- Ensure system reliability

🔧 Automated Recovery
- Self-healing capabilities
- Graceful degradation
- Error recovery strategies
- Minimal manual intervention
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import json

logger = logging.getLogger(__name__)


class RecoveryStatus(Enum):
    """Recovery status enumeration"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL_RECOVERY = "partial_recovery"


class RecoveryAction(Enum):
    """Recovery action types"""
    STATE_VALIDATION = "state_validation"
    POSITION_RECONCILIATION = "position_reconciliation"
    WORKER_REBALANCING = "worker_rebalancing"
    DATA_REPAIR = "data_repair"
    SYSTEM_RESTART = "system_restart"


@dataclass
class RecoveryIssue:
    """Recovery issue description"""
    issue_type: str
    severity: str  # critical, warning, info
    description: str
    affected_component: str
    recommended_action: str
    auto_fixable: bool
    timestamp: datetime


@dataclass
class PositionDiscrepancy:
    """Position discrepancy between system and broker"""
    symbol: str
    system_position: Optional[Dict[str, Any]]
    broker_position: Optional[Dict[str, Any]]
    discrepancy_type: str  # missing_in_system, missing_in_broker, quantity_mismatch, price_mismatch
    severity: str
    recommended_action: str


@dataclass
class RecoveryReport:
    """Comprehensive recovery report"""
    recovery_start_time: datetime
    recovery_end_time: Optional[datetime]
    recovery_status: RecoveryStatus
    issues_found: List[RecoveryIssue]
    position_discrepancies: List[PositionDiscrepancy]
    actions_taken: List[str]
    manual_interventions_required: List[str]
    system_health_score: float  # 0.0 to 1.0
    recommendations: List[str]


class SystemRecoveryManager:
    """
    Manages system recovery operations for the multi-agent trading system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize System Recovery Manager"""
        self.config = config or {}
        
        # Recovery state
        self.recovery_status = RecoveryStatus.NOT_STARTED
        self.current_recovery_report: Optional[RecoveryReport] = None
        self.recovery_history: List[RecoveryReport] = []
        
        # Component references (will be injected)
        self.worker_state_manager = None
        self.execution_worker_pool = None
        self.persistence_manager = None
        self.broker_interface = None
        
        # Recovery configuration
        self.max_recovery_attempts = int(os.getenv('MAX_RECOVERY_ATTEMPTS', '3'))
        self.position_tolerance = float(os.getenv('POSITION_RECONCILIATION_TOLERANCE', '0.01'))
        self.auto_fix_enabled = os.getenv('AUTO_FIX_ENABLED', 'true').lower() == 'true'
        
        # Recovery data storage
        self.recovery_data_path = Path("data/recovery")
        self.recovery_data_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("[INIT] System Recovery Manager initialized")
    
    def inject_dependencies(self, worker_state_manager=None, execution_worker_pool=None, 
                          persistence_manager=None, broker_interface=None):
        """Inject component dependencies"""
        self.worker_state_manager = worker_state_manager
        self.execution_worker_pool = execution_worker_pool
        self.persistence_manager = persistence_manager
        self.broker_interface = broker_interface
        
        logger.info("[DEPS] Dependencies injected into Recovery Manager")
    
    async def perform_startup_recovery(self) -> RecoveryReport:
        """Perform comprehensive startup recovery"""
        try:
            logger.info("[RECOVERY] Starting system startup recovery...")
            
            # Initialize recovery report
            self.current_recovery_report = RecoveryReport(
                recovery_start_time=datetime.now(),
                recovery_end_time=None,
                recovery_status=RecoveryStatus.IN_PROGRESS,
                issues_found=[],
                position_discrepancies=[],
                actions_taken=[],
                manual_interventions_required=[],
                system_health_score=0.0,
                recommendations=[]
            )
            
            self.recovery_status = RecoveryStatus.IN_PROGRESS
            
            # Step 1: Detect recovery scenario
            recovery_scenario = await self._detect_recovery_scenario()
            logger.info(f"[RECOVERY] Detected recovery scenario: {recovery_scenario}")
            
            # Step 2: Validate system state
            await self._validate_system_state()
            
            # Step 3: Reconcile positions with broker
            if self.broker_interface:
                await self._reconcile_positions_with_broker()
            else:
                logger.warning("[RECOVERY] No broker interface available for position reconciliation")
            
            # Step 4: Validate worker states
            await self._validate_worker_states()
            
            # Step 5: Check data integrity
            await self._check_data_integrity()
            
            # Step 6: Apply automatic fixes
            if self.auto_fix_enabled:
                await self._apply_automatic_fixes()
            
            # Step 7: Calculate system health score
            await self._calculate_system_health_score()
            
            # Step 8: Generate recommendations
            await self._generate_recovery_recommendations()
            
            # Complete recovery
            self.current_recovery_report.recovery_end_time = datetime.now()
            
            # Determine final status
            if not self.current_recovery_report.issues_found:
                self.recovery_status = RecoveryStatus.COMPLETED
                self.current_recovery_report.recovery_status = RecoveryStatus.COMPLETED
                logger.info("[SUCCESS] System recovery completed successfully")
            elif any(issue.severity == 'critical' for issue in self.current_recovery_report.issues_found):
                self.recovery_status = RecoveryStatus.FAILED
                self.current_recovery_report.recovery_status = RecoveryStatus.FAILED
                logger.error("[FAILED] System recovery failed due to critical issues")
            else:
                self.recovery_status = RecoveryStatus.PARTIAL_RECOVERY
                self.current_recovery_report.recovery_status = RecoveryStatus.PARTIAL_RECOVERY
                logger.warning("[PARTIAL] System recovery completed with warnings")
            
            # Save recovery report
            await self._save_recovery_report()
            
            # Add to history
            self.recovery_history.append(self.current_recovery_report)
            
            return self.current_recovery_report
            
        except Exception as e:
            logger.error(f"[ERROR] System recovery failed: {e}")
            
            if self.current_recovery_report:
                self.current_recovery_report.recovery_status = RecoveryStatus.FAILED
                self.current_recovery_report.recovery_end_time = datetime.now()
                self.current_recovery_report.issues_found.append(
                    RecoveryIssue(
                        issue_type="recovery_failure",
                        severity="critical",
                        description=f"Recovery process failed: {str(e)}",
                        affected_component="recovery_manager",
                        recommended_action="Manual intervention required",
                        auto_fixable=False,
                        timestamp=datetime.now()
                    )
                )
            
            self.recovery_status = RecoveryStatus.FAILED
            return self.current_recovery_report
    
    async def _detect_recovery_scenario(self) -> str:
        """Detect the type of recovery scenario"""
        try:
            # Check for crash recovery indicators
            crash_indicators = []
            
            # Check for unexpected shutdown
            if self.persistence_manager:
                latest_snapshot = await self.persistence_manager.load_latest_snapshot()
                if latest_snapshot:
                    time_since_last_backup = datetime.now() - latest_snapshot.timestamp
                    if time_since_last_backup > timedelta(hours=1):
                        crash_indicators.append("stale_backup")
                    
                    # Check for active positions in last snapshot
                    active_positions = latest_snapshot.active_positions
                    if active_positions:
                        crash_indicators.append("active_positions_found")
            
            # Check for lock files or PID files
            lock_file = Path("data/system.lock")
            if lock_file.exists():
                crash_indicators.append("lock_file_exists")
            
            # Determine scenario
            if not crash_indicators:
                return "clean_startup"
            elif "active_positions_found" in crash_indicators:
                return "crash_with_active_positions"
            elif "stale_backup" in crash_indicators:
                return "unexpected_shutdown"
            else:
                return "abnormal_shutdown"
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to detect recovery scenario: {e}")
            return "unknown_scenario"
    
    async def _validate_system_state(self):
        """Validate system state consistency"""
        try:
            logger.info("[RECOVERY] Validating system state...")
            
            if not self.worker_state_manager:
                self._add_recovery_issue(
                    "missing_component",
                    "critical",
                    "Worker State Manager not available",
                    "worker_state_manager",
                    "Initialize Worker State Manager",
                    False
                )
                return
            
            # Get system status
            system_status = self.worker_state_manager.get_system_status()
            
            # Check for inconsistencies
            if system_status.get('status') != 'running':
                self._add_recovery_issue(
                    "system_not_running",
                    "warning",
                    f"System status is {system_status.get('status')}",
                    "system",
                    "Start system components",
                    True
                )
            
            # Check worker count consistency
            max_trades = system_status.get('max_trades', 0)
            total_workers = system_status.get('total_workers', 0)
            
            if total_workers != max_trades:
                self._add_recovery_issue(
                    "worker_count_mismatch",
                    "warning",
                    f"Worker count ({total_workers}) doesn't match MAX_TRADES ({max_trades})",
                    "worker_management",
                    "Adjust worker count",
                    True
                )
            
            # Check for orphaned workers
            worker_states = system_status.get('worker_states', {})
            error_workers = worker_states.get('ERROR', 0)
            
            if error_workers > 0:
                self._add_recovery_issue(
                    "workers_in_error_state",
                    "warning",
                    f"{error_workers} workers in ERROR state",
                    "worker_management",
                    "Reset error workers",
                    True
                )
            
            logger.info("[SUCCESS] System state validation completed")
            
        except Exception as e:
            logger.error(f"[ERROR] System state validation failed: {e}")
            self._add_recovery_issue(
                "state_validation_error",
                "critical",
                f"State validation failed: {str(e)}",
                "recovery_manager",
                "Manual investigation required",
                False
            )
    
    async def _reconcile_positions_with_broker(self):
        """Reconcile system positions with broker positions"""
        try:
            logger.info("[RECOVERY] Reconciling positions with broker...")
            
            if not self.broker_interface:
                logger.warning("[RECOVERY] No broker interface available")
                return
            
            # Get system positions
            system_positions = {}
            if self.persistence_manager:
                latest_snapshot = await self.persistence_manager.load_latest_snapshot()
                if latest_snapshot:
                    system_positions = latest_snapshot.active_positions
            
            # Get broker positions (placeholder - would need actual broker API)
            broker_positions = await self._get_broker_positions()
            
            # Compare positions
            discrepancies = []
            
            # Check for positions in system but not in broker
            for position_id, system_pos in system_positions.items():
                symbol = system_pos.get('symbol')
                if symbol not in broker_positions:
                    discrepancy = PositionDiscrepancy(
                        symbol=symbol,
                        system_position=system_pos,
                        broker_position=None,
                        discrepancy_type="missing_in_broker",
                        severity="critical",
                        recommended_action="Close system position or verify broker connection"
                    )
                    discrepancies.append(discrepancy)
            
            # Check for positions in broker but not in system
            for symbol, broker_pos in broker_positions.items():
                if not any(pos.get('symbol') == symbol for pos in system_positions.values()):
                    discrepancy = PositionDiscrepancy(
                        symbol=symbol,
                        system_position=None,
                        broker_position=broker_pos,
                        discrepancy_type="missing_in_system",
                        severity="warning",
                        recommended_action="Add position to system tracking or close broker position"
                    )
                    discrepancies.append(discrepancy)
            
            # Add discrepancies to report
            self.current_recovery_report.position_discrepancies.extend(discrepancies)
            
            if discrepancies:
                logger.warning(f"[RECOVERY] Found {len(discrepancies)} position discrepancies")
                for disc in discrepancies:
                    logger.warning(f"[DISCREPANCY] {disc.symbol}: {disc.discrepancy_type}")
            else:
                logger.info("[SUCCESS] No position discrepancies found")
            
        except Exception as e:
            logger.error(f"[ERROR] Position reconciliation failed: {e}")
            self._add_recovery_issue(
                "position_reconciliation_error",
                "critical",
                f"Position reconciliation failed: {str(e)}",
                "broker_interface",
                "Check broker connection and retry",
                False
            )

    async def _get_broker_positions(self) -> Dict[str, Any]:
        """Get current positions from broker (placeholder implementation)"""
        try:
            # This would typically query the broker API
            # For now, return empty dict as placeholder
            logger.info("[BROKER] Querying broker positions (placeholder)")

            # Placeholder implementation
            return {}

        except Exception as e:
            logger.error(f"[ERROR] Failed to get broker positions: {e}")
            return {}

    async def _validate_worker_states(self):
        """Validate worker state consistency"""
        try:
            logger.info("[RECOVERY] Validating worker states...")

            if not self.worker_state_manager:
                return

            # Get worker details
            worker_details = self.worker_state_manager.get_worker_details()

            if 'workers' not in worker_details:
                self._add_recovery_issue(
                    "no_worker_data",
                    "critical",
                    "No worker data available",
                    "worker_state_manager",
                    "Initialize workers",
                    True
                )
                return

            # Check each worker
            for worker_id, worker_info in worker_details['workers'].items():
                # Check for workers stuck in ACTIVE state without actual trades
                if worker_info.get('state') == 'ACTIVE' and not worker_info.get('active_position'):
                    self._add_recovery_issue(
                        "worker_stuck_active",
                        "warning",
                        f"Worker {worker_id} in ACTIVE state without position",
                        "worker_state",
                        "Reset worker to IDLE state",
                        True
                    )

                # Check for workers with expired cooldowns
                if worker_info.get('state') == 'COOLDOWN':
                    # This would need actual cooldown expiry checking
                    # For now, just log
                    logger.debug(f"[RECOVERY] Worker {worker_id} in COOLDOWN state")

                # Check symbol assignments
                symbols_count = worker_info.get('symbols_count', 0)
                if symbols_count == 0:
                    self._add_recovery_issue(
                        "worker_no_symbols",
                        "warning",
                        f"Worker {worker_id} has no assigned symbols",
                        "symbol_assignment",
                        "Rebalance symbol assignments",
                        True
                    )

            logger.info("[SUCCESS] Worker state validation completed")

        except Exception as e:
            logger.error(f"[ERROR] Worker state validation failed: {e}")
            self._add_recovery_issue(
                "worker_validation_error",
                "critical",
                f"Worker validation failed: {str(e)}",
                "worker_state_manager",
                "Manual investigation required",
                False
            )

    async def _check_data_integrity(self):
        """Check data integrity across all components"""
        try:
            logger.info("[RECOVERY] Checking data integrity...")

            # Check persistence manager integrity
            if self.persistence_manager:
                stats = self.persistence_manager.get_persistence_stats()

                if stats.get('error'):
                    self._add_recovery_issue(
                        "persistence_error",
                        "critical",
                        f"Persistence manager error: {stats['error']}",
                        "persistence_manager",
                        "Check storage backend",
                        False
                    )

                # Check backup count
                backup_count = stats.get('backup_count', 0)
                if backup_count == 0:
                    self._add_recovery_issue(
                        "no_backups",
                        "warning",
                        "No backup files found",
                        "persistence_manager",
                        "Create initial backup",
                        True
                    )

            # Check for corrupted configuration files
            config_files = [
                "config/execution_config.yaml",
                "config/risk_management_config.yaml",
                "config/signal_generation_config.yaml"
            ]

            for config_file in config_files:
                if not Path(config_file).exists():
                    self._add_recovery_issue(
                        "missing_config",
                        "warning",
                        f"Configuration file missing: {config_file}",
                        "configuration",
                        "Restore configuration file",
                        False
                    )

            # Check data directory structure
            required_dirs = [
                "data/system_state",
                "data/performance",
                "data/recovery",
                "logs"
            ]

            for dir_path in required_dirs:
                if not Path(dir_path).exists():
                    self._add_recovery_issue(
                        "missing_directory",
                        "info",
                        f"Required directory missing: {dir_path}",
                        "file_system",
                        "Create directory",
                        True
                    )

            logger.info("[SUCCESS] Data integrity check completed")

        except Exception as e:
            logger.error(f"[ERROR] Data integrity check failed: {e}")
            self._add_recovery_issue(
                "integrity_check_error",
                "critical",
                f"Data integrity check failed: {str(e)}",
                "recovery_manager",
                "Manual investigation required",
                False
            )

    async def _apply_automatic_fixes(self):
        """Apply automatic fixes for recoverable issues"""
        try:
            logger.info("[RECOVERY] Applying automatic fixes...")

            fixes_applied = []

            for issue in self.current_recovery_report.issues_found:
                if issue.auto_fixable:
                    try:
                        success = await self._apply_fix_for_issue(issue)
                        if success:
                            fixes_applied.append(f"Fixed {issue.issue_type}: {issue.description}")
                            logger.info(f"[FIX] Applied fix for {issue.issue_type}")
                        else:
                            logger.warning(f"[FIX] Failed to apply fix for {issue.issue_type}")
                    except Exception as e:
                        logger.error(f"[ERROR] Error applying fix for {issue.issue_type}: {e}")

            self.current_recovery_report.actions_taken.extend(fixes_applied)

            if fixes_applied:
                logger.info(f"[SUCCESS] Applied {len(fixes_applied)} automatic fixes")
            else:
                logger.info("[INFO] No automatic fixes were applied")

        except Exception as e:
            logger.error(f"[ERROR] Automatic fixes failed: {e}")

    async def _apply_fix_for_issue(self, issue: RecoveryIssue) -> bool:
        """Apply fix for a specific issue"""
        try:
            if issue.issue_type == "worker_stuck_active":
                # Reset worker to IDLE state
                worker_id = issue.affected_component.replace("worker_", "")
                if self.worker_state_manager:
                    from agents.execution.worker_state_manager import WorkerState
                    success = await self.worker_state_manager.force_worker_state_change(
                        worker_id, WorkerState.IDLE, "Recovery: Reset stuck worker"
                    )
                    return success

            elif issue.issue_type == "missing_directory":
                # Create missing directory
                dir_path = issue.description.split(": ")[1]
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                return True

            elif issue.issue_type == "worker_count_mismatch":
                # This would trigger worker rebalancing
                if self.execution_worker_pool:
                    success = await self.execution_worker_pool.rebalance_workers()
                    return success

            elif issue.issue_type == "workers_in_error_state":
                # Reset error workers
                if self.worker_state_manager:
                    worker_details = self.worker_state_manager.get_worker_details()
                    if 'workers' in worker_details:
                        for worker_id, worker_info in worker_details['workers'].items():
                            if worker_info.get('state') == 'ERROR':
                                from agents.execution.worker_state_manager import WorkerState
                                await self.worker_state_manager.force_worker_state_change(
                                    worker_id, WorkerState.IDLE, "Recovery: Reset error worker"
                                )
                    return True

            elif issue.issue_type == "no_backups":
                # Create initial backup
                if self.persistence_manager and self.worker_state_manager:
                    from agents.state_persistence.persistence_manager import create_state_snapshot
                    system_status = self.worker_state_manager.get_system_status()
                    worker_details = self.worker_state_manager.get_worker_details()

                    snapshot = await create_state_snapshot(
                        system_state=system_status,
                        worker_states=worker_details.get('workers', {}),
                        system_metrics={'recovery_backup': True}
                    )

                    success = await self.persistence_manager.save_state_snapshot(snapshot)
                    return success

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply fix for {issue.issue_type}: {e}")
            return False

    async def _calculate_system_health_score(self):
        """Calculate overall system health score"""
        try:
            logger.info("[RECOVERY] Calculating system health score...")

            total_score = 1.0

            # Deduct points for issues
            for issue in self.current_recovery_report.issues_found:
                if issue.severity == 'critical':
                    total_score -= 0.3
                elif issue.severity == 'warning':
                    total_score -= 0.1
                elif issue.severity == 'info':
                    total_score -= 0.02

            # Deduct points for position discrepancies
            for discrepancy in self.current_recovery_report.position_discrepancies:
                if discrepancy.severity == 'critical':
                    total_score -= 0.2
                elif discrepancy.severity == 'warning':
                    total_score -= 0.05

            # Ensure score is between 0 and 1
            total_score = max(0.0, min(1.0, total_score))

            self.current_recovery_report.system_health_score = total_score

            logger.info(f"[HEALTH] System health score: {total_score:.2f}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate system health score: {e}")
            self.current_recovery_report.system_health_score = 0.0

    async def _generate_recovery_recommendations(self):
        """Generate recovery recommendations"""
        try:
            logger.info("[RECOVERY] Generating recovery recommendations...")

            recommendations = []

            # Recommendations based on issues
            critical_issues = [i for i in self.current_recovery_report.issues_found if i.severity == 'critical']
            if critical_issues:
                recommendations.append("Address critical issues before starting live trading")
                recommendations.append("Consider running system in safe mode until issues are resolved")

            # Recommendations based on position discrepancies
            if self.current_recovery_report.position_discrepancies:
                recommendations.append("Review and reconcile position discrepancies with broker")
                recommendations.append("Consider manual position verification before resuming trading")

            # Recommendations based on health score
            health_score = self.current_recovery_report.system_health_score
            if health_score < 0.5:
                recommendations.append("System health is poor - consider full system restart")
                recommendations.append("Review system logs for additional issues")
            elif health_score < 0.8:
                recommendations.append("Monitor system closely after recovery")
                recommendations.append("Consider reducing trading activity until stability improves")

            # General recommendations
            if not self.current_recovery_report.actions_taken:
                recommendations.append("No automatic fixes were applied - manual intervention may be required")

            self.current_recovery_report.recommendations = recommendations

            logger.info(f"[RECOMMENDATIONS] Generated {len(recommendations)} recommendations")

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate recovery recommendations: {e}")

    def _add_recovery_issue(self, issue_type: str, severity: str, description: str,
                          affected_component: str, recommended_action: str, auto_fixable: bool):
        """Add a recovery issue to the current report"""
        issue = RecoveryIssue(
            issue_type=issue_type,
            severity=severity,
            description=description,
            affected_component=affected_component,
            recommended_action=recommended_action,
            auto_fixable=auto_fixable,
            timestamp=datetime.now()
        )

        if self.current_recovery_report:
            self.current_recovery_report.issues_found.append(issue)

        logger.warning(f"[ISSUE] {severity.upper()}: {description}")

    async def _save_recovery_report(self):
        """Save recovery report to file"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = self.recovery_data_path / f"recovery_report_{timestamp}.json"

            # Convert report to dict for serialization
            report_dict = {
                'recovery_start_time': self.current_recovery_report.recovery_start_time.isoformat(),
                'recovery_end_time': self.current_recovery_report.recovery_end_time.isoformat() if self.current_recovery_report.recovery_end_time else None,
                'recovery_status': self.current_recovery_report.recovery_status.value,
                'issues_found': [
                    {
                        'issue_type': issue.issue_type,
                        'severity': issue.severity,
                        'description': issue.description,
                        'affected_component': issue.affected_component,
                        'recommended_action': issue.recommended_action,
                        'auto_fixable': issue.auto_fixable,
                        'timestamp': issue.timestamp.isoformat()
                    }
                    for issue in self.current_recovery_report.issues_found
                ],
                'position_discrepancies': [
                    {
                        'symbol': disc.symbol,
                        'system_position': disc.system_position,
                        'broker_position': disc.broker_position,
                        'discrepancy_type': disc.discrepancy_type,
                        'severity': disc.severity,
                        'recommended_action': disc.recommended_action
                    }
                    for disc in self.current_recovery_report.position_discrepancies
                ],
                'actions_taken': self.current_recovery_report.actions_taken,
                'manual_interventions_required': self.current_recovery_report.manual_interventions_required,
                'system_health_score': self.current_recovery_report.system_health_score,
                'recommendations': self.current_recovery_report.recommendations
            }

            with open(report_file, 'w') as f:
                json.dump(report_dict, f, indent=2)

            logger.info(f"[SAVE] Recovery report saved: {report_file}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save recovery report: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # PUBLIC API METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def get_recovery_status(self) -> Dict[str, Any]:
        """Get current recovery status"""
        try:
            return {
                'status': self.recovery_status.value,
                'current_report': {
                    'start_time': self.current_recovery_report.recovery_start_time.isoformat() if self.current_recovery_report else None,
                    'end_time': self.current_recovery_report.recovery_end_time.isoformat() if self.current_recovery_report and self.current_recovery_report.recovery_end_time else None,
                    'issues_count': len(self.current_recovery_report.issues_found) if self.current_recovery_report else 0,
                    'discrepancies_count': len(self.current_recovery_report.position_discrepancies) if self.current_recovery_report else 0,
                    'health_score': self.current_recovery_report.system_health_score if self.current_recovery_report else 0.0,
                    'actions_taken_count': len(self.current_recovery_report.actions_taken) if self.current_recovery_report else 0
                },
                'recovery_history_count': len(self.recovery_history),
                'auto_fix_enabled': self.auto_fix_enabled,
                'max_recovery_attempts': self.max_recovery_attempts
            }

        except Exception as e:
            logger.error(f"[ERROR] Failed to get recovery status: {e}")
            return {'error': str(e)}

    def get_latest_recovery_report(self) -> Optional[RecoveryReport]:
        """Get the latest recovery report"""
        return self.current_recovery_report

    def get_recovery_history(self) -> List[RecoveryReport]:
        """Get recovery history"""
        return self.recovery_history.copy()

    async def perform_health_check(self) -> Dict[str, Any]:
        """Perform a quick health check without full recovery"""
        try:
            logger.info("[HEALTH] Performing quick health check...")

            health_issues = []

            # Check component availability
            if not self.worker_state_manager:
                health_issues.append("Worker State Manager not available")

            if not self.execution_worker_pool:
                health_issues.append("Execution Worker Pool not available")

            if not self.persistence_manager:
                health_issues.append("Persistence Manager not available")

            # Check system status if available
            if self.worker_state_manager:
                try:
                    system_status = self.worker_state_manager.get_system_status()
                    if system_status.get('status') != 'running':
                        health_issues.append(f"System not running: {system_status.get('status')}")

                    error_workers = system_status.get('worker_states', {}).get('ERROR', 0)
                    if error_workers > 0:
                        health_issues.append(f"{error_workers} workers in error state")

                except Exception as e:
                    health_issues.append(f"Failed to get system status: {str(e)}")

            # Calculate health score
            health_score = 1.0 - (len(health_issues) * 0.2)
            health_score = max(0.0, min(1.0, health_score))

            return {
                'health_score': health_score,
                'status': 'healthy' if health_score > 0.8 else 'degraded' if health_score > 0.5 else 'unhealthy',
                'issues': health_issues,
                'timestamp': datetime.now().isoformat(),
                'components_checked': {
                    'worker_state_manager': self.worker_state_manager is not None,
                    'execution_worker_pool': self.execution_worker_pool is not None,
                    'persistence_manager': self.persistence_manager is not None,
                    'broker_interface': self.broker_interface is not None
                }
            }

        except Exception as e:
            logger.error(f"[ERROR] Health check failed: {e}")
            return {
                'health_score': 0.0,
                'status': 'error',
                'issues': [f"Health check failed: {str(e)}"],
                'timestamp': datetime.now().isoformat()
            }

    async def force_position_reconciliation(self) -> Dict[str, Any]:
        """Force position reconciliation with broker"""
        try:
            logger.info("[FORCE] Forcing position reconciliation...")

            if not self.broker_interface:
                return {
                    'success': False,
                    'message': 'No broker interface available',
                    'discrepancies': []
                }

            # Perform position reconciliation
            await self._reconcile_positions_with_broker()

            # Return results
            discrepancies = []
            if self.current_recovery_report:
                discrepancies = [
                    {
                        'symbol': disc.symbol,
                        'discrepancy_type': disc.discrepancy_type,
                        'severity': disc.severity,
                        'recommended_action': disc.recommended_action
                    }
                    for disc in self.current_recovery_report.position_discrepancies
                ]

            return {
                'success': True,
                'message': f'Found {len(discrepancies)} discrepancies',
                'discrepancies': discrepancies,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"[ERROR] Force position reconciliation failed: {e}")
            return {
                'success': False,
                'message': f'Reconciliation failed: {str(e)}',
                'discrepancies': []
            }

    async def emergency_recovery(self) -> RecoveryReport:
        """Perform emergency recovery with aggressive fixes"""
        try:
            logger.warning("[EMERGENCY] Performing emergency recovery...")

            # Temporarily enable auto-fix for emergency
            original_auto_fix = self.auto_fix_enabled
            self.auto_fix_enabled = True

            # Perform full recovery
            recovery_report = await self.perform_startup_recovery()

            # Apply additional emergency fixes
            if recovery_report.recovery_status != RecoveryStatus.COMPLETED:
                await self._apply_emergency_fixes()

            # Restore original auto-fix setting
            self.auto_fix_enabled = original_auto_fix

            logger.warning("[EMERGENCY] Emergency recovery completed")
            return recovery_report

        except Exception as e:
            logger.error(f"[ERROR] Emergency recovery failed: {e}")
            # Return a failed recovery report
            return RecoveryReport(
                recovery_start_time=datetime.now(),
                recovery_end_time=datetime.now(),
                recovery_status=RecoveryStatus.FAILED,
                issues_found=[
                    RecoveryIssue(
                        issue_type="emergency_recovery_failure",
                        severity="critical",
                        description=f"Emergency recovery failed: {str(e)}",
                        affected_component="recovery_manager",
                        recommended_action="Manual intervention required",
                        auto_fixable=False,
                        timestamp=datetime.now()
                    )
                ],
                position_discrepancies=[],
                actions_taken=[],
                manual_interventions_required=["Full system restart may be required"],
                system_health_score=0.0,
                recommendations=["Contact system administrator", "Review system logs", "Consider full system restart"]
            )

    async def _apply_emergency_fixes(self):
        """Apply emergency fixes for critical situations"""
        try:
            logger.warning("[EMERGENCY] Applying emergency fixes...")

            emergency_actions = []

            # Force reset all workers to IDLE
            if self.worker_state_manager:
                try:
                    worker_details = self.worker_state_manager.get_worker_details()
                    if 'workers' in worker_details:
                        from agents.execution.worker_state_manager import WorkerState
                        for worker_id in worker_details['workers'].keys():
                            await self.worker_state_manager.force_worker_state_change(
                                worker_id, WorkerState.IDLE, "Emergency recovery: Force reset"
                            )
                        emergency_actions.append("Force reset all workers to IDLE")
                except Exception as e:
                    logger.error(f"[ERROR] Failed to reset workers: {e}")

            # Emergency stop all trades
            if self.execution_worker_pool:
                try:
                    stop_results = await self.execution_worker_pool.emergency_stop_all_trades()
                    emergency_actions.append(f"Emergency stopped trades: {stop_results}")
                except Exception as e:
                    logger.error(f"[ERROR] Failed to emergency stop trades: {e}")

            # Create emergency backup
            if self.persistence_manager and self.worker_state_manager:
                try:
                    from agents.state_persistence.persistence_manager import create_state_snapshot
                    system_status = self.worker_state_manager.get_system_status()

                    emergency_snapshot = await create_state_snapshot(
                        system_state=system_status,
                        worker_states={},
                        system_metrics={'emergency_recovery': True, 'timestamp': datetime.now().isoformat()}
                    )

                    await self.persistence_manager.save_state_snapshot(emergency_snapshot)
                    emergency_actions.append("Created emergency backup")
                except Exception as e:
                    logger.error(f"[ERROR] Failed to create emergency backup: {e}")

            if self.current_recovery_report:
                self.current_recovery_report.actions_taken.extend(emergency_actions)

            logger.warning(f"[EMERGENCY] Applied {len(emergency_actions)} emergency fixes")

        except Exception as e:
            logger.error(f"[ERROR] Emergency fixes failed: {e}")


# ═══════════════════════════════════════════════════════════════════════════════
# UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def create_recovery_manager(config: Dict[str, Any] = None) -> SystemRecoveryManager:
    """Factory function to create SystemRecoveryManager instance"""
    return SystemRecoveryManager(config)


async def main():
    """Test function for SystemRecoveryManager"""
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create recovery manager
    recovery_manager = SystemRecoveryManager()

    try:
        # Perform health check
        health_status = await recovery_manager.perform_health_check()
        print(f"Health Status: {json.dumps(health_status, indent=2)}")

        # Perform startup recovery (without dependencies for testing)
        recovery_report = await recovery_manager.perform_startup_recovery()

        print(f"Recovery Status: {recovery_report.recovery_status.value}")
        print(f"Issues Found: {len(recovery_report.issues_found)}")
        print(f"Health Score: {recovery_report.system_health_score:.2f}")

        # Get recovery status
        status = recovery_manager.get_recovery_status()
        print(f"Recovery Manager Status: {json.dumps(status, indent=2)}")

        print("Test completed successfully")

    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
