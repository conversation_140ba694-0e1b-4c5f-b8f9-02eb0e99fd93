#!/usr/bin/env python3
"""
[INIT] CENTRALIZED MAIN ENTRY POINT FOR INTRADAY AI TRADING SYSTEM
═══════════════════════════════════════════════════════════════════════════════

This is the unified entry point for all trading system agents and workflows.
Supports both individual agent execution and complete workflow orchestration.

Features:
[TARGET] Individual agent execution with custom configurations
[WORKFLOW] Complete workflow orchestration with dependency management
[FAST] GPU optimization and performance monitoring
[STATUS] Real-time status monitoring and health checks
[SECURITY] Error handling and graceful shutdown
[METRICS] Performance metrics and logging

Usage Examples:
  # Individual Agents
  python main.py --agent signal_generation
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting
  
  # Complete Workflows
  python main.py --workflow full_pipeline
  python main.py --workflow training_pipeline
  python main.py --workflow live_trading
  
  # System Management
  python main.py --health_check
  python main.py --status
  python main.py --optimize_gpu

Author: AI Trading System
Version: 2.0.0 (2024-2025 Optimized)
"""

import os
import sys
import asyncio
import argparse
import logging
import signal
import yaml
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.append(str(PROJECT_ROOT))

# Import utilities
from utils.config_loader import ConfigurationLoader

# Configure logging with enhanced timestamps
# Create a logger for the main application
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Default level for the logger

# Enhanced timestamp format with milliseconds
timestamp_format = '%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s'
date_format = '%H:%M:%S'

# File handler for full logs
file_handler = logging.FileHandler('logs/main.log')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter(timestamp_format, datefmt=date_format))
logger.addHandler(file_handler)

# Stream handler with timestamps for better visibility
stream_handler = logging.StreamHandler()
stream_handler.setLevel(logging.INFO) # Changed to INFO to show more details with timestamps
stream_handler.setFormatter(logging.Formatter(timestamp_format, datefmt=date_format))
logger.addHandler(stream_handler)

# Also configure the root logger to avoid duplicate messages if other modules use it
logging.basicConfig(level=logging.INFO, handlers=[stream_handler], format=timestamp_format, datefmt=date_format)

# Suppress Numba CUDA driver informational messages
logging.getLogger('numba.cuda.cudadrv.driver').setLevel(logging.WARNING)

# Suppress Numba CUDA driver informational messages
logging.getLogger('numba.cuda.cudadrv.driver').setLevel(logging.WARNING)

class TradingSystemOrchestrator:
    """
    Main orchestrator for the trading system
    Manages agent lifecycle, workflows, and system health
    """

    def __init__(self, config_loader: ConfigurationLoader):
        """Initialize the orchestrator"""
        self.config_loader = config_loader
        self.running_agents = {}
        self.shutdown_event = asyncio.Event()
        self.start_time = datetime.now()

        # Trading mode configuration
        self.trading_mode = self._get_trading_mode()
        self.paper_trading_enabled = self.trading_mode == "paper"

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        print(f"🚀 [{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Trading System Orchestrator initialized - Mode: {self.trading_mode.upper()}")
        logger.info(f"[INIT] Trading System Orchestrator initialized - Mode: {self.trading_mode.upper()}")

    def _get_trading_mode(self) -> str:
        """Get trading mode from environment variables"""
        trading_mode = os.getenv('TRADING_MODE', 'paper').lower()

        # Validate trading mode
        valid_modes = ['paper', 'real']
        if trading_mode not in valid_modes:
            logger.error(f"[ERROR] Invalid TRADING_MODE '{trading_mode}'. Must be 'paper' or 'real'.")
            raise ValueError(f"Invalid TRADING_MODE: {trading_mode}")

        # Strict check for PAPER_TRADING_ENABLED in real mode
        paper_trading_enabled_env = os.getenv('PAPER_TRADING_ENABLED')
        if trading_mode == 'real' and paper_trading_enabled_env and paper_trading_enabled_env.lower() == 'true':
            logger.error("[ERROR] TRADING_MODE is 'real', but PAPER_TRADING_ENABLED is 'true'. This is not allowed. "
                         "Set PAPER_TRADING_ENABLED to 'false' or remove it for real trading.")
            raise ValueError("Conflicting trading mode configuration: Real trading with paper trading enabled.")

        return trading_mode

    def _get_symbol_universe(self) -> List[str]:
        """Get symbol universe for trading"""
        # This could be loaded from configuration or database
        # For now, return a default set of liquid Indian stocks
        return [
            'RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'HINDUNILVR-EQ',
            'ICICIBANK-EQ', 'KOTAKBANK-EQ', 'SBIN-EQ', 'BHARTIARTL-EQ', 'ITC-EQ',
            'ASIANPAINT-EQ', 'LT-EQ', 'AXISBANK-EQ', 'MARUTI-EQ', 'SUNPHARMA-EQ',
            'TITAN-EQ', 'ULTRACEMCO-EQ', 'WIPRO-EQ', 'NESTLEIND-EQ', 'POWERGRID-EQ',
            'BAJFINANCE-EQ', 'HCLTECH-EQ', 'TECHM-EQ', 'ONGC-EQ', 'TATASTEEL-EQ'
        ]

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()

    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration based on current mode"""
        base_config = {
            'trading_mode': self.trading_mode,
            'paper_trading_enabled': self.paper_trading_enabled,
            'timestamp': datetime.now().isoformat()
        }

        if self.trading_mode == 'paper':
            base_config.update({
                'paper_trading': {
                    'initial_balance': float(os.getenv('PAPER_TRADING_INITIAL_BALANCE', 100000)),
                    'max_trades_per_day': int(os.getenv('PAPER_TRADING_MAX_TRADES_PER_DAY', 5)),
                    'commission_rate': float(os.getenv('PAPER_TRADING_COMMISSION_RATE', 0.0003)),
                    'max_position_size': float(os.getenv('PAPER_TRADING_MAX_POSITION_SIZE', 20000)),
                    'max_daily_loss': float(os.getenv('PAPER_TRADING_MAX_DAILY_LOSS', 5000)),
                    'margin_multiplier': float(os.getenv('PAPER_TRADING_MARGIN_MULTIPLIER', 3.5))
                }
            })
        else:
            smartapi_config = {
                'api_key': os.getenv('SMARTAPI_API_KEY'),
                'username': os.getenv('SMARTAPI_USERNAME'),
                'password': os.getenv('SMARTAPI_PASSWORD'),
                'totp_token': os.getenv('SMARTAPI_TOTP_TOKEN')
            }

            # Ensure all SmartAPI credentials are provided for real trading
            missing_credentials = [key for key, value in smartapi_config.items() if not value]
            if missing_credentials:
                logger.error(f"[ERROR] Missing SmartAPI credentials for real trading: {', '.join(missing_credentials)}. No fallback.")
                raise ValueError(f"Missing SmartAPI credentials: {', '.join(missing_credentials)}")

            base_config.update({'real_trading': smartapi_config})

        return base_config
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [TARGET] INDIVIDUAL AGENT EXECUTION
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def run_agent(self, agent_name: str, config_path: Optional[str] = None, **kwargs) -> bool:
        """Run a specific agent"""
        try:
            print(f"🎯 [{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Starting {agent_name} agent in {self.trading_mode.upper()} mode...")
            logger.info(f"[START] Starting {agent_name} agent in {self.trading_mode.upper()} mode...")

            # Agent mapping with their runners
            agent_runners = {
                'data_ingestion': self._run_data_ingestion,
                'feature_engineering': self._run_feature_engineering,
                'strategy_generation': self._run_strategy_generation,
                'backtesting': self._run_backtesting,
                'ai_training': self._run_ai_training,
                'market_monitoring': self._run_market_monitoring,
                'signal_generation': self._run_signal_generation,
                'risk_management': self._run_risk_management,
                'execution': self._run_execution,
                'performance_analysis': self._run_performance_analysis,
                'llm_interface': self._run_llm_interface,
                'strategy_evolution': self._run_strategy_evolution
            }

            if agent_name not in agent_runners:
                logger.error(f"[ERROR] Unknown agent: {agent_name}")
                return False

            # Add trading mode to kwargs
            kwargs['trading_mode'] = self.trading_mode
            kwargs['trading_config'] = self.get_trading_config()

            # Add infinite mode if specified
            if hasattr(self, 'infinite_mode'):
                kwargs['infinite_mode'] = self.infinite_mode

            # Run the specific agent
            success = await agent_runners[agent_name](config_path, **kwargs)

            if success:
                print(f"✅ [{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {agent_name} agent completed successfully")
                logger.info(f"[SUCCESS] {agent_name} agent completed successfully")
            else:
                print(f"❌ [{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {agent_name} agent failed")
                logger.error(f"[ERROR] {agent_name} agent failed")

            return success

        except Exception as e:
            logger.error(f"[ERROR] Error running {agent_name} agent: {e}")
            return False
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] WORKFLOW ORCHESTRATION
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def run_workflow(self, workflow_name: str, **kwargs) -> bool:
        """Run a complete workflow"""
        try:
            logger.info(f"[WORKFLOW] Starting {workflow_name} workflow...")
            
            workflows = {
                'full_pipeline': self._workflow_full_pipeline,
                'training_pipeline': self._workflow_training_pipeline,
                'live_trading': self._workflow_live_trading,
                'data_pipeline': self._workflow_data_pipeline,
                'strategy_development': self._workflow_strategy_development
            }
            
            if workflow_name not in workflows:
                logger.error(f"[ERROR] Unknown workflow: {workflow_name}")
                return False
            
            success = await workflows[workflow_name](**kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {workflow_name} workflow completed successfully")
            else:
                logger.error(f"[ERROR] {workflow_name} workflow failed")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {workflow_name} workflow: {e}")
            return False
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] AGENT IMPLEMENTATIONS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def _run_data_ingestion(self, config_path: Optional[str], **kwargs) -> bool:
        """Run data ingestion agent"""
        try:
            # Import and run data ingestion
            from scripts.historical_data_downloader import main as download_main
            await download_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Data ingestion failed: {e}")
            return False
    
    async def _run_feature_engineering(self, config_path: Optional[str], **kwargs) -> bool:
        """Run feature engineering agent"""
        try:
            from agents.run_feature_engineering import main as fe_main
            await fe_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Feature engineering failed: {e}")
            return False
    
    async def _run_strategy_generation(self, config_path: Optional[str], **kwargs) -> bool:
        """Run strategy generation agent"""
        try:
            # Strategy generation is handled by strategy evolution agent
            return await self._run_strategy_evolution(config_path, **kwargs)
        except Exception as e:
            logger.error(f"[ERROR] Strategy generation failed: {e}")
            return False
    
    async def _run_backtesting(self, config_path: Optional[str], **kwargs) -> bool:
        """Run backtesting agent"""
        try:
            # Import and run the backtesting module directly
            from agents.backtesting.enhanced_backtesting_kimi import main_async
            
            # Call the async function directly for full backtesting
            await main_async()
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Backtesting failed: {e}")
            return False
    
    async def _run_ai_training(self, config_path: Optional[str], **kwargs) -> bool:
        """Run AI training agent with fast startup"""
        try:
            # Try new modular agent first with fast startup
            try:
                from agents.ai_training import EnhancedAITrainingAgent, EnhancedAITrainingConfig
                
                # Load custom config if provided
                if config_path and Path(config_path).exists():
                    with open(config_path, 'r') as f:
                        config_data = yaml.safe_load(f)
                    config = EnhancedAITrainingConfig(**config_data)
                else:
                    config = EnhancedAITrainingConfig()
                
                # Initialize enhanced agent with fast startup
                agent = EnhancedAITrainingAgent(config, fast_startup=True)
                
                # Extract symbol and timeframe from kwargs if provided
                symbol = kwargs.get('symbol')
                timeframe = kwargs.get('timeframe')
                
                results = await agent.train_enhanced_models(
                    file_path=config_path, 
                    symbol=symbol, 
                    timeframe=timeframe
                )
                
                if results.get('status') == 'success':
                    logger.info(f"[SUCCESS] Enhanced AI training completed - {results.get('tasks_trained', 0)} tasks trained")
                    return True
                else:
                    logger.error(f"[ERROR] Enhanced AI training failed: {results.get('error', 'Unknown error')}")
                    return False
                    
            except ImportError:
                # Fallback to legacy agent
                logger.info("[FALLBACK] Using legacy AI training agent")
                from agents.run_ai_training import main as ai_main
                sys.argv = ['run_ai_training.py']
                if config_path:
                    sys.argv.extend(['--config', config_path])
                
                await ai_main()
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] AI training failed: {e}")
            return False
    
    async def _run_market_monitoring(self, config_path: Optional[str], **kwargs) -> bool:
        """Run market monitoring agent"""
        try:
            from agents.run_market_monitoring import MarketMonitoringRunner
            config = config_path or "config/market_monitoring_config.yaml"
            runner = MarketMonitoringRunner(config)
            await runner.start()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Market monitoring failed: {e}")
            return False
    
    async def _run_signal_generation(self, config_path: Optional[str], **kwargs) -> bool:
        """Run signal generation agent"""
        try:
            from agents.signal_generation.signal_generation_agent import SignalGenerationAgent
            config = config_path or "config/signal_generation_config.yaml"
            agent = SignalGenerationAgent(config)
            await agent.start()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Signal generation failed: {e}")
            return False
    
    async def _run_risk_management(self, config_path: Optional[str], **kwargs) -> bool:
        """Run risk management agent"""
        try:
            from agents.risk_management.risk_agent import RiskManagementAgent
            config = config_path or "config/risk_management_config.yaml"
            agent = RiskManagementAgent(config)
            await agent.setup()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Risk management failed: {e}")
            return False
    
    async def _run_execution(self, config_path: Optional[str], **kwargs) -> bool:
        """Run execution agent"""
        try:
            from agents.run_execution_agent import ExecutionAgentRunner
            config = config_path or "config/execution_config.yaml"
            runner = ExecutionAgentRunner(config)

            # Initialize the runner first
            if not await runner.initialize():
                logger.error("[ERROR] Failed to initialize execution agent runner")
                return False

            await runner.run()

            return True
        except Exception as e:
            logger.error(f"[ERROR] Execution agent failed: {e}")
            return False
    
    async def _run_performance_analysis(self, config_path: Optional[str], **kwargs) -> bool:
        """Run performance analysis agent"""
        try:
            from agents.run_performance_analysis import PerformanceAnalysisRunner
            config = config_path or "config/performance_analysis_config.yaml"
            runner = PerformanceAnalysisRunner(config)

            # Initialize the runner first
            if not await runner.initialize():
                logger.error("[ERROR] Failed to initialize performance analysis runner")
                return False

            # Run the performance analysis
            await runner.run()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Performance analysis failed: {e}")
            return False
    
    async def _run_llm_interface(self, config_path: Optional[str], **kwargs) -> bool:
        """Run LLM interface agent"""
        try:
            from agents.run_llm_interface_demo import main as llm_main
            await llm_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] LLM interface failed: {e}")
            return False
    
    async def _run_strategy_evolution(self, config_path: Optional[str], **kwargs) -> bool:
        """Run strategy evolution agent with enhanced error handling and performance monitoring"""
        try:
            # Import performance monitor
            from scripts.gpu_performance_monitor import gpu_performance_monitor
            
            # Check for infinite mode
            infinite_mode = kwargs.get('infinite_mode', False)

            # Enhanced strategy evolution with robust error handling
            try:
                from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent
                config = config_path or "config/enhanced_strategy_evolution_config.yaml"

                # Validate config file exists
                if not Path(config).exists():
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ❌ Config file not found: {config}")
                    logger.error(f"[ERROR] Config file not found: {config}")
                    return False

                # Initialize agent with error handling
                try:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] 🔧 Initializing Enhanced Strategy Evolution Agent...")
                    agent = EnhancedStrategyEvolutionAgent(config)
                except Exception as init_error:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ❌ Failed to initialize enhanced agent: {init_error}")
                    logger.error(f"[ERROR] Failed to initialize enhanced agent: {init_error}")
                    # Only fallback if it's a critical initialization error
                    if "No module named" in str(init_error) or "ImportError" in str(type(init_error).__name__):
                        raise ImportError(f"Module import failed: {init_error}")
                    else:
                        # For other initialization errors, try to fix and retry
                        logger.warning(f"[RETRY] Attempting to recover from initialization error")
                        return False

                # Start performance monitoring
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] 📈 Starting GPU performance monitoring...")
                gpu_performance_monitor.start_monitoring()

                # Run evolution with comprehensive error handling
                try:
                    if infinite_mode:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] 🔄 Starting infinite evolution mode - Press Ctrl+C to stop")
                        logger.info("[INFO] Starting infinite evolution mode - Press Ctrl+C to stop")
                        success = await agent.enhance_strategies_yaml(infinite_mode=True)
                    else:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] 🚀 Starting single evolution cycle with GPU parallel processing")
                        success = await agent.enhance_strategies_yaml(infinite_mode=False)
                finally:
                    # Stop performance monitoring and get summary
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] 📊 Stopping performance monitoring...")
                    performance_summary = gpu_performance_monitor.stop_monitoring()

                if success:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ✅ Enhanced strategy evolution completed successfully")
                    logger.info("[SUCCESS] Enhanced strategy evolution completed successfully")
                    return True
                else:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] ⚠️ Enhanced strategy evolution completed with warnings")
                    logger.warning("[WARN] Enhanced strategy evolution completed with warnings")
                    return False

            except ImportError as e:
                logger.error(f"[ERROR] Enhanced agent import failed: {e}")
                print("❌ Enhanced strategy evolution agent not available")
                logger.info("[FALLBACK] Enhanced agent not available, using legacy strategy evolution")

            except FileNotFoundError as e:
                logger.error(f"[ERROR] Required files missing: {e}")
                print(f"❌ Required files missing: {e}")
                return False

            except KeyboardInterrupt:
                print("\n🛑 Evolution stopped by user")
                logger.info("[INFO] Evolution stopped by user")
                return True

            except Exception as e:
                # Only fallback for specific recoverable errors
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['syntax error', 'invalid decimal literal', 'merge conflict']):
                    logger.error(f"[ERROR] Code syntax issue detected: {e}")
                    print(f"❌ Code syntax issue: {e}")
                    return False
                elif any(keyword in error_msg for keyword in ['memory', 'resource', 'timeout']):
                    logger.error(f"[ERROR] Resource issue: {e}")
                    print(f"❌ Resource issue: {e}")
                    return False
                else:
                    logger.error(f"[ERROR] Enhanced strategy evolution failed: {e}")
                    print(f"❌ Enhanced strategy evolution failed: {e}")
                    logger.info("[FALLBACK] Falling back to legacy strategy evolution")

            # Fallback to legacy strategy evolution only for specific cases
            try:
                print("🔄 Attempting legacy strategy evolution...")
                from agents.run_strategy_evolution import StrategyEvolutionRunner
                config = config_path or "config/strategy_evolution_config.yaml"
                runner = StrategyEvolutionRunner(config)
                await runner.start_agent()
                print("✅ Legacy strategy evolution completed")
                return True
            except Exception as fallback_error:
                logger.error(f"[ERROR] Legacy strategy evolution also failed: {fallback_error}")
                print(f"❌ Legacy strategy evolution failed: {fallback_error}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Strategy evolution system failure: {e}")
            print(f"❌ Strategy evolution system failure: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] WORKFLOW IMPLEMENTATIONS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _workflow_full_pipeline(self, **kwargs) -> bool:
        """Complete end-to-end trading pipeline"""
        try:
            logger.info("[WORKFLOW] Starting full pipeline workflow...")

            # Step 1: Data Ingestion
            if not await self.run_agent('data_ingestion'):
                return False

            # Step 2: Feature Engineering
            if not await self.run_agent('feature_engineering'):
                return False

            # Step 3: Strategy Generation
            if not await self.run_agent('strategy_generation'):
                return False

            # Step 4: Backtesting
            if not await self.run_agent('backtesting'):
                return False

            # Step 5: AI Training
            if not await self.run_agent('ai_training'):
                return False

            # Step 6: Start live trading agents
            await asyncio.gather(
                self.run_agent('market_monitoring'),
                self.run_agent('signal_generation'),
                self.run_agent('risk_management'),
                self.run_agent('execution'),
                self.run_agent('performance_analysis')
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] Full pipeline workflow failed: {e}")
            return False

    async def _workflow_training_pipeline(self, **kwargs) -> bool:
        """Training and strategy development pipeline"""
        try:
            logger.info("[WORKFLOW] Starting training pipeline workflow...")

            # Data preparation
            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            # Strategy development
            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            # AI training
            if not await self.run_agent('ai_training'):
                return False

            # Strategy evolution
            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Training pipeline workflow failed: {e}")
            return False

    async def _workflow_live_trading(self, **kwargs) -> bool:
        """Enhanced live trading workflow with multi-agent architecture"""
        try:
            logger.info("[WORKFLOW] Starting enhanced live trading workflow...")

            # Import the new live trading orchestrator
            from agents.live_trading_orchestrator import LiveTradingOrchestrator, SystemConfiguration

            # Create system configuration
            config = SystemConfiguration(
                max_trades=int(os.getenv('MAX_TRADES', '5')),
                trading_mode=self.trading_mode,
                symbol_universe=self._get_symbol_universe()
            )

            # Create and initialize orchestrator
            orchestrator = LiveTradingOrchestrator(config)

            logger.info("[WORKFLOW] Initializing multi-agent trading system...")
            if not await orchestrator.initialize():
                logger.error("[ERROR] Failed to initialize live trading orchestrator")
                return False

            logger.info("[WORKFLOW] Starting live trading system...")
            if not await orchestrator.start_live_trading():
                logger.error("[ERROR] Failed to start live trading system")
                await orchestrator.shutdown()
                return False

            # Log system status
            status = orchestrator.get_system_status()
            logger.info(f"[SUCCESS] Live trading system started successfully")
            logger.info(f"[STATUS] Workers: {status.get('total_workers', 0)}, Active: {status.get('worker_states', {}).get('ACTIVE', 0)}")

            try:
                # Keep the system running
                logger.info("[WORKFLOW] Live trading system is now running. Press Ctrl+C to stop.")

                # Run until interrupted
                while True:
                    await asyncio.sleep(60)  # Check every minute

                    # Log periodic status
                    current_status = orchestrator.get_system_status()
                    active_trades = current_status.get('worker_states', {}).get('ACTIVE', 0)
                    daily_pnl = current_status.get('system_metrics', {}).get('daily_pnl', 0.0)

                    if active_trades > 0 or daily_pnl != 0:
                        logger.info(f"[STATUS] Active trades: {active_trades}, Daily PnL: ₹{daily_pnl:.2f}")

            except KeyboardInterrupt:
                logger.info("[WORKFLOW] Shutdown requested by user")

            # Graceful shutdown
            logger.info("[WORKFLOW] Shutting down live trading system...")
            await orchestrator.shutdown()

            return True

        except Exception as e:
            logger.error(f"[ERROR] Enhanced live trading workflow failed: {e}")
            return False

    async def _workflow_data_pipeline(self, **kwargs) -> bool:
        """Data processing pipeline"""
        try:
            logger.info("[WORKFLOW] Starting data pipeline workflow...")

            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Data pipeline workflow failed: {e}")
            return False

    async def _workflow_strategy_development(self, **kwargs) -> bool:
        """Strategy development and optimization workflow"""
        try:
            logger.info("[WORKFLOW] Starting strategy development workflow...")

            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Strategy development workflow failed: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # [SECURITY] SYSTEM MANAGEMENT & HEALTH CHECKS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        try:
            logger.info("[HEALTH] Running system health check...")

            health_status = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'components': {},
                'system_info': {},
                'recommendations': []
            }

            # Check system resources
            try:
                import psutil
                health_status['system_info'] = {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent,
                    'uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600
                }
            except ImportError:
                health_status['system_info']['note'] = 'psutil not available'

            # Check GPU availability
            try:
                import torch
                health_status['components']['gpu'] = {
                    'available': torch.cuda.is_available(),
                    'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
                }
                if torch.cuda.is_available():
                    health_status['components']['gpu']['memory_allocated'] = torch.cuda.memory_allocated()
                    health_status['components']['gpu']['memory_reserved'] = torch.cuda.memory_reserved()
            except ImportError:
                health_status['components']['gpu'] = {'status': 'torch not available'}

            # Check data directories
            data_dirs = ['data/features', 'data/backtest', 'data/models', 'logs']
            for dir_path in data_dirs:
                path = Path(dir_path)
                health_status['components'][f'dir_{dir_path.replace("/", "_")}'] = {
                    'exists': path.exists(),
                    'writable': path.exists() and os.access(path, os.W_OK)
                }

            # Check configuration files
            config_files = [
                'config/ai_training_config.yaml',
                'config/market_monitoring_config.yaml',
                'config/signal_generation_config.yaml',
                'config/execution_config.yaml'
            ]
            for config_file in config_files:
                path = Path(config_file)
                health_status['components'][f'config_{path.stem}'] = {
                    'exists': path.exists(),
                    'readable': path.exists() and os.access(path, os.R_OK)
                }

            # Generate recommendations
            if health_status['system_info'].get('memory_percent', 0) > 85:
                health_status['recommendations'].append('High memory usage detected - consider reducing chunk sizes')

            if not health_status['components'].get('gpu', {}).get('available', False):
                health_status['recommendations'].append('GPU not available - using CPU-only mode')

            logger.info("[SUCCESS] Health check completed")
            return health_status

        except Exception as e:
            logger.error(f"[ERROR] Health check failed: {e}")
            return {'status': 'error', 'message': str(e)}

    async def optimize_gpu(self) -> bool:
        """Enhanced GPU optimization for better performance"""
        try:
            logger.info("[CONFIG] Optimizing GPU settings with enhanced optimizer...")

            # Use CUDA optimizer
            from utils.cuda_optimizer import get_cuda_optimizer
            cuda_optimizer = get_cuda_optimizer()
            
            if not cuda_optimizer.cuda_available:
                logger.warning("[WARN] GPU not available, skipping optimization")
                return False

            # Apply enhanced optimizations
            optimizations = cuda_optimizer.optimize_for_backtesting()
            
            # Print optimization results
            logger.info(f"[SUCCESS] GPU optimization completed")
            logger.info(f"[OPTIMIZATIONS] Applied: {optimizations.get('optimizations_applied', [])}")
            
            # Print memory info
            memory_info = cuda_optimizer.get_memory_info()
            logger.info(f"[GPU] {memory_info.get('device_name', 'Unknown')} - {memory_info.get('memory_total', 0):.1f}GB total")
            
            return True

        except Exception as e:
            logger.error(f"[ERROR] Enhanced GPU optimization failed: {e}")
            return False

    async def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600,
                'running_agents': list(self.running_agents.keys()),
                'agent_count': len(self.running_agents)
            }

            return status

        except Exception as e:
            logger.error(f"[ERROR] Status check failed: {e}")
            return {'status': 'error', 'message': str(e)}


# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN FUNCTION & ARGUMENT PARSING
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main entry point for the trading system"""
    parser = argparse.ArgumentParser(
        description='[INIT] Intraday AI Trading System - Centralized Control Center',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
[TARGET] INDIVIDUAL AGENT EXAMPLES:
  python main.py --agent data_ingestion
  python main.py --agent feature_engineering
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting
  python main.py --agent signal_generation
  python main.py --agent market_monitoring
  python main.py --agent risk_management
  python main.py --agent execution
  python main.py --agent performance_analysis
  python main.py --agent llm_interface
  python main.py --agent strategy_evolution

[WORKFLOW] WORKFLOW EXAMPLES:
  python main.py --workflow full_pipeline
  python main.py --workflow training_pipeline
  python main.py --workflow live_trading
  python main.py --workflow data_pipeline
  python main.py --workflow strategy_development

[SECURITY] SYSTEM MANAGEMENT:
  python main.py --health_check
  python main.py --status
  python main.py --optimize_gpu

[STATUS] MONITORING:
  python main.py --agent market_monitoring --config config/market_monitoring_config.yaml
  python main.py --workflow live_trading --monitor
        """
    )

    # Agent execution
    parser.add_argument(
        '--agent',
        choices=[
            'data_ingestion', 'feature_engineering', 'strategy_generation',
            'backtesting', 'ai_training', 'market_monitoring', 'signal_generation',
            'risk_management', 'execution', 'performance_analysis',
            'llm_interface', 'strategy_evolution'
        ],
        help='Run a specific agent'
    )

    # Workflow execution
    parser.add_argument(
        '--workflow',
        choices=[
            'full_pipeline', 'training_pipeline', 'live_trading',
            'data_pipeline', 'strategy_development'
        ],
        help='Run a complete workflow'
    )

    # Configuration
    parser.add_argument(
        '--config',
        type=str,
        help='Path to custom configuration file'
    )

    # Trading mode
    parser.add_argument(
        '--trading-mode',
        choices=['paper', 'real'],
        help='Override trading mode (paper/real) from environment'
    )

    # Infinite evolution mode
    parser.add_argument(
        '--infinite',
        action='store_true',
        help='Run strategy evolution in infinite loop mode'
    )

    # System management
    parser.add_argument(
        '--health_check',
        action='store_true',
        help='Run comprehensive system health check'
    )

    parser.add_argument(
        '--status',
        action='store_true',
        help='Show current system status'
    )

    parser.add_argument(
        '--optimize_gpu',
        action='store_true',
        help='Optimize GPU settings for better performance'
    )

    # Monitoring
    parser.add_argument(
        '--monitor',
        action='store_true',
        help='Enable real-time monitoring during execution'
    )

    # Verbose logging
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("[DEBUG] Verbose logging enabled")
    
    # Adjust stream handler level for specific agents to enable more verbose output
    if args.agent == 'strategy_evolution':
        stream_handler.setLevel(logging.INFO)
        logger.info("[INFO] Stream handler level set to INFO for strategy_evolution agent.")
    
    # Override trading mode if specified
    if hasattr(args, 'trading_mode') and args.trading_mode:
        os.environ['TRADING_MODE'] = args.trading_mode
        logger.info(f"[WORKFLOW] Trading mode overridden to: {args.trading_mode}")
    elif args.agent == 'backtesting' or args.agent == 'ai_training':
        # If backtesting or ai_training agent is selected and trading mode is not explicitly set,
        # force it to 'real' to ensure real data usage.
        os.environ['TRADING_MODE'] = 'real'
        logger.info(f"[WORKFLOW] Forcing trading mode to 'real' for {args.agent} agent.")
    # Initialize ConfigurationLoader after argument parsing
    config_loader_instance = ConfigurationLoader()

    # Create orchestrator
    orchestrator = TradingSystemOrchestrator(config_loader_instance)

    # Set infinite mode if specified
    if hasattr(args, 'infinite') and args.infinite:
        orchestrator.infinite_mode = True
        logger.info("[INFO] Infinite evolution mode enabled")

    try:
        # System management commands
        if args.health_check:
            logger.info("[HEALTH] Running health check...")
            health_status = await orchestrator.health_check()
            print("\n" + "="*80)
            print("[HEALTH] SYSTEM HEALTH CHECK RESULTS")
            print("="*80)
            print(f"[STATUS] Overall Status: {health_status.get('overall_status', 'unknown')}")
            print(f"[TIME] Timestamp: {health_status.get('timestamp', 'unknown')}")

            if 'system_info' in health_status:
                print(f"\n[SYSTEM] System Resources:")
                for key, value in health_status['system_info'].items():
                    print(f"   • {key}: {value}")

            if 'components' in health_status:
                print(f"\n[CONFIG] Components:")
                for component, status in health_status['components'].items():
                    print(f"   • {component}: {status}")

            if health_status.get('recommendations'):
                print(f"\n[INFO] Recommendations:")
                for rec in health_status['recommendations']:
                    print(f"   • {rec}")

            return

        if args.status:
            logger.info("[STATUS] Getting system status...")
            status = await orchestrator.get_status()
            print("\n" + "="*80)
            print("[STATUS] SYSTEM STATUS")
            print("="*80)
            print(f"[TIME] Timestamp: {status.get('timestamp', 'unknown')}")
            print(f"[UPTIME] Uptime: {status.get('uptime_hours', 0):.2f} hours")
            print(f"[AGENT] Running Agents: {status.get('agent_count', 0)}")

            if status.get('running_agents'):
                print(f"[LIST] Active Agents:")
                for agent in status['running_agents']:
                    print(f"   • {agent}")

            return

        if args.optimize_gpu:
            logger.info("[CONFIG] Optimizing GPU settings...")
            success = await orchestrator.optimize_gpu()
            if success:
                print("[SUCCESS] GPU optimization completed successfully")
            else:
                print("[WARN] GPU optimization skipped or failed")
            return

        # Agent execution
        if args.agent:
            logger.info(f"[TARGET] Running {args.agent} agent...")
            success = await orchestrator.run_agent(
                args.agent,
                config_path=args.config,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.agent} agent completed successfully")
            else:
                print(f"[ERROR] {args.agent} agent failed")
                sys.exit(1)

            return

        # Workflow execution
        if args.workflow:
            logger.info(f"[WORKFLOW] Running {args.workflow} workflow...")
            success = await orchestrator.run_workflow(
                args.workflow,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.workflow} workflow completed successfully")
            else:
                print(f"[ERROR] {args.workflow} workflow failed")
                sys.exit(1)

            return

        # If no specific command, show help
        parser.print_help()

    except KeyboardInterrupt:
        logger.info("[EXIT] Trading system interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    """Entry point"""
    try:
        # Ensure required directories exist
        required_dirs = ['logs', 'data/features', 'data/backtest', 'data/models']
        for dir_path in required_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

        # Run main function
        asyncio.run(main())

    except KeyboardInterrupt:
        print("\n[EXIT] Trading system interrupted by user")
    except Exception as e:
        print(f"[ERROR] Fatal error: {e}")
        sys.exit(1)
