#!/usr/bin/env python3
"""
Comprehensive Test Suite for Execution System

This test suite covers all execution system modules:
- ML Execution Optimizer
- Advanced Order Manager
- Enhanced Error Handler
- Performance Monitor
- Execution Gateway
"""

import pytest
import asyncio
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import tempfile
import os

# Import execution modules
from agents.execution_gateway import ExecutionGateway, ExecutionSystemStatus
from agents.execution.core.ml_execution_optimizer import MLExecutionOptimizer, ExecutionAlgorithm, MarketRegime
from agents.execution.core.advanced_order_manager import AdvancedOrderManager, OrderType, OrderStatus
from agents.execution.core.error_handler import Enhanced<PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorCategory, RecoveryAction
from agents.execution.core.performance_monitor import PerformanceMonitor, MetricType, TimeWindow

# Import execution models
from utils.execution_models import ExecutionRequest, ExecutionResult, ExecutionStatus
from utils.event_bus import EventBus, EventTypes


class TestExecutionSystem:
    """Test suite for the complete execution system"""
    
    @pytest.fixture
    def sample_config(self):
        """Sample configuration for testing"""
        return {
            'execution': {
                'default_venue': 'NSE',
                'timeout_seconds': 30,
                'max_retries': 3
            },
            'ml_execution': {
                'model_directory': 'test_models/',
                'min_training_samples': 10,
                'retrain_frequency_hours': 1,
                'feature_window_minutes': 5
            },
            'order_management': {
                'venues': ['NSE', 'BSE'],
                'max_order_age_hours': 1,
                'partial_fill_timeout_minutes': 5,
                'max_retries': 2
            },
            'error_handling': {
                'max_retry_attempts': 3,
                'retry_delay_seconds': 0.1,
                'exponential_backoff': True,
                'idempotency_ttl_hours': 1,
                'circuit_breakers': {
                    'test_operation': {
                        'failure_threshold': 3,
                        'recovery_timeout_seconds': 10,
                        'half_open_max_calls': 2
                    }
                }
            },
            'performance_monitoring': {
                'metric_retention_hours': 1,
                'benchmarks': {
                    'avg_slippage_bps': 5.0,
                    'avg_latency_ms': 100.0,
                    'fill_rate_percent': 95.0
                },
                'alert_thresholds': {
                    'slippage_TEST': 10.0,
                    'latency_TEST': 200.0
                }
            }
        }
    
    @pytest.fixture
    def sample_execution_request(self):
        """Sample execution request for testing"""
        return ExecutionRequest(
            order_id="TEST_001",
            symbol="TEST",
            side="BUY",
            quantity=100,
            price=100.0,
            order_type="LIMIT",
            venue="NSE",
            strategy_name="test_strategy",
            urgency=0.5,
            risk_tolerance=0.5
        )
    
    @pytest.fixture
    def mock_event_bus(self):
        """Mock event bus for testing"""
        event_bus = Mock(spec=EventBus)
        event_bus.publish = AsyncMock()
        event_bus.subscribe = AsyncMock()
        return event_bus
    
    @pytest.fixture
    def sample_market_data(self):
        """Sample market data for testing"""
        return {
            'symbol': 'TEST',
            'ltp': 100.0,
            'bid': 99.5,
            'ask': 100.5,
            'volume': 50000,
            'avg_volume': 45000,
            'volatility': 0.02,
            'price_change_percent': 1.5
        }


class TestMLExecutionOptimizer:
    """Test ML execution optimization functionality"""
    
    def test_ml_optimizer_initialization(self, sample_config):
        """Test ML optimizer initialization"""
        optimizer = MLExecutionOptimizer(sample_config)
        
        assert optimizer.config == sample_config
        assert optimizer.ml_config == sample_config.get('ml_execution', {})
        assert optimizer.min_training_samples == 10
        assert optimizer.retrain_frequency_hours == 1
    
    @pytest.mark.asyncio
    async def test_slippage_prediction(self, sample_config, sample_market_data):
        """Test slippage prediction functionality"""
        optimizer = MLExecutionOptimizer(sample_config)
        await optimizer.setup()
        
        prediction = await optimizer.predict_slippage(
            symbol="TEST",
            quantity=100,
            side="BUY",
            market_data=sample_market_data
        )
        
        assert prediction.predicted_slippage_bps >= 0
        assert 0 <= prediction.confidence <= 1
        assert prediction.optimal_chunk_size > 0
        assert prediction.optimal_chunk_size <= 100
        assert isinstance(prediction.features_used, dict)
    
    @pytest.mark.asyncio
    async def test_execution_algorithm_recommendation(self, sample_config, sample_market_data):
        """Test execution algorithm recommendation"""
        optimizer = MLExecutionOptimizer(sample_config)
        await optimizer.setup()
        
        recommendation = await optimizer.recommend_execution_algorithm(
            symbol="TEST",
            quantity=100,
            side="BUY",
            urgency=0.8,
            market_data=sample_market_data
        )
        
        assert isinstance(recommendation.algorithm, ExecutionAlgorithm)
        assert isinstance(recommendation.parameters, dict)
        assert 0 <= recommendation.confidence <= 1
        assert recommendation.expected_slippage_bps >= 0
        assert recommendation.expected_duration_seconds > 0
        assert len(recommendation.reasoning) > 0
    
    @pytest.mark.asyncio
    async def test_market_regime_detection(self, sample_config, sample_market_data):
        """Test market regime detection"""
        optimizer = MLExecutionOptimizer(sample_config)
        await optimizer.setup()
        
        regime_analysis = await optimizer.detect_market_regime(
            symbol="TEST",
            market_data=sample_market_data
        )
        
        assert isinstance(regime_analysis.current_regime, MarketRegime)
        assert 0 <= regime_analysis.regime_confidence <= 1
        assert 0 <= regime_analysis.volatility_percentile <= 100
        assert -1 <= regime_analysis.trend_strength <= 1
        assert 0 <= regime_analysis.liquidity_score <= 1
    
    @pytest.mark.asyncio
    async def test_adaptive_order_sizing(self, sample_config, sample_market_data):
        """Test adaptive order sizing"""
        optimizer = MLExecutionOptimizer(sample_config)
        await optimizer.setup()
        
        adjusted_quantity, reasoning = await optimizer.adaptive_order_sizing(
            base_quantity=100,
            symbol="TEST",
            market_data=sample_market_data,
            risk_tolerance=0.5
        )
        
        assert adjusted_quantity > 0
        assert isinstance(reasoning, str)
        assert len(reasoning) > 0


class TestAdvancedOrderManager:
    """Test advanced order management functionality"""
    
    def test_order_manager_initialization(self, sample_config):
        """Test order manager initialization"""
        manager = AdvancedOrderManager(sample_config)
        
        assert len(manager.active_orders) == 0
        assert len(manager.completed_orders) == 0
        assert 'NSE' in manager.available_venues
        assert 'BSE' in manager.available_venues
    
    @pytest.mark.asyncio
    async def test_order_creation(self, sample_config):
        """Test order creation"""
        manager = AdvancedOrderManager(sample_config)
        
        order = await manager.create_order(
            symbol="TEST",
            side="BUY",
            quantity=100,
            order_type=OrderType.LIMIT,
            price=100.0,
            strategy_name="test_strategy"
        )
        
        assert order.symbol == "TEST"
        assert order.side == "BUY"
        assert order.quantity == 100
        assert order.order_type == OrderType.LIMIT
        assert order.price == 100.0
        assert order.status == OrderStatus.PENDING
        assert order.order_id in manager.active_orders
    
    @pytest.mark.asyncio
    async def test_order_submission(self, sample_config, sample_market_data):
        """Test order submission"""
        manager = AdvancedOrderManager(sample_config)
        
        order = await manager.create_order(
            symbol="TEST",
            side="BUY",
            quantity=100,
            order_type=OrderType.LIMIT,
            price=100.0
        )
        
        success = await manager.submit_order(order.order_id, sample_market_data)
        
        assert success is True
        assert order.status == OrderStatus.SUBMITTED
        assert order.submitted_at is not None
    
    @pytest.mark.asyncio
    async def test_order_execution_handling(self, sample_config):
        """Test order execution handling"""
        manager = AdvancedOrderManager(sample_config)
        
        order = await manager.create_order(
            symbol="TEST",
            side="BUY",
            quantity=100,
            order_type=OrderType.LIMIT,
            price=100.0
        )
        
        # Simulate execution
        execution_data = {
            'execution_id': 'EXEC_001',
            'timestamp': datetime.now(),
            'quantity': 100,
            'price': 100.0,
            'commission': 3.0,
            'exchange_order_id': 'EXC_001',
            'venue': 'NSE',
            'liquidity_flag': 'taker'
        }
        
        success = await manager.handle_execution(order.order_id, execution_data)
        
        assert success is True
        assert order.filled_quantity == 100
        assert order.status == OrderStatus.FILLED
        assert len(order.executions) == 1
        assert order.average_fill_price == 100.0
    
    @pytest.mark.asyncio
    async def test_order_cancellation(self, sample_config):
        """Test order cancellation"""
        manager = AdvancedOrderManager(sample_config)
        
        order = await manager.create_order(
            symbol="TEST",
            side="BUY",
            quantity=100,
            order_type=OrderType.LIMIT,
            price=100.0
        )
        
        success = await manager.cancel_order(order.order_id, "Test cancellation")
        
        assert success is True
        assert order.order_id not in manager.active_orders
        assert order.order_id in manager.completed_orders
        assert order.status == OrderStatus.CANCELLED


class TestEnhancedErrorHandler:
    """Test enhanced error handling functionality"""
    
    def test_error_handler_initialization(self, sample_config):
        """Test error handler initialization"""
        handler = EnhancedErrorHandler(sample_config)
        
        assert handler.max_retry_attempts == 3
        assert handler.retry_delay_seconds == 0.1
        assert handler.exponential_backoff is True
        assert len(handler.recovery_strategies) > 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, sample_config):
        """Test error handling functionality"""
        handler = EnhancedErrorHandler(sample_config)
        
        test_error = ValueError("Test error")
        context = {'operation': 'test_operation', 'data': 'test_data'}
        
        should_retry, recovery_result = await handler.handle_error(
            test_error, context, "test_operation"
        )
        
        assert isinstance(should_retry, bool)
        assert recovery_result is not None
        assert len(handler.error_history) > 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker(self, sample_config):
        """Test circuit breaker functionality"""
        handler = EnhancedErrorHandler(sample_config)
        
        # Test successful operation
        def successful_operation():
            return "success"
        
        success, result = await handler.execute_with_circuit_breaker(
            "test_operation", successful_operation
        )
        
        assert success is True
        assert result == "success"
        
        # Test failing operation
        def failing_operation():
            raise Exception("Test failure")
        
        success, result = await handler.execute_with_circuit_breaker(
            "test_operation", failing_operation
        )
        
        assert success is False
        assert "Test failure" in str(result)
    
    @pytest.mark.asyncio
    async def test_idempotency(self, sample_config):
        """Test idempotency functionality"""
        handler = EnhancedErrorHandler(sample_config)
        
        call_count = 0
        
        def test_operation(value):
            nonlocal call_count
            call_count += 1
            return f"result_{value}"
        
        # First call
        is_duplicate, result = await handler.execute_with_idempotency(
            "test_key", test_operation, "test_value"
        )
        
        assert is_duplicate is False
        assert result == "result_test_value"
        assert call_count == 1
        
        # Second call with same key - should return cached result
        is_duplicate, result = await handler.execute_with_idempotency(
            "test_key", test_operation, "test_value"
        )
        
        assert is_duplicate is True
        assert result == "result_test_value"
        assert call_count == 1  # Should not increment


class TestPerformanceMonitor:
    """Test performance monitoring functionality"""
    
    def test_performance_monitor_initialization(self, sample_config):
        """Test performance monitor initialization"""
        monitor = PerformanceMonitor(sample_config)
        
        assert len(monitor.metrics) == len(MetricType)
        assert len(monitor.benchmarks) > 0
        assert monitor.metric_retention_hours == 1
    
    @pytest.mark.asyncio
    async def test_execution_tracking(self, sample_config):
        """Test execution tracking"""
        monitor = PerformanceMonitor(sample_config)
        
        # Start tracking
        await monitor.record_execution_start(
            order_id="TEST_001",
            symbol="TEST",
            venue="NSE",
            strategy="test_strategy",
            order_details={'quantity': 100, 'price': 100.0}
        )
        
        assert "TEST_001" in monitor.active_orders
        
        # Complete tracking
        await monitor.record_execution_complete(
            order_id="TEST_001",
            execution_details={
                'expected_price': 100.0,
                'actual_price': 100.5,
                'requested_quantity': 100,
                'filled_quantity': 100,
                'commission': 3.0,
                'side': 'BUY',
                'trade_value': 10050.0
            }
        )
        
        assert "TEST_001" not in monitor.active_orders
        assert len(monitor.execution_sessions["TEST_001"]) > 0
    
    @pytest.mark.asyncio
    async def test_slippage_analysis(self, sample_config):
        """Test slippage analysis"""
        monitor = PerformanceMonitor(sample_config)
        
        # Add some test slippage data
        await monitor._record_metric(
            MetricType.SLIPPAGE, 5.0, "TEST_001", "TEST", "NSE", "test_strategy"
        )
        await monitor._record_metric(
            MetricType.SLIPPAGE, 3.0, "TEST_002", "TEST", "NSE", "test_strategy"
        )
        await monitor._record_metric(
            MetricType.SLIPPAGE, 7.0, "TEST_003", "TEST", "NSE", "test_strategy"
        )
        
        analysis = await monitor.analyze_slippage(
            symbol="TEST", time_window=TimeWindow.HOUR
        )
        
        assert analysis.symbol == "TEST"
        assert analysis.sample_count == 3
        assert analysis.avg_slippage_bps == 5.0  # (5+3+7)/3
        assert analysis.median_slippage_bps == 5.0
        assert analysis.max_slippage_bps == 7.0
        assert analysis.min_slippage_bps == 3.0
    
    def test_performance_summary(self, sample_config):
        """Test performance summary generation"""
        monitor = PerformanceMonitor(sample_config)
        
        summary = monitor.get_performance_summary(TimeWindow.HOUR)
        
        assert 'time_window' in summary
        assert 'metrics' in summary
        assert 'summary_timestamp' in summary
        
        # Check that all metric types are included
        for metric_type in MetricType:
            assert metric_type.value in summary['metrics']


class TestExecutionGateway:
    """Test the unified execution gateway"""
    
    @pytest.mark.asyncio
    async def test_gateway_initialization(self, sample_config, mock_event_bus):
        """Test gateway initialization"""
        config = Mock()
        
        gateway = ExecutionGateway(mock_event_bus, config, "test_session")
        
        assert gateway.name == "ExecutionGateway"
        assert gateway.event_bus == mock_event_bus
        assert gateway.system_operational is False  # Not setup yet
    
    def test_system_status(self, sample_config, mock_event_bus):
        """Test system status reporting"""
        config = Mock()
        
        gateway = ExecutionGateway(mock_event_bus, config, "test_session")
        status = gateway.get_system_status()
        
        assert isinstance(status, ExecutionSystemStatus)
        assert hasattr(status, 'system_operational')
        assert hasattr(status, 'modules_loaded')
        assert hasattr(status, 'execution_allowed')
    
    def test_performance_metrics(self, sample_config, mock_event_bus):
        """Test performance metrics"""
        config = Mock()
        
        gateway = ExecutionGateway(mock_event_bus, config, "test_session")
        
        # Simulate some executions
        gateway.execution_count = 10
        gateway.success_count = 8
        gateway.error_count = 2
        
        metrics = gateway.get_performance_metrics()
        
        assert metrics['execution_count'] == 10
        assert metrics['success_count'] == 8
        assert metrics['error_count'] == 2
        assert metrics['success_rate'] == 80.0


# Integration Tests
class TestExecutionIntegration:
    """Integration tests for the complete execution system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_execution_flow(self, sample_config, sample_execution_request, mock_event_bus):
        """Test complete end-to-end execution flow"""
        # This would test the full pipeline from execution request to completion
        # Including all modules working together
        
        config = Mock()
        gateway = ExecutionGateway(mock_event_bus, config, "test_session")
        
        # Test that the gateway has the correct execution method
        assert hasattr(gateway, 'execute_order')
        assert callable(gateway.execute_order)
        
        # Test system status
        status = gateway.get_system_status()
        assert isinstance(status, ExecutionSystemStatus)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
