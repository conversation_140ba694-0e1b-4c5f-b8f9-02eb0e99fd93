#!/usr/bin/env python3
"""
Comprehensive Test Suite for Risk Management System

This test suite covers all core risk management modules:
- Capital Allocator
- Position Sizer
- Pre-Trade Filters
- Circuit Breakers
- Portfolio Monitor
- Risk Calculator
- Configuration Manager
- Risk Management Gateway
"""

import pytest
import asyncio
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import yaml
import tempfile
import os

# Import risk management modules
from agents.risk_management_gateway import RiskManagementGateway, RiskManagementStatus
from agents.risk_management.core.capital_allocator import CapitalAllocator, CapitalAllocation
from agents.risk_management.core.position_sizer import PositionSizer, PositionSizingMethod
from agents.risk_management.core.pre_trade_filters import PreTradeFilters, ValidationSummary
from agents.risk_management.core.circuit_breakers import CircuitBreakers, CircuitBreakerType
from agents.risk_management.core.portfolio_monitor import PortfolioMonitor, VaRResult
from agents.risk_management.core.risk_calculator import <PERSON><PERSON><PERSON><PERSON>tor, PositionRiskAssessment
from agents.risk_management.core.configuration_manager import ConfigurationManager

# Import risk models
from utils.risk_models import (
    TradeRequest, Position, ValidationResult, RiskMetrics,
    TradeDirection, ProductType, OrderType, RiskLevel
)
from utils.event_bus import EventBus, EventTypes


class TestRiskManagementSystem:
    """Test suite for the complete risk management system"""
    
    @pytest.fixture
    def sample_config(self):
        """Sample configuration for testing"""
        return {
            'capital_management': {
                'initial_balance': 500000,
                'max_daily_risk': 0.02,
                'max_position_risk': 0.003,
                'max_portfolio_heat': 0.08,
                'emergency_cash_reserve': 0.15,
                'margin': {
                    'intraday_multiplier': 3.5,
                    'buffer_percent': 10.0
                }
            },
            'position_sizing': {
                'default_method': 'adaptive',
                'max_position_size_percent': 5.0,
                'min_position_size_percent': 0.1,
                'kelly_criterion': {
                    'lookback_days': 30,
                    'max_fraction': 0.25,
                    'min_trades': 10
                },
                'volatility_adjustment': {
                    'lookback_days': 20,
                    'target_volatility': 0.02
                }
            },
            'pre_trade_filters': {
                'min_risk_reward_ratio': 1.5,
                'max_concurrent_positions': 5,
                'max_positions_per_symbol': 1,
                'max_sector_concentration': 0.3,
                'max_volatility_threshold': 0.05,
                'min_volatility_threshold': 0.005,
                'trading_hours': {
                    'start': '09:15',
                    'end': '15:30'
                }
            },
            'circuit_breakers': {
                'drawdown': {
                    'enabled': True,
                    'threshold': 0.05,
                    'action': 'stop_new_trades',
                    'cooldown_minutes': 30
                },
                'daily_loss': {
                    'enabled': True,
                    'threshold': 0.03,
                    'action': 'close_losing_positions',
                    'cooldown_minutes': 60
                }
            },
            'portfolio_monitor': {
                'var_confidence_levels': [0.95, 0.99],
                'var_lookback_days': 252,
                'correlation_lookback_days': 60
            },
            'risk_calculator': {
                'low_risk_threshold': 0.01,
                'medium_risk_threshold': 0.02,
                'high_risk_threshold': 0.05
            }
        }
    
    @pytest.fixture
    def sample_trade_request(self):
        """Sample trade request for testing"""
        return TradeRequest(
            signal_id="TEST_001",
            symbol="RELIANCE",
            exchange="NSE",
            strategy_name="momentum",
            direction=TradeDirection.LONG,
            entry_price=2500.0,
            stop_loss=2450.0,
            take_profit=2600.0,
            quantity=10,
            product_type=ProductType.MIS,
            order_type=OrderType.LIMIT,
            risk_amount=500.0,
            capital_allocated=25000.0,
            risk_reward_ratio=2.0,
            market_regime="bullish",
            confidence=0.8,
            timestamp=datetime.now(),
            context={}
        )
    
    @pytest.fixture
    def sample_position(self):
        """Sample position for testing"""
        return Position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=2500.0,
            current_price=2520.0,
            direction=TradeDirection.LONG,
            product_type=ProductType.MIS,
            timestamp=datetime.now()
        )
    
    @pytest.fixture
    def mock_event_bus(self):
        """Mock event bus for testing"""
        event_bus = Mock(spec=EventBus)
        event_bus.publish = AsyncMock()
        event_bus.subscribe = AsyncMock()
        return event_bus


class TestCapitalAllocator:
    """Test capital allocation functionality"""
    
    def test_capital_allocator_initialization(self, sample_config):
        """Test capital allocator initialization"""
        allocator = CapitalAllocator(sample_config)
        
        assert allocator.total_capital == 500000
        assert allocator.available_capital == 500000
        assert allocator.allocated_capital == 0.0
        assert allocator.reserved_capital == 75000  # 15% of 500000
    
    def test_capital_allocation_success(self, sample_config, sample_trade_request):
        """Test successful capital allocation"""
        allocator = CapitalAllocator(sample_config)
        
        success, allocated_amount, reason = allocator.allocate_capital(sample_trade_request)
        
        assert success is True
        assert allocated_amount > 0
        assert "successfully" in reason.lower()
        assert allocator.allocated_capital > 0
    
    def test_capital_allocation_insufficient_funds(self, sample_config, sample_trade_request):
        """Test capital allocation with insufficient funds"""
        # Reduce available capital
        sample_config['capital_management']['initial_balance'] = 1000
        allocator = CapitalAllocator(sample_config)
        
        success, allocated_amount, reason = allocator.allocate_capital(sample_trade_request)
        
        assert success is False
        assert allocated_amount == 0.0
        assert "insufficient" in reason.lower()
    
    def test_capital_release(self, sample_config, sample_trade_request):
        """Test capital release functionality"""
        allocator = CapitalAllocator(sample_config)
        
        # Allocate capital first
        success, allocated_amount, _ = allocator.allocate_capital(sample_trade_request)
        assert success is True
        
        initial_allocated = allocator.allocated_capital
        
        # Release capital
        allocation_id = f"{sample_trade_request.symbol}_{sample_trade_request.signal_id}"
        release_success = allocator.release_capital(allocation_id)
        
        assert release_success is True
        assert allocator.allocated_capital < initial_allocated


class TestPositionSizer:
    """Test position sizing functionality"""
    
    def test_position_sizer_initialization(self, sample_config):
        """Test position sizer initialization"""
        sizer = PositionSizer(sample_config)
        
        assert sizer.default_method == PositionSizingMethod.ADAPTIVE
        assert sizer.max_position_size_percent == 5.0
        assert sizer.min_position_size_percent == 0.1
    
    def test_fixed_fraction_sizing(self, sample_config, sample_trade_request):
        """Test fixed fraction position sizing"""
        sizer = PositionSizer(sample_config)
        available_capital = 100000
        
        result = sizer.calculate_position_size(
            sample_trade_request, 
            available_capital, 
            PositionSizingMethod.FIXED_FRACTION
        )
        
        assert result.quantity > 0
        assert result.capital_allocated > 0
        assert result.method_used == "fixed_fraction"
        assert result.confidence > 0
    
    def test_adaptive_sizing(self, sample_config, sample_trade_request):
        """Test adaptive position sizing"""
        sizer = PositionSizer(sample_config)
        available_capital = 100000
        
        result = sizer.calculate_position_size(
            sample_trade_request, 
            available_capital, 
            PositionSizingMethod.ADAPTIVE
        )
        
        assert result.quantity > 0
        assert result.method_used == "adaptive"
        assert len(result.sizing_factors) > 0
    
    def test_position_size_limits(self, sample_config, sample_trade_request):
        """Test position size limits are enforced"""
        sizer = PositionSizer(sample_config)
        available_capital = 100000
        
        # Test with very high risk trade
        sample_trade_request.quantity = 1000  # Very large quantity
        
        result = sizer.calculate_position_size(
            sample_trade_request, 
            available_capital, 
            PositionSizingMethod.FIXED_FRACTION
        )
        
        # Should be limited by max position size
        position_value = result.quantity * sample_trade_request.entry_price
        position_percent = (position_value / available_capital) * 100
        assert position_percent <= sample_config['position_sizing']['max_position_size_percent']


class TestPreTradeFilters:
    """Test pre-trade filtering functionality"""
    
    def test_pre_trade_filters_initialization(self, sample_config):
        """Test pre-trade filters initialization"""
        filters = PreTradeFilters(sample_config)
        
        assert filters.min_risk_reward_ratio == 1.5
        assert filters.max_concurrent_positions == 5
        assert filters.max_positions_per_symbol == 1
    
    def test_risk_reward_validation_pass(self, sample_config, sample_trade_request):
        """Test risk-reward ratio validation passes"""
        filters = PreTradeFilters(sample_config)
        
        validation = filters.validate_trade(sample_trade_request)
        
        assert validation.overall_passed is True
        assert len(validation.critical_issues) == 0
    
    def test_risk_reward_validation_fail(self, sample_config, sample_trade_request):
        """Test risk-reward ratio validation fails"""
        filters = PreTradeFilters(sample_config)
        
        # Set poor risk-reward ratio
        sample_trade_request.take_profit = 2510.0  # Very small profit target
        
        validation = filters.validate_trade(sample_trade_request)
        
        # Should fail due to poor risk-reward ratio
        assert validation.overall_passed is False
        assert len(validation.errors) > 0
    
    def test_position_limit_validation(self, sample_config, sample_trade_request):
        """Test position limit validation"""
        filters = PreTradeFilters(sample_config)
        
        # Simulate maximum positions reached
        current_positions = {f"pos_{i}": {"symbol": f"STOCK_{i}"} for i in range(5)}
        
        validation = filters.validate_trade(sample_trade_request, current_positions)
        
        # Should fail due to position limit
        assert validation.overall_passed is False
        assert any("position" in error.lower() for error in validation.errors)


class TestCircuitBreakers:
    """Test circuit breaker functionality"""
    
    def test_circuit_breakers_initialization(self, sample_config):
        """Test circuit breakers initialization"""
        breakers = CircuitBreakers(sample_config)
        
        assert len(breakers.rules) > 0
        assert CircuitBreakerType.DRAWDOWN in breakers.rules
        assert CircuitBreakerType.DAILY_LOSS in breakers.rules
    
    def test_drawdown_circuit_breaker(self, sample_config):
        """Test drawdown circuit breaker triggering"""
        breakers = CircuitBreakers(sample_config)
        
        # Simulate high drawdown
        portfolio_metrics = {
            'current_drawdown': 0.06,  # 6% drawdown, above 5% threshold
            'daily_pnl': -10000,
            'portfolio_heat': 0.04
        }
        
        triggered_events = breakers.check_circuit_breakers(portfolio_metrics)
        
        assert len(triggered_events) > 0
        assert any(event.breaker_type == CircuitBreakerType.DRAWDOWN for event in triggered_events)
    
    def test_emergency_stop(self, sample_config):
        """Test emergency stop functionality"""
        breakers = CircuitBreakers(sample_config)
        
        breakers.trigger_emergency_stop("Test emergency stop")
        
        assert breakers.emergency_stop_active is True
        trading_allowed, reason = breakers.is_trading_allowed()
        assert trading_allowed is False
        assert "emergency" in reason.lower()


class TestRiskManagementGateway:
    """Test the unified risk management gateway"""
    
    @pytest.mark.asyncio
    async def test_gateway_initialization(self, sample_config, mock_event_bus):
        """Test gateway initialization"""
        # Create a mock config object
        config = Mock()
        config.config_path = 'test_config.yaml'
        
        gateway = RiskManagementGateway(mock_event_bus, config, "test_session")
        
        assert gateway.name == "RiskManagementGateway"
        assert gateway.event_bus == mock_event_bus
        assert gateway.system_operational is False  # Not setup yet
    
    @pytest.mark.asyncio
    async def test_trade_validation_success(self, sample_config, sample_trade_request, mock_event_bus):
        """Test successful trade validation through gateway"""
        # This would require mocking the setup process
        # For now, test the validation logic structure
        config = Mock()
        config.config_path = 'test_config.yaml'
        
        gateway = RiskManagementGateway(mock_event_bus, config, "test_session")
        
        # Test that the gateway has the correct validation method
        assert hasattr(gateway, 'validate_trade_request')
        assert callable(gateway.validate_trade_request)
    
    def test_system_status(self, sample_config, mock_event_bus):
        """Test system status reporting"""
        config = Mock()
        config.config_path = 'test_config.yaml'
        
        gateway = RiskManagementGateway(mock_event_bus, config, "test_session")
        status = gateway.get_system_status()
        
        assert isinstance(status, RiskManagementStatus)
        assert hasattr(status, 'system_operational')
        assert hasattr(status, 'modules_loaded')
        assert hasattr(status, 'trading_allowed')


# Integration Tests
class TestRiskManagementIntegration:
    """Integration tests for the complete risk management system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_trade_validation(self, sample_config, sample_trade_request, mock_event_bus):
        """Test complete end-to-end trade validation process"""
        # This would test the full pipeline from trade request to approval/rejection
        # Including all modules working together
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(sample_config, f)
            config_path = f.name
        
        try:
            # Test configuration manager
            config_manager = ConfigurationManager(config_path, auto_reload=False)
            loaded_config = config_manager.get_configuration()
            
            assert loaded_config is not None
            assert 'capital_management' in loaded_config
            
            # Test individual modules with loaded config
            capital_allocator = CapitalAllocator(loaded_config)
            position_sizer = PositionSizer(loaded_config)
            pre_trade_filters = PreTradeFilters(loaded_config)
            
            # Test capital allocation
            success, amount, reason = capital_allocator.allocate_capital(sample_trade_request)
            assert success is True
            
            # Test position sizing
            sizing_result = position_sizer.calculate_position_size(
                sample_trade_request, 100000, PositionSizingMethod.ADAPTIVE
            )
            assert sizing_result.quantity > 0
            
            # Test pre-trade filters
            validation = pre_trade_filters.validate_trade(sample_trade_request)
            assert validation.overall_passed is True
            
        finally:
            # Clean up temporary file
            os.unlink(config_path)
    
    def test_configuration_hot_reload(self, sample_config):
        """Test configuration hot-reload functionality"""
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(sample_config, f)
            config_path = f.name
        
        try:
            config_manager = ConfigurationManager(config_path, auto_reload=False)
            
            # Load initial config
            initial_balance = config_manager.get_value('capital_management', 'initial_balance')
            assert initial_balance == 500000
            
            # Update configuration
            success = config_manager.update_configuration(
                'capital_management',
                {'initial_balance': 600000},
                'test_user',
                'Test update'
            )
            assert success is True
            
            # Verify update
            updated_balance = config_manager.get_value('capital_management', 'initial_balance')
            assert updated_balance == 600000
            
            # Test change history
            changes = config_manager.get_change_history('capital_management', limit=1)
            assert len(changes) > 0
            assert changes[0]['new_value'] == 600000
            
        finally:
            os.unlink(config_path)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
