#!/usr/bin/env python3
"""
Tests for Signal Generation Gateway
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from agents.signal_generation_gateway import SignalGenerationGateway, SignalGenerationGatewayConfig
from agents.signal_generation.core.data_models import (
    SignalInput, TradingSignal, SignalType, SignalAction, MarketIndicators, OHLCVData
)


@pytest.fixture
def gateway_config():
    """Create test gateway configuration"""
    return SignalGenerationGatewayConfig()


@pytest.fixture
def sample_signal_input():
    """Create sample signal input for testing"""
    ohlcv_data = [
        OHLCVData(
            timestamp=datetime.now(),
            open=100.0,
            high=101.0,
            low=99.0,
            close=100.5,
            volume=10000
        )
    ]
    
    indicators = MarketIndicators(
        symbol="NIFTY",
        timestamp=datetime.now(),
        ema_5=100.2,
        ema_20=99.8,
        rsi_14=55.0,
        macd=0.1,
        macd_signal=0.08,
        bb_upper=102.0,
        bb_lower=98.0,
        bb_middle=100.0,
        atr=1.5
    )
    
    return SignalInput(
        symbol="NIFTY",
        timeframe="5min",
        timestamp=datetime.now(),
        ohlcv_data=ohlcv_data,
        indicators=indicators,
        market_regime="bull"
    )


@pytest.fixture
def sample_trading_signal():
    """Create sample trading signal for testing"""
    return TradingSignal(
        signal_id="test_signal_001",
        symbol="NIFTY",
        strategy_name="test_strategy",
        signal_type=SignalType.LONG,
        action=SignalAction.BUY,
        entry_price=100.5,
        stop_loss=98.0,
        take_profit=105.0,
        quantity=10,
        risk_reward_ratio=2.0,
        confidence=0.75,
        market_regime="bull",
        timestamp=datetime.now()
    )


class TestSignalGenerationGateway:
    """Test cases for Signal Generation Gateway"""
    
    @pytest.mark.asyncio
    async def test_gateway_initialization(self, gateway_config):
        """Test gateway initialization"""
        gateway = SignalGenerationGateway(gateway_config)
        
        # Mock component initialization
        with patch.object(gateway.strategy_manager, 'initialize', return_value=True), \
             patch.object(gateway.strategy_evaluator, 'initialize', return_value=True), \
             patch.object(gateway.position_sizer, 'initialize', return_value=True), \
             patch.object(gateway.signal_validator, 'initialize', return_value=True), \
             patch.object(gateway.signal_processor, 'initialize', return_value=True), \
             patch.object(gateway.multi_timeframe_fusion, 'initialize', return_value=True), \
             patch.object(gateway.ml_integrator, 'initialize', return_value=True):
            
            result = await gateway.initialize()
            
            assert result is True
            assert gateway.is_initialized is True
            assert gateway.is_running is True
    
    @pytest.mark.asyncio
    async def test_gateway_initialization_failure(self, gateway_config):
        """Test gateway initialization failure"""
        gateway = SignalGenerationGateway(gateway_config)
        
        # Mock component initialization failure
        with patch.object(gateway.strategy_manager, 'initialize', return_value=False):
            result = await gateway.initialize()
            
            assert result is False
            assert gateway.is_initialized is False
    
    @pytest.mark.asyncio
    async def test_signal_generation_success(self, gateway_config, sample_signal_input):
        """Test successful signal generation"""
        gateway = SignalGenerationGateway(gateway_config)
        gateway.is_initialized = True
        gateway.is_running = True
        
        # Mock strategy manager
        mock_strategies = {
            'test_strategy': {'name': 'test_strategy', 'enabled': True}
        }
        gateway.strategy_manager.get_active_strategies = AsyncMock(return_value=mock_strategies)
        
        # Mock strategy evaluator
        from agents.signal_generation.core.data_models import StrategyEvaluationResult
        mock_result = StrategyEvaluationResult(
            strategy_name='test_strategy',
            long_condition=True,
            short_condition=False,
            confidence_score=0.8,
            signal_strength=0.75,
            evaluation_time_ms=10.0,
            condition_details={}
        )
        gateway.strategy_evaluator.evaluate_multiple_strategies = AsyncMock(return_value=[mock_result])
        
        # Mock other components
        gateway.signal_processor.process_signal = AsyncMock(return_value=Mock(processed_signal=None))
        gateway.ml_integrator.enhance_signal = AsyncMock(side_effect=lambda x, y: x)
        
        from agents.signal_generation.core.data_models import PositionSizingResult
        mock_position_result = PositionSizingResult(
            quantity=10,
            capital_allocated=1000.0,
            risk_amount=50.0
        )
        gateway.position_sizer.calculate_position_size = AsyncMock(return_value=mock_position_result)
        
        from agents.signal_generation.core.data_models import SignalValidationResult
        mock_validation_result = SignalValidationResult(
            is_valid=True,
            time_filter_result=True,
            cooldown_result=True
        )
        gateway.signal_validator.validate_signal = AsyncMock(return_value=mock_validation_result)
        
        # Test signal generation
        result = await gateway.generate_signal(sample_signal_input)
        
        assert result is not None
        assert isinstance(result, TradingSignal)
        assert result.symbol == sample_signal_input.symbol
        assert result.quantity == 10
        assert result.capital_allocated == 1000.0
    
    @pytest.mark.asyncio
    async def test_signal_generation_no_strategies(self, gateway_config, sample_signal_input):
        """Test signal generation with no active strategies"""
        gateway = SignalGenerationGateway(gateway_config)
        gateway.is_initialized = True
        gateway.is_running = True
        
        # Mock empty strategies
        gateway.strategy_manager.get_active_strategies = AsyncMock(return_value={})
        
        result = await gateway.generate_signal(sample_signal_input)
        
        assert result is None
        assert gateway.stats['signals_processed'] == 1
    
    @pytest.mark.asyncio
    async def test_signal_generation_validation_failure(self, gateway_config, sample_signal_input):
        """Test signal generation with validation failure"""
        gateway = SignalGenerationGateway(gateway_config)
        gateway.is_initialized = True
        gateway.is_running = True
        
        # Mock strategy components
        mock_strategies = {'test_strategy': {'name': 'test_strategy', 'enabled': True}}
        gateway.strategy_manager.get_active_strategies = AsyncMock(return_value=mock_strategies)
        
        from agents.signal_generation.core.data_models import StrategyEvaluationResult
        mock_result = StrategyEvaluationResult(
            strategy_name='test_strategy',
            long_condition=True,
            short_condition=False,
            confidence_score=0.8,
            signal_strength=0.75,
            evaluation_time_ms=10.0,
            condition_details={}
        )
        gateway.strategy_evaluator.evaluate_multiple_strategies = AsyncMock(return_value=[mock_result])
        
        # Mock processing components
        gateway.signal_processor.process_signal = AsyncMock(return_value=Mock(processed_signal=None))
        gateway.ml_integrator.enhance_signal = AsyncMock(side_effect=lambda x, y: x)
        
        from agents.signal_generation.core.data_models import PositionSizingResult
        mock_position_result = PositionSizingResult(quantity=10, capital_allocated=1000.0, risk_amount=50.0)
        gateway.position_sizer.calculate_position_size = AsyncMock(return_value=mock_position_result)
        
        # Mock validation failure
        from agents.signal_generation.core.data_models import SignalValidationResult
        mock_validation_result = SignalValidationResult(
            is_valid=False,
            rejection_reason="Test rejection",
            time_filter_result=False,
            cooldown_result=True
        )
        gateway.signal_validator.validate_signal = AsyncMock(return_value=mock_validation_result)
        
        result = await gateway.generate_signal(sample_signal_input)
        
        assert result is None
        assert gateway.stats['signals_rejected'] == 1
    
    @pytest.mark.asyncio
    async def test_multi_timeframe_signal_generation(self, gateway_config):
        """Test multi-timeframe signal generation"""
        gateway = SignalGenerationGateway(gateway_config)
        gateway.is_initialized = True
        gateway.is_running = True
        
        # Mock timeframe inputs
        timeframe_inputs = {
            '1min': Mock(),
            '5min': Mock(),
            '15min': Mock()
        }
        
        # Mock signal generation for each timeframe
        mock_signal = Mock()
        gateway.generate_signal = AsyncMock(return_value=mock_signal)
        
        # Mock multi-timeframe fusion
        from agents.signal_generation.core.data_models import MultiTimeframeSignalResult
        mock_fusion_result = MultiTimeframeSignalResult(
            primary_signal=mock_signal,
            timeframe_signals={'5min': mock_signal},
            consensus_signal=mock_signal,
            consensus_strength=0.8,
            timeframe_agreement=0.9,
            fusion_method="weighted_average"
        )
        gateway.multi_timeframe_fusion.fuse_timeframe_signals = AsyncMock(return_value=mock_fusion_result)
        
        result = await gateway.generate_multi_timeframe_signal(timeframe_inputs)
        
        assert result == mock_signal
    
    @pytest.mark.asyncio
    async def test_gateway_cleanup(self, gateway_config):
        """Test gateway cleanup"""
        gateway = SignalGenerationGateway(gateway_config)
        gateway.is_initialized = True
        gateway.is_running = True
        
        # Mock component cleanup
        for component in [gateway.strategy_manager, gateway.strategy_evaluator, 
                         gateway.position_sizer, gateway.signal_validator,
                         gateway.signal_processor, gateway.multi_timeframe_fusion,
                         gateway.ml_integrator]:
            component.cleanup = AsyncMock()
        
        await gateway.cleanup()
        
        assert gateway.is_initialized is False
        assert gateway.is_running is False
    
    def test_gateway_stats(self, gateway_config):
        """Test gateway statistics"""
        gateway = SignalGenerationGateway(gateway_config)
        
        # Mock component stats
        gateway.strategy_evaluator.get_performance_metrics = Mock(return_value={})
        gateway.position_sizer.get_portfolio_status = Mock(return_value={})
        gateway.signal_validator.get_validation_stats = Mock(return_value={})
        gateway.signal_processor.get_processing_stats = Mock(return_value={})
        gateway.multi_timeframe_fusion.get_fusion_stats = Mock(return_value={})
        gateway.strategy_manager.get_manager_stats = Mock(return_value={})
        
        stats = gateway.get_gateway_stats()
        
        assert 'gateway_stats' in stats
        assert 'component_stats' in stats
        assert stats['is_initialized'] is False
        assert stats['is_running'] is False
    
    def test_stats_reset(self, gateway_config):
        """Test statistics reset"""
        gateway = SignalGenerationGateway(gateway_config)
        
        # Set some stats
        gateway.stats['signals_processed'] = 10
        gateway.stats['signals_generated'] = 5
        
        gateway.reset_stats()
        
        assert gateway.stats['signals_processed'] == 0
        assert gateway.stats['signals_generated'] == 0


if __name__ == '__main__':
    pytest.main([__file__])
