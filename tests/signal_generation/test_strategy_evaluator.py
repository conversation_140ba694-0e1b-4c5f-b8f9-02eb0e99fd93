#!/usr/bin/env python3
"""
Tests for Strategy Evaluator Component
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from agents.signal_generation.components.strategy_evaluator import (
    StrategyEvaluator, StrategyEvaluatorConfig
)
from agents.signal_generation.core.data_models import (
    SignalInput, MarketIndicators, OHLCVData, StrategyEvaluationResult
)


@pytest.fixture
def evaluator_config():
    """Create test evaluator configuration"""
    return StrategyEvaluatorConfig(
        enable_compilation=True,
        cache_compiled_expressions=True,
        expression_timeout_seconds=5.0,
        enable_safety_checks=True
    )


@pytest.fixture
def sample_signal_input():
    """Create sample signal input for testing"""
    ohlcv_data = []
    for i in range(50):  # 50 data points
        ohlcv_data.append(OHLCVData(
            timestamp=datetime.now(),
            open=100.0 + i * 0.1,
            high=101.0 + i * 0.1,
            low=99.0 + i * 0.1,
            close=100.5 + i * 0.1,
            volume=10000 + i * 100
        ))
    
    indicators = MarketIndicators(
        symbol="NIFTY",
        timestamp=datetime.now(),
        ema_5=105.2,
        ema_20=104.8,
        rsi_14=65.0,
        macd=0.15,
        macd_signal=0.12,
        bb_upper=107.0,
        bb_lower=103.0,
        bb_middle=105.0,
        atr=1.8
    )
    
    return SignalInput(
        symbol="NIFTY",
        timeframe="5min",
        timestamp=datetime.now(),
        ohlcv_data=ohlcv_data,
        indicators=indicators,
        market_regime="bull"
    )


@pytest.fixture
def sample_strategy():
    """Create sample strategy configuration"""
    return {
        'name': 'test_rsi_strategy',
        'enabled': True,
        'long': 'rsi_14 > 60 and ema_5 > ema_20',
        'short': 'rsi_14 < 40 and ema_5 < ema_20',
        'long_exit': 'rsi_14 > 80',
        'short_exit': 'rsi_14 < 20',
        'parameters': {
            'rsi_period': 14,
            'ema_fast': 5,
            'ema_slow': 20
        }
    }


class TestStrategyEvaluator:
    """Test cases for Strategy Evaluator"""
    
    @pytest.mark.asyncio
    async def test_evaluator_initialization(self, evaluator_config):
        """Test evaluator initialization"""
        evaluator = StrategyEvaluator(evaluator_config)
        
        result = await evaluator.initialize()
        
        assert result is True
        assert evaluator.is_initialized is True
    
    @pytest.mark.asyncio
    async def test_single_strategy_evaluation_long_signal(self, evaluator_config, sample_signal_input, sample_strategy):
        """Test single strategy evaluation with long signal"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        # Modify indicators to trigger long condition
        sample_signal_input.indicators.rsi_14 = 65.0  # > 60
        sample_signal_input.indicators.ema_5 = 105.0
        sample_signal_input.indicators.ema_20 = 104.0  # ema_5 > ema_20
        
        result = await evaluator.evaluate_strategy(sample_signal_input, sample_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.strategy_name == 'test_rsi_strategy'
        assert result.long_condition is True
        assert result.short_condition is False
        assert result.confidence_score > 0
        assert result.signal_strength > 0
    
    @pytest.mark.asyncio
    async def test_single_strategy_evaluation_short_signal(self, evaluator_config, sample_signal_input, sample_strategy):
        """Test single strategy evaluation with short signal"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        # Modify indicators to trigger short condition
        sample_signal_input.indicators.rsi_14 = 35.0  # < 40
        sample_signal_input.indicators.ema_5 = 104.0
        sample_signal_input.indicators.ema_20 = 105.0  # ema_5 < ema_20
        
        result = await evaluator.evaluate_strategy(sample_signal_input, sample_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.strategy_name == 'test_rsi_strategy'
        assert result.long_condition is False
        assert result.short_condition is True
        assert result.confidence_score > 0
        assert result.signal_strength > 0
    
    @pytest.mark.asyncio
    async def test_single_strategy_evaluation_no_signal(self, evaluator_config, sample_signal_input, sample_strategy):
        """Test single strategy evaluation with no signal"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        # Modify indicators to not trigger any condition
        sample_signal_input.indicators.rsi_14 = 50.0  # Between thresholds
        sample_signal_input.indicators.ema_5 = 105.0
        sample_signal_input.indicators.ema_20 = 105.0  # Equal EMAs
        
        result = await evaluator.evaluate_strategy(sample_signal_input, sample_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.strategy_name == 'test_rsi_strategy'
        assert result.long_condition is False
        assert result.short_condition is False
        assert result.confidence_score >= 0
    
    @pytest.mark.asyncio
    async def test_multiple_strategies_evaluation(self, evaluator_config, sample_signal_input):
        """Test multiple strategies evaluation"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        strategies = [
            {
                'name': 'rsi_strategy',
                'enabled': True,
                'long': 'rsi_14 > 60',
                'short': 'rsi_14 < 40'
            },
            {
                'name': 'ema_strategy',
                'enabled': True,
                'long': 'ema_5 > ema_20',
                'short': 'ema_5 < ema_20'
            }
        ]
        
        # Set indicators to trigger both strategies
        sample_signal_input.indicators.rsi_14 = 65.0
        sample_signal_input.indicators.ema_5 = 105.0
        sample_signal_input.indicators.ema_20 = 104.0
        
        results = await evaluator.evaluate_multiple_strategies(sample_signal_input, strategies)
        
        assert len(results) == 2
        assert all(isinstance(r, StrategyEvaluationResult) for r in results)
        assert results[0].strategy_name == 'rsi_strategy'
        assert results[1].strategy_name == 'ema_strategy'
    
    @pytest.mark.asyncio
    async def test_expression_compilation_and_caching(self, evaluator_config, sample_signal_input, sample_strategy):
        """Test expression compilation and caching"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        # First evaluation - should compile and cache
        result1 = await evaluator.evaluate_strategy(sample_signal_input, sample_strategy)
        
        # Check that expressions were cached
        cache_key = f"{sample_strategy['name']}_long"
        assert cache_key in evaluator.compiled_expressions
        
        # Second evaluation - should use cached expressions
        result2 = await evaluator.evaluate_strategy(sample_signal_input, sample_strategy)
        
        assert result1.strategy_name == result2.strategy_name
    
    @pytest.mark.asyncio
    async def test_invalid_expression_handling(self, evaluator_config, sample_signal_input):
        """Test handling of invalid expressions"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        invalid_strategy = {
            'name': 'invalid_strategy',
            'enabled': True,
            'long': 'invalid_function(rsi_14)',  # Invalid function
            'short': 'rsi_14 < 40'
        }
        
        result = await evaluator.evaluate_strategy(sample_signal_input, invalid_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.strategy_name == 'invalid_strategy'
        assert result.long_condition is False
        assert result.short_condition is False
        assert 'error' in result.condition_details
    
    @pytest.mark.asyncio
    async def test_safety_checks(self, evaluator_config, sample_signal_input):
        """Test safety checks for malicious expressions"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        malicious_strategy = {
            'name': 'malicious_strategy',
            'enabled': True,
            'long': '__import__("os").system("rm -rf /")',  # Malicious code
            'short': 'rsi_14 < 40'
        }
        
        result = await evaluator.evaluate_strategy(sample_signal_input, malicious_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.long_condition is False
        assert 'safety' in result.condition_details.get('long_error', '').lower()
    
    @pytest.mark.asyncio
    async def test_expression_timeout(self, sample_signal_input):
        """Test expression timeout handling"""
        config = StrategyEvaluatorConfig(expression_timeout_seconds=0.001)  # Very short timeout
        evaluator = StrategyEvaluator(config)
        await evaluator.initialize()
        
        # Strategy with potentially slow expression
        slow_strategy = {
            'name': 'slow_strategy',
            'enabled': True,
            'long': 'sum([i for i in range(1000000)]) > 0',  # Slow computation
            'short': 'rsi_14 < 40'
        }
        
        result = await evaluator.evaluate_strategy(sample_signal_input, slow_strategy)
        
        assert isinstance(result, StrategyEvaluationResult)
        assert result.long_condition is False
    
    def test_data_frame_creation(self, evaluator_config, sample_signal_input):
        """Test data frame creation from signal input"""
        evaluator = StrategyEvaluator(evaluator_config)
        
        df = evaluator._create_dataframe(sample_signal_input)
        
        assert df is not None
        assert len(df) == len(sample_signal_input.ohlcv_data)
        assert 'close' in df.columns
        assert 'rsi_14' in df.columns
        assert 'ema_5' in df.columns
        assert 'ema_20' in df.columns
    
    def test_confidence_calculation(self, evaluator_config):
        """Test confidence score calculation"""
        evaluator = StrategyEvaluator(evaluator_config)
        
        # Test with strong signal
        confidence = evaluator._calculate_confidence_score(
            long_condition=True,
            short_condition=False,
            signal_strength=0.9
        )
        assert 0.7 <= confidence <= 1.0
        
        # Test with weak signal
        confidence = evaluator._calculate_confidence_score(
            long_condition=True,
            short_condition=False,
            signal_strength=0.3
        )
        assert 0.0 <= confidence <= 0.7
        
        # Test with conflicting signals
        confidence = evaluator._calculate_confidence_score(
            long_condition=True,
            short_condition=True,
            signal_strength=0.8
        )
        assert confidence < 0.5
    
    def test_signal_strength_calculation(self, evaluator_config, sample_signal_input):
        """Test signal strength calculation"""
        evaluator = StrategyEvaluator(evaluator_config)
        
        df = evaluator._create_dataframe(sample_signal_input)
        
        # Test RSI-based signal strength
        strength = evaluator._calculate_signal_strength(df, 'rsi_14 > 60')
        assert 0.0 <= strength <= 1.0
        
        # Test volume-based signal strength
        strength = evaluator._calculate_signal_strength(df, 'volume > 10000')
        assert 0.0 <= strength <= 1.0
    
    @pytest.mark.asyncio
    async def test_evaluator_cleanup(self, evaluator_config):
        """Test evaluator cleanup"""
        evaluator = StrategyEvaluator(evaluator_config)
        await evaluator.initialize()
        
        # Add some cached expressions
        evaluator.compiled_expressions['test'] = Mock()
        
        await evaluator.cleanup()
        
        assert len(evaluator.compiled_expressions) == 0
        assert evaluator.is_initialized is False
    
    def test_performance_metrics(self, evaluator_config):
        """Test performance metrics collection"""
        evaluator = StrategyEvaluator(evaluator_config)
        
        # Simulate some evaluations
        evaluator.evaluation_stats['total_evaluations'] = 100
        evaluator.evaluation_stats['successful_evaluations'] = 95
        evaluator.evaluation_stats['failed_evaluations'] = 5
        
        metrics = evaluator.get_performance_metrics()
        
        assert 'evaluation_stats' in metrics
        assert metrics['evaluation_stats']['total_evaluations'] == 100
        assert metrics['evaluation_stats']['success_rate'] == 0.95


if __name__ == '__main__':
    pytest.main([__file__])
