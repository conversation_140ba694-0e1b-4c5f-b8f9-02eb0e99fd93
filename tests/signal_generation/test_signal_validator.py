#!/usr/bin/env python3
"""
Tests for Signal Validator Component
"""

import pytest
import asyncio
from datetime import datetime, time
from unittest.mock import Mock, AsyncMock, patch

from agents.signal_generation.components.signal_validator import (
    SignalValidator, SignalValidatorConfig
)
from agents.signal_generation.core.data_models import (
    TradingSignal, SignalType, SignalAction, SignalValidationResult, MarketDepthData
)


@pytest.fixture
def validator_config():
    """Create test validator configuration"""
    return SignalValidatorConfig(
        min_confidence=0.6,
        market_hours_only=True,
        market_open_time="09:20",
        market_close_time="15:00",
        avoid_first_minutes=10,
        avoid_last_minutes=30,
        enable_liquidity_checks=True,
        min_volume_ratio=1.5,
        minutes_between_signals=5,
        max_signals_per_symbol_per_day=3,
        min_risk_reward_ratio=1.5
    )


@pytest.fixture
def sample_trading_signal():
    """Create sample trading signal for testing"""
    return TradingSignal(
        signal_id="test_signal_001",
        symbol="NIFTY",
        strategy_name="test_strategy",
        signal_type=SignalType.LONG,
        action=SignalAction.BUY,
        entry_price=100.0,
        stop_loss=98.0,
        take_profit=104.0,
        quantity=10,
        risk_reward_ratio=2.0,
        confidence=0.75,
        market_regime="bull",
        timestamp=datetime.now().replace(hour=10, minute=30)  # Within market hours
    )


@pytest.fixture
def sample_market_depth():
    """Create sample market depth data"""
    return MarketDepthData(
        symbol="NIFTY",
        timestamp=datetime.now(),
        bid_price=99.95,
        ask_price=100.05,
        bid_size=1000,
        ask_size=1200,
        bid_levels=5,
        ask_levels=5,
        total_bid_volume=50000,
        total_ask_volume=55000
    )


class TestSignalValidator:
    """Test cases for Signal Validator"""
    
    @pytest.mark.asyncio
    async def test_validator_initialization(self, validator_config):
        """Test validator initialization"""
        validator = SignalValidator(validator_config)
        
        result = await validator.initialize()
        
        assert result is True
        assert validator.is_initialized is True
    
    @pytest.mark.asyncio
    async def test_valid_signal_validation(self, validator_config, sample_trading_signal, sample_market_depth):
        """Test validation of a valid signal"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        result = await validator.validate_signal(sample_trading_signal, sample_market_depth)
        
        assert isinstance(result, SignalValidationResult)
        assert result.is_valid is True
        assert result.rejection_reason is None
        assert result.validation_score > 0.5
        assert result.time_filter_result is True
        assert result.cooldown_result is True
    
    @pytest.mark.asyncio
    async def test_low_confidence_rejection(self, validator_config, sample_trading_signal):
        """Test rejection due to low confidence"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Set confidence below threshold
        sample_trading_signal.confidence = 0.4
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "confidence" in result.rejection_reason.lower()
        assert result.validation_score < 0.6
    
    @pytest.mark.asyncio
    async def test_time_filter_rejection(self, validator_config, sample_trading_signal):
        """Test rejection due to time filter"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Set timestamp outside market hours
        sample_trading_signal.timestamp = datetime.now().replace(hour=8, minute=30)
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "time" in result.rejection_reason.lower()
        assert result.time_filter_result is False
    
    @pytest.mark.asyncio
    async def test_avoid_first_minutes_rejection(self, validator_config, sample_trading_signal):
        """Test rejection during first minutes of trading"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Set timestamp within first 10 minutes (avoid_first_minutes=10)
        sample_trading_signal.timestamp = datetime.now().replace(hour=9, minute=25)
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert result.time_filter_result is False
    
    @pytest.mark.asyncio
    async def test_avoid_last_minutes_rejection(self, validator_config, sample_trading_signal):
        """Test rejection during last minutes of trading"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Set timestamp within last 30 minutes (avoid_last_minutes=30)
        sample_trading_signal.timestamp = datetime.now().replace(hour=14, minute=45)
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert result.time_filter_result is False
    
    @pytest.mark.asyncio
    async def test_risk_reward_ratio_rejection(self, validator_config, sample_trading_signal):
        """Test rejection due to poor risk-reward ratio"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Set poor risk-reward ratio
        sample_trading_signal.risk_reward_ratio = 1.0  # Below min_risk_reward_ratio=1.5
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "rr ratio" in result.rejection_reason.lower()
    
    @pytest.mark.asyncio
    async def test_cooldown_violation(self, validator_config, sample_trading_signal):
        """Test rejection due to cooldown violation"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # First signal should pass
        result1 = await validator.validate_signal(sample_trading_signal)
        assert result1.is_valid is True
        
        # Second signal immediately after should fail cooldown
        sample_trading_signal.signal_id = "test_signal_002"
        result2 = await validator.validate_signal(sample_trading_signal)
        
        assert result2.is_valid is False
        assert "cooldown" in result2.rejection_reason.lower()
        assert result2.cooldown_result is False
    
    @pytest.mark.asyncio
    async def test_daily_signal_limit(self, validator_config, sample_trading_signal):
        """Test daily signal limit enforcement"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Simulate reaching daily limit
        today = sample_trading_signal.timestamp.date()
        symbol_today_key = f"{sample_trading_signal.symbol}_{today}"
        validator.daily_signal_counts[symbol_today_key] = validator_config.max_signals_per_symbol_per_day
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "daily" in result.rejection_reason.lower()
        assert "limit" in result.rejection_reason.lower()
    
    @pytest.mark.asyncio
    async def test_liquidity_validation_pass(self, validator_config, sample_trading_signal, sample_market_depth):
        """Test liquidity validation passing"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Mock liquidity analyzer to return liquid
        validator.liquidity_analyzer.analyze_liquidity = AsyncMock(return_value={
            'is_liquid': True,
            'volume_check': True,
            'spread_check': True,
            'depth_check': True,
            'liquidity_score': 0.8
        })
        
        result = await validator.validate_signal(sample_trading_signal, sample_market_depth)
        
        assert result.is_valid is True
        assert 'liquidity' in result.validation_details
        assert result.liquidity_metrics['is_liquid'] is True
    
    @pytest.mark.asyncio
    async def test_liquidity_validation_fail(self, validator_config, sample_trading_signal, sample_market_depth):
        """Test liquidity validation failing"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Mock liquidity analyzer to return illiquid
        validator.liquidity_analyzer.analyze_liquidity = AsyncMock(return_value={
            'is_liquid': False,
            'volume_check': False,
            'spread_check': True,
            'depth_check': True,
            'liquidity_score': 0.3
        })
        
        result = await validator.validate_signal(sample_trading_signal, sample_market_depth)
        
        assert result.is_valid is False
        assert "liquidity" in result.rejection_reason.lower()
        assert result.liquidity_metrics['is_liquid'] is False
    
    @pytest.mark.asyncio
    async def test_market_regime_validation(self, sample_trading_signal):
        """Test market regime validation"""
        config = SignalValidatorConfig(
            enable_regime_filters=True,
            allowed_regimes=["bull", "sideways"]
        )
        validator = SignalValidator(config)
        await validator.initialize()
        
        # Test allowed regime
        sample_trading_signal.market_regime = "bull"
        result = await validator.validate_signal(sample_trading_signal)
        assert result.is_valid is True
        
        # Test disallowed regime
        sample_trading_signal.market_regime = "bear"
        result = await validator.validate_signal(sample_trading_signal)
        assert result.is_valid is False
        assert "regime" in result.rejection_reason.lower()
    
    @pytest.mark.asyncio
    async def test_signal_quality_validation(self, validator_config, sample_trading_signal):
        """Test signal quality validation"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Test with excessive stop loss
        sample_trading_signal.entry_price = 100.0
        sample_trading_signal.stop_loss = 95.0  # 5% stop loss (> max 3%)
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "stop loss" in result.rejection_reason.lower()
    
    @pytest.mark.asyncio
    async def test_risk_agent_validation(self, sample_trading_signal):
        """Test risk agent validation"""
        config = SignalValidatorConfig(use_risk_agent_validation=True)
        validator = SignalValidator(config)
        await validator.initialize()
        
        # Mock risk agent
        validator.risk_agent = Mock()
        
        # Test with high risk amount
        sample_trading_signal.risk_amount = 3000  # 3% of 100k capital
        
        result = await validator.validate_signal(sample_trading_signal)
        
        assert result.is_valid is False
        assert "risk" in result.rejection_reason.lower()
    
    def test_confidence_validation(self, validator_config):
        """Test confidence validation logic"""
        validator = SignalValidator(validator_config)
        
        # Test passing confidence
        result = validator._validate_confidence(Mock(confidence=0.8))
        assert result['passed'] is True
        
        # Test failing confidence
        result = validator._validate_confidence(Mock(confidence=0.4))
        assert result['passed'] is False
        assert result['reason'] is not None
    
    def test_time_filter_validation(self, validator_config):
        """Test time filter validation logic"""
        validator = SignalValidator(validator_config)
        
        # Test signal within market hours
        signal = Mock()
        signal.timestamp = datetime.now().replace(hour=11, minute=30)
        
        result = validator._validate_time_filters(signal)
        assert result['passed'] is True
        
        # Test signal outside market hours
        signal.timestamp = datetime.now().replace(hour=8, minute=30)
        
        result = validator._validate_time_filters(signal)
        assert result['passed'] is False
    
    def test_signal_tracking_update(self, validator_config, sample_trading_signal):
        """Test signal tracking update"""
        validator = SignalValidator(validator_config)
        
        # Update tracking
        validator._update_signal_tracking(sample_trading_signal)
        
        # Check that tracking was updated
        symbol_strategy_key = f"{sample_trading_signal.symbol}_{sample_trading_signal.strategy_name}"
        assert symbol_strategy_key in validator.signal_history
        
        today = sample_trading_signal.timestamp.date()
        symbol_today_key = f"{sample_trading_signal.symbol}_{today}"
        assert validator.daily_signal_counts[symbol_today_key] == 1
    
    def test_daily_tracking_reset(self, validator_config):
        """Test daily tracking reset"""
        validator = SignalValidator(validator_config)
        
        # Add some tracking data
        validator.daily_signal_counts['NIFTY_2024-01-01'] = 5
        validator.strategy_signal_counts['test_strategy_2024-01-01'] = 3
        
        validator.reset_daily_tracking()
        
        assert len(validator.daily_signal_counts) == 0
        assert len(validator.strategy_signal_counts) == 0
    
    def test_validation_stats(self, validator_config):
        """Test validation statistics"""
        validator = SignalValidator(validator_config)
        
        # Add some tracking data
        validator.signal_history['NIFTY_test'] = datetime.now()
        validator.daily_signal_counts['NIFTY_2024-01-01'] = 2
        
        stats = validator.get_validation_stats()
        
        assert 'total_signals_tracked' in stats
        assert 'daily_signal_counts' in stats
        assert 'config' in stats
        assert stats['total_signals_tracked'] == 1
    
    @pytest.mark.asyncio
    async def test_validator_cleanup(self, validator_config):
        """Test validator cleanup"""
        validator = SignalValidator(validator_config)
        await validator.initialize()
        
        # Add some tracking data
        validator.signal_history['test'] = datetime.now()
        validator.daily_signal_counts['test'] = 1
        
        await validator.cleanup()
        
        assert len(validator.signal_history) == 0
        assert len(validator.daily_signal_counts) == 0
        assert validator.is_initialized is False


if __name__ == '__main__':
    pytest.main([__file__])
