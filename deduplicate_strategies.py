import yaml
import os

def deduplicate_strategies(file_path):
    with open(file_path, 'r') as f:
        config = yaml.safe_load(f)

    if 'strategies' not in config:
        print("No 'strategies' section found in the YAML file.")
        return

    unique_strategies = {}
    for i, strategy in enumerate(config['strategies']):
        # Create a unique key for each strategy based on name, stock_name, and timeframe
        unique_key_parts = []
        
        strategy_name = strategy.get('name')
        if strategy_name:
            unique_key_parts.append(strategy_name)
        
        stock_name = strategy.get('stock_name')
        if stock_name:
            unique_key_parts.append(stock_name)
        
        timeframe_val = None
        if 'timeframe' in strategy:
            timeframe_val = strategy['timeframe']
            if isinstance(timeframe_val, list):
                unique_key_parts.append(tuple(timeframe_val))
            else:
                unique_key_parts.append(tuple([timeframe_val]))
        elif 'target_timeframe' in strategy: # For evolved strategies
            timeframe_val = strategy['target_timeframe']
            unique_key_parts.append(str(timeframe_val))
        
        unique_key = tuple(unique_key_parts)

        # Debugging print statements
        print(f"Strategy {i}: Name='{strategy_name}', Stock='{stock_name}', Timeframe='{timeframe_val}' -> Key: {unique_key}")

        # Handle cases where unique_key might be empty for some reason
        if not unique_key:
            print(f"Warning: Strategy with no identifiable key: {strategy.get('name', 'Unnamed')}. Skipping for deduplication.")
            continue

        current_ranking = strategy.get('ranking', 0)
        current_last_updated = strategy.get('last_updated', '1970-01-01 00:00:00') # Default to old date

        if unique_key not in unique_strategies:
            unique_strategies[unique_key] = strategy
        else:
            existing_strategy = unique_strategies[unique_key]
            existing_ranking = existing_strategy.get('ranking', 0)
            existing_last_updated = existing_strategy.get('last_updated', '1970-01-01 00:00:00')

            print(f"  Duplicate found for key {unique_key}. Existing ranking: {existing_ranking}, current ranking: {current_ranking}")
            print(f"  Existing last_updated: {existing_last_updated}, current last_updated: {current_last_updated}")

            # Keep the strategy with higher ranking
            if current_ranking > existing_ranking:
                unique_strategies[unique_key] = strategy
                print(f"  Keeping current strategy due to higher ranking: {strategy_name}")
            # If rankings are equal, keep the one with the more recent last_updated timestamp
            elif current_ranking == existing_ranking:
                if current_last_updated > existing_last_updated:
                    unique_strategies[unique_key] = strategy
                    print(f"  Keeping current strategy due to more recent last_updated: {strategy_name}")
                else:
                    print(f"  Keeping existing strategy (ranking and last_updated equal or current is older): {existing_strategy.get('name')}")
            else:
                print(f"  Keeping existing strategy due to higher ranking: {existing_strategy.get('name')}")
            
    config['strategies'] = list(unique_strategies.values())

    # Write the deduplicated content back to the file
    with open(file_path, 'w') as f:
        yaml.dump(config, f, sort_keys=False, default_flow_style=False, indent=2)

    print(f"Deduplication complete. Total strategies after deduplication: {len(unique_strategies)}")

if __name__ == "__main__":
    yaml_file = 'config/strategies.yaml'
    deduplicate_strategies(yaml_file)
