#!/usr/bin/env python3
"""
Basic Signal Generation Example
Demonstrates how to use the Signal Generation Gateway for basic signal generation
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from agents.signal_generation_gateway import SignalGenerationGateway, SignalGenerationGatewayConfig
from agents.signal_generation.core.data_models import (
    SignalInput, MarketIndicators, OHLCVData
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_ohlcv_data(symbol: str, num_points: int = 100) -> list:
    """Create sample OHLCV data for testing"""
    ohlcv_data = []
    base_price = 100.0
    
    for i in range(num_points):
        timestamp = datetime.now() - timedelta(minutes=num_points - i)
        
        # Simulate price movement
        price_change = (i % 10 - 5) * 0.1  # Simple oscillation
        open_price = base_price + price_change
        high_price = open_price + abs(price_change) * 0.5
        low_price = open_price - abs(price_change) * 0.3
        close_price = open_price + price_change * 0.2
        volume = 10000 + (i * 100)
        
        ohlcv_data.append(OHLCVData(
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        ))
    
    return ohlcv_data


def create_sample_indicators(symbol: str) -> MarketIndicators:
    """Create sample technical indicators"""
    return MarketIndicators(
        symbol=symbol,
        timestamp=datetime.now(),
        ema_5=100.5,
        ema_20=99.8,
        rsi_14=65.2,  # Above 60 to trigger RSI strategy
        macd=0.15,
        macd_signal=0.12,
        bb_upper=102.0,
        bb_lower=98.0,
        bb_middle=100.0,
        atr=1.5
    )


async def basic_signal_generation_example():
    """Basic signal generation example"""
    logger.info("Starting basic signal generation example...")
    
    # Create gateway configuration
    config = SignalGenerationGatewayConfig(
        enable_signal_processing=True,
        enable_multi_timeframe_fusion=False,  # Disable for basic example
        enable_ml_enhancement=False  # Disable for basic example
    )
    
    # Initialize gateway
    gateway = SignalGenerationGateway(config)
    
    try:
        # Initialize the gateway
        logger.info("Initializing Signal Generation Gateway...")
        if not await gateway.initialize():
            logger.error("Failed to initialize gateway")
            return
        
        # Create sample market data
        symbol = "NIFTY"
        ohlcv_data = create_sample_ohlcv_data(symbol)
        indicators = create_sample_indicators(symbol)
        
        # Create signal input
        signal_input = SignalInput(
            symbol=symbol,
            timeframe="5min",
            timestamp=datetime.now(),
            ohlcv_data=ohlcv_data,
            indicators=indicators,
            market_regime="bull"
        )
        
        logger.info(f"Generating signal for {symbol}...")
        
        # Generate signal
        signal = await gateway.generate_signal(signal_input)
        
        if signal:
            logger.info("✅ Signal generated successfully!")
            logger.info(f"Signal Details:")
            logger.info(f"  ID: {signal.signal_id}")
            logger.info(f"  Symbol: {signal.symbol}")
            logger.info(f"  Strategy: {signal.strategy_name}")
            logger.info(f"  Action: {signal.action}")
            logger.info(f"  Entry Price: {signal.entry_price:.2f}")
            logger.info(f"  Stop Loss: {signal.stop_loss:.2f}")
            logger.info(f"  Take Profit: {signal.take_profit:.2f}")
            logger.info(f"  Quantity: {signal.quantity}")
            logger.info(f"  Confidence: {signal.confidence:.2f}")
            logger.info(f"  Risk/Reward Ratio: {signal.risk_reward_ratio:.2f}")
            logger.info(f"  Capital Allocated: ${signal.capital_allocated:.2f}")
            logger.info(f"  Risk Amount: ${signal.risk_amount:.2f}")
        else:
            logger.info("❌ No signal generated")
        
        # Get gateway statistics
        stats = gateway.get_gateway_stats()
        logger.info(f"Gateway Statistics:")
        logger.info(f"  Signals Processed: {stats['gateway_stats']['signals_processed']}")
        logger.info(f"  Signals Generated: {stats['gateway_stats']['signals_generated']}")
        logger.info(f"  Signals Validated: {stats['gateway_stats']['signals_validated']}")
        logger.info(f"  Signals Rejected: {stats['gateway_stats']['signals_rejected']}")
        
    except Exception as e:
        logger.error(f"Error in signal generation: {e}")
        
    finally:
        # Cleanup
        logger.info("Cleaning up...")
        await gateway.cleanup()


async def multiple_symbols_example():
    """Example of generating signals for multiple symbols"""
    logger.info("Starting multiple symbols example...")
    
    config = SignalGenerationGatewayConfig()
    gateway = SignalGenerationGateway(config)
    
    try:
        await gateway.initialize()
        
        symbols = ["NIFTY", "BANKNIFTY", "RELIANCE", "TCS"]
        
        for symbol in symbols:
            logger.info(f"Processing {symbol}...")
            
            # Create market data for each symbol
            ohlcv_data = create_sample_ohlcv_data(symbol)
            indicators = create_sample_indicators(symbol)
            
            # Vary indicators slightly for each symbol
            if symbol == "BANKNIFTY":
                indicators.rsi_14 = 35.0  # Trigger short condition
                indicators.ema_5 = 99.0
                indicators.ema_20 = 100.0
            elif symbol == "RELIANCE":
                indicators.rsi_14 = 50.0  # Neutral
            
            signal_input = SignalInput(
                symbol=symbol,
                timeframe="5min",
                timestamp=datetime.now(),
                ohlcv_data=ohlcv_data,
                indicators=indicators,
                market_regime="bull"
            )
            
            signal = await gateway.generate_signal(signal_input)
            
            if signal:
                logger.info(f"  ✅ {symbol}: {signal.action} at {signal.entry_price:.2f} (Confidence: {signal.confidence:.2f})")
            else:
                logger.info(f"  ❌ {symbol}: No signal")
        
        # Final statistics
        stats = gateway.get_gateway_stats()
        logger.info(f"Final Statistics: {stats['gateway_stats']['signals_processed']} processed, {stats['gateway_stats']['signals_generated']} generated")
        
    finally:
        await gateway.cleanup()


async def main():
    """Main function to run examples"""
    logger.info("Signal Generation Examples")
    logger.info("=" * 50)
    
    # Run basic example
    await basic_signal_generation_example()
    
    logger.info("\n" + "=" * 50)
    
    # Run multiple symbols example
    await multiple_symbols_example()
    
    logger.info("\nExamples completed!")


if __name__ == "__main__":
    asyncio.run(main())
