#!/usr/bin/env python3
"""
Advanced Integration Example
Demonstrates integration with other agents and advanced features
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
import logging
import yaml

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from agents.signal_generation_gateway import SignalGenerationGateway, SignalGenerationGatewayConfig
from agents.signal_generation.integration.integration_manager import IntegrationManager, IntegrationManagerConfig
from agents.signal_generation.core.data_models import (
    SignalInput, MarketIndicators, OHLCVData, TradingSignal
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def integration_example():
    """Example showing integration with other agents"""
    logger.info("Starting advanced integration example...")
    
    # Create comprehensive configuration
    config = SignalGenerationGatewayConfig(
        enable_signal_processing=True,
        enable_multi_timeframe_fusion=True,
        enable_ml_enhancement=True
    )
    
    # Configure integration
    integration_config = IntegrationManagerConfig(
        enable_market_data_integration=True,
        enable_backtesting_integration=True,
        enable_evolution_integration=True,
        enable_live_trading_integration=True
    )
    
    gateway = SignalGenerationGateway(config)
    integration_manager = IntegrationManager(integration_config)
    
    try:
        # Initialize both systems
        logger.info("Initializing systems...")
        await gateway.initialize()
        await integration_manager.initialize()
        
        symbol = "RELIANCE"
        
        # 1. Get market data through integration
        logger.info(f"Getting market data for {symbol}...")
        signal_input = await integration_manager.get_market_data(symbol, "5min")
        
        if not signal_input:
            logger.warning("No market data available, creating sample data...")
            # Create sample data as fallback
            signal_input = create_sample_signal_input(symbol)
        
        # 2. Generate signal
        logger.info("Generating signal...")
        signal = await gateway.generate_signal(signal_input)
        
        if signal:
            logger.info(f"✅ Signal generated: {signal.action} {signal.symbol}")
            
            # 3. Get strategy performance from backtesting
            logger.info("Getting strategy performance...")
            performance = await integration_manager.get_strategy_performance(
                signal.strategy_name, symbol
            )
            
            if performance:
                logger.info(f"Strategy Performance:")
                logger.info(f"  Win Rate: {performance['win_rate']:.2%}")
                logger.info(f"  Total Return: {performance['total_return']:.2%}")
                logger.info(f"  Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
                logger.info(f"  Max Drawdown: {performance['max_drawdown']:.2%}")
            
            # 4. Submit strategy for evolution if performance is good
            if performance and performance['performance_score'] > 70:
                logger.info("Submitting strategy for evolution...")
                evolution_id = await integration_manager.submit_strategy_for_evolution(
                    {'name': signal.strategy_name, 'enabled': True},
                    performance
                )
                
                if evolution_id:
                    logger.info(f"Strategy submitted for evolution: {evolution_id}")
            
            # 5. Deliver signal to live trading
            logger.info("Delivering signal to live trading...")
            delivery_success = await integration_manager.deliver_signal_to_live_trading(signal)
            
            if delivery_success:
                logger.info("✅ Signal delivered to live trading")
                
                # 6. Get execution feedback
                await asyncio.sleep(1)  # Wait a moment
                feedback = await integration_manager.get_execution_feedback(signal.signal_id)
                
                if feedback:
                    logger.info(f"Execution Feedback:")
                    logger.info(f"  Status: {feedback['execution_status']}")
                    logger.info(f"  Executed Price: {feedback.get('executed_price', 'N/A')}")
                    logger.info(f"  Executed Quantity: {feedback.get('executed_quantity', 'N/A')}")
            else:
                logger.warning("Failed to deliver signal to live trading")
        
        else:
            logger.info("❌ No signal generated")
        
        # 7. Check for evolved strategies
        logger.info("Checking for evolved strategies...")
        evolved_strategies = await integration_manager.get_evolved_strategies()
        
        if evolved_strategies:
            logger.info(f"Found {len(evolved_strategies)} evolved strategies:")
            for evolved in evolved_strategies:
                logger.info(f"  - {evolved['evolved_config']['name']}")
        
        # 8. Display integration health
        health_status = await integration_manager.health_check()
        logger.info(f"Integration Health: {'✅ Healthy' if health_status['overall_health'] else '❌ Issues'}")
        
        # 9. Display comprehensive statistics
        gateway_stats = gateway.get_gateway_stats()
        integration_stats = integration_manager.get_integration_stats()
        
        logger.info("\n📊 System Statistics:")
        logger.info(f"Gateway - Processed: {gateway_stats['gateway_stats']['signals_processed']}, Generated: {gateway_stats['gateway_stats']['signals_generated']}")
        logger.info(f"Integration - Market Data Requests: {integration_stats['integration_stats']['market_data_requests']}")
        logger.info(f"Integration - Live Trading Deliveries: {integration_stats['integration_stats']['live_trading_deliveries']}")
        
    except Exception as e:
        logger.error(f"Error in integration example: {e}")
        
    finally:
        await gateway.cleanup()
        await integration_manager.cleanup()


def create_sample_signal_input(symbol: str) -> SignalInput:
    """Create sample signal input for testing"""
    ohlcv_data = []
    for i in range(50):
        ohlcv_data.append(OHLCVData(
            timestamp=datetime.now() - timedelta(minutes=50-i),
            open=100.0 + i * 0.1,
            high=101.0 + i * 0.1,
            low=99.0 + i * 0.1,
            close=100.5 + i * 0.1,
            volume=10000 + i * 100
        ))
    
    indicators = MarketIndicators(
        symbol=symbol,
        timestamp=datetime.now(),
        ema_5=105.0,
        ema_20=104.0,
        rsi_14=65.0,
        macd=0.15,
        macd_signal=0.12,
        bb_upper=107.0,
        bb_lower=103.0,
        bb_middle=105.0,
        atr=1.8
    )
    
    return SignalInput(
        symbol=symbol,
        timeframe="5min",
        timestamp=datetime.now(),
        ohlcv_data=ohlcv_data,
        indicators=indicators,
        market_regime="bull"
    )


async def strategy_management_example():
    """Example showing strategy management features"""
    logger.info("Starting strategy management example...")
    
    config = SignalGenerationGatewayConfig()
    gateway = SignalGenerationGateway(config)
    
    try:
        await gateway.initialize()
        
        # Get strategy rankings
        rankings = gateway.strategy_manager.get_strategy_rankings()
        logger.info(f"Current Strategy Rankings: {len(rankings)} strategies")
        
        # Get top strategies
        top_strategies = gateway.strategy_manager.get_top_strategies(5)
        logger.info("Top 5 Strategies:")
        for i, (strategy_name, ranking) in enumerate(top_strategies, 1):
            logger.info(f"  {i}. {strategy_name}: {ranking}")
        
        # Simulate strategy performance update
        await gateway.strategy_manager.update_strategy_performance(
            "rsi_oversold_reversal",
            {
                'total_trades': 150,
                'winning_trades': 95,
                'total_return': 18.5,
                'sharpe_ratio': 1.4,
                'max_drawdown': 8.2
            }
        )
        
        logger.info("Updated strategy performance")
        
        # Get manager statistics
        manager_stats = gateway.strategy_manager.get_manager_stats()
        logger.info(f"Strategy Manager Stats:")
        logger.info(f"  Total Strategies: {manager_stats['total_strategies']}")
        logger.info(f"  Active Strategies: {manager_stats['active_strategies']}")
        
    finally:
        await gateway.cleanup()


async def performance_monitoring_example():
    """Example showing performance monitoring"""
    logger.info("Starting performance monitoring example...")
    
    config = SignalGenerationGatewayConfig()
    gateway = SignalGenerationGateway(config)
    
    try:
        await gateway.initialize()
        
        # Generate multiple signals to collect performance data
        symbols = ["NIFTY", "BANKNIFTY", "RELIANCE", "TCS", "INFY"]
        
        for symbol in symbols:
            signal_input = create_sample_signal_input(symbol)
            signal = await gateway.generate_signal(signal_input)
            
            if signal:
                logger.info(f"Generated signal for {symbol}")
        
        # Get comprehensive statistics
        stats = gateway.get_gateway_stats()
        
        logger.info("\n📈 Performance Metrics:")
        logger.info(f"Signals Processed: {stats['gateway_stats']['signals_processed']}")
        logger.info(f"Signals Generated: {stats['gateway_stats']['signals_generated']}")
        logger.info(f"Success Rate: {stats['gateway_stats']['signals_generated'] / max(stats['gateway_stats']['signals_processed'], 1) * 100:.1f}%")
        
        # Component-specific metrics
        for component, component_stats in stats['component_stats'].items():
            if component_stats:
                logger.info(f"{component.title()}: {len(component_stats)} metrics")
        
    finally:
        await gateway.cleanup()


async def main():
    """Main function to run advanced examples"""
    logger.info("Advanced Signal Generation Integration Examples")
    logger.info("=" * 70)
    
    # Run integration example
    await integration_example()
    
    logger.info("\n" + "=" * 70)
    
    # Run strategy management example
    await strategy_management_example()
    
    logger.info("\n" + "=" * 70)
    
    # Run performance monitoring example
    await performance_monitoring_example()
    
    logger.info("\nAdvanced examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
