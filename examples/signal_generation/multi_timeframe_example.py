#!/usr/bin/env python3
"""
Multi-Timeframe Signal Generation Example
Demonstrates multi-timeframe analysis and consensus building
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from agents.signal_generation_gateway import SignalGenerationGateway, SignalGenerationGatewayConfig
from agents.signal_generation.core.data_models import (
    SignalInput, MarketIndicators, OHLCVData
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_timeframe_ohlcv_data(symbol: str, timeframe: str, num_points: int = 100) -> list:
    """Create OHLCV data for specific timeframe"""
    ohlcv_data = []
    base_price = 100.0
    
    # Adjust time intervals based on timeframe
    time_intervals = {
        "1min": 1,
        "5min": 5,
        "15min": 15,
        "1h": 60
    }
    
    interval_minutes = time_intervals.get(timeframe, 5)
    
    for i in range(num_points):
        timestamp = datetime.now() - timedelta(minutes=(num_points - i) * interval_minutes)
        
        # Create different price patterns for different timeframes
        if timeframe == "1min":
            # More volatile for 1min
            price_change = (i % 20 - 10) * 0.05
        elif timeframe == "5min":
            # Medium volatility for 5min
            price_change = (i % 15 - 7) * 0.08
        elif timeframe == "15min":
            # Less volatile for 15min
            price_change = (i % 10 - 5) * 0.12
        else:  # 1h
            # Trend-following for 1h
            price_change = i * 0.02
        
        open_price = base_price + price_change
        high_price = open_price + abs(price_change) * 0.6
        low_price = open_price - abs(price_change) * 0.4
        close_price = open_price + price_change * 0.3
        volume = 10000 + (i * 100)
        
        ohlcv_data.append(OHLCVData(
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        ))
    
    return ohlcv_data


def create_timeframe_indicators(symbol: str, timeframe: str) -> MarketIndicators:
    """Create indicators for specific timeframe"""
    # Base indicators
    base_indicators = {
        "ema_5": 100.0,
        "ema_20": 99.5,
        "rsi_14": 55.0,
        "macd": 0.1,
        "macd_signal": 0.08,
        "bb_upper": 102.0,
        "bb_lower": 98.0,
        "bb_middle": 100.0,
        "atr": 1.5
    }
    
    # Adjust indicators based on timeframe to create different signals
    if timeframe == "1min":
        # 1min shows bullish signal
        base_indicators["rsi_14"] = 65.0  # Above 60
        base_indicators["ema_5"] = 100.5
        base_indicators["ema_20"] = 99.8  # ema_5 > ema_20
        base_indicators["macd"] = 0.15
        base_indicators["macd_signal"] = 0.12
        
    elif timeframe == "5min":
        # 5min shows strong bullish signal
        base_indicators["rsi_14"] = 70.0
        base_indicators["ema_5"] = 101.0
        base_indicators["ema_20"] = 99.5
        base_indicators["macd"] = 0.20
        base_indicators["macd_signal"] = 0.15
        
    elif timeframe == "15min":
        # 15min shows moderate bullish signal
        base_indicators["rsi_14"] = 62.0
        base_indicators["ema_5"] = 100.3
        base_indicators["ema_20"] = 99.9
        base_indicators["macd"] = 0.12
        base_indicators["macd_signal"] = 0.10
        
    else:  # 1h
        # 1h shows neutral to slightly bullish
        base_indicators["rsi_14"] = 58.0
        base_indicators["ema_5"] = 100.1
        base_indicators["ema_20"] = 100.0
        base_indicators["macd"] = 0.08
        base_indicators["macd_signal"] = 0.07
    
    return MarketIndicators(
        symbol=symbol,
        timestamp=datetime.now(),
        **base_indicators
    )


async def multi_timeframe_example():
    """Multi-timeframe signal generation example"""
    logger.info("Starting multi-timeframe signal generation example...")
    
    # Create gateway configuration with multi-timeframe enabled
    config = SignalGenerationGatewayConfig(
        enable_signal_processing=True,
        enable_multi_timeframe_fusion=True,
        enable_ml_enhancement=False
    )
    
    # Configure multi-timeframe fusion
    config.multi_timeframe_config.primary_timeframe = "5min"
    config.multi_timeframe_config.secondary_timeframes = ["1min", "15min", "1h"]
    config.multi_timeframe_config.fusion_method = "weighted_average"
    config.multi_timeframe_config.weighting_scheme = "higher_bias"
    
    gateway = SignalGenerationGateway(config)
    
    try:
        # Initialize gateway
        logger.info("Initializing Signal Generation Gateway...")
        if not await gateway.initialize():
            logger.error("Failed to initialize gateway")
            return
        
        symbol = "NIFTY"
        timeframes = ["1min", "5min", "15min", "1h"]
        
        # Create signal inputs for each timeframe
        timeframe_inputs = {}
        
        logger.info(f"Creating signal inputs for {symbol} across timeframes...")
        
        for timeframe in timeframes:
            ohlcv_data = create_timeframe_ohlcv_data(symbol, timeframe)
            indicators = create_timeframe_indicators(symbol, timeframe)
            
            signal_input = SignalInput(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                ohlcv_data=ohlcv_data,
                indicators=indicators,
                market_regime="bull"
            )
            
            timeframe_inputs[timeframe] = signal_input
            
            logger.info(f"  {timeframe}: RSI={indicators.rsi_14:.1f}, EMA5={indicators.ema_5:.2f}, EMA20={indicators.ema_20:.2f}")
        
        # Generate individual signals for each timeframe
        logger.info("\nGenerating individual timeframe signals...")
        individual_signals = {}
        
        for timeframe, signal_input in timeframe_inputs.items():
            signal = await gateway.generate_signal(signal_input)
            individual_signals[timeframe] = signal
            
            if signal:
                logger.info(f"  {timeframe}: ✅ {signal.action} (Confidence: {signal.confidence:.2f})")
            else:
                logger.info(f"  {timeframe}: ❌ No signal")
        
        # Generate multi-timeframe consensus signal
        logger.info("\nGenerating multi-timeframe consensus signal...")
        consensus_signal = await gateway.generate_multi_timeframe_signal(timeframe_inputs)
        
        if consensus_signal:
            logger.info("✅ Multi-timeframe consensus signal generated!")
            logger.info(f"Consensus Signal Details:")
            logger.info(f"  Action: {consensus_signal.action}")
            logger.info(f"  Entry Price: {consensus_signal.entry_price:.2f}")
            logger.info(f"  Confidence: {consensus_signal.confidence:.2f}")
            logger.info(f"  Risk/Reward: {consensus_signal.risk_reward_ratio:.2f}")
            
            # Check if consensus signal has higher confidence than individual signals
            max_individual_confidence = max(
                (s.confidence for s in individual_signals.values() if s), 
                default=0
            )
            
            if consensus_signal.confidence > max_individual_confidence:
                logger.info(f"  🎯 Consensus boosted confidence from {max_individual_confidence:.2f} to {consensus_signal.confidence:.2f}")
            
        else:
            logger.info("❌ No consensus signal generated")
            logger.info("This could be due to:")
            logger.info("  - Insufficient timeframe agreement")
            logger.info("  - Conflicting signals across timeframes")
            logger.info("  - Low consensus strength")
        
        # Display fusion statistics
        if hasattr(gateway.multi_timeframe_fusion, 'get_fusion_stats'):
            fusion_stats = gateway.multi_timeframe_fusion.get_fusion_stats()
            logger.info(f"\nFusion Statistics:")
            logger.info(f"  Signals Fused: {fusion_stats.get('signals_fused', 0)}")
            logger.info(f"  Consensus Success Rate: {fusion_stats.get('consensus_success_rate', 0):.2f}")
        
    except Exception as e:
        logger.error(f"Error in multi-timeframe example: {e}")
        
    finally:
        await gateway.cleanup()


async def timeframe_disagreement_example():
    """Example showing what happens when timeframes disagree"""
    logger.info("Starting timeframe disagreement example...")
    
    config = SignalGenerationGatewayConfig(enable_multi_timeframe_fusion=True)
    gateway = SignalGenerationGateway(config)
    
    try:
        await gateway.initialize()
        
        symbol = "BANKNIFTY"
        
        # Create conflicting signals across timeframes
        timeframe_inputs = {}
        
        # 1min: Strong bullish
        indicators_1min = create_timeframe_indicators(symbol, "1min")
        indicators_1min.rsi_14 = 75.0  # Very bullish
        indicators_1min.ema_5 = 102.0
        indicators_1min.ema_20 = 99.0
        
        # 5min: Bearish
        indicators_5min = create_timeframe_indicators(symbol, "5min")
        indicators_5min.rsi_14 = 25.0  # Very bearish
        indicators_5min.ema_5 = 98.0
        indicators_5min.ema_20 = 101.0
        
        # 15min: Neutral
        indicators_15min = create_timeframe_indicators(symbol, "15min")
        indicators_15min.rsi_14 = 50.0  # Neutral
        indicators_15min.ema_5 = 100.0
        indicators_15min.ema_20 = 100.0
        
        timeframes_data = [
            ("1min", indicators_1min, "Bullish"),
            ("5min", indicators_5min, "Bearish"),
            ("15min", indicators_15min, "Neutral")
        ]
        
        logger.info("Creating conflicting timeframe signals:")
        
        for timeframe, indicators, bias in timeframes_data:
            ohlcv_data = create_timeframe_ohlcv_data(symbol, timeframe)
            
            signal_input = SignalInput(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                ohlcv_data=ohlcv_data,
                indicators=indicators,
                market_regime="sideways"
            )
            
            timeframe_inputs[timeframe] = signal_input
            logger.info(f"  {timeframe}: {bias} (RSI: {indicators.rsi_14:.1f})")
        
        # Try to generate consensus
        consensus_signal = await gateway.generate_multi_timeframe_signal(timeframe_inputs)
        
        if consensus_signal:
            logger.info(f"✅ Consensus achieved despite disagreement!")
            logger.info(f"  Action: {consensus_signal.action}")
            logger.info(f"  Confidence: {consensus_signal.confidence:.2f}")
        else:
            logger.info("❌ No consensus due to timeframe disagreement")
            logger.info("This is expected behavior when timeframes strongly disagree")
        
    finally:
        await gateway.cleanup()


async def main():
    """Main function to run multi-timeframe examples"""
    logger.info("Multi-Timeframe Signal Generation Examples")
    logger.info("=" * 60)
    
    # Run multi-timeframe consensus example
    await multi_timeframe_example()
    
    logger.info("\n" + "=" * 60)
    
    # Run disagreement example
    await timeframe_disagreement_example()
    
    logger.info("\nMulti-timeframe examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
