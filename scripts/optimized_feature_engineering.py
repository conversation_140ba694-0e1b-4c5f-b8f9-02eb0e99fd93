#!/usr/bin/env python3
"""
Optimized Feature Engineering for Historical Data
Uses polars and pyarrow for high-performance feature generation
Works with timestamp, OHLCV data from the Fixed Historical Data Downloader
"""

import os
import sys
import asyncio
import logging
import time
import polars as pl
import pyarrow as pa

from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')
import numpy as np # Added for Numba CUDA integration

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# GPU acceleration libraries (removed as GPU is not effectively utilized)
NUMBA_CUDA_AVAILABLE = False

# Technical analysis libraries
try:
    import polars_talib as plta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    print("Warning: polars-talib not available. Install with: pip install polars-talib")

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

try:
    import vectorbt as vbt
    VECTORBT_AVAILABLE = True
except ImportError:
    VECTORBT_AVAILABLE = False
    print("Warning: vectorbt not available. Install with: pip install vectorbt")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure Polars for CPU processing (GPU engine removed as it's not effectively utilized)
try:
    pl.Config.set_engine("cpu")
    logger.info("Polars CPU engine enabled successfully")
except Exception as e:
    logger.warning(f"Could not explicitly set Polars CPU engine: {e}. Default engine will be used.")


class OptimizedFeatureEngineering:
    """
    Optimized Feature Engineering using Polars with GPU acceleration
    Works with historical data files containing: timestamp, open, high, low, close, volume
    """

    def __init__(self, input_dir: str, output_dir: str, chunk_size: int = 100000):
        """Initialize the feature engineering processor"""
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size

        # GPU optimization settings removed as GPU is not effectively utilized
        self.use_gpu = False

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Check library availability
        if POLARS_TALIB_AVAILABLE:
            logger.info("Using polars-talib for technical indicators")
        else:
            logger.info("Using pure Polars implementations for technical indicators")
        
        logger.info("GPU acceleration explicitly disabled based on performance analysis.")

        # Optimize chunk size for CPU processing
        # For CPU processing, a moderate chunk size is generally good.
        # Keeping the default or slightly increasing it for Polars efficiency.
        self.chunk_size = max(self.chunk_size, 100000) # Ensure a reasonable minimum chunk size for Polars
        logger.info(f"CPU processing enabled - optimized chunk size to {self.chunk_size:,}")

        logger.info(f"Feature Engineering initialized - Chunk size: {self.chunk_size:,}")
        logger.info(f"GPU acceleration enabled: {self.use_gpu}")


    
    def _parse_timestamp_and_create_metadata(self, df: Any, filename: str) -> Any:
        """Parse timestamp and create date, time, stock_name, symboltoken columns"""
        try:
            stock_name = filename.replace('.parquet', '').split('_')[0]

            if isinstance(df, pl.DataFrame):
                # Handle timezone-aware timestamp parsing for Polars
                # Format: 2015-02-02T09:15:00+0530
                # Polars-only: keep IST local clock times; do NOT convert to UTC
                # Strip timezone offset like +0530 and parse as naive local datetime
                df = df.with_columns([
                    pl.col("timestamp")
                      .str.replace(r"\+\d{4}$", "")
                      .str.strptime(pl.Datetime, format="%Y-%m-%dT%H:%M:%S", strict=False)
                      .alias("datetime")
                ])

                # Create metadata columns
                df = df.with_columns([
                    pl.col("datetime").dt.date().cast(pl.String).alias("date"),
                    pl.col("datetime").dt.time().cast(pl.String).alias("time"),
                    pl.col("datetime").dt.hour().alias("hour"),
                    pl.lit(stock_name).alias("stock_name"),
                    pl.lit(0).alias("symboltoken")
                ])



            return df
        except Exception as e:
            logger.error(f"Error parsing timestamp and creating metadata: {e}")
            # Return original dataframe with basic metadata if parsing fails
            try:
                if isinstance(df, pl.DataFrame):
                    df = df.with_columns([
                        pl.lit(stock_name).alias("stock_name"),
                        pl.lit(0).alias("symboltoken")
                    ])

            except Exception:
                pass
            return df
    
    def _add_basic_features(self, df: Any) -> Any:
        """Add basic features like lag and returns"""
        try:
            df = df.with_columns([
                pl.col("close").shift(1).alias("close_lag_1"),
                (pl.col("close") / pl.col("close").shift(1)).log().alias("log_return"),
                (pl.when(pl.col("close") > pl.col("close").rolling_mean(window_size=20))
                 .then(1)
                 .otherwise(0)
                 .alias("regime"))
            ])
            return df
        except Exception as e:
            logger.error(f"Error adding basic features: {e}")
            return df

    def _add_moving_averages(self, df: Any) -> Any:
        """Add moving average indicators using polars-talib or pure polars"""
        try:
            if isinstance(df, pl.DataFrame):
                if POLARS_TALIB_AVAILABLE:
                    df = df.with_columns([
                        pl.col("close").ta.ema(5).alias("ema_5"),
                        pl.col("close").ta.ema(10).alias("ema_10"),
                        pl.col("close").ta.ema(13).alias("ema_13"),
                        pl.col("close").ta.ema(20).alias("ema_20"),
                        pl.col("close").ta.ema(21).alias("ema_21"),
                        pl.col("close").ta.ema(30).alias("ema_30"),
                        pl.col("close").ta.ema(50).alias("ema_50"),
                        pl.col("close").ta.ema(100).alias("ema_100"),
                        pl.col("close").ta.sma(20).alias("sma_20")
                    ])
                else:
                    df = df.with_columns([
                        pl.col("close").ewm_mean(span=5).alias("ema_5"),
                        pl.col("close").ewm_mean(span=10).alias("ema_10"),
                        pl.col("close").ewm_mean(span=13).alias("ema_13"),
                        pl.col("close").ewm_mean(span=20).alias("ema_20"),
                        pl.col("close").ewm_mean(span=21).alias("ema_21"),
                        pl.col("close").ewm_mean(span=30).alias("ema_30"),
                        pl.col("close").ewm_mean(span=50).alias("ema_50"),
                        pl.col("close").ewm_mean(span=100).alias("ema_100"),
                        pl.col("close").rolling_mean(window_size=20).alias("sma_20")
                    ])
            return df
        except Exception as e:
            logger.error(f"Error adding moving averages: {e}")
            return df
    
    def _add_momentum_indicators(self, df: Any) -> Any:
        """Add momentum indicators using polars-talib or pure polars"""
        try:
            if POLARS_TALIB_AVAILABLE:
                df = df.with_columns([
                    pl.col("close").ta.rsi(5).alias("rsi_5"),
                    pl.col("close").ta.rsi(14).alias("rsi_14"),
                    pl.col("close").ta.macd(12, 26, 9).struct.field("macd").alias("macd"),
                    pl.col("close").ta.macd(12, 26, 9).struct.field("macdsignal").alias("macd_signal"),
                    plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowk").alias("stoch_k"),
                    plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowd").alias("stoch_d"),
                    plta.cci(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=20).alias("cci"),
                    plta.adx(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("adx"),
                    plta.mfi(pl.col("high"), pl.col("low"), pl.col("close"), pl.col("volume"), timeperiod=14).alias("mfi"),
                    plta.atr(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("atr")
                ])
                df = df.with_columns([
                    pl.col("atr").rolling_mean(window_size=14).alias("atr_sma_14")
                ])
            else:
                logger.info("Using pure Polars for momentum indicators (polars-talib not available)")
                # Fallback to pure polars implementations for ADX
                high_low = pl.col("high") - pl.col("low")
                high_close = (pl.col("high") - pl.col("close").shift(1)).abs()
                low_close = (pl.col("low") - pl.col("close").shift(1)).abs()
                tr = pl.max_horizontal([high_low, high_close, low_close])
                atr = tr.ewm_mean(span=14)
                plus_dm = (pl.when(pl.col("high").diff() > pl.col("low").diff().abs())
                          .then(pl.col("high").diff().clip_min(0))
                          .otherwise(0))
                minus_dm = (pl.when(pl.col("low").diff().abs() > pl.col("high").diff())
                           .then(pl.col("low").diff().abs())
                           .otherwise(0))
                plus_di = 100 * plus_dm.ewm_mean(span=14) / atr
                minus_di = 100 * minus_dm.ewm_mean(span=14) / atr
                di_sum = plus_di + minus_di
                dx = 100 * (plus_di - minus_di).abs() / di_sum
                adx = dx.ewm_mean(span=14)
                atr_sma_14 = atr.rolling_mean(window_size=14)
                df = df.with_columns([atr.alias("atr"), atr_sma_14.alias("atr_sma_14"), adx.alias("adx")])

                # Other momentum indicators (RSI, MACD, Stoch, CCI, MFI) still use pure Polars fallback
                for period in [5, 14]:
                    delta = pl.col("close").diff()
                    gain = delta.clip_min(0)
                    loss = (-delta).clip_min(0)
                    alpha = 1.0 / period
                    avg_gain = gain.ewm_mean(alpha=alpha)
                    avg_loss = loss.ewm_mean(alpha=alpha)
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    df = df.with_columns([rsi.alias(f"rsi_{period}")])

                ema_12 = pl.col("close").ewm_mean(span=12)
                ema_26 = pl.col("close").ewm_mean(span=26)
                macd_line = ema_12 - ema_26
                macd_signal = macd_line.ewm_mean(span=9)
                df = df.with_columns([macd_line.alias("macd"), macd_signal.alias("macd_signal")])

                high_14 = pl.col("high").rolling_max(window_size=14)
                low_14 = pl.col("low").rolling_min(window_size=14)
                stoch_k = 100 * (pl.col("close") - low_14) / (high_14 - low_14)
                stoch_d = stoch_k.rolling_mean(window_size=3)
                df = df.with_columns([stoch_k.alias("stoch_k"), stoch_d.alias("stoch_d")])

                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                sma_tp = typical_price.rolling_mean(window_size=20)
                mean_dev = (typical_price - sma_tp).abs().rolling_mean(window_size=20)
                cci = (typical_price - sma_tp) / (0.015 * mean_dev)
                df = df.with_columns([cci.alias("cci")])

                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                money_flow = typical_price * pl.col("volume")
                pos_mf = (pl.when(typical_price > typical_price.shift(1))
                         .then(money_flow)
                         .otherwise(0))
                neg_mf = (pl.when(typical_price < typical_price.shift(1))
                         .then(money_flow)
                         .otherwise(0))
                pos_mf_sum = pos_mf.rolling_sum(window_size=14)
                neg_mf_sum = neg_mf.rolling_sum(window_size=14)
                mfi = 100 - (100 / (1 + pos_mf_sum / neg_mf_sum))
                df = df.with_columns([mfi.alias("mfi")])

            return df
        except Exception as e:
            logger.error(f"Error adding momentum indicators: {e}")
            return df
    
    def _add_volatility_indicators(self, df: Any) -> Any:
        """Add volatility indicators using polars-talib or pure polars"""
        try:
            if isinstance(df, pl.DataFrame):
                if POLARS_TALIB_AVAILABLE:
                    bb_result = plta.bbands(pl.col("close"), timeperiod=20, nbdevup=2, nbdevdn=2)
                    df = df.with_columns([
                        bb_result.struct.field("upperband").alias("bb_upper"),
                        bb_result.struct.field("middleband").alias("bb_middle"),
                        bb_result.struct.field("lowerband").alias("bb_lower")
                    ])
                else:
                    sma_20 = pl.col("close").rolling_mean(window_size=20)
                    std_20 = pl.col("close").rolling_std(window_size=20)
                    df = df.with_columns([
                        (sma_20 + 2 * std_20).alias("bb_upper"),
                        sma_20.alias("bb_middle"),
                        (sma_20 - 2 * std_20).alias("bb_lower")
                    ])

            return df
        except Exception as e:
            logger.error(f"Error adding volatility indicators: {e}")
            return df
    
    def _add_volume_indicators(self, df: Any) -> Any:
        """Add volume-based indicators"""
        try:
            if isinstance(df, pl.DataFrame):
                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                vwap = (typical_price * pl.col("volume")).rolling_sum(window_size=20) / pl.col("volume").rolling_sum(window_size=20)
                sma_20_volume = pl.col("volume").rolling_mean(window_size=20)
                df = df.with_columns([vwap.alias("vwap"), sma_20_volume.alias("sma_20_volume")])

            return df
        except Exception as e:
            logger.error(f"Error adding volume indicators: {e}")
            return df
    
    def _add_custom_indicators(self, df: Any) -> Any:
        """Add custom indicators matching the exact structure"""
        try:
            if isinstance(df, pl.DataFrame):
                # Ensure ATR is available for Supertrend calculation
                if "atr" not in df.columns:
                    logger.warning("ATR not found in DataFrame, Supertrend calculation might be inaccurate.")
                    # Fallback or re-calculate ATR if necessary
                    high_low = pl.col("high") - pl.col("low")
                    high_close = (pl.col("high") - pl.col("close").shift(1)).abs()
                    low_close = (pl.col("low") - pl.col("close").shift(1)).abs()
                    tr = pl.max_horizontal([high_low, high_close, low_close])
                    df = df.with_columns(tr.ewm_mean(span=14).alias("atr"))

                # Supertrend calculation (Numba CUDA removed)
                logger.info("Using pure Polars for Supertrend calculation (GPU acceleration removed)")
                hl2 = (pl.col("high") + pl.col("low")) / 2
                atr = pl.col("atr") if "atr" in df.columns else pl.lit(1.0)
                multiplier = 3.0
                upper_band = hl2 + (multiplier * atr)
                lower_band = hl2 - (multiplier * atr)
                supertrend = (pl.when(pl.col("close") > upper_band.shift(1))
                             .then(lower_band)
                             .when(pl.col("close") < lower_band.shift(1))
                             .then(upper_band)
                             .otherwise(pl.col("close")))
                df = df.with_columns([supertrend.alias("supertrend")])

                donchian_high = pl.col("high").rolling_max(window_size=20)
                donchian_low = pl.col("low").rolling_min(window_size=20)
                df = df.with_columns([donchian_high.alias("donchian_high"), donchian_low.alias("donchian_low")])

                pivot = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                cpr_top = (pl.col("high") + pl.col("low")) / 2
                cpr_bottom = (pivot - (pl.col("high") - pl.col("low")))
                support = pivot - (pl.col("high") - pl.col("low"))
                resistance = pivot + (pl.col("high") - pl.col("low"))
                df = df.with_columns([
                    pivot.alias("pivot"), cpr_top.alias("cpr_top"), cpr_bottom.alias("cpr_bottom"),
                    support.alias("support"), resistance.alias("resistance")
                ])

                trendline = pl.col("close").rolling_mean(window_size=20).rolling_mean(window_size=5)
                df = df.with_columns([trendline.alias("trendline")])

                volume_avg = pl.col("volume").rolling_mean(window_size=20)
                price_range = pl.col("high") - pl.col("low")
                price_range_avg = price_range.rolling_mean(window_size=20)
                vcp_pattern = (pl.when(
                    (pl.col("volume") < volume_avg * 0.8) &
                    (price_range < price_range_avg * 0.8)
                ).then(1).otherwise(0))
                df = df.with_columns([vcp_pattern.alias("vcp_pattern")])

                upward_candle = pl.when(pl.col("close") > pl.col("open")).then(1).otherwise(0)
                downward_candle = pl.when(pl.col("close") < pl.col("open")).then(1).otherwise(0)
                df = df.with_columns([upward_candle.alias("upward_candle"), downward_candle.alias("downward_candle")])

            return df
        except Exception as e:
            logger.error(f"Error adding custom indicators: {e}")
            return df

    def _add_vectorbt_features(self, df: Any) -> Any:
        """Add features using vectorbt for vectorized operations"""
        try:
            if VECTORBT_AVAILABLE and isinstance(df, pl.DataFrame):
                logger.info("Using vectorbt for additional vectorized features")
                # Convert Polars DataFrame to Pandas DataFrame for vectorbt
                # Only select necessary columns to avoid memory issues
                pdf = df.select(["open", "high", "low", "close", "volume", "datetime"]).to_pandas()
                pdf = pdf.set_index("datetime")

                # Example: Add a simple moving average using vectorbt
                # vbt.MA.run returns a vectorbt.indicators.basic.MA object, which has a .ma property
                # We need to ensure the output is a Series or DataFrame that can be converted back to Polars
                
                # Calculate SMA using vectorbt
                sma_vbt = vbt.MA.run(pdf["close"], window=10).ma.rename("vbt_sma_10")
                
                # Calculate RSI using vectorbt
                rsi_vbt = vbt.RSI.run(pdf["close"], window=14).rsi.rename("vbt_rsi_14")

                # Convert back to Polars Series and add to original DataFrame
                df = df.with_columns([
                    pl.Series("vbt_sma_10", sma_vbt.values).alias("vbt_sma_10"),
                    pl.Series("vbt_rsi_14", rsi_vbt.values).alias("vbt_rsi_14")
                ])
            return df
        except Exception as e:
            logger.error(f"Error adding vectorbt features: {e}")
            return df

    def _process_single_file(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Process a single file's data with all technical indicators, potentially using GPU"""
        try:
            # Force Polars-only path per requirement
            processed_df = df

            # Parse timestamp and create metadata columns
            processed_df = self._parse_timestamp_and_create_metadata(processed_df, filename)
            
            # Sort by datetime for proper technical indicator calculation
            # cuDF sort_values is equivalent to Polars sort
            processed_df = processed_df.sort("datetime")

            # Add all features
            processed_df = self._add_basic_features(processed_df)
            processed_df = self._add_moving_averages(processed_df)
            processed_df = self._add_momentum_indicators(processed_df)
            processed_df = self._add_volatility_indicators(processed_df)
            processed_df = self._add_volume_indicators(processed_df)
            processed_df = self._add_custom_indicators(processed_df)
            processed_df = self._add_vectorbt_features(processed_df) # Add vectorbt features

            return processed_df

        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            # Return original dataframe with null columns for missing features
            return self._add_missing_columns(df, filename)

    def _add_missing_columns(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Add missing feature columns with null values"""
        try:
            # First add metadata if missing
            if "stock_name" not in df.columns:
                df = self._parse_timestamp_and_create_metadata(df, filename)
            
            expected_columns = [
                'date', 'time', 'hour', 'stock_name', 'symboltoken', 'open', 'high', 'low', 'close', 'volume', 'datetime',
                'close_lag_1', 'log_return', 'regime', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_30',
                'ema_50', 'ema_100', 'sma_20', 'rsi_5', 'rsi_14', 'macd', 'macd_signal', 'stoch_k', 'stoch_d',
                'cci', 'adx', 'mfi', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'vwap', 'sma_20_volume', 'atr_sma_14', 'supertrend', 'donchian_high',
                'donchian_low', 'pivot', 'cpr_top', 'cpr_bottom', 'support', 'resistance', 'trendline',
                'vcp_pattern', 'upward_candle', 'downward_candle'
            ]

            # Add missing columns with null values
            for col in expected_columns:
                if col not in df.columns:
                    df = df.with_columns([pl.lit(None).alias(col)])

            return df
        except Exception as e:
            logger.error(f"Error adding missing columns: {e}")
            return df
    
    def process_file_with_delay(self, input_file: Path, worker_id: int, delay: float = 0.02) -> bool:
        """Process a single file with worker delay"""
        # Add delay between workers for resource management
        time.sleep(worker_id * delay)
        return self.process_file(input_file)
    
    def process_file(self, input_file: Path) -> bool:
        """Process a single file"""
        try:
            logger.info(f"Processing file: {input_file.name}")

            # Read file
            df = pl.read_parquet(input_file)
            logger.info(f"Loaded {len(df):,} rows from {input_file.name}")

            # Check if file has required columns
            required_cols = ["timestamp", "open", "high", "low", "close", "volume"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return False

            # Process the file
            logger.info("Processing features...")
            processed_df = self._process_single_file(df, input_file.name)

            # Ensure all expected columns are present
            expected_columns = [
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'datetime', 'date', 'time', 'hour', 'stock_name', 'symboltoken',
                'rsi_14', 'ema_10', 'macd', 'macd_signal', 'ema_20', 'ema_5', 'ema_13', 'ema_21', 'bb_lower', 'bb_upper', 'rsi_5',
                'vwap', 'donchian_high', 'donchian_low', 'supertrend', 'cpr_bottom', 'cpr_top', 'adx', 'vcp_pattern',
                'upward_candle', 'downward_candle', 'vbt_sma_10', 'vbt_rsi_14'
            ]

            # Add missing columns with null values
            for col in expected_columns:
                if col not in processed_df.columns:
                    processed_df = processed_df.with_columns([pl.lit(None).alias(col)])

            # Select only expected columns in correct order, dropping any extra columns
            final_df = processed_df.select(expected_columns)

            # Save output with features_ prefix
            output_file = self.output_dir / f"features_{input_file.name}"
            final_df.write_parquet(output_file, compression="zstd", compression_level=15)

            logger.info(f"[SUCCESS] Saved {len(final_df):,} rows with {len(final_df.columns)} features to {output_file}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error processing file {input_file}: {e}")
            return False
    
    def process_all_files(self) -> bool:
        """Process all parquet files using ThreadPoolExecutor with 6 workers"""
        try:
            # Find all parquet files that don't start with "features_"
            parquet_files = [f for f in self.input_dir.glob("*.parquet") if not f.name.startswith("features_")]

            if not parquet_files:
                logger.error(f"No historical parquet files found in {self.input_dir}")
                return False

            logger.info(f"Found {len(parquet_files)} files to process")

            # Optimize worker count for CPU processing
            # For CPU processing, use more workers, typically based on CPU core count.
            # Using 20 workers as a good balance for general CPU workloads.
            max_workers = 20
            delay = 0.02
            logger.info("Using CPU-optimized ThreadPoolExecutor with 20 workers and 0.02s delay")

            # Process files using ThreadPoolExecutor
            success_count = 0
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit tasks with worker delays
                futures = []
                for i, file_path in enumerate(parquet_files):
                    future = executor.submit(self.process_file_with_delay, file_path, i, delay)
                    futures.append(future)
                
                # Collect results
                for future in futures:
                    try:
                        success = future.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logger.error(f"Worker failed: {e}")

            logger.info(f"[SUCCESS] Successfully processed {success_count}/{len(parquet_files)} files")
            return success_count > 0

        except Exception as e:
            logger.error(f"[ERROR] Error processing files: {e}")
            return False

def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Optimized Feature Engineering using Polars',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical --chunk-size 200000
        """
    )
    parser.add_argument('--input-dir', type=str, default='data/historical',
                       help='Input directory containing parquet files (default: data/historical)')
    parser.add_argument('--output-dir', type=str, default='data/features',
                       help='Output directory for feature files (default: data/historical)')
    parser.add_argument('--chunk-size', type=int, default=500000,
                       help='Chunk size for processing large files (default: 500000)')

    args = parser.parse_args()

    logger.info("[INIT] Starting Optimized Feature Engineering")
    logger.info(f"[FOLDER] Input directory: {args.input_dir}")
    logger.info(f"[FOLDER] Output directory: {args.output_dir}")
    logger.info(f"[STATUS] Chunk size: {args.chunk_size:,}")
    logger.info(f"[CONFIG] Polars-TAlib available: {POLARS_TALIB_AVAILABLE}")

    # Create processor and run
    processor = OptimizedFeatureEngineering(args.input_dir, args.output_dir, args.chunk_size)
    success = processor.process_all_files()

    if success:
        logger.info("[SUCCESS] Feature engineering completed successfully!")
        logger.info("[METRICS] Features generated with comprehensive technical indicators")
    else:
        logger.error("[ERROR] Feature engineering failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
