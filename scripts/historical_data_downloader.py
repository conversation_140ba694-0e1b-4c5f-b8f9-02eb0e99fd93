#!/usr/bin/env python3
"""
Simple Historical Data Downloader for SmartAPI
Downloads 5-minute historical data with manual date input
"""

import os
import sys
import asyncio
import logging
import json
import polars as pl
import yaml # Import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# SmartAPI integration
try:
    from SmartApi import SmartConnect
except ImportError:
    logger.error("ERROR: SmartAPI not installed. Install with: pip install smartapi-python")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/historical_downloader.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SimpleHistoricalDownloader:
    """Simple Historical Data Downloader"""
    
    def __init__(self):
        """Initialize the downloader"""
        self.smart_api = None
        self.symbols = []
        self.data_dir = "data/historical"
        self.intervals = ["ONE_MINUTE", "THREE_MINUTE", "FIVE_MINUTE", "FIFTEEN_MINUTE"] # Define supported intervals
        self.strategy_config_path = Path("config/strategies.yaml")
        
        # Create data directory
        Path(self.data_dir).mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
        
        logger.info("Simple Historical Data Downloader initialized")
    
    async def initialize(self) -> bool:
        """Initialize API connection"""
        try:
            # Get credentials from environment
            api_key = os.getenv('SMARTAPI_API_KEY')
            username = os.getenv('SMARTAPI_USERNAME')
            password = os.getenv('SMARTAPI_PASSWORD')
            totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
            
            if not all([api_key, username, password, totp_token]):
                logger.error("Missing SmartAPI credentials in environment variables. Please set SMARTAPI_API_KEY, SMARTAPI_USERNAME, SMARTAPI_PASSWORD, SMARTAPI_TOTP_TOKEN. No fallback.")
                raise ValueError("Missing SmartAPI credentials")
            
            # Initialize SmartAPI
            self.smart_api = SmartConnect(api_key=api_key)
            
            logger.info("Logging into SmartAPI...")
            
            # Generate TOTP
            import pyotp
            totp = pyotp.TOTP(totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.smart_api.generateSession(username, password, totp_code)
            
            if data['status']:
                logger.info("SmartAPI login successful")
                return True
            else:
                error_message = data.get('message', 'Unknown error')
                logger.error(f"SmartAPI login failed: {error_message}. No fallback.")
                raise Exception(f"SmartAPI login failed: {error_message}")
                
        except Exception as e:
            logger.error(f"Initialization failed: {e}. No fallback.")
            raise
    
    def _get_required_stock_names(self) -> Set[str]:
        """Load required stock names from strategies.yaml"""
        if not self.strategy_config_path.exists():
            logger.error(f"Strategies config file not found: {self.strategy_config_path}. Cannot determine required stocks. No fallback.")
            raise FileNotFoundError(f"Strategies config file not found: {self.strategy_config_path}")

        with open(self.strategy_config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        stock_names = set()
        for strategy in config.get('strategies', []):
            stock_name = strategy.get('stock_name')
            if stock_name and stock_name != "":
                stock_names.add(stock_name)
        
        if not stock_names:
            logger.warning("No specific stock_name found in strategies.yaml. Will attempt to download all available NSE stocks.")
        
        return stock_names

    def load_symbols(self):
        """Load symbols from SmartAPI instrument list, filtered by strategies.yaml"""
        try:
            required_stock_names = self._get_required_stock_names()
            
            logger.info("Fetching all instruments from SmartAPI...")
            all_instruments_response = self.smart_api.get_all_instruments()
            
            if not all_instruments_response or not all_instruments_response.get('data'):
                logger.error("Failed to fetch instruments from SmartAPI or no data returned. No fallback.")
                raise Exception("Failed to fetch instruments from SmartAPI")
            
            all_instruments = all_instruments_response['data']
            logger.info(f"Fetched {len(all_instruments)} instruments from SmartAPI.")
            
            self.symbols = []
            for instrument in all_instruments:
                if instrument.get('exchange') == 'NSE' and instrument.get('instrumenttype') == 'EQ': # Only Equity from NSE
                    symbol = instrument.get('tradingsymbol')
                    token = instrument.get('instrumenttoken')
                    
                    if symbol and token:
                        if not required_stock_names or symbol in required_stock_names:
                            self.symbols.append({
                                'symbol': symbol,
                                'token': str(token),
                                'exchange': 'NSE'
                            })
            
            if not self.symbols:
                logger.error("No relevant symbols found after filtering by strategies.yaml and NSE Equity. No fallback.")
                raise ValueError("No relevant symbols found for download.")

            logger.info(f"Loaded {len(self.symbols)} symbols for download (filtered by strategies.yaml and NSE Equity).")

        except Exception as e:
            logger.error(f"Error loading symbols from SmartAPI: {e}. No fallback.")
            raise
    
    async def download_symbol_data(self, symbol: str, token: str, exchange: str,
                                 from_date: datetime, to_date: datetime, interval: str) -> Optional[pl.DataFrame]:
        """Download data for a single symbol"""
        try:
            # Prepare request with correct SmartAPI format
            hist_params = {
                "exchange": exchange,
                "symboltoken": token,
                "interval": interval,  # Use the provided interval
                "fromdate": from_date.strftime("%Y-%m-%d %H:%M"),
                "todate": to_date.strftime("%Y-%m-%d %H:%M")
            }

            logger.debug(f"Requesting {symbol} with params: {hist_params}")
            
            # Call API with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.smart_api.getCandleData(hist_params)

                    if response.get('status'):
                        break  # Success, exit retry loop
                    else:
                        error_msg = response.get('message', 'Unknown error')
                        if attempt < max_retries - 1:
                            logger.warning(f"Attempt {attempt + 1} failed for {symbol} ({interval}): {error_msg}. Retrying...")
                            await asyncio.sleep(1)  # Wait before retry
                        else:
                            logger.error(f"Failed to download {symbol} ({interval}) after {max_retries} attempts: {error_msg}. No fallback.")
                            return None
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Attempt {attempt + 1} failed for {symbol} ({interval}): {e}. Retrying...")
                        await asyncio.sleep(1)
                    else:
                        logger.error(f"Failed to download {symbol} ({interval}) after {max_retries} attempts: {e}. No fallback.")
                        return None
            
            data = response.get('data', [])
            if not data:
                logger.warning(f"No data for {symbol} ({interval}) from SmartAPI for {from_date} to {to_date}. No fallback.")
                return None
            
            # Convert to DataFrame with separate date and time columns
            timestamps = [datetime.fromisoformat(row[0]) for row in data]
            df = pl.DataFrame({
                'datetime': timestamps, # Store as datetime for easier aggregation
                'open': [float(row[1]) for row in data],
                'high': [float(row[2]) for row in data],
                'low': [float(row[3]) for row in data],
                'close': [float(row[4]) for row in data],
                'volume': [int(row[5]) for row in data],
                'symbol': [symbol] * len(data)
            })
            
            logger.info(f"Downloaded {len(df)} records for {symbol} ({interval})")
            return df
            
        except Exception as e:
            logger.error(f"Error downloading {symbol} ({interval}): {e}. No fallback.")
            return None
    
    async def download_all_symbols(self, from_date_str: str = None, to_date_str: str = None, 
                                days_back: int = 30) -> bool:
        """Download data for all symbols with retry on failures"""
        try:
            await self.initialize() # This will now raise an exception on failure

            self.load_symbols() # This will now raise an exception on failure

            # Determine the overall to_date (current date)
            overall_to_date = datetime.now().replace(second=0, microsecond=0)
            if to_date_str:
                overall_to_date = datetime.strptime(to_date_str, "%d-%m-%Y").replace(hour=15, minute=30)

            logger.info(f"Starting data download up to {overall_to_date} for {len(self.symbols)} symbols")

            # Process each symbol individually
            for i, symbol_info in enumerate(self.symbols):
                symbol = symbol_info['symbol']
                token = symbol_info['token']
                exchange = symbol_info['exchange']
                
                logger.info(f"Processing {i + 1}/{len(self.symbols)}: {symbol}")

                # Determine from_date for this symbol
                if from_date_str:
                    symbol_from_date = datetime.strptime(from_date_str, "%d-%m-%Y").replace(hour=9, minute=15)
                else:
                    symbol_from_date = overall_to_date - timedelta(days=days_back)
                    symbol_from_date = symbol_from_date.replace(hour=9, minute=15, second=0, microsecond=0)
                
                if symbol_from_date >= overall_to_date:
                    logger.info(f"Skipping download for {symbol}: from_date {symbol_from_date} is not before to_date {overall_to_date}")
                    continue

                # --- Download 1-minute data ---
                logger.info(f"Downloading 1min data for {symbol} from {symbol_from_date} to {overall_to_date}")
                df_1min = await self.download_symbol_data(symbol, token, exchange, symbol_from_date, overall_to_date, "ONE_MINUTE")
                
                if df_1min is not None and len(df_1min) > 0:
                    output_1min_file = Path(self.data_dir) / f"{symbol}_1min.parquet"
                    df_1min.write_parquet(output_1min_file, compression="zstd", compression_level=15)
                    logger.info(f"Saved {len(df_1min)} records to {output_1min_file}")

                    # --- Aggregate and save other timeframes from the combined 1-min data ---
                    for interval_str in ["3min", "5min", "15min"]:
                        logger.info(f"Aggregating and saving {interval_str} data for {symbol}...")
                        aggregated_df = self._aggregate_data(df_1min, interval_str)
                        output_agg_file = Path(self.data_dir) / f"{symbol}_{interval_str}.parquet"
                        aggregated_df.write_parquet(output_agg_file, compression="zstd", compression_level=15)
                        logger.info(f"Saved {len(aggregated_df)} records to {output_agg_file}")
                else:
                    logger.warning(f"No 1-minute data downloaded for {symbol}. Skipping aggregation for other intervals. No fallback.")
                
                await asyncio.sleep(0.75) # Small delay between symbols

            logger.info(f"Historical data download and aggregation complete for all symbols.")
            return True

        except Exception as e:
            logger.error(f"Download failed: {e}. No fallback.")
            raise

    def _aggregate_data(self, df: pl.DataFrame, interval_str: str) -> pl.DataFrame:
        """
        Aggregates 1-minute data to the specified interval (e.g., "3min", "5min", "15min").
        """
        interval_map = {
            "3min": "3m",
            "5min": "5m",
            "15min": "15m"
        }
        polars_interval = interval_map.get(interval_str)
        if not polars_interval:
            raise ValueError(f"Unsupported aggregation interval: {interval_str}")

        # Ensure datetime column is sorted for proper resampling
        df = df.sort(['symbol', 'datetime'])

        # Group by symbol and resample
        aggregated_df = df.group_by_dynamic(
            index_column="datetime",
            every=polars_interval,
            by="symbol",
            offset="0s", # Align to the start of the interval
            label="left" # Label with the start of the interval
        ).agg([
            pl.col("open").first().alias("open"),
            pl.col("high").max().alias("high"),
            pl.col("low").min().alias("low"),
            pl.col("close").last().alias("close"),
            pl.col("volume").sum().alias("volume"),
        ]).sort(['symbol', 'datetime'])

        return aggregated_df.with_columns([
            pl.col('datetime').dt.strftime('%d-%m-%Y').alias('date'),
            pl.col('datetime').dt.strftime('%H:%M:%S').alias('time')
        ])


async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Download historical data from SmartAPI')
    parser.add_argument('--from-date', type=str, help='Start date in dd-mm-yyyy format (for initial download if no existing data)')
    parser.add_argument('--to-date', type=str, help='End date in dd-mm-yyyy format (defaults to current date)')
    parser.add_argument('--days', type=int, default=30, help='Days back if no existing data and --from-date not provided (default: 30 days)')
    
    args = parser.parse_args()
    
    if (args.from_date and not args.to_date) or (args.to_date and not args.from_date and args.from_date is not None):
        logger.error("If --from-date is provided, --to-date must also be provided.")
        sys.exit(1)
    
    downloader = SimpleHistoricalDownloader()
    try:
        success = await downloader.download_all_symbols(
            from_date_str=args.from_date,
            to_date_str=args.to_date,
            days_back=args.days
        )
        if not success:
            sys.exit(1)
    except Exception as e:
        logger.error(f"Critical error during data download: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
