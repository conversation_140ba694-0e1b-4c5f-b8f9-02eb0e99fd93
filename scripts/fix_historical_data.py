#!/usr/bin/env python3
"""
Fix Historical Data Corruption

This script fixes the historical data by removing zero/null values and regenerating
clean resampled timeframes without synthetic data points.
"""

import polars as pl
import numpy as np
from pathlib import Path
import logging
from typing import List, Tuple, Optional
import sys

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HistoricalDataFixer:
    def __init__(self):
        self.historical_dir = Path("data/historical")
        self.backup_dir = Path("data/historical_backup")
        
    def identify_corrupted_files(self) -> List[Tuple[str, int]]:
        """Identify historical files with zero/negative prices"""
        corrupted_files = []
        
        logger.info("🔍 Scanning historical files for corruption...")
        
        historical_files = list(self.historical_dir.glob("*.parquet"))
        
        for file_path in historical_files:
            try:
                df = pl.read_parquet(file_path)
                
                # Check for zero/negative prices in OHLC columns
                price_cols = ['open', 'high', 'low', 'close']
                total_zero_count = 0
                
                for col in price_cols:
                    if col in df.columns:
                        zero_count = (df[col] <= 0).sum()
                        total_zero_count += zero_count
                
                if total_zero_count > 0:
                    corrupted_files.append((file_path.name, total_zero_count))
                    logger.warning(f"⚠️ CORRUPTED: {file_path.name} has {total_zero_count} zero/negative prices")
                    
            except Exception as e:
                logger.error(f"❌ Error checking {file_path.name}: {e}")
                
        logger.info(f"🔍 Found {len(corrupted_files)} corrupted historical files")
        return corrupted_files
    
    def backup_corrupted_files(self, corrupted_files: List[Tuple[str, int]]):
        """Backup corrupted files before fixing"""
        if not corrupted_files:
            return
            
        logger.info("💾 Backing up corrupted files...")
        self.backup_dir.mkdir(exist_ok=True)
        
        for filename, _ in corrupted_files:
            source = self.historical_dir / filename
            backup = self.backup_dir / filename
            
            try:
                # Copy file to backup
                df = pl.read_parquet(source)
                df.write_parquet(backup, compression="zstd")
                logger.info(f"💾 Backed up {filename}")
            except Exception as e:
                logger.error(f"❌ Failed to backup {filename}: {e}")
    
    def clean_data(self, df: pl.DataFrame) -> pl.DataFrame:
        """Clean data by removing zero/null values and invalid entries"""
        original_count = len(df)
        
        # Remove rows with zero or negative prices
        df_clean = df.filter(
            (pl.col("open") > 0) & 
            (pl.col("high") > 0) & 
            (pl.col("low") > 0) & 
            (pl.col("close") > 0) &
            (pl.col("volume") >= 0)  # Volume can be zero but not negative
        )
        
        # Remove rows with null values
        df_clean = df_clean.drop_nulls()
        
        # Validate OHLC relationships
        df_clean = df_clean.filter(
            (pl.col("high") >= pl.col("low")) &
            (pl.col("high") >= pl.col("open")) &
            (pl.col("high") >= pl.col("close")) &
            (pl.col("low") <= pl.col("open")) &
            (pl.col("low") <= pl.col("close"))
        )
        
        removed_count = original_count - len(df_clean)
        if removed_count > 0:
            logger.info(f"   Removed {removed_count} invalid rows ({removed_count/original_count*100:.1f}%)")
        
        return df_clean
    
    def resample_timeframe(self, df: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Resample data to different timeframes without creating synthetic data"""
        
        # Convert timestamp to datetime for resampling
        df = df.with_columns([
            pl.col("timestamp").str.strptime(pl.Datetime, format="%Y-%m-%dT%H:%M:%S%z").alias("datetime")
        ])
        
        # Define timeframe mappings
        timeframe_map = {
            "3min": "3m",
            "5min": "5m", 
            "15min": "15m",
            "30min": "30m",
            "1h": "1h",
            "1d": "1d"
        }
        
        if timeframe not in timeframe_map:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        polars_interval = timeframe_map[timeframe]
        
        # Sort by datetime
        df = df.sort("datetime")
        
        # Resample using group_by_dynamic - only aggregate existing data
        resampled = df.group_by_dynamic(
            "datetime",
            every=polars_interval,
            closed="left",
            label="left"
        ).agg([
            pl.col("open").first().alias("open"),
            pl.col("high").max().alias("high"), 
            pl.col("low").min().alias("low"),
            pl.col("close").last().alias("close"),
            pl.col("volume").sum().alias("volume")
        ]).sort("datetime")
        
        # Filter out any groups that might have null values (empty periods)
        resampled = resampled.filter(
            pl.col("open").is_not_null() &
            pl.col("high").is_not_null() &
            pl.col("low").is_not_null() &
            pl.col("close").is_not_null()
        )
        
        # Convert datetime back to timestamp string
        resampled = resampled.with_columns([
            pl.col("datetime").dt.strftime("%Y-%m-%dT%H:%M:%S+05:30").alias("timestamp")
        ]).drop("datetime")
        
        # Reorder columns
        resampled = resampled.select(["timestamp", "open", "high", "low", "close", "volume"])
        
        return resampled
    
    def fix_single_file(self, filename: str, zero_count: int) -> bool:
        """Fix a single corrupted historical file"""
        try:
            file_path = self.historical_dir / filename
            logger.info(f"🔧 Fixing {filename} (had {zero_count} zero prices)...")
            
            # Load the corrupted data
            df = pl.read_parquet(file_path)
            
            # Extract symbol and timeframe from filename
            parts = filename.replace(".parquet", "").split("_")
            if len(parts) < 2:
                logger.error(f"❌ Cannot parse filename: {filename}")
                return False
                
            symbol = "_".join(parts[:-1])
            timeframe = parts[-1]
            
            if timeframe == "1min":
                # For 1min data, just clean it
                df_clean = self.clean_data(df)
                
                if len(df_clean) == 0:
                    logger.error(f"❌ No valid data remaining after cleaning {filename}")
                    return False
                
                # Save cleaned 1min data
                df_clean.write_parquet(file_path, compression="zstd")
                logger.info(f"✅ Cleaned {filename}: {len(df)} → {len(df_clean)} rows")
                
            else:
                # For other timeframes, regenerate from clean 1min data
                base_1min_file = self.historical_dir / f"{symbol}_1min.parquet"
                
                if not base_1min_file.exists():
                    logger.error(f"❌ Base 1min file not found: {base_1min_file}")
                    return False
                
                # Load and clean 1min data
                df_1min = pl.read_parquet(base_1min_file)
                df_1min_clean = self.clean_data(df_1min)
                
                if len(df_1min_clean) == 0:
                    logger.error(f"❌ No valid 1min data for resampling {filename}")
                    return False
                
                # Resample to target timeframe
                df_resampled = self.resample_timeframe(df_1min_clean, timeframe)
                
                if len(df_resampled) == 0:
                    logger.error(f"❌ No data after resampling {filename}")
                    return False
                
                # Save resampled data
                df_resampled.write_parquet(file_path, compression="zstd")
                logger.info(f"✅ Regenerated {filename}: {len(df_resampled)} rows from {len(df_1min_clean)} 1min rows")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error fixing {filename}: {e}")
            return False
    
    def fix_corrupted_files(self, corrupted_files: List[Tuple[str, int]]):
        """Fix all corrupted historical files"""
        if not corrupted_files:
            logger.info("✅ No corrupted files to fix")
            return
            
        logger.info(f"🔧 Fixing {len(corrupted_files)} corrupted historical files...")
        
        # Sort files to process 1min files first (needed for resampling)
        corrupted_files.sort(key=lambda x: (0 if "1min" in x[0] else 1, x[0]))
        
        fixed_count = 0
        failed_count = 0
        
        for filename, zero_count in corrupted_files:
            success = self.fix_single_file(filename, zero_count)
            if success:
                fixed_count += 1
            else:
                failed_count += 1
        
        logger.info(f"🔧 Fix complete: {fixed_count} fixed, {failed_count} failed")
    
    def run_fix(self):
        """Main fix process"""
        logger.info("🚀 Starting historical data corruption fix...")
        
        # Step 1: Identify corrupted files
        corrupted_files = self.identify_corrupted_files()
        
        if not corrupted_files:
            logger.info("✅ No corrupted files found!")
            return
        
        # Step 2: Backup corrupted files
        self.backup_corrupted_files(corrupted_files)
        
        # Step 3: Fix corrupted files
        self.fix_corrupted_files(corrupted_files)
        
        logger.info("🎉 Historical data corruption fix complete!")

def main():
    """Main entry point"""
    fixer = HistoricalDataFixer()
    fixer.run_fix()

if __name__ == "__main__":
    main()
