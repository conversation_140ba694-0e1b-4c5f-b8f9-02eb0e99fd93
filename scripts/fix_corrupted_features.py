#!/usr/bin/env python3
"""
Fix Corrupted Feature Files

This script identifies and fixes corrupted feature files that contain zero/negative prices.
It regenerates clean feature files from the original raw data.
"""

import polars as pl
import numpy as np
from pathlib import Path
import logging
from typing import List, Tuple
import sys
import os

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from scripts.optimized_feature_engineering import OptimizedFeatureEngineering

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CorruptedFeatureFixer:
    def __init__(self):
        self.raw_data_dir = Path("data/live")
        self.feature_data_dir = Path("data/features")
        self.backup_dir = Path("data/features_backup")
        self.feature_engineer = OptimizedFeatureEngineering()
        
    def identify_corrupted_files(self) -> List[Tuple[str, int]]:
        """Identify feature files with zero/negative prices"""
        corrupted_files = []
        
        logger.info("🔍 Scanning feature files for corruption...")
        
        feature_files = list(self.feature_data_dir.glob("features_*.parquet"))
        
        for file_path in feature_files:
            try:
                df = pl.read_parquet(file_path)
                
                # Check for zero/negative prices in OHLC columns
                price_cols = ['open', 'high', 'low', 'close']
                total_zero_count = 0
                
                for col in price_cols:
                    if col in df.columns:
                        zero_count = (df[col] <= 0).sum()
                        total_zero_count += zero_count
                
                if total_zero_count > 0:
                    corrupted_files.append((file_path.name, total_zero_count))
                    logger.warning(f"⚠️ CORRUPTED: {file_path.name} has {total_zero_count} zero/negative prices")
                    
            except Exception as e:
                logger.error(f"❌ Error checking {file_path.name}: {e}")
                
        logger.info(f"🔍 Found {len(corrupted_files)} corrupted feature files")
        return corrupted_files
    
    def backup_corrupted_files(self, corrupted_files: List[Tuple[str, int]]):
        """Backup corrupted files before fixing"""
        if not corrupted_files:
            return
            
        logger.info("💾 Backing up corrupted files...")
        self.backup_dir.mkdir(exist_ok=True)
        
        for filename, _ in corrupted_files:
            source = self.feature_data_dir / filename
            backup = self.backup_dir / filename
            
            try:
                # Copy file to backup
                df = pl.read_parquet(source)
                df.write_parquet(backup, compression="zstd")
                logger.info(f"💾 Backed up {filename}")
            except Exception as e:
                logger.error(f"❌ Failed to backup {filename}: {e}")
    
    def regenerate_clean_features(self, corrupted_files: List[Tuple[str, int]]):
        """Regenerate clean feature files from raw data"""
        if not corrupted_files:
            logger.info("✅ No corrupted files to fix")
            return
            
        logger.info(f"🔧 Regenerating {len(corrupted_files)} corrupted feature files...")
        
        fixed_count = 0
        failed_count = 0
        
        for filename, zero_count in corrupted_files:
            try:
                # Extract symbol and timeframe from filename
                # Format: features_SYMBOL_TIMEFRAME.parquet
                parts = filename.replace("features_", "").replace(".parquet", "").split("_")
                if len(parts) < 2:
                    logger.error(f"❌ Cannot parse filename: {filename}")
                    failed_count += 1
                    continue
                    
                symbol = "_".join(parts[:-1])  # Handle symbols with underscores
                timeframe = parts[-1]
                
                # Find corresponding raw data file
                raw_file = self.raw_data_dir / f"{symbol}_{timeframe}.parquet"
                
                if not raw_file.exists():
                    logger.error(f"❌ Raw data not found: {raw_file}")
                    failed_count += 1
                    continue
                
                logger.info(f"🔧 Fixing {filename} (had {zero_count} zero prices)...")
                
                # Process the raw file to generate clean features
                success = self.feature_engineer.process_file(raw_file)
                
                if success:
                    # Verify the fix worked
                    feature_file = self.feature_data_dir / filename
                    if feature_file.exists():
                        df = pl.read_parquet(feature_file)
                        
                        # Check if zero prices are gone
                        price_cols = ['open', 'high', 'low', 'close']
                        total_zero_count = 0
                        
                        for col in price_cols:
                            if col in df.columns:
                                zero_count_new = (df[col] <= 0).sum()
                                total_zero_count += zero_count_new
                        
                        if total_zero_count == 0:
                            logger.info(f"✅ Successfully fixed {filename}")
                            fixed_count += 1
                        else:
                            logger.error(f"❌ Fix failed for {filename} - still has {total_zero_count} zero prices")
                            failed_count += 1
                    else:
                        logger.error(f"❌ Feature file not generated: {filename}")
                        failed_count += 1
                else:
                    logger.error(f"❌ Failed to process {filename}")
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"❌ Error fixing {filename}: {e}")
                failed_count += 1
        
        logger.info(f"🔧 Fix complete: {fixed_count} fixed, {failed_count} failed")
    
    def run_fix(self):
        """Main fix process"""
        logger.info("🚀 Starting corrupted feature file fix...")
        
        # Step 1: Identify corrupted files
        corrupted_files = self.identify_corrupted_files()
        
        if not corrupted_files:
            logger.info("✅ No corrupted files found!")
            return
        
        # Step 2: Backup corrupted files
        self.backup_corrupted_files(corrupted_files)
        
        # Step 3: Regenerate clean features
        self.regenerate_clean_features(corrupted_files)
        
        logger.info("🎉 Corrupted feature file fix complete!")

def main():
    """Main entry point"""
    fixer = CorruptedFeatureFixer()
    fixer.run_fix()

if __name__ == "__main__":
    main()
