# Signal Generation API Reference

## SignalGenerationGateway

The main entry point for the signal generation system.

### Class: `SignalGenerationGateway`

```python
class SignalGenerationGateway:
    def __init__(self, config: SignalGenerationGatewayConfig, event_bus=None)
```

#### Methods

##### `async initialize() -> bool`
Initialize all components of the signal generation system.

**Returns:** `bool` - True if initialization successful

##### `async generate_signal(signal_input: SignalInput) -> Optional[TradingSignal]`
Generate a trading signal from market data.

**Parameters:**
- `signal_input`: Market data and indicators for signal generation

**Returns:** `TradingSignal` or `None` if no signal generated

##### `async generate_multi_timeframe_signal(timeframe_inputs: Dict[str, SignalInput]) -> Optional[TradingSignal]`
Generate signal using multi-timeframe analysis.

**Parameters:**
- `timeframe_inputs`: Dictionary mapping timeframes to signal inputs

**Returns:** Consensus signal or `None`

##### `get_gateway_stats() -> Dict[str, Any]`
Get comprehensive gateway statistics.

**Returns:** Dictionary containing performance metrics and component stats

## Core Components

### StrategyEvaluator

Evaluates trading strategies against market data.

```python
class StrategyEvaluator(BaseSignalComponent):
    async def evaluate_strategy(self, signal_input: SignalInput, strategy: Dict[str, Any]) -> StrategyEvaluationResult
    async def evaluate_multiple_strategies(self, signal_input: SignalInput, strategies: List[Dict[str, Any]]) -> List[StrategyEvaluationResult]
```

### PositionSizer

Calculates optimal position sizes using various methods.

```python
class PositionSizer(BaseSignalComponent):
    async def calculate_position_size(self, signal_input: SignalInput, strategy: Dict[str, Any], entry_price: float, stop_loss: float) -> PositionSizingResult
```

**Methods:**
- `kelly`: Kelly Criterion-based sizing
- `fixed_fraction`: Fixed percentage of capital
- `volatility_scaled`: ATR-based volatility scaling
- `risk_parity`: Equal risk allocation

### SignalValidator

Validates signals against multiple criteria.

```python
class SignalValidator(BaseSignalComponent):
    async def validate_signal(self, signal: TradingSignal, market_depth: Optional[MarketDepthData] = None) -> SignalValidationResult
```

**Validation Checks:**
- Confidence threshold
- Time filters (market hours, cooldowns)
- Liquidity analysis
- Risk management
- Daily signal limits

### SignalProcessor

Processes and enhances raw signals.

```python
class SignalProcessor(BaseSignalComponent):
    async def process_signal(self, signal: TradingSignal) -> SignalProcessingResult
```

**Processing Methods:**
- Kalman filtering
- Outlier detection
- Signal smoothing
- Quality assessment

### MultiTimeframeFusion

Combines signals from multiple timeframes.

```python
class MultiTimeframeFusion(BaseSignalComponent):
    async def fuse_timeframe_signals(self, timeframe_signals: Dict[str, TradingSignal]) -> MultiTimeframeSignalResult
```

**Fusion Methods:**
- `weighted_average`: Weighted by timeframe hierarchy
- `majority_vote`: Democratic consensus
- `confidence_weighted`: Weighted by signal confidence

### MLIntegrator

Integrates machine learning models for signal enhancement.

```python
class MLIntegrator(BaseSignalComponent):
    async def enhance_signal(self, signal: TradingSignal, signal_input: SignalInput) -> TradingSignal
```

### StrategyManager

Manages strategy loading, ranking, and A/B testing.

```python
class StrategyManager(BaseSignalComponent):
    async def get_active_strategies(self, symbol: str = None) -> Dict[str, Dict[str, Any]]
    async def update_strategy_performance(self, strategy_name: str, performance_data: Dict[str, Any])
    def get_strategy_rankings() -> Dict[str, int]
```

## Data Models

### SignalInput

Input data for signal generation.

```python
@dataclass
class SignalInput:
    symbol: str
    timeframe: str
    timestamp: datetime
    ohlcv_data: List[OHLCVData]
    indicators: MarketIndicators
    market_regime: str
```

### TradingSignal

Generated trading signal.

```python
@dataclass
class TradingSignal:
    signal_id: str
    symbol: str
    strategy_name: str
    signal_type: SignalType
    action: SignalAction
    entry_price: float
    stop_loss: float
    take_profit: float
    quantity: int
    risk_reward_ratio: float
    confidence: float
    timestamp: datetime
    # ... additional fields
```

### StrategyEvaluationResult

Result of strategy evaluation.

```python
@dataclass
class StrategyEvaluationResult:
    strategy_name: str
    long_condition: bool
    short_condition: bool
    confidence_score: float
    signal_strength: float
    evaluation_time_ms: float
    condition_details: Dict[str, Any]
```

### SignalValidationResult

Result of signal validation.

```python
@dataclass
class SignalValidationResult:
    is_valid: bool
    rejection_reason: Optional[str]
    validation_score: float
    time_filter_result: bool
    cooldown_result: bool
    liquidity_metrics: Dict[str, Any]
    validation_details: Dict[str, Any]
```

## Integration Adapters

### MarketDataAdapter

Interfaces with market monitoring agent.

```python
class MarketDataAdapter:
    async def get_signal_input(self, symbol: str, timeframe: str = "5min") -> Optional[SignalInput]
    def validate_data(self, signal_input: SignalInput) -> bool
```

### BacktestingAdapter

Interfaces with backtesting agent.

```python
class BacktestingAdapter:
    async def get_strategy_performance(self, strategy_name: str, symbol: str = "ALL", timeframe: str = "5min") -> Optional[StrategyPerformanceMetrics]
    async def validate_strategy_with_backtesting(self, strategy_config: Dict[str, Any]) -> Dict[str, Any]
```

### EvolutionAdapter

Interfaces with evolution agent.

```python
class EvolutionAdapter:
    async def submit_strategy_for_evolution(self, strategy_config: Dict[str, Any], performance_metrics: Dict[str, Any]) -> Optional[str]
    async def get_evolved_strategies() -> List[Dict[str, Any]]
```

### LiveTradingAdapter

Interfaces with live trading agent.

```python
class LiveTradingAdapter:
    async def deliver_signal(self, signal: TradingSignal) -> bool
    async def get_execution_feedback(self, signal_id: str) -> Optional[Dict[str, Any]]
```

## Configuration Classes

### SignalGenerationGatewayConfig

Main gateway configuration.

```python
@dataclass
class SignalGenerationGatewayConfig:
    enable_async_processing: bool = True
    max_concurrent_signals: int = 10
    signal_timeout_seconds: float = 30.0
    enable_signal_processing: bool = True
    enable_multi_timeframe_fusion: bool = True
    enable_ml_enhancement: bool = True
```

### Component Configurations

Each component has its own configuration class:

- `StrategyEvaluatorConfig`
- `PositionSizerConfig`
- `SignalValidatorConfig`
- `SignalProcessorConfig`
- `MultiTimeframeFusionConfig`
- `MLIntegratorConfig`
- `StrategyManagerConfig`

## Event Types

The system publishes various events:

```python
class SignalEventTypes(Enum):
    SIGNAL_GENERATED = "signal_generated"
    SIGNAL_VALIDATED = "signal_validated"
    SIGNAL_REJECTED = "signal_rejected"
    STRATEGY_EVALUATION_COMPLETED = "strategy_evaluation_completed"
    POSITION_SIZE_CALCULATED = "position_size_calculated"
    SIGNAL_PROCESSED = "signal_processed"
    TIMEFRAME_FUSION_COMPLETED = "timeframe_fusion_completed"
    STRATEGY_LOADED = "strategy_loaded"
    STRATEGY_PERFORMANCE_UPDATED = "strategy_performance_updated"
    STRATEGY_RANKING_UPDATED = "strategy_ranking_updated"
```

## Error Handling

All components implement comprehensive error handling:

- Graceful degradation on component failures
- Timeout handling for long-running operations
- Validation of input data
- Logging of errors and warnings
- Fallback mechanisms where appropriate

## Performance Considerations

- Expression compilation and caching in StrategyEvaluator
- Async processing for I/O operations
- Configurable timeouts
- Memory-efficient data structures
- Optional GPU acceleration with polars[gpu]
