# Signal Generation System

A comprehensive, modular signal generation system for algorithmic trading with advanced features including multi-timeframe analysis, ML integration, and seamless agent communication.

## Overview

The Signal Generation System is designed to provide high-quality trading signals through a modular architecture that supports:

- **Strategy Evaluation**: Dynamic evaluation of trading strategies with safety checks
- **Position Sizing**: Advanced position sizing with Kelly Criterion and risk management
- **Signal Validation**: Comprehensive validation including liquidity, time filters, and risk checks
- **Signal Processing**: Noise reduction, outlier detection, and signal enhancement
- **Multi-Timeframe Fusion**: Consensus building across multiple timeframes
- **ML Integration**: Seamless integration with machine learning models
- **Strategy Management**: Dynamic strategy loading with performance-based ranking

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Signal Generation Gateway                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Strategy        │  │ Position        │  │ Signal          │ │
│  │ Evaluator       │  │ Sizer           │  │ Validator       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Signal          │  │ Multi-Timeframe │  │ ML              │ │
│  │ Processor       │  │ Fusion          │  │ Integrator      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐                                           │
│  │ Strategy        │                                           │
│  │ Manager         │                                           │
│  └─────────────────┘                                           │
├─────────────────────────────────────────────────────────────────┤
│                     Integration Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Market Data     │  │ Backtesting     │  │ Evolution       │ │
│  │ Adapter         │  │ Adapter         │  │ Adapter         │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                     │
│  │ Live Trading    │  │ Integration     │                     │
│  │ Adapter         │  │ Manager         │                     │
│  └─────────────────┘  └─────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

## Key Features

### 🎯 Strategy Evaluation
- Dynamic strategy compilation and execution
- Safety checks for malicious code
- Expression caching for performance
- Confidence scoring and signal strength calculation

### 💰 Position Sizing
- Kelly Criterion implementation
- Volatility-based scaling
- Risk parity methods
- Portfolio-aware sizing

### ✅ Signal Validation
- Time-based filtering (market hours, cooldowns)
- Liquidity analysis
- Risk management checks
- Daily signal limits

### 🔧 Signal Processing
- Kalman filtering for noise reduction
- Outlier detection and removal
- Signal smoothing algorithms
- Quality assessment metrics

### 📊 Multi-Timeframe Analysis
- Consensus building across timeframes
- Configurable weighting schemes
- Alignment analysis
- Fusion methods (weighted average, majority vote)

### 🤖 ML Integration
- Feature engineering pipeline
- Model ensemble support
- Async prediction capabilities
- Fallback mechanisms

### 📈 Strategy Management
- Dynamic strategy loading
- Performance-based ranking (initial score: 100)
- A/B testing framework
- Risk/reward ratio configuration

## Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Install with GPU support (optional)
pip install polars[gpu]
```

### Basic Usage

```python
from agents.signal_generation_gateway import SignalGenerationGateway, SignalGenerationGatewayConfig
from agents.signal_generation.core.data_models import SignalInput

# Initialize gateway
config = SignalGenerationGatewayConfig()
gateway = SignalGenerationGateway(config)

# Initialize components
await gateway.initialize()

# Create signal input
signal_input = SignalInput(
    symbol="NIFTY",
    timeframe="5min",
    timestamp=datetime.now(),
    ohlcv_data=ohlcv_data,
    indicators=indicators,
    market_regime="bull"
)

# Generate signal
signal = await gateway.generate_signal(signal_input)

if signal:
    print(f"Generated signal: {signal.action} {signal.symbol} at {signal.entry_price}")
    print(f"Confidence: {signal.confidence:.2f}")
    print(f"Risk/Reward: {signal.risk_reward_ratio:.2f}")
```

### Multi-Timeframe Analysis

```python
# Prepare timeframe inputs
timeframe_inputs = {
    "1min": signal_input_1min,
    "5min": signal_input_5min,
    "15min": signal_input_15min
}

# Generate consensus signal
consensus_signal = await gateway.generate_multi_timeframe_signal(timeframe_inputs)
```

## Configuration

### Strategy Configuration (YAML)

```yaml
strategies:
  rsi_oversold_reversal:
    name: "RSI Oversold Reversal"
    enabled: true
    risk_reward_ratio: 2.5
    long: "rsi_14 < 30 and rsi_14 > rsi_14.shift(1) and close > ema_5"
    short: ""
    parameters:
      rsi_period: 14
      rsi_oversold: 30
    symbols: ["NIFTY", "BANKNIFTY"]
    initial_ranking: 100
```

### System Configuration

```yaml
gateway:
  enable_signal_processing: true
  enable_multi_timeframe_fusion: true
  enable_ml_enhancement: true

strategy_evaluator:
  enable_compilation: true
  cache_compiled_expressions: true
  expression_timeout_seconds: 5.0

position_sizer:
  default_method: "kelly"
  total_capital: 100000.0
  max_position_size_percent: 2.0

signal_validator:
  min_confidence: 0.6
  market_hours_only: true
  min_risk_reward_ratio: 1.5
```

## Integration with Other Agents

### Market Monitoring Agent
- Real-time market data feed
- Technical indicator calculations
- Market regime detection

### Backtesting Agent
- Strategy performance validation
- Historical performance metrics
- Real-time strategy testing

### Evolution Agent
- Strategy optimization
- Parameter evolution
- Performance improvement

### Live Trading Agent
- Signal delivery
- Execution feedback
- Portfolio status updates

## Performance Metrics

The system tracks comprehensive performance metrics:

- **Signal Generation**: Signals processed, generated, validated, rejected
- **Strategy Performance**: Win rate, Sharpe ratio, drawdown, ranking scores
- **Processing Time**: Component-level timing metrics
- **Quality Scores**: Signal quality, consensus strength, validation scores

## Testing

```bash
# Run all tests
python tests/signal_generation/test_runner.py all

# Run specific component tests
python tests/signal_generation/test_runner.py component:strategy_evaluator

# Run with coverage
pytest tests/signal_generation/ --cov=agents.signal_generation --cov-report=html
```

## Monitoring and Logging

The system provides comprehensive logging and monitoring:

- Component-level health checks
- Performance metrics collection
- Error tracking and alerting
- Integration status monitoring

## Contributing

1. Follow the modular architecture patterns
2. Add comprehensive tests for new components
3. Update documentation for new features
4. Ensure backward compatibility

## License

This project is part of the Equity Trading System and follows the same licensing terms.
