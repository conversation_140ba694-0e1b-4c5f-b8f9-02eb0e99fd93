#!/usr/bin/env python3
"""
Execution Models

Data models and structures for the execution system.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any


class ExecutionStatus(Enum):
    """Execution status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


@dataclass
class ExecutionRequest:
    """Execution request model"""
    order_id: str
    symbol: str
    side: str  # BUY or SELL
    quantity: int
    price: float
    order_type: str = "LIMIT"
    venue: Optional[str] = None
    strategy_name: Optional[str] = None
    urgency: Optional[float] = 0.5  # 0-1, higher = more urgent
    risk_tolerance: Optional[float] = 0.5  # 0-1, higher = more aggressive
    time_in_force: str = "DAY"
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ExecutionResult:
    """Execution result model"""
    order_id: str
    status: ExecutionStatus
    filled_quantity: int
    average_price: float
    commission: float
    execution_time_ms: float
    slippage_bps: float
    venue: Optional[str]
    execution_details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class OrderRequest:
    """Order request model"""
    symbol: str
    side: str
    quantity: int
    order_type: str
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "DAY"
    strategy_name: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OrderResult:
    """Order result model"""
    order_id: str
    status: str
    filled_quantity: int
    remaining_quantity: int
    average_fill_price: float
    total_commission: float
    executions: List[Dict[str, Any]] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ExecutionMetrics:
    """Execution performance metrics"""
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_execution_time_ms: float
    average_slippage_bps: float
    average_commission_bps: float
    fill_rate_percent: float
    success_rate_percent: float
    timestamp: datetime = field(default_factory=datetime.now)
