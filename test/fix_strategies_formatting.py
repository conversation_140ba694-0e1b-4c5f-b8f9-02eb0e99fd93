#!/usr/bin/env python3
"""
Script to fix strategies.yaml formatting:
1. Add proper spacing between strategies
2. Round decimal values to 4 digits maximum
3. Improve overall readability
"""

import yaml
import json
from pathlib import Path
from decimal import Decimal, ROUND_HALF_UP
import re

def round_decimal_values(obj, max_digits=4):
    """Recursively round decimal values in nested structures"""
    if isinstance(obj, dict):
        return {k: round_decimal_values(v, max_digits) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [round_decimal_values(item, max_digits) for item in obj]
    elif isinstance(obj, float):
        # Round to max_digits decimal places
        return round(obj, max_digits)
    elif isinstance(obj, str):
        # Check if string contains decimal numbers and round them
        def replace_decimal(match):
            num = float(match.group())
            return str(round(num, max_digits))
        
        # Pattern to match decimal numbers
        decimal_pattern = r'\d+\.\d{5,}'  # 5 or more decimal places
        if re.search(decimal_pattern, obj):
            return re.sub(decimal_pattern, replace_decimal, obj)
        return obj
    else:
        return obj

def format_strategies_yaml():
    """Format the strategies.yaml file with proper spacing and decimal rounding"""
    
    strategies_path = Path("config/strategies.yaml")
    if not strategies_path.exists():
        print("strategies.yaml not found!")
        return False
    
    print("Loading strategies.yaml...")
    with open(strategies_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    if not data or 'strategies' not in data:
        print("Invalid strategies.yaml format!")
        return False
    
    strategies = data['strategies']
    print(f"Processing {len(strategies)} strategies...")
    
    # Round decimal values in all strategies
    formatted_strategies = []
    for i, strategy in enumerate(strategies):
        print(f"Processing strategy {i+1}: {strategy.get('name', 'Unknown')}")
        
        # Round all decimal values
        formatted_strategy = round_decimal_values(strategy, max_digits=4)
        formatted_strategies.append(formatted_strategy)
    
    # Update data
    data['strategies'] = formatted_strategies
    
    # Create backup
    backup_path = strategies_path.with_suffix('.yaml.backup')
    print(f"Creating backup: {backup_path}")
    strategies_path.rename(backup_path)
    
    # Write formatted YAML with custom formatting
    print("Writing formatted strategies.yaml...")
    with open(strategies_path, 'w', encoding='utf-8') as f:
        f.write("strategies:\n")
        
        for i, strategy in enumerate(formatted_strategies):
            # Add spacing between strategies (except first one)
            if i > 0:
                f.write("\n")  # Extra line between strategies
            
            # Write strategy with proper indentation
            strategy_yaml = yaml.dump(strategy, default_flow_style=False, sort_keys=False, indent=2)
            
            # Add proper indentation for each line
            lines = strategy_yaml.strip().split('\n')
            for line in lines:
                f.write(f"- {line}\n" if line == lines[0] else f"  {line}\n")
    
    print("✅ Strategies formatting completed successfully!")
    print(f"📁 Backup saved as: {backup_path}")
    return True

if __name__ == "__main__":
    format_strategies_yaml()
