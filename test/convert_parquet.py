import pandas as pd
import os
import pyarrow.parquet as pq
import asyncio
import functools

async def process_file(semaphore, source_path, target_path, file_name):
    """
    Asynchronously converts a single parquet file.
    """
    async with semaphore:
        # Ensure target subdirectory exists
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        try:
            print(f"Converting {source_path} to {target_path}...")
            # Read the parquet file (using run_in_executor for blocking I/O)
            loop = asyncio.get_running_loop()
            df = await loop.run_in_executor(
                None, functools.partial(pd.read_parquet, source_path)
            )

            # Write with zstd compression and level 15 (using run_in_executor for blocking I/O)
            await loop.run_in_executor(
                None, functools.partial(df.to_parquet, target_path, compression="zstd", compression_level=15)
            )
            print(f"Successfully converted {file_name}")
        except Exception as e:
            print(f"Error converting {file_name}: {e}")

async def convert_parquet_files_parallel(source_dir="data/historical/backup", target_dir="data/historical", max_parallel_tasks=40):
    """
    Converts all parquet files in the source directory to new parquet files
    in the target directory with zstd compression and level 15, using parallel processing.

    Args:
        source_dir (str): The directory containing the original parquet files.
        target_dir (str): The directory where the compressed parquet files will be saved.
        max_parallel_tasks (int): The maximum number of files to process in parallel.
    """
    os.makedirs(source_dir, exist_ok=True)
    os.makedirs(target_dir, exist_ok=True)

    files_to_process = []
    for root, _, files in os.walk(source_dir):
        for file in files:
            if file.endswith(".parquet"):
                source_path = os.path.join(root, file)
                relative_path = os.path.relpath(source_path, source_dir)
                target_path = os.path.join(target_dir, relative_path)
                files_to_process.append((source_path, target_path, file))

    semaphore = asyncio.Semaphore(max_parallel_tasks)
    tasks = [
        process_file(semaphore, source_path, target_path, file_name)
        for source_path, target_path, file_name in files_to_process
    ]

    await asyncio.gather(*tasks)

if __name__ == "__main__":
    asyncio.run(convert_parquet_files_parallel())
