#!/usr/bin/env python3
"""
Fix strategies.yaml by removing numpy serialization issues
"""

import yaml
import re
from pathlib import Path

def fix_strategies_yaml():
    """Fix the strategies.yaml file by removing numpy serialization"""
    
    strategies_path = Path("config/strategies.yaml")
    backup_path = Path("config/strategies_backup.yaml")
    
    print("🔧 Fixing strategies.yaml numpy serialization issues...")
    
    # Create backup
    if strategies_path.exists():
        import shutil
        shutil.copy2(strategies_path, backup_path)
        print(f"✅ Backup created: {backup_path}")
    
    # Read the raw file content
    with open(strategies_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📊 Original file size: {len(content)} characters")
    
    # Remove numpy serialization patterns
    patterns_to_remove = [
        r'!!python/object/apply:numpy\._core\.multiarray\.scalar.*?\n',
        r'!!python/object/apply:numpy\.dtype.*?\n',
        r'- &id\d+.*?\n',
        r'args:.*?\n',
        r'- f8.*?\n',
        r'- false.*?\n',
        r'- true.*?\n',
        r'state: !!python/tuple.*?\n',
        r'- \d+.*?\n',
    ]
    
    # Clean up the content
    cleaned_content = content
    for pattern in patterns_to_remove:
        cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.MULTILINE | re.DOTALL)
    
    # Fix broken YAML structure by replacing problematic values with defaults
    replacements = {
        r'sharpe_ratio:\s*$': 'sharpe_ratio: 0.0',
        r'total_return:\s*$': 'total_return: 0.0',
        r'max_drawdown:\s*$': 'max_drawdown: 0.0',
        r'win_rate:\s*$': 'win_rate: 0.0',
        r'profit_factor:\s*$': 'profit_factor: 1.0',
        r'best_risk_reward:\s*$': 'best_risk_reward: 1.0',
    }
    
    for pattern, replacement in replacements.items():
        cleaned_content = re.sub(pattern, replacement, cleaned_content, flags=re.MULTILINE)
    
    # Remove empty lines and fix indentation
    lines = cleaned_content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Skip completely empty lines in the middle of structures
        if line.strip() == '' and len(cleaned_lines) > 0 and cleaned_lines[-1].strip() != '':
            continue
        cleaned_lines.append(line)
    
    cleaned_content = '\n'.join(cleaned_lines)
    
    print(f"📊 Cleaned file size: {len(cleaned_content)} characters")
    print(f"📉 Reduction: {len(content) - len(cleaned_content)} characters")
    
    # Write the cleaned content
    with open(strategies_path, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    # Test if the YAML can be loaded
    try:
        with open(strategies_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        strategies = data.get('strategies', [])
        print(f"✅ YAML file fixed successfully!")
        print(f"📊 Loaded {len(strategies)} strategies")
        
        # Count strategies by ranking
        ranking_counts = {}
        for strategy in strategies:
            ranking = strategy.get('ranking', 0)
            ranking_counts[ranking] = ranking_counts.get(ranking, 0) + 1
        
        print("📈 Strategy distribution by ranking:")
        for ranking in sorted(ranking_counts.keys()):
            print(f"   Ranking {ranking}: {ranking_counts[ranking]} strategies")
        
        return True
        
    except Exception as e:
        print(f"❌ YAML file still has issues: {e}")
        
        # Restore backup
        if backup_path.exists():
            import shutil
            shutil.copy2(backup_path, strategies_path)
            print("🔄 Restored from backup")
        
        return False

if __name__ == "__main__":
    success = fix_strategies_yaml()
    if success:
        print("🎉 strategies.yaml fixed successfully!")
    else:
        print("❌ Failed to fix strategies.yaml")
