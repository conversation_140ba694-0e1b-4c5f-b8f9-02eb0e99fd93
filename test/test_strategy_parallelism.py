#!/usr/bin/env python3
"""
Test script for strategy-level parallelism

This script tests that multiple strategies can be processed concurrently
instead of sequentially, addressing the bottleneck identified in the logs.
"""

import asyncio
import sys
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

try:
    from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent
    print("✅ Successfully imported EnhancedStrategyEvolutionAgent")
except ImportError as e:
    print(f"❌ Failed to import EnhancedStrategyEvolutionAgent: {e}")
    sys.exit(1)

async def simulate_strategy_processing(strategy_idx: int, strategy_name: str, processing_time: float = 2.0):
    """Simulate processing a single strategy"""
    start_time = time.time()
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] 🧬 Processing strategy {strategy_idx}: {strategy_name} (concurrent)")
    
    # Simulate processing time
    await asyncio.sleep(processing_time)
    
    end_time = time.time()
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] ✅ Strategy {strategy_idx}: {strategy_name} completed in {end_time - start_time:.3f}s")
    
    return f"result_for_{strategy_name}"

async def test_sequential_processing():
    """Test sequential strategy processing (old method)"""
    print("\n🔄 Testing Sequential Strategy Processing (Old Method)")
    start_time = time.time()
    
    strategies = [
        "Bollinger_Bounce_360ONE_1min",
        "RSI_Reversal_HDFC_3min", 
        "MACD_Crossover_RELIANCE_5min",
        "EMA_Strategy_TCS_1min"
    ]
    
    results = []
    for i, strategy in enumerate(strategies):
        result = await simulate_strategy_processing(i + 1, strategy)
        results.append(result)
    
    total_time = time.time() - start_time
    print(f"📊 Sequential Processing: {len(strategies)} strategies in {total_time:.3f}s")
    return results, total_time

async def test_concurrent_processing():
    """Test concurrent strategy processing (new method)"""
    print("\n🚀 Testing Concurrent Strategy Processing (New Method)")
    start_time = time.time()
    
    strategies = [
        "Bollinger_Bounce_360ONE_1min",
        "RSI_Reversal_HDFC_3min", 
        "MACD_Crossover_RELIANCE_5min",
        "EMA_Strategy_TCS_1min"
    ]
    
    # Create concurrent tasks
    strategy_tasks = []
    for i, strategy in enumerate(strategies):
        task = asyncio.create_task(simulate_strategy_processing(i + 1, strategy))
        strategy_tasks.append(task)
    
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] 🚀 Launching {len(strategy_tasks)} concurrent strategy processing tasks")
    
    # Run all strategies concurrently
    results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
    
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] ✅ Concurrent strategy processing completed")
    
    total_time = time.time() - start_time
    print(f"📊 Concurrent Processing: {len(strategies)} strategies in {total_time:.3f}s")
    return results, total_time

async def test_semaphore_controlled_processing():
    """Test semaphore-controlled concurrent processing (production method)"""
    print("\n🎯 Testing Semaphore-Controlled Concurrent Processing (Production Method)")
    start_time = time.time()
    
    strategies = [
        "Bollinger_Bounce_360ONE_1min",
        "RSI_Reversal_HDFC_3min", 
        "MACD_Crossover_RELIANCE_5min",
        "EMA_Strategy_TCS_1min",
        "Stochastic_INFY_1min",
        "Williams_R_WIPRO_3min"
    ]
    
    # Create semaphore for controlled concurrency (max 2 concurrent strategies)
    semaphore = asyncio.Semaphore(2)
    
    async def process_with_semaphore(strategy_idx: int, strategy_name: str):
        async with semaphore:
            return await simulate_strategy_processing(strategy_idx, strategy_name)
    
    # Create concurrent tasks with semaphore control
    strategy_tasks = []
    for i, strategy in enumerate(strategies):
        task = asyncio.create_task(process_with_semaphore(i + 1, strategy))
        strategy_tasks.append(task)
    
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] 🚀 Launching {len(strategy_tasks)} semaphore-controlled concurrent tasks (max 2 concurrent)")
    
    # Run all strategies with controlled concurrency
    results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
    
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] ✅ Semaphore-controlled processing completed")
    
    total_time = time.time() - start_time
    print(f"📊 Semaphore-Controlled Processing: {len(strategies)} strategies in {total_time:.3f}s")
    return results, total_time

async def test_agent_initialization():
    """Test that the agent initializes with proper strategy-level parallelism"""
    print("\n🔧 Testing Agent Initialization with Strategy-Level Parallelism")
    
    try:
        agent = EnhancedStrategyEvolutionAgent()
        
        # Check if strategy processing semaphore is initialized
        if hasattr(agent, 'strategy_processing_semaphore'):
            semaphore_value = agent.strategy_processing_semaphore._value
            print(f"✅ Strategy processing semaphore initialized: {semaphore_value} concurrent strategies allowed")
        else:
            print("❌ Strategy processing semaphore not found")
        
        # Check GPU manager configuration
        if hasattr(agent, 'gpu_manager'):
            gpu_mgr = agent.gpu_manager
            print(f"✅ GPU Manager - Concurrent Strategies: {gpu_mgr.max_concurrent_strategies}")
            print(f"✅ GPU Manager - Available Workers: {len(gpu_mgr.gpu_worker_pool)}")
            print(f"✅ GPU Manager - CUDA Streams: {len(gpu_mgr.cuda_streams)}")
        else:
            print("❌ GPU manager not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False

async def main():
    """Main test function"""
    print("="*80)
    print("🧪 STRATEGY-LEVEL PARALLELISM TEST")
    print("="*80)
    
    # Test agent initialization
    init_success = await test_agent_initialization()
    if not init_success:
        print("❌ Agent initialization failed, skipping other tests")
        return
    
    # Test different processing approaches
    seq_results, seq_time = await test_sequential_processing()
    conc_results, conc_time = await test_concurrent_processing()
    sem_results, sem_time = await test_semaphore_controlled_processing()
    
    # Calculate improvements
    print("\n" + "="*80)
    print("📈 STRATEGY PARALLELISM ANALYSIS")
    print("="*80)
    print(f"Sequential Processing Time: {seq_time:.3f}s")
    print(f"Concurrent Processing Time: {conc_time:.3f}s")
    print(f"Semaphore-Controlled Time: {sem_time:.3f}s")
    
    if seq_time > 0:
        conc_speedup = seq_time / conc_time if conc_time > 0 else 0
        sem_speedup = seq_time / sem_time if sem_time > 0 else 0
        
        print(f"Concurrent Speedup: {conc_speedup:.2f}x")
        print(f"Semaphore-Controlled Speedup: {sem_speedup:.2f}x")
        
        print("\n🎯 EXPECTED PRODUCTION BEHAVIOR:")
        print("- Sequential: Each strategy waits for the previous one to complete")
        print("- Concurrent: All strategies run simultaneously (may cause resource contention)")
        print("- Semaphore-Controlled: Limited concurrent strategies (optimal for GPU resources)")
        
        if sem_speedup > 1.5:
            print("✅ EXCELLENT: Semaphore-controlled processing shows significant improvement!")
        elif sem_speedup > 1.1:
            print("✅ GOOD: Some improvement with controlled concurrency")
        else:
            print("⚠️  LIMITED: May need further optimization")

if __name__ == "__main__":
    asyncio.run(main())
