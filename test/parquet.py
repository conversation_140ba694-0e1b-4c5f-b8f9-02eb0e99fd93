import polars as pl
import numpy as np
import time
import os
import sys
import logging

# Import CUDA utilities from the backtesting agent
try:
    # Temporarily add the parent directory to sys.path to import utils
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from utils.real_gpu_accelerator import RealGPUAccelerator, CUDAOptimizer
    sys.path.pop(0) # Remove it after import
    
    # Initialize the global accelerator and optimizer
    real_gpu_accelerator_instance = RealGPUAccelerator()
    cuda_optimizer = CUDAOptimizer(real_gpu_accelerator_instance)
    CUDA_AVAILABLE = cuda_optimizer.cuda_available
    
    if CUDA_AVAILABLE:
        import cudf # For GPU operations
        import pyarrow as pa # For direct conversion to arrow table
        print(f"🚀 CUDA acceleration detected: {cuda_optimizer.get_memory_info().get('device_name', 'Unknown')}")
        cuda_optimizer.optimize_polars_for_gpu()
    else:
        print("⚠️ CUDA not available. Running only Polars benchmarks.")
        
except ImportError as e:
    print(f"⚠️ Could not import GPU acceleration libraries (cudf, utils.real_gpu_accelerator): {e}")
    print("Running only Polars benchmarks.")
    CUDA_AVAILABLE = False
except Exception as e:
    print(f"⚠️ Error during CUDA initialization: {e}")
    print("Running only Polars benchmarks.")
    CUDA_AVAILABLE = False

# Settings
n_rows = 1_000_000
n_cols = 40
output_dir = "compression_benchmark"
os.makedirs(output_dir, exist_ok=True)

# Generate synthetic float data with 8 decimal places
print("Generating DataFrame...")
data = {
    f"col_{i}": np.round(np.random.rand(n_rows), 8)
    for i in range(n_cols)
}
df_polars = pl.DataFrame(data) # Renamed to avoid conflict

# Helper function to benchmark write, read, and file size for Polars
def benchmark_polars_parquet(df, compression, file_name, compression_level=None):
    file_path = os.path.join(output_dir, file_name)

    # Write
    print(f"\nWriting with Polars {compression}...")
    start_write = time.time()
    df.write_parquet(
        file_path,
        compression=compression,
        compression_level=compression_level
    )
    write_time = time.time() - start_write

    # File size
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

    # Read
    print(f"Reading with Polars {compression}...")
    start_read = time.time()
    _ = pl.read_parquet(file_path)
    read_time = time.time() - start_read

    # Report
    print(f"POLARS {compression.upper()} | Write Time: {write_time:.2f}s | Read Time: {read_time:.2f}s | File Size: {file_size_mb:.2f} MB")
    return {
        "library": "polars",
        "compression": compression,
        "write_time": write_time,
        "read_time": read_time,
        "file_size_mb": file_size_mb
    }

# Helper function to benchmark write, read, and file size for cuDF
def benchmark_cudf_parquet(df_cudf, compression, file_name, compression_level=None):
    file_path = os.path.join(output_dir, file_name)

    # Write
    print(f"\nWriting with cuDF {compression} (GPU-accelerated)...")
    start_write = time.time()
    df_cudf.to_parquet(
        file_path,
        compression=compression,
        compression_level=compression_level
    )
    write_time = time.time() - start_write

    # File size
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

    # Read
    print(f"Reading with cuDF {compression} (GPU-accelerated)...")
    start_read = time.time()
    _ = cudf.read_parquet(file_path)
    read_time = time.time() - start_read

    # Report
    print(f"CUDF {compression.upper()} | Write Time: {write_time:.2f}s | Read Time: {read_time:.2f}s | File Size: {file_size_mb:.2f} MB")
    return {
        "library": "cudf",
        "compression": compression,
        "write_time": write_time,
        "read_time": read_time,
        "file_size_mb": file_size_mb
    }

# Run benchmarks
results = []
# Polars benchmarks (with corrected brotli compression level)
results.append(benchmark_polars_parquet(df_polars, compression="brotli", file_name="data_polars_brotli.parquet", compression_level=11))
results.append(benchmark_polars_parquet(df_polars, compression="zstd", file_name="data_polars_zstd.parquet", compression_level=15))

if CUDA_AVAILABLE:
    # Convert Polars DataFrame to cuDF DataFrame
    print("\nConverting Polars DataFrame to cuDF DataFrame...")
    try:
        df_cudf = cudf.DataFrame.from_arrow(df_polars.to_arrow())
        # cuDF benchmarks
        results.append(benchmark_cudf_parquet(df_cudf, compression="brotli", file_name="data_cudf_brotli.parquet", compression_level=8))
        results.append(benchmark_cudf_parquet(df_cudf, compression="zstd", file_name="data_cudf_zstd.parquet", compression_level=15))
        cuda_optimizer.cleanup_memory() # Clean up GPU memory after benchmarks
    except Exception as e:
        print(f"⚠️ Failed to run cuDF benchmarks: {e}")
        print("Ensure `cudf` is correctly installed and your CUDA environment is properly configured.")
        print("You can install `cudf` by following the instructions on the RAPIDS website: https://rapids.ai/start.html")
        print("A typical installation might involve `conda install -c rapidsai -c conda-forge -c nvidia cudf`")


# Summary
print("\n📊 Summary:")
for r in results:
    print(f"{r['library'].upper():<6} {r['compression'].upper():<6} | Write: {r['write_time']:.2f}s | Read: {r['read_time']:.2f}s | Size: {r['file_size_mb']:.2f} MB")

if not CUDA_AVAILABLE:
    print("\nNOTE: To enable GPU benchmarks, you need to have a CUDA-enabled GPU and the `cudf` library installed.")
    print("Please ensure your CUDA Toolkit is correctly installed and `LD_LIBRARY_PATH` is set.")
    print("You can install `cudf` by following the instructions on the RAPIDS website: https://rapids.ai/start.html")
    print("A typical installation might involve `conda install -c rapidsai -c conda-forge -c nvidia cudf`")
